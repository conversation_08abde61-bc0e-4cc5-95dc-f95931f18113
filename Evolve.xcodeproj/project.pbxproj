// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		2118B3742DDF4E5E0067EFF5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2118B35E2DDF4E5D0067EFF5 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2118B3652DDF4E5D0067EFF5;
			remoteInfo = Evolve;
		};
		2118B37E2DDF4E5E0067EFF5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2118B35E2DDF4E5D0067EFF5 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2118B3652DDF4E5D0067EFF5;
			remoteInfo = Evolve;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		2118B3662DDF4E5D0067EFF5 /* Evolve.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Evolve.app; sourceTree = BUILT_PRODUCTS_DIR; };
		2118B3732DDF4E5E0067EFF5 /* EvolveTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = EvolveTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		2118B37D2DDF4E5E0067EFF5 /* EvolveUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = EvolveUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		2118B3682DDF4E5D0067EFF5 /* Evolve */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Evolve;
			sourceTree = "<group>";
		};
		2118B3762DDF4E5E0067EFF5 /* EvolveTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = EvolveTests;
			sourceTree = "<group>";
		};
		2118B3802DDF4E5E0067EFF5 /* EvolveUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = EvolveUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		2118B3632DDF4E5D0067EFF5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2118B3702DDF4E5E0067EFF5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2118B37A2DDF4E5E0067EFF5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2118B35D2DDF4E5D0067EFF5 = {
			isa = PBXGroup;
			children = (
				2118B3682DDF4E5D0067EFF5 /* Evolve */,
				2118B3762DDF4E5E0067EFF5 /* EvolveTests */,
				2118B3802DDF4E5E0067EFF5 /* EvolveUITests */,
				2118B3672DDF4E5D0067EFF5 /* Products */,
			);
			sourceTree = "<group>";
		};
		2118B3672DDF4E5D0067EFF5 /* Products */ = {
			isa = PBXGroup;
			children = (
				2118B3662DDF4E5D0067EFF5 /* Evolve.app */,
				2118B3732DDF4E5E0067EFF5 /* EvolveTests.xctest */,
				2118B37D2DDF4E5E0067EFF5 /* EvolveUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		2118B3652DDF4E5D0067EFF5 /* Evolve */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2118B3872DDF4E5E0067EFF5 /* Build configuration list for PBXNativeTarget "Evolve" */;
			buildPhases = (
				2118B3622DDF4E5D0067EFF5 /* Sources */,
				2118B3632DDF4E5D0067EFF5 /* Frameworks */,
				2118B3642DDF4E5D0067EFF5 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				2118B3682DDF4E5D0067EFF5 /* Evolve */,
			);
			name = Evolve;
			packageProductDependencies = (
			);
			productName = Evolve;
			productReference = 2118B3662DDF4E5D0067EFF5 /* Evolve.app */;
			productType = "com.apple.product-type.application";
		};
		2118B3722DDF4E5E0067EFF5 /* EvolveTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2118B38A2DDF4E5E0067EFF5 /* Build configuration list for PBXNativeTarget "EvolveTests" */;
			buildPhases = (
				2118B36F2DDF4E5E0067EFF5 /* Sources */,
				2118B3702DDF4E5E0067EFF5 /* Frameworks */,
				2118B3712DDF4E5E0067EFF5 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				2118B3752DDF4E5E0067EFF5 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				2118B3762DDF4E5E0067EFF5 /* EvolveTests */,
			);
			name = EvolveTests;
			packageProductDependencies = (
			);
			productName = EvolveTests;
			productReference = 2118B3732DDF4E5E0067EFF5 /* EvolveTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		2118B37C2DDF4E5E0067EFF5 /* EvolveUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2118B38D2DDF4E5E0067EFF5 /* Build configuration list for PBXNativeTarget "EvolveUITests" */;
			buildPhases = (
				2118B3792DDF4E5E0067EFF5 /* Sources */,
				2118B37A2DDF4E5E0067EFF5 /* Frameworks */,
				2118B37B2DDF4E5E0067EFF5 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				2118B37F2DDF4E5E0067EFF5 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				2118B3802DDF4E5E0067EFF5 /* EvolveUITests */,
			);
			name = EvolveUITests;
			packageProductDependencies = (
			);
			productName = EvolveUITests;
			productReference = 2118B37D2DDF4E5E0067EFF5 /* EvolveUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		2118B35E2DDF4E5D0067EFF5 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					2118B3652DDF4E5D0067EFF5 = {
						CreatedOnToolsVersion = 16.3;
					};
					2118B3722DDF4E5E0067EFF5 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 2118B3652DDF4E5D0067EFF5;
					};
					2118B37C2DDF4E5E0067EFF5 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 2118B3652DDF4E5D0067EFF5;
					};
				};
			};
			buildConfigurationList = 2118B3612DDF4E5D0067EFF5 /* Build configuration list for PBXProject "Evolve" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 2118B35D2DDF4E5D0067EFF5;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 2118B3672DDF4E5D0067EFF5 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				2118B3652DDF4E5D0067EFF5 /* Evolve */,
				2118B3722DDF4E5E0067EFF5 /* EvolveTests */,
				2118B37C2DDF4E5E0067EFF5 /* EvolveUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		2118B3642DDF4E5D0067EFF5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2118B3712DDF4E5E0067EFF5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2118B37B2DDF4E5E0067EFF5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		2118B3622DDF4E5D0067EFF5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2118B36F2DDF4E5E0067EFF5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2118B3792DDF4E5E0067EFF5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		2118B3752DDF4E5E0067EFF5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2118B3652DDF4E5D0067EFF5 /* Evolve */;
			targetProxy = 2118B3742DDF4E5E0067EFF5 /* PBXContainerItemProxy */;
		};
		2118B37F2DDF4E5E0067EFF5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2118B3652DDF4E5D0067EFF5 /* Evolve */;
			targetProxy = 2118B37E2DDF4E5E0067EFF5 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		2118B3852DDF4E5E0067EFF5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 5S7PV8UMPX;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		2118B3862DDF4E5E0067EFF5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 5S7PV8UMPX;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		2118B3882DDF4E5E0067EFF5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5S7PV8UMPX;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "Evolve需要相册权限来保存您喜欢的图片";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "Evolve需要访问相册来查看和管理您的图片";
				INFOPLIST_KEY_NSUserNotificationsUsageDescription = "Evolve需要通知权限来提醒您完成习惯和接收好友消息";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = aihge.com.Evolve;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		2118B3892DDF4E5E0067EFF5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5S7PV8UMPX;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "Evolve需要相册权限来保存您喜欢的图片";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "Evolve需要访问相册来查看和管理您的图片";
				INFOPLIST_KEY_NSUserNotificationsUsageDescription = "Evolve需要通知权限来提醒您完成习惯和接收好友消息";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = aihge.com.Evolve;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		2118B38B2DDF4E5E0067EFF5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5S7PV8UMPX;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = aihge.com.EvolveTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Evolve.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Evolve";
			};
			name = Debug;
		};
		2118B38C2DDF4E5E0067EFF5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5S7PV8UMPX;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = aihge.com.EvolveTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Evolve.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Evolve";
			};
			name = Release;
		};
		2118B38E2DDF4E5E0067EFF5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5S7PV8UMPX;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = aihge.com.EvolveUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Evolve;
			};
			name = Debug;
		};
		2118B38F2DDF4E5E0067EFF5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5S7PV8UMPX;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = aihge.com.EvolveUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Evolve;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2118B3612DDF4E5D0067EFF5 /* Build configuration list for PBXProject "Evolve" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2118B3852DDF4E5E0067EFF5 /* Debug */,
				2118B3862DDF4E5E0067EFF5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2118B3872DDF4E5E0067EFF5 /* Build configuration list for PBXNativeTarget "Evolve" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2118B3882DDF4E5E0067EFF5 /* Debug */,
				2118B3892DDF4E5E0067EFF5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2118B38A2DDF4E5E0067EFF5 /* Build configuration list for PBXNativeTarget "EvolveTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2118B38B2DDF4E5E0067EFF5 /* Debug */,
				2118B38C2DDF4E5E0067EFF5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2118B38D2DDF4E5E0067EFF5 /* Build configuration list for PBXNativeTarget "EvolveUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2118B38E2DDF4E5E0067EFF5 /* Debug */,
				2118B38F2DDF4E5E0067EFF5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 2118B35E2DDF4E5D0067EFF5 /* Project object */;
}
