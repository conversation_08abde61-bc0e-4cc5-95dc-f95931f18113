# Evolve项目ID使用规范文档

## 📋 概述

本文档规范了Evolve项目中各种ID类型的正确使用场景，确保数据一致性和系统稳定性。

## 🔑 核心ID类型定义

### 1. 用户ID (`EAUser.id: UUID`)
- **定义**: 用户实体的唯一标识符
- **用途**: 用户身份验证、权限控制、数据隔离
- **特点**: 永久不变，跨系统唯一

### 2. 社交档案ID (`EAUserSocialProfile.id: UUID`)
- **定义**: 用户社交档案的唯一标识符
- **用途**: 社交关系建立、好友系统、社区功能
- **特点**: 与用户ID一对一关联，但在社交场景中使用

### 3. 好友关系ID (`EAFriendship.id: UUID`)
- **定义**: 好友关系实体的唯一标识符
- **用途**: 聊天导航、关系管理、消息关联
- **特点**: 代表两个用户之间的具体关系

### 4. 好友请求ID (`EAFriendRequest.id: UUID`)
- **定义**: 好友请求实体的唯一标识符
- **用途**: 请求处理、状态跟踪
- **特点**: 临时性，处理后可能被删除

## 🎯 使用场景规范

### 好友功能场景

#### ✅ 正确使用模式

```swift
// 1. 好友请求发送 - 使用用户ID
func sendFriendRequest(to targetUserID: UUID, message: String?) async throws

// 2. 好友关系查询 - 使用社交档案ID
func fetchUserFriendships(userProfileId: UUID) async throws -> [EAFriendship]

// 3. 聊天导航 - 使用好友关系ID
NavigationLink(value: EACommunityNavigationDestination.friendChat(friendshipId))

// 4. 屏蔽操作 - 使用用户ID
func blockUser(currentUserID: UUID, targetUserID: UUID) async
```

#### ❌ 错误使用模式

```swift
// 错误：使用社交档案ID进行屏蔽
func blockUser(socialProfileId: UUID) // ❌

// 错误：使用用户ID建立好友关系
func createFriendship(userId1: UUID, userId2: UUID) // ❌

// 错误：混用ID类型
func checkFriendship(userId: UUID, profileId: UUID) // ❌
```

### 聊天功能场景

#### ✅ 正确使用模式

```swift
// 1. 聊天页面初始化 - 使用好友关系ID
struct EAFriendChatView: View {
    let friendshipId: UUID
}

// 2. 消息发送 - 通过好友关系获取参与者
func sendMessage(friendshipId: UUID, content: String) async throws

// 3. 聊天历史加载 - 使用好友关系ID
func loadChatHistory(for friendshipId: UUID) async throws -> [EAFriendMessage]
```

### 屏蔽功能场景

#### ✅ 正确使用模式

```swift
// 1. 屏蔽用户 - 始终使用用户ID
func blockUser(currentUserID: UUID, targetUserID: UUID) async

// 2. 检查屏蔽状态 - 使用用户ID
func isUserBlocked(userId: UUID) -> Bool

// 3. 屏蔽列表管理 - 使用用户ID
var blockedUserIds: Set<UUID>
```

## 🔧 Repository层规范

### 方法命名规范

```swift
// 用户相关 - 使用 userId 参数
func fetchUser(by userId: UUID) async -> EAUser?
func updateUser(userId: UUID, data: UserData) async throws

// 社交档案相关 - 使用 profileId 参数
func fetchSocialProfile(by profileId: UUID) async -> EAUserSocialProfile?
func updateSocialProfile(profileId: UUID, data: ProfileData) async throws

// 好友关系相关 - 使用 friendshipId 参数
func fetchFriendship(by friendshipId: UUID) async throws -> EAFriendship?
func updateFriendship(friendshipId: UUID, status: Status) async throws
```

### ID转换规范

```swift
// 用户ID → 社交档案ID
func getSocialProfileId(for userId: UUID) -> UUID? {
    return user.socialProfile?.id
}

// 社交档案ID → 用户ID
func getUserId(for profileId: UUID) -> UUID? {
    return socialProfile.user?.id
}

// 好友关系ID → 参与者用户ID
func getParticipantUserIds(for friendshipId: UUID) -> (UUID, UUID)? {
    guard let friendship = getFriendship(friendshipId),
          let user1Id = friendship.initiatorProfile?.user?.id,
          let user2Id = friendship.friendProfile?.user?.id else {
        return nil
    }
    return (user1Id, user2Id)
}
```

## 🚨 常见错误和修复

### 错误1: Repository方法参数命名误导

**问题代码**:
```swift
func fetchReceivedRequests(userProfileId: UUID) async throws -> [EAFriendRequest]
// 实际处理的是用户ID，但参数名暗示是社交档案ID
```

**修复方案**:
```swift
func fetchReceivedRequests(userId: UUID) async throws -> [EAFriendRequest]
// 或者
func fetchReceivedRequestsByProfileId(profileId: UUID) async throws -> [EAFriendRequest]
```

### 错误2: ID类型混用

**问题代码**:
```swift
func blockFriend(friendshipId: UUID) {
    // 错误：应该屏蔽用户，而不是好友关系
}
```

**修复方案**:
```swift
func blockFriend(friendshipId: UUID) {
    // 1. 通过好友关系ID获取对方用户ID
    guard let friendship = getFriendship(friendshipId),
          let otherUserId = getOtherUserId(in: friendship) else { return }
    
    // 2. 使用用户ID进行屏蔽
    blockUser(targetUserID: otherUserId)
}
```

## 📊 验证清单

### 开发时检查清单

- [ ] 屏蔽功能是否使用用户ID？
- [ ] 好友关系建立是否使用社交档案ID？
- [ ] 聊天导航是否使用好友关系ID？
- [ ] Repository方法命名是否明确ID类型？
- [ ] 是否存在ID类型转换的地方？
- [ ] 错误处理是否考虑ID不存在的情况？

### 代码审查清单

- [ ] 方法参数名是否与实际处理的ID类型一致？
- [ ] 是否存在硬编码的ID转换逻辑？
- [ ] 跨Context操作是否正确传递ID而非对象？
- [ ] 导航参数是否使用正确的ID类型？

## 🎯 最佳实践

1. **明确命名**: 方法和参数名应明确指示ID类型
2. **类型安全**: 使用强类型而非通用UUID
3. **文档注释**: 在关键方法上注明ID类型要求
4. **错误处理**: 对ID不存在的情况进行适当处理
5. **测试覆盖**: 确保ID转换逻辑有充分的测试覆盖

## 📝 更新日志

- **2025-07-11**: 初始版本，基于系统性ID使用规范审查结果创建
- **修复内容**: 解决了好友列表崩溃问题和导航冲突问题
