# Evolve iOS - 核心开发规范 (AI必读)

**🚨 最高优先级架构约束 - 绝对不可违反**

## 🔒 SwiftData关系设计 (iOS 18+关键要求)

### 单端inverse规则 (绝对不可妥协)
```swift
// ✅ 正确：只在一端使用@Relationship(inverse:)
@Model class EAUser {
    @Relationship(deleteRule: .cascade, inverse: \EAHabit.user)
    var habits: [EAHabit] = []
}
@Model class EAHabit {
    var user: EAUser? // 普通属性，SwiftData自动维护关系
}

// ❌ 严禁：两端都使用@Relationship
@Model class EAHabit {
    @Relationship(inverse: \EAUser.habits) // 禁止！
    var user: EAUser?
}
```

### 🚫 外键模式严格禁止
```swift
// ❌ 绝对禁止：外键字段
@Model class EAPost {
    var userId: UUID? // 禁止！
    var habitId: UUID? // 禁止！
}

// ✅ 正确：使用关系
@Model class EAPost {
    var user: EAUser? // 只用关系
    var habit: EAHabit? // 只用关系
}
```

## 🚫 单例模式严格禁止

```swift
// ❌ 绝对禁止：单例模式
class BadService {
    static let shared = BadService() // 禁止！
}

// ✅ 正确：依赖注入
@MainActor
class GoodService: ObservableObject {
    init() {} // 公开初始化器
}

// App入口配置依赖注入
@StateObject private var goodService = GoodService()
.environment(\.goodService, goodService)
```

## 🔒 Repository模式强制执行

```swift
// ❌ 禁止：直接ModelContext访问
@MainActor
class BadViewModel: ObservableObject {
    @Environment(\.modelContext) private var modelContext // 禁止！
}

// ✅ 正确：通过Repository访问数据
@MainActor
class GoodViewModel: ObservableObject {
    @Environment(\.repositoryContainer) private var repositories
    
    func loadData() async {
        let data = await repositories.userRepository.fetchUsers()
    }
}

// Repository必须使用@ModelActor
@ModelActor
actor EAUserRepository {
    func fetchUsers() async -> [EAUser] {
        // 数据访问逻辑
    }
}
```

## 🔒 Context一致性要求 (iOS 18+崩溃预防)

```swift
// ✅ 所有Repository必须共享同一个ModelContainer
@main
struct App: App {
    @MainActor
    static let sharedContainer = try! ModelContainer(for: EAUser.self, EAHabit.self)
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .modelContainer(Self.sharedContainer)
        }
    }
}
```

## 🔒 关系赋值安全顺序 (iOS 18+要求)

```swift
// ✅ 正确：安全赋值顺序
@ModelActor
actor Repository {
    func createHabit(for user: EAUser) async throws {
        let habit = EAHabit(name: "新习惯")
        modelContext.insert(habit) // 1. 先插入Context
        habit.user = user          // 2. 再赋值关系
        try modelContext.save()    // 3. 保存
    }
}
```

## 🚨 ViewModel线程安全

```swift
// ✅ 所有ViewModel必须标记@MainActor
@MainActor
final class EAViewModel: ObservableObject {
    @Published var data: [Item] = []
    
    func loadData() async {
        // UI更新在主线程
    }
}
```

## 🚫 调试代码禁止

```swift
// ❌ 严禁：print调试语句
print("调试信息") // 禁止在生产代码中！

// ✅ 正确：使用条件编译
#if DEBUG
// 调试代码只在调试环境
#endif
```

## 📋 核心检查清单

开发任何功能前必须确认：
- [ ] 使用关系而非外键字段？
- [ ] 遵循单端inverse规则？
- [ ] 避免单例模式？
- [ ] 通过Repository访问数据？
- [ ] ViewModel标记@MainActor？
- [ ] 清理所有print语句？

## 🚨 违规后果

违反以上任一规则将导致：
- iOS 18+环境崩溃
- 数据一致性问题
- 架构审查失败
- 必须重构代码

**记住：架构规范 > 功能实现 > 性能优化** 