---
description: 
globs: 
alwaysApply: true
---
注意！注意！完成修复优化任务的时候禁止更新README.md，只在执行新建开发的任务才可以更新到README.md！所有的修复都不准采取临时方案，必须制定永久性解决方案，无法永久解决的问题请告知我。
在需要向我发送日期或者相关报告、文档需要填写或记录时间日期的时候，请调用time MCP来获取准确的当地时间。
注意！在执行开发或修复的时候，不要写print()语句，除非是修复问题需要调试，并且经过我的同意才写print()语句。
注意！在Swiftdata数据库方面必须按照开发文档规范执行，也不得随意修改Swiftdata设计框架和底层模型，需要修改和调整需要经过我同意！例如数据模型开发、状态管理、关系赋值、关系定义必须符合开发规范文档.md和IOS移动端开发规范的要求！
注意！所有修复工作都要严格遵守“开发规范文档”或.cursorrules
每次执行代码修改或开发，都要告诉我做了哪些地方的修改或开发，是否符合@开发规范文档.md 
提示：在某些问题修复超过两次都没有解决，可以启用搜索功能，全网搜索和IOS开发者社区寻找参考资料，在使用搜索功能搜索全网资料的时候，如果要用年份去搜索请记得增加2025年，因为现在是2025年了。
注意！违反熔断机制：超过了3次编译错误阈值，应该触发回滚策略，重新审查相关代码文件和执行方案，重新制定合理的方案再进行开发或者修复、优化工作。
在修复某些比较深度的问题的时候可以调用XcodeBuildMCP来进行编码测试、构建、获取调试日志和相关模拟器、真机调试的重要信息。
最高等级，以下问题除非是我要求修改或者我同意，否则必须遵守以下保护措施，基于我们项目的特殊要求如下：
SwiftData关系保护
任何修改数据模型时，必须保持现有的关系定义
不能删除或修改已有的@Relationship定义
Repository模式保护
修改数据访问时，必须保持Repository模式
不能回退到直接ModelContext访问
UI组件集成保护
修改页面时，必须保留所有已集成的UI组件
新增功能时，不能覆盖现有组件的引用

# Evolve iOS - 快速开发规范

## 🚨 绝对禁止 (违反=崩溃)

1. **外键字段**: `var userId: UUID` ❌ → 使用 `var user: EAUser?` ✅
2. **单例模式**: `static let shared` ❌ → 依赖注入 ✅
3. **直接ModelContext**: `@Environment(\.modelContext)` ❌ → Repository模式 ✅
4. **双端@Relationship**: 两端都用@Relationship ❌ → 单端inverse ✅
5. **print语句**: `print("...")` ❌ → 条件编译 ✅

## ✅ 标准模式
**SwiftData关系** (只在一端用@Relationship):
```swift
@Model class EAUser {
    @Relationship(inverse: \EAHabit.user) var habits: [EAHabit] = []
}
@Model class EAHabit {
    var user: EAUser? // 普通属性
}
```

**Repository模式**:
```swift
@ModelActor actor EARepository { }
@MainActor class ViewModel: ObservableObject {
    @Environment(\.repositoryContainer) private var repos
}
```

**依赖注入**:
```swift
@StateObject private var service = EAService()
.environment(\.service, service)
```

## 🔒 必须遵守

- 所有ViewModel标记`@MainActor`
- 所有Repository使用`@ModelActor`
- 关系赋值顺序: 插入Context → 赋值关系 → 保存
- Context一致性: 共享ModelContainer

**检查清单**: 关系✅ 单例✅ Repository✅ MainActor✅ Print✅ 
