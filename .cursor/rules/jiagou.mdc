---
description: 
globs: 
alwaysApply: true
---
# Evolve - 技术架构文档 v2.4

**📅 更新时间：2025-06-26 11:19:48**
**🎯 更新重点：补充用户身份系统架构设计，解决用户生命周期管理和数据完整性问题**

## 重要说明
本文档已根据《开发规范文档 v2.4》和《社区功能架构式开发文档v4.0》进行全面更新，确保技术架构与开发规范完全一致，并体现数字宇宙主题的社区功能升级。所有开发工作必须严格遵循本文档和开发规范文档的要求。

## 一、项目概述

### 1.1 产品定位
Evolve AI是一款基于AI驱动的个人计划养成应用，通过"生态隐喻"和"能量流转"的设计理念，为用户提供智能化的计划培养体验。

### 1.2 核心特性
- **AI深度集成**：AI作为私人教练，提供个性化指导和情境感知
- **生态化设计**：计划养成过程可视化为生态系统的成长
- **社区共振**：用户分享计划成果，形成能量共振效应
- **星际伙伴系统**：好友功能融入数字宇宙主题，支持AI增强聊天和能量共振
- **数据驱动**：基于用户行为数据提供智能分析和建议

## 二、技术栈选择

### 2.1 核心技术栈
- **编程语言**：Swift 5.9+ （原生性能，类型安全）
- **UI框架**：SwiftUI （声明式UI，iOS 17.0+新特性支持）
- **架构模式**：MVVM （数据绑定，可测试性）
- **数据持久化**：SwiftData （现代化ORM，类型安全）
- **并发处理**：Swift Concurrency （async/await，@MainActor）

### 2.2 技术选型原因
- **SwiftUI**：支持iOS 17.0+最新特性，声明式编程提高开发效率
- **SwiftData**：替代Core Data，提供更简洁的数据模型定义
- **MVVM**：分离业务逻辑与UI，支持响应式编程
- **原生开发**：最佳性能表现，完整的iOS生态集成

## 三、架构模式

### 3.1 MVVM + Repository架构设计
```
View (SwiftUI) ←→ ViewModel (@ObservableObject) ←→ Repository (@ModelActor) ←→ Model (SwiftData)
                        ↓                              ↓
                   Service Layer                 Data Access Layer
```

### 3.2 设计原则
- **单一职责**：每个组件专注单一功能
- **Repository模式强制执行**：所有数据访问必须通过Repository层
- **依赖注入**：通过环境对象传递依赖，统一的Repository容器管理
- **响应式编程**：使用@Published实现数据绑定
- **错误处理**：统一的错误处理和用户反馈机制

## 四、项目结构

### 4.1 目录架构
```
Evolve/
├── AppEntry.swift                          # SwiftUI App入口点
├── Assets.xcassets/                        # 图片、颜色集、图标等静态资源管理
│   └── AppColors/                          # 应用所有颜色集，包括主色调、强调色、辅助色、状态色等
├── Preview Content/                        # SwiftUI预览辅助文件目录
│   └── PreviewData.swift                   # 提供SwiftUI视图预览时使用的模拟数据
├── Core/                                   # 核心服务和共享功能模块
│   ├── Constants/                          # 全局常量定义目录
│   │   └── AppConstants.swift              # 应用级别常量定义（尺寸、字体、动画、阴影规范）
│   ├── Config/                             # 应用配置目录
│   │   ├── AppConfig.swift                 # 应用配置信息
│   │   └── EAFeatureManager.swift          # 功能可用性管理器，智能检测模型可用性
│   ├── DataModels/                         # 数据模型定义目录 (SwiftData @Model)
│   │   ├── EAHabit.swift                   # 计划数据模型
│   │   ├── EACompletion.swift              # 计划完成记录模型（遵循EA简化命名规范）
│   │   ├── EAUser.swift                    # 用户资料模型（遵循EA简化命名规范）
│   │   ├── EAUserSettings.swift            # 用户设置模型
│   │   ├── EAPayment.swift                 # 支付记录模型
│   │   ├── EAContent.swift                 # 内容模型（动力包等）
│   │   ├── EAPath.swift                    # 计划路径模型
│   │   ├── EAAnalytics.swift               # 用户行为分析模型
│   │   ├── EACommunityPost.swift           # 社区帖子模型（已扩展数字宇宙属性）
│   │   ├── EACommunityComment.swift        # 社区评论模型
│   │   ├── EACommunityLike.swift           # 社区点赞模型
│   │   ├── EACommunityFollow.swift         # 用户关注模型
│   │   ├── EACommunityReport.swift         # 社区举报模型
│   │   ├── EAUniverseChallenge.swift       # 宇宙探索挑战模型（数字宇宙主题）
│   │   ├── EAUserSocialProfile.swift       # 用户社交档案模型（已扩展星际等级属性和好友功能）
│   │   ├── EAUserDataProfile.swift         # 用户数据档案模型
│   │   ├── EAUserModerationProfile.swift   # 用户审核档案模型
│   │   ├── EAFriendship.swift              # 好友关系模型（星际伙伴系统）
│   │   ├── EAFriendRequest.swift           # 好友请求模型（星际邀请系统）
│   │   ├── EAFriendMessage.swift           # 好友消息模型（AI增强聊天）
│   │   ├── EAFriendNotification.swift      # 好友通知模型
│   │   ├── EAAIMessage.swift               # AI消息模型
│   │   ├── EAAIDataModels.swift            # AI数据模型集合
│   │   ├── EAAIUserHabitSummary.swift      # AI用户习惯摘要模型
│   │   ├── EAAvatarType.swift              # 头像类型模型
│   │   ├── EASharedTypes.swift             # 共享类型定义
│   │   ├── AI/                             # AI相关数据模型子目录
│   │   │   └── EAAIFriendChatContext.swift # AI好友聊天上下文模型
│   │   ├── <!-- 待开发组件（用户身份系统核心） -->
│   │   ├── <!-- EAAIConversation.swift     # AI对话记录模型（待开发）-->
│   │   ├── <!-- EAHabitPathStage.swift     # 计划路径阶段模型（待开发）-->
│   │   └── <!-- EANotificationSettings.swift # 通知设置模型（待开发）-->
│   ├── Persistence/                        # 数据持久化相关代码目录
│   │   └── EADatabaseManager.swift         # 数据库管理器，多层次数据库恢复策略
│   ├── Repositories/                       # Repository数据访问层 (遵循强制执行规范)
│   │   ├── EAUserRepository.swift          # 用户数据Repository，使用@ModelActor
│   │   ├── EAHabitRepository.swift         # 计划数据Repository，使用@ModelActor
│   │   ├── EACommunityRepository.swift     # 社区数据Repository，使用@ModelActor
│   │   ├── EAAIInsightRepository.swift     # AI洞察数据Repository，使用@ModelActor
│   │   ├── EAFriendshipRepository.swift    # 好友关系数据Repository，使用@ModelActor
│   │   ├── EAFriendMessageRepository.swift # 好友消息数据Repository，使用@ModelActor
│   │   └── EARepositoryContainer.swift     # Repository统一管理容器
│   ├── Services/                           # 服务层，封装具体业务逻辑或第三方服务调用
│   │   ├── EANetworkService.swift          # 通用网络请求服务
│   │   ├── EAAIService.swift               # AI服务集成，支持对话管理和智能回复
│   │   ├── EAPaymentService.swift          # StoreKit支付服务，完整支持StoreKit 2 API
│   │   ├── EAContentService.swift          # 内容管理服务，管理智慧宝库内容
│   │   ├── EAAuthService.swift             # 用户认证服务
│   │   ├── EASessionManager.swift          # 会话管理服务（已增强用户身份完整性检查）
│   │   ├── EACommunityService.swift        # 社区服务，处理帖子CRUD、点赞评论、AI分享建议等功能
│   │   ├── EAUniverseChallengeService.swift # 宇宙挑战服务，管理挑战系统和星际奖励
│   │   ├── EACommunityAIDataBridge.swift   # 社区AI数据桥接服务，AI数据格式转换和缓存
│   │   ├── EAContentModerationService.swift # 内容审核服务，处理内容过滤和举报管理
│   │   ├── EASharingReminderService.swift  # 分享提醒服务，管理AI分享时机检测和提醒
│   │   ├── EAFriendshipService.swift       # 好友关系服务，处理好友添加、删除、搜索等功能
│   │   ├── EAFriendChatService.swift       # 好友聊天服务，处理AI增强聊天和消息管理
│   │   ├── <!-- 待开发组件（用户身份系统核心） -->
│   │   ├── <!-- EAUserIntegrityGuard.swift  # 用户身份完整性守护服务（待开发-核心组件）-->
│   │   ├── <!-- EAAsyncProfileInitializer.swift # 异步用户档案初始化服务（待开发）-->
│   │   ├── <!-- EAUserIdentityMonitor.swift # 用户身份监控服务（待开发-监控组件）-->
│   │   ├── <!-- EAUserValidationService.swift # 用户验证服务（待开发-数据完整性检查）-->
│   │   └── EANotificationService.swift     # 通知服务主类，处理iOS原生通知权限和计划提醒调度
│   ├── AI/                                 # AI引擎核心模块（实际目录名）
│   │   ├── EAAIService.swift               # AI服务主类，处理AI对话和智能分析
│   │   ├── EAAICostController.swift        # AI调用成本控制器
│   │   ├── EAAICacheManager.swift          # AI响应缓存管理器
│   │   └── EAAIDataBridge.swift            # AI数据桥接服务
│   └── Notifications/                      # 通知系统模块
│       ├── EANotificationService.swift     # 通知服务主类，处理iOS原生通知权限和计划提醒调度
│       └── NotificationDelegate.swift      # 通知代理类，处理通知点击事件和前台通知展示
├── Common/                                 # 通用工具和扩展代码目录
│   ├── Extensions/                         # Swift标准库类型或自定义类型的扩展
│   │   ├── Color+Hex.swift                 # 颜色十六进制扩展（支持hex字符串转Color）
│   │   └── View+KeyboardAdaptive.swift     # 键盘自适应视图扩展，自动处理键盘弹出时的视图调整
│   └── Tools/                              # 全局工具类、辅助函数
│       └── PreviewHelper.swift             # SwiftUI预览辅助工具，提供键盘支持和文本框预览包装器
├── Features/                               # 功能模块 (按业务划分)
│   ├── Onboarding/                         # 用户引导模块
│   │   ├── EAOnboardingView.swift          # 用户引导页面主视图
│   │   └── EAOnboardingViewModel.swift     # 用户引导页面ViewModel
│   ├── Authentication/                     # 用户身份验证模块
│   │   ├── EALoginView.swift               # 登录页面
│   │   ├── EARegistrationView.swift        # 注册页面
│   │   ├── EAForgotPasswordView.swift      # 忘记密码页面
│   │   └── EAAuthViewModel.swift           # 认证业务逻辑管理
│   ├── MainTab/                            # 主Tab容器视图模块
│   │   └── EAMainTabView.swift             # 主Tab导航容器
│   ├── Today/                              # "今日"功能模块
│   │   ├── EATodayView.swift               # Today页面主视图
│   │   └── EATodayViewModel.swift          # Today页面ViewModel
│   ├── Atlas/                              # "图鉴"功能模块
│   │   ├── EAAtlasView.swift               # Atlas主页面，计划管理和统计概览
│   │   ├── EAAtlasViewModel.swift          # Atlas主页面ViewModel
│   │   ├── EAHabitCreationView.swift       # 计划创建页面主视图
│   │   ├── EAHabitCreationViewModel.swift  # 计划创建ViewModel
│   │   ├── EAHabitDetailView.swift         # 计划详情页面主视图
│   │   └── EAHabitDetailViewModel.swift    # 计划详情ViewModel
│   ├── AuraSpace/                          # "灵境"功能模块 (AI交互空间)
│   │   ├── EAAuraSpaceView.swift           # AI对话主界面
│   │   ├── EAAuraSpaceViewModel.swift      # AI对话ViewModel
│   │   ├── EAContentLibraryView.swift      # 智慧宝库界面
│   │   └── EAContentLibraryViewModel.swift # 智慧宝库ViewModel
│   ├── Community/                          # 社区功能模块
│   │   ├── EACommunityView.swift           # 社区主页面（已集成好友功能入口）
│   │   ├── EACommunityViewModel.swift      # 社区主页面ViewModel
│   │   ├── EAPostDetailView.swift          # 帖子详情页面
│   │   ├── EAPostDetailViewModel.swift     # 帖子详情ViewModel
│   │   ├── EAFriendListView.swift          # 好友列表页面（星际伙伴）
│   │   ├── EAFriendListViewModel.swift     # 好友列表ViewModel
│   │   ├── EAFriendChatView.swift          # 好友聊天页面（AI增强）
│   │   ├── EAFriendChatViewModel.swift     # 好友聊天ViewModel
│   │   ├── EAAddFriendView.swift           # 添加好友页面
│   │   ├── EAAddFriendViewModel.swift      # 添加好友ViewModel
│   │   ├── <!-- 待开发组件（社区功能扩展） -->
│   │   ├── <!-- EACreatePostView.swift     # 创建帖子页面（待开发）-->
│   │   └── <!-- EACreatePostViewModel.swift # 创建帖子ViewModel（待开发）-->
│   └── Me/                                 # "我的"功能模块 (用户中心)
│       ├── EAMeView.swift                  # 我的页面主视图
│       ├── EAMeViewModel.swift             # 我的页面ViewModel
│       ├── EAProMembershipView.swift       # Pro会员页面
│       ├── EASubscriptionConfirmationView.swift # 订阅确认页面
│       ├── EASettingsView.swift            # 设置页面
│       ├── EAAchievementDetailView.swift   # 成就详情页面
│       └── EAAchievementHallView.swift     # 成就大厅页面
└── UIComponents/                           # 可复用UI组件目录
    ├── EAOnboardingActionButton.swift      # 引导页专用操作按钮
    ├── EAPaginationDotsView.swift          # 分页指示器组件
    ├── EAButton.swift                      # 通用按钮组件
    ├── EATextField.swift                   # 高级输入框组件
    ├── EABackgroundView.swift              # 可复用背景组件
    ├── EAPlaceholderView.swift             # 占位视图组件
    ├── EAHabitCard.swift                   # 计划卡片组件
    ├── EAEnergyMeter.swift                 # 能量进度条组件
    ├── EACompletionButton.swift            # 完成打卡按钮组件
    ├── EAStreakIndicator.swift             # 连续天数指示器组件
    ├── EADailyInsight.swift                # 每日洞察组件
    ├── EAAIAvatarView.swift                # AI头像组件，支持呼吸动画和状态指示
    ├── EAChatBubble.swift                  # 聊天气泡组件，支持用户/AI消息样式
    ├── EAQuickReplyButton.swift            # 快速回复按钮组件，包含触觉反馈
    ├── EAContentCard.swift                 # 内容卡片组件，显示智慧宝库内容
    ├── EAFrequencySelector.swift           # 频率选择组件，支持每周、全天、每月三种打卡模式
    ├── EAIconCategorySelector.swift        # 图标分类选择组件，提供8个分类的丰富图标选择
    ├── EATimeSelector.swift                # 时间选择器组件，用于设置计划提醒时间
    ├── EAHabitActionMenu.swift             # 计划操作菜单组件，提供编辑、提醒设置、删除等操作
    ├── EAAtlasComponents.swift             # 图鉴页面组件集合，包含统计卡片、计划概览卡片、空状态视图等
    ├── EABreathingWisdomCore.swift         # 发光呼吸智慧核心组件，实现真正的呼吸动画和多层发光效果
    ├── EACommunityPostCard.swift           # 社区帖子卡片组件
    ├── EAOptimizedCommunityPostCard.swift  # 优化版社区帖子卡片组件
    ├── EACommentCell.swift                 # 评论单元组件
    ├── EALikeButton.swift                  # 点赞按钮组件
    ├── EAUniverseChallengeCard.swift       # 宇宙挑战卡片组件（数字宇宙主题）
    ├── EAFriendRowView.swift               # 好友行视图组件（星际伙伴）
    ├── EAUniverseGuideView.swift           # 宇宙向导视图组件
    ├── EACosmicExplorerProfileView.swift   # 宇宙探索者档案组件
    ├── EAStellarEnergyView.swift           # 星际能量视图组件
    ├── EAStellarAchievementTimeline.swift  # 星际成就时间线组件
    ├── EAChatMessageBubble.swift           # 聊天消息气泡组件
    ├── EAMessageActionMenu.swift           # 消息操作菜单组件
    ├── EAVideoRecorderView.swift           # 视频录制视图组件
    ├── EAVideoPlayerView.swift             # 视频播放器组件
    ├── EAAvatarPicker.swift                # 头像选择器组件
    ├── EAMediaInputToolbar.swift           # 媒体输入工具栏组件
    ├── EARefreshControl.swift              # 下拉刷新控制组件
    ├── EACalendarDateSelector.swift        # 日历日期选择器组件
    ├── EAOneTimeCalendarAdapter.swift      # 一次性日历适配器组件
    ├── EAZoomableImageView.swift           # 可缩放图片视图组件
    ├── EACommunityFilterBar.swift          # 社区过滤栏组件
    ├── EAAchievementCard.swift             # 成就卡片组件
    ├── EAPhotoSelector.swift               # 照片选择器组件
    ├── EACameraPickerView.swift            # 相机选择视图组件
    ├── EAAIMessageIndicator.swift          # AI消息指示器组件
    ├── EAAvatarView.swift                  # 头像视图组件
    ├── EAMultiTimeReminderSelector.swift   # 多时间提醒选择器组件
    ├── EAChatMediaViewer.swift             # 聊天媒体查看器组件
    ├── EAImageGridView.swift               # 图片网格视图组件
    ├── EAOptimizedAsyncImage.swift         # 优化异步图片组件
    ├── EAHighPerformanceAsyncImage.swift   # 高性能异步图片组件
    ├── <!-- 待开发组件（UI组件扩展） -->
    ├── <!-- EATodayWisdomCoreTest.swift    # 智慧核心测试视图（待开发）-->
    ├── <!-- EAUserAvatarView.swift         # 用户头像组件（待开发）-->
    ├── <!-- EAEnergyLevelIndicator.swift   # 能量等级指示器组件（待开发）-->
    ├── <!-- EAMilestoneBadge.swift         # 里程碑徽章组件（待开发）-->
    ├── <!-- EASharingOpportunityAlert.swift # 分享时机提醒弹窗（待开发）-->
    ├── <!-- EAChallengeProgressIndicator.swift # 挑战进度指示器组件（待开发）-->
    ├── <!-- EARewardDisplayComponent.swift # 奖励展示组件（待开发）-->
    ├── <!-- EAStellarEnergyIndicator.swift # 星际能量指示器组件（待开发）-->
    ├── <!-- EADigitalUniverseBackground.swift # 数字宇宙主题背景组件（待开发）-->
    ├── <!-- EAFriendCard.swift             # 好友卡片组件（待开发）-->
    ├── <!-- EAFriendChatBubble.swift       # 好友聊天气泡组件（待开发）-->
    ├── <!-- EAFriendRequestCard.swift      # 好友请求卡片组件（待开发）-->
    ├── <!-- EAMessagesCenterView.swift     # 消息中心组件（待开发）-->
    └── <!-- EANotificationManager.swift    # 全局通知管理器组件（待开发）-->
```

### 4.2 模块化设计
- **功能模块独立**：每个Feature包含完整的View、ViewModel、子组件
- **组件复用**：UIComponents提供跨模块复用的UI组件
- **Repository统一管理**：Core/Repositories提供统一的数据访问层
- **服务共享**：Core/Services提供全局业务服务

## 五、数据层架构

### 5.1 数据模型设计（遵循开发规范文档v2.1）

**🚨 SwiftData开发核心约束（iOS 17.0-18.5+兼容性）**：
- **完整双向关系强制要求（绝对不可妥协）**：所有@Relationship必须有对应的反向关系(inverse)，避免iOS 18.2+Schema验证失败
- **关系对称性验证**：每个关系在两个模型中都要定义，确保inverse路径正确
- **UUID自动生成**：不在init中手动设置UUID，让系统自动生成
- **关系集合初始化**：所有集合类型的关系属性必须在init中初始化，而非设置默认值
- **关系赋值顺序（iOS 18+强制要求）**：所有关系属性（包括集合和单对象）必须在对象插入ModelContext后再赋值，禁止在init或插入前赋值关系，确保兼容iOS 18+

**核心实体关系**：
- **EAHabit** ←→ **EACompletion** （一对多，遵循单端inverse规则）
- **EAUser** ←→ **EAHabit** （一对多，遵循单端inverse规则）
- **EACommunityPost** ←→ **EAHabit** （多对一）
- **EAAIConversation** ←→ **EAUser** （多对一）
- **EAUserSocialProfile** ←→ **EAFriendship** （一对多，星际伙伴关系）
- **EAUserSocialProfile** ←→ **EAFriendRequest** （一对多，好友请求关系）
- **EAUserSocialProfile** ←→ **EAFriendMessage** （一对多，好友消息关系）
- **EAFriendship** ←→ **EAFriendMessage** （一对多，聊天记录关系）

### 5.2 SwiftData模型实现规范

**🔒 Apple官方ModelContainer配置规范（WWDC 2023/2024确认）**：
```swift
// ✅ 正确：多模型使用可变参数语法（Apple官方标准）
let container = try ModelContainer(for: 
    EAUser.self,
    EAHabit.self,
    EACompletion.self,
    EAUserSettings.self,
    EAUserSocialProfile.self,
    EAFriendship.self,
    EAFriendRequest.self,
    EAFriendMessage.self
)

// ✅ 正确：单模型直接传递
let container = try ModelContainer(for: EAUser.self)

// ❌ 错误：使用数组语法（编译错误）
let container = try ModelContainer(for: [EAUser.self, EAHabit.self]) // 编译错误
```

**🔒 完整双向inverse关系原则（绝对不可妥协）**：
```swift
// ✅ 正确：单端inverse模式
@Model
final class EAHabit {
    var id: UUID = UUID()
    var name: String
    var creationDate: Date = Date()
    var iconName: String
    var targetFrequency: Int
    var timeOfDay: String?
    var isActive: Bool = true
    
    // ✅ 正确：定义inverse指向另一端的属性（不设置默认值）
    @Relationship(deleteRule: .cascade, inverse: \EACompletion.habit) 
    var completions: [EACompletion]
    
    // ✅ 正确：反向关系使用普通属性
    var user: EAUser?
    
    // ✅ 正确：SwiftData原生支持基础类型数组（iOS 18.2+日志警告是系统级问题）
    var selectedWeekdays: [Int] = []
    var reminderTimes: [String] = []
    
    init(name: String, iconName: String, targetFrequency: Int) {
        self.name = name
        self.iconName = iconName
        self.targetFrequency = targetFrequency
        self.completions = [] // 在init中初始化，而非属性默认值
    }
}

@Model
final class EACompletion {
    var id: UUID = UUID()
    var date: Date = Date()
    var completionNote: String?
    var energyLevel: Int = 5 // 1-10
    
    // ✅ 正确：使用普通属性，SwiftData自动维护inverse关系
    var habit: EAHabit?
    
    init(completionNote: String? = nil, energyLevel: Int = 5) {
        self.completionNote = completionNote
        self.energyLevel = energyLevel
    }
}

@Model
final class EAUser {
    var id: UUID = UUID()
    var username: String
    var email: String?
    var creationDate: Date = Date()
    
    // ✅ 正确：定义inverse指向另一端的属性（不设置默认值）
    @Relationship(deleteRule: .cascade, inverse: \EAHabit.user)
    var habits: [EAHabit]
    
    init(username: String, email: String? = nil) {
        self.username = username
        self.email = email
        self.habits = [] // 在init中初始化，而非属性默认值
    }
}
```

**🚫 严格禁止的做法**：
- **删除inverse参数**：绝对不能通过删除inverse来"解决"编译错误
- **两端同时使用@Relationship**：会导致关系冲突和循环引用
- **完全省略inverse**：所有双向关系必须在一端正确定义inverse
- **单向关系设计**：需要双向访问的关系必须在两端正确定义
- **关系默认值设置**：@Model会忽略关系属性的默认值
- **构造器中创建关联对象**：避免在init中创建关联对象，会导致运行时错误

### 5.3 数据持久化策略
- **SwiftData容器**：统一的数据模型管理，使用可变参数语法
- **版本迁移**：支持数据模型演进和向后兼容
- **错误恢复**：多层次数据库恢复机制
- **性能优化**：懒加载和批量操作

### 5.3 数据安全
- **本地存储**：敏感数据使用Keychain存储
- **数据备份**：开发环境自动备份机制
- **隐私保护**：用户数据本地化处理

## 六、服务层架构

### 6.1 Repository数据访问层（强制执行规范）
- **数据访问统一入口**：所有数据操作必须通过Repository层
- **@ModelActor线程安全**：所有Repository使用@ModelActor确保线程安全
- **Repository容器管理**：通过EARepositoryContainer统一管理所有Repository
- **禁止直接ModelContext访问**：Service和ViewModel不得直接注入ModelContext

### 6.2 核心服务设计
- **EAAuthService**：用户认证和会话管理
- **EAAIService**：AI对话和智能分析
- **EACommunityService**：社区功能和内容管理（通过Repository访问数据）
- **EAFriendshipService**：好友关系管理，处理好友添加、删除、搜索等功能
- **EAFriendChatService**：好友聊天服务，处理AI增强聊天和消息管理
- **EAPaymentService**：StoreKit 2支付集成
- **EANotificationService**：智能通知调度（包含好友消息提醒）

### 6.3 服务层特性
- **Repository优先原则**：所有数据访问通过Repository层，不直接操作ModelContext
- **依赖注入**：通过环境对象注入服务和Repository容器
- **错误处理**：统一的错误类型和处理机制
- **异步操作**：基于Swift Concurrency的异步设计
- **缓存策略**：智能缓存提升用户体验

## 七、AI引擎集成

### 7.1 分层AI架构设计

**核心设计理念**：建立成本可控、性能优化的分层AI架构，确保AI真正驱动各功能板块。

```
🧠 核心AI层 (API调用)
├── 复杂个性化分析 (EAIntelligentAnalysisEngine)
├── 情绪状态识别 (EAEmotionalStateAnalyzer)
├── 危机干预响应 (EAInterventionMessageService)
└── 创新内容生成 (EAPersonalizedMessageGenerator)

⚡ 智能规则层 (本地处理)
├── 行为模式匹配 (EABehaviorPatternDetector)
├── 基础统计分析 (EALocalIntelligenceEngine)
├── 预设触发条件 (EAOptimalTimingAnalyzer)
└── 模板化响应 (EAMessageTemplateManager)

💾 缓存优化层
├── AI结果缓存复用 (EAAIResponseCache)
├── 用户画像持久化 (用户画像缓存7天)
├── 内容模板库 (EAMessageTemplateManager)
└── 历史分析数据 (行为分析缓存3天)

🔧 成本控制层
├── AI调用成本控制 (EAAICostController)
├── 智能决策制定 (EAIntelligentDecisionMaker)
├── 性能监控优化 (EAAIPerformanceMonitor)
└── 效果评估分析 (EAAIEffectivenessAnalyzer)
```

### 7.2 AI深度集成策略

**功能板块AI驱动**：
- **今日页面**：基于行为分析的个性化洞察和完成概率预测
- **图鉴页面**：智能计划创建引导和个性化建议
- **灵境页面**：深度AI对话和情境感知回应
- **我的页面**：个性化数据分析和智能推荐
- **社区页面**：智能内容推荐和分享时机检测
- **好友功能**：AI增强聊天建议、情感分析、智能好友推荐

**AI成本控制策略**：
- **高优先级场景**：用户主动求助、危机干预、重大里程碑
- **中优先级场景**：每日洞察生成、习惯创建建议（缓存24小时）
- **低优先级场景**：日常提醒、基础统计（本地处理优先）

### 7.3 智能行为分析引擎

**核心分析能力**：
- **打卡行为模式识别**：分析用户计划完成的时间规律和成功因素
- **情绪状态变化追踪**：基于文本输入和行为数据推断情绪趋势
- **个性化完成概率预测**：预测用户今日计划完成可能性
- **主动干预时机检测**：识别需要AI支持的关键时刻

**技术实现**：
- 本地规则引擎处理基础分析（成本低）
- AI深度分析处理复杂模式识别（精度高）
- 智能缓存机制避免重复计算
- 分级干预策略确保及时响应

### 7.4 个性化消息生成系统

**智能消息策略**：
- **模板优先原则**：日常提醒使用个性化模板（成本几乎为零）
- **AI增强生成**：重要时机调用AI生成个性化内容
- **情境感知适配**：基于用户当前状态选择最佳消息风格
- **效果跟踪优化**：监控消息效果并持续优化策略

**成本优化机制**：
- 用户价值评估：为高价值用户提供更多AI服务
- 智能缓存复用：相似场景复用AI生成结果
- 降级策略：AI不可用时的高质量备选方案

### 7.5 AI数据桥接架构（社区功能增强）

**社区AI数据桥接设计**：
```swift
// AI数据格式转换架构
EACommunityAIDataBridge
├── 用户社交数据摘要 (EAAISocialSummary)
├── 内容推荐上下文 (EAAIRecommendationContext)  
├── 社区交互记录 (EACommunityInteraction)
└── 数字宇宙数据转换 (stellarLevel, totalStellarEnergy)
```

**数据桥接核心功能**：
- **数据格式转换**：将SwiftData模型转换为AI可理解的格式
- **缓存策略优化**：社交数据7天缓存，推荐结果24小时缓存
- **降级处理机制**：AI不可用时提供基础数据摘要
- **隐私保护**：最小化数据传输，本地预处理优先

**数字宇宙主题集成**：
- **星际等级分析**：基于用户习惯完成情况的等级计算
- **能量流转追踪**：社区互动产生的星际能量记录
- **宇宙探索建议**：AI基于用户等级提供个性化挑战推荐

### 7.6 AI服务集成与监控

**服务集成架构**：
- **模块化设计**：支持多AI服务商切换
- **统一接口**：EAAIService提供统一的AI调用接口
- **降级策略**：AI服务不可用时的智能降级机制
- **隐私保护**：本地处理优先，最小化数据传输

**监控与优化体系**：
- **性能监控**：AI调用响应时间、成功率、成本跟踪
- **效果评估**：AI生成内容的用户满意度和准确性分析
- **自动优化**：基于监控数据自动调整AI调用策略
- **A/B测试**：持续优化AI模型和策略参数

## 八、社区系统架构

### 8.1 社区功能设计（数字宇宙主题升级）
- **内容管理**：帖子、评论、点赞的完整生命周期，扩展数字宇宙属性
- **用户关系**：关注、粉丝关系管理，集成星际等级系统
- **内容审核**：自动化内容过滤和举报处理
- **AI增强**：智能分享时机检测和内容建议，宇宙向导对话功能
- **挑战系统**：宇宙探索挑战的创建、参与、进度跟踪和奖励发放

### 8.2 挑战系统架构设计

**核心组件架构**：
```
EAUniverseChallengeService (挑战业务逻辑)
├── 挑战生命周期管理 (创建→进行→完成→归档)
├── 用户参与管理 (加入、退出、进度跟踪)
├── 星际奖励系统 (能量计算、等级提升、徽章发放)
└── 排行榜和统计 (实时排名、完成率分析)

EAUniverseChallenge (数据模型)
├── 基础信息 (标题、描述、时间、奖励)
├── 挑战规则 (类型、目标值、难度等级)
└── 宇宙主题 (区域、徽章、探索者称号)
```

**挑战类型设计**：
- **习惯完成挑战**：连续N天完成特定习惯
- **连击目标挑战**：达到特定连击天数
- **社区互动挑战**：分享、点赞、评论达到目标数量
- **探索者成长挑战**：星际等级提升或能量积累目标

### 8.3 星际能量系统架构
**能量计算引擎**：
- **基础奖励**：习惯完成10-50星际能量
- **连击奖励**：连续完成倍数奖励机制
- **社区奖励**：分享5能量，获赞5能量，评论10能量
- **挑战奖励**：完成挑战100-500星际能量

**等级提升系统**：
- **新手探索者**（1-3级）：0-999星际能量
- **星际旅者**（4-6级）：1000-4999星际能量  
- **宇宙领航员**（7-10级）：5000+星际能量

### 8.4 数据同步策略
- **本地优先**：离线功能支持
- **增量同步**：减少网络传输
- **冲突解决**：数据冲突的智能处理
- **挑战数据同步**：实时进度更新和排行榜同步

## 九、性能与扩展性

### 9.1 性能优化策略
- **UI性能**：SwiftUI最佳实践，避免不必要重绘
- **数据性能**：SwiftData查询优化，批量操作
- **内存管理**：图片缓存，大对象及时释放
- **启动优化**：延迟加载，关键路径优化

### 9.2 扩展性设计
- **模块化架构**：新功能模块独立开发，挑战系统可独立扩展
- **插件化AI**：支持新AI服务商接入，数字宇宙主题AI增强
- **国际化支持**：多语言和文化适配预留，宇宙主题本地化
- **平台扩展**：为watchOS、macOS扩展预留接口
- **挑战系统扩展**：支持新挑战类型和奖励机制的动态配置

## 十、用户身份系统架构设计

### 10.1 用户身份系统概述

**设计理念**：建立完整的用户生命周期管理系统，确保从用户注册到使用各功能模块的无缝体验，解决新用户崩溃问题。

**核心目标**：
- **消除崩溃风险**：解决新用户注册后立即使用功能时的Signal 9崩溃
- **数据完整性保障**：确保用户相关数据的一致性和完整性
- **性能优化**：异步处理用户初始化，避免主线程阻塞
- **用户体验提升**：提供流畅的用户身份管理体验

### 10.2 用户身份架构设计

**分层架构模式**：
```
┌─────────────────────────────────────────────────────────────┐
│                    UI层 (@MainActor保护)                     │
├─────────────────────────────────────────────────────────────┤
│                  ViewModel层 (响应式状态管理)                  │
├─────────────────────────────────────────────────────────────┤
│    Service层 (业务逻辑 + 用户身份完整性检查)                    │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │  SessionManager │ │ IntegrityGuard  │ │ ProfileInitializer│ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│              Repository层 (@ModelActor线程安全)               │
└─────────────────────────────────────────────────────────────┘
```

### 10.3 核心组件设计

**EAUserIntegrityGuard**（用户完整性守护）：
- 用户身份完整性检查和验证
- 异步数据修复和自动恢复
- 用户状态监控和异常检测
- 优雅降级和错误处理

**EAAsyncProfileInitializer**（异步档案初始化器）：
- 用户社交档案异步初始化
- 后台线程处理复杂计算
- 主线程安全的状态更新
- 初始化进度监控

**Enhanced EASessionManager**（增强会话管理器）：
- 用户会话生命周期管理
- 应用启动时的用户状态恢复
- 用户数据完整性验证
- 会话状态监控和维护

### 10.4 用户生命周期管理

**用户注册流程**：
```swift
// 安全的用户创建流程
1. 创建基础用户实体 (同步，快速)
2. 插入ModelContext并保存 (同步)
3. 异步初始化社交档案 (后台线程)
4. 完整性验证和状态更新 (主线程安全)
5. 会话建立和功能解锁 (渐进式)
```

**会话恢复策略**：
- 应用启动时快速用户状态检查
- 异步数据完整性验证
- 损坏数据的自动修复
- 用户体验的优雅降级

### 10.5 数据完整性保障

**完整性检查机制**：
- **快速安全检查**：基础字段存在性验证（<50ms）
- **深度完整性检查**：关系数据和业务逻辑验证（异步）
- **自动修复策略**：检测到问题时的自动数据修复
- **降级处理**：无法修复时的功能降级方案

**性能优化策略**：
- 主线程保护：所有复杂操作异步执行
- 智能缓存：用户状态和验证结果缓存
- 渐进式加载：按需初始化用户相关数据
- 监控预警：性能指标监控和异常预警

### 10.6 错误处理和恢复

**多层级错误处理**：
```swift
// 错误处理层级
Level 1: 快速本地修复 (自动)
Level 2: 异步数据重建 (后台)
Level 3: 用户引导修复 (交互)
Level 4: 重置和重新开始 (最后手段)
```

**恢复策略**：
- **自动恢复**：检测到数据问题时自动修复
- **用户引导**：需要用户参与的修复流程
- **数据重置**：极端情况下的数据重置选项
- **状态监控**：持续监控用户身份状态健康度

## 十一、安全与隐私

### 11.1 数据安全
- **本地加密**：敏感数据Keychain存储
- **网络安全**：HTTPS通信，证书验证
- **权限管理**：最小权限原则
- **用户身份安全**：多层级身份验证和完整性保护

### 11.2 隐私保护
- **数据最小化**：只收集必要数据
- **用户控制**：数据导出和删除功能
- **透明度**：清晰的隐私政策和数据使用说明
- **身份隐私**：用户身份信息的本地化处理

## 十二、部署与监控

### 12.1 部署策略
- **版本管理**：语义化版本控制
- **渐进发布**：分阶段用户推送
- **回滚机制**：快速回滚到稳定版本
- **用户身份迁移**：版本升级时的用户数据迁移策略

### 12.2 监控体系
- **性能监控**：启动时间、内存使用、崩溃率
- **用户行为**：功能使用情况、用户路径分析
- **错误追踪**：异常日志收集和分析
- **用户身份监控**：用户身份状态健康度和完整性监控

## 十三、README.md更新规范（遵循开发规范文档v2.1）

### 13.1 更新时机

AI完成以下开发任务后必须更新README.md：
- **新功能模块**：完成PRD中定义的功能模块
- **新组件创建**：UI组件、核心服务、ViewModel
- **数据模型变更**：新增或修改@Model实体
- **API集成**：新的第三方API或支付功能
- **用户身份系统变更**：用户生命周期管理相关的重要变更

### 13.2 更新内容

- **日期和版本**：变更发生的日期和版本号
- **变更类型**：新增、修改、修复、移除
- **变更描述**：功能点、组件路径、数据模型变更
- **简要说明**：复杂变更的设计思路
- **用户影响**：用户身份系统变更对用户体验的影响

### 13.3 文档更新责任

AI在完成一个导致重要变更（如新组件、数据模型修改、核心逻辑实现）的开发任务后，必须按照本规范的要求，生成README.md的更新内容。确保路径符合 `Evolve/` 开头。

## 十四、好友功能架构设计（星际伙伴系统）

### 14.1 好友功能核心架构

**设计理念**：
- **星际伙伴概念**：好友关系融入数字宇宙主题，称为"星际伙伴"
- **AI增强聊天**：集成AI智能回复建议和情感分析
- **能量共振机制**：好友互动产生星际能量奖励
- **零影响集成**：通过EAUserSocialProfile扩展实现，不修改核心架构

### 14.2 数据模型架构

**核心模型设计**：
```swift
// 好友关系模型（星际伙伴）
@Model
final class EAFriendship {
    var id: UUID = UUID()
    var createdAt: Date = Date()
    var status: FriendshipStatus = .active
    var sharedEnergyTotal: Int = 0 // 共同产生的星际能量
    var lastInteractionDate: Date?
    
    // 关系定义（遵循单端inverse规则）
    var userProfile: EAUserSocialProfile?
    var friendProfile: EAUserSocialProfile?
    
    @Relationship(deleteRule: .cascade, inverse: \EAFriendMessage.friendship)
    var messages: [EAFriendMessage]
}

// 好友请求模型（星际邀请）
@Model
final class EAFriendRequest {
    var id: UUID = UUID()
    var createdAt: Date = Date()
    var status: FriendRequestStatus = .pending
    var message: String?
    
    // 关系定义
    var senderProfile: EAUserSocialProfile?
    var receiverProfile: EAUserSocialProfile?
}

// 好友消息模型（AI增强聊天）
@Model
final class EAFriendMessage {
    var id: UUID = UUID()
    var content: String
    var timestamp: Date = Date()
    var messageType: MessageType = .text
    var isRead: Bool = false
    var aiSuggestionUsed: Bool = false // 是否使用了AI建议
    
    // 关系定义
    var senderProfile: EAUserSocialProfile?
    var friendship: EAFriendship?
}
```

### 13.3 Repository架构扩展

**新增Repository组件**：
- **EAFriendshipRepository**：好友关系数据访问层，使用@ModelActor
- **EAFriendMessageRepository**：好友消息数据访问层，使用@ModelActor

**Repository容器集成**：
```swift
// EARepositoryContainer扩展
extension EARepositoryContainer {
    var friendshipRepository: EAFriendshipRepositoryProtocol { get }
    var friendMessageRepository: EAFriendMessageRepositoryProtocol { get }
}
```

### 13.4 服务层架构扩展

**新增服务组件**：
- **EAFriendshipService**：好友关系管理服务
  - 好友添加、删除、搜索功能
  - 好友状态管理和验证
  - 星际能量共振计算
- **EAFriendChatService**：好友聊天服务
  - AI增强聊天建议生成
  - 消息发送和接收管理
  - 聊天记录同步和缓存

### 13.5 AI集成架构

**AI增强聊天系统**：
```swift
// AI聊天建议架构
class EAFriendChatAIEngine {
    // 智能回复建议
    func generateReplyOptions(for message: String, context: ChatContext) async -> [String]
    
    // 情感分析
    func analyzeMessageSentiment(_ message: String) async -> SentimentAnalysis
    
    // 聊天氛围检测
    func detectChatMood(messages: [EAFriendMessage]) async -> ChatMood
}

// AI数据桥接扩展
extension EACommunityAIDataBridge {
    // 好友聊天上下文
    func getFriendChatContext(friendship: EAFriendship) async -> EAAIFriendChatContext
    
    // 好友推荐数据
    func getFriendRecommendationData(user: EAUser) async -> EAAIFriendRecommendation
}
```

### 13.6 UI架构集成

**社区页面集成**：
- **导航栏扩展**：在EACommunityView导航栏添加好友功能入口
- **Sheet管理**：复用现有EASheetManager，新增好友相关Sheet类型
- **数字宇宙主题**：所有好友功能UI保持星域主题一致性

**新增UI组件**：
- **EAFriendCard**：好友卡片组件（星际伙伴）
- **EAFriendChatBubble**：好友聊天气泡组件（AI增强）
- **EAFriendRequestCard**：好友请求卡片组件
- **EAMessagesCenterView**：消息中心组件

### 13.7 通知系统集成

**全局消息提醒**：
- **Tab Bar红点提醒**：在主Tab导航中显示未读消息数量
- **推送通知**：好友请求和新消息的推送通知
- **应用内提醒**：实时消息提醒和好友状态更新

**通知管理器扩展**：
```swift
// EANotificationManager扩展
extension EANotificationManager {
    // 好友消息通知
    func scheduleFriendMessageNotification(_ message: EAFriendMessage)
    
    // 好友请求通知
    func scheduleFriendRequestNotification(_ request: EAFriendRequest)
    
    // 更新Tab Bar徽章
    func updateMessagesTabBadge(count: Int)
}
```

### 13.8 性能优化架构

**缓存策略**：
- **好友列表缓存**：本地缓存好友列表，减少网络请求
- **聊天记录分页**：聊天记录分页加载，每页20条消息
- **AI建议缓存**：AI聊天建议结果缓存24小时
- **图片懒加载**：好友头像和聊天图片懒加载

**内存管理**：
- **聊天页面优化**：离开聊天页面时及时释放消息数据
- **大图片处理**：聊天图片压缩和缓存管理
- **后台任务**：消息同步和AI分析在后台执行

### 13.9 安全与隐私架构

**数据安全**：
- **端到端加密**：好友消息传输加密保护
- **隐私控制**：用户可控制好友可见信息范围
- **数据最小化**：只收集必要的好友互动数据

**权限管理**：
- **好友搜索权限**：用户可控制是否允许被搜索
- **消息通知权限**：细粒度的消息通知控制
- **数据导出**：支持好友数据的导出和删除

## 十四、AI开发约束与质量控制（遵循开发规范文档v2.1）

### 14.1 AI角色定位与交互规范

- **核心角色**：AI必须作为"私人教练、引导者、对话伙伴、赋能者"，而非简单的工具或助手
- **对话优先原则**：
  - 所有核心功能优先采用对话式交互设计
  - AI回复必须具备情感温度和个性化特征
  - 支持多轮对话和上下文记忆
  - 避免机械化的模板回复
- **情境感知要求**：
  - AI必须能够识别用户当前的情绪状态
  - 根据对话上下文调整回复风格和内容
  - 考虑用户的个性偏好和历史行为
  - 在适当时机主动提供支持和建议

### 14.2 AI成本控制开发规范

- **分层调用策略**：
  - **高优先级场景**：用户主动求助、危机干预、重大里程碑（必须调用AI API）
  - **中优先级场景**：每日洞察生成、计划创建建议（缓存24小时后调用）
  - **低优先级场景**：日常提醒、基础统计（优先本地处理）
- **缓存机制要求**：
  - 用户画像缓存7天有效期
  - AI洞察缓存24小时有效期
  - 行为分析结果缓存3天有效期
  - 相似问题回复缓存1天有效期
- **降级策略实现**：
  - AI服务不可用时必须有高质量备选方案
  - 本地智能引擎能独立处理基础场景
  - 模板化消息系统确保用户体验连续性

### 14.3 智能分析引擎开发规范

- **行为模式检测**：
  - 识别用户计划完成的时间规律（本地算法处理）
  - 检测连续失败模式和触发因素（AI深度分析）
  - 分析用户交互偏好和成功因素（混合处理）
- **情绪状态分析**：
  - 基于文本输入分析情绪状态（AI处理）
  - 通过行为数据推断情绪变化（本地+AI结合）
  - 识别需要情绪支持的关键时机（AI处理）
- **预测性洞察**：
  - 预测用户今日计划完成概率（本地算法）
  - 生成个性化的每日洞察（AI生成+模板结合）
  - 预警潜在的计划中断风险（AI分析）

## 十五、编译错误系统性修复规范（遵循开发规范文档v2.1）

### 15.1 禁止循环修复原则

**严禁单点修复模式**：AI在遇到编译错误时，必须采用系统性分析和一次性修复策略，避免"修复A导致B错误，修复B导致C错误"的无限循环。

### 15.2 错误修复标准流程

**第一步：全面错误收集**
- 执行完整项目编译，收集所有编译错误和警告
- 识别错误类型：SwiftData关系错误、语法错误、类型冲突、重复定义、依赖缺失、@MainActor问题等
- 分析错误间的关联性和依赖关系

**第二步：根因分析**
- **SwiftData关系错误（最高优先级）**：检查inverse参数的KeyPath是否正确，属性名是否拼写准确，关系两端类型是否匹配，确保遵循单端inverse规则
- **重复定义问题**：检查是否在多个文件中定义了相同的类型、枚举或结构体
- **MainActor冲突**：识别所有需要@MainActor标记的ViewModel和相关调用
- **依赖链问题**：分析import缺失、模块依赖和文件引用关系
- **命名空间冲突**：检查EA前缀使用是否一致，是否与系统类型冲突

**第三步：制定修复计划**
- 按优先级排序：**首先解决SwiftData关系问题**，再解决基础依赖问题，最后解决上层业务逻辑问题
- 识别需要同时修改的文件组合
- 预判修复可能引发的连锁反应

**第四步：批量执行修复**
- **一次性修复所有相关文件**，而非逐个修复
- 确保修复方案的一致性和完整性
- 优先使用项目既有的命名规范和架构模式

### 15.3 SwiftData关系错误专项处理

**核心原则：遵循单端inverse规则，绝对不能删除inverse参数来"解决"编译错误**

**常见错误修正示例**：
```swift
// ❌ 错误：两端都使用@Relationship（违反单端inverse规则）
@Model class EAHabit {
    @Relationship(inverse: \EAUser.habits)
    var user: EAUser?
}
@Model class EAUser {
    @Relationship(inverse: \EAHabit.user) // 错误：冗余定义
    var habits: [EAHabit] = []
}

// ✅ 正确：单端inverse规则
@Model class EAUser {
    @Relationship(deleteRule: .cascade, inverse: \EAHabit.user)
    var habits: [EAHabit] = []
}
@Model class EAHabit {
    var user: EAUser? // 普通属性，无需@Relationship
}
```

## 十六、iOS 17.0+兼容性架构设计（遵循开发规范文档v2.1）

### 16.1 技术栈兼容性保障

#### Swift Concurrency在iOS 17.0+的架构适配

**@MainActor架构模式**：
```swift
// 架构层面的@MainActor使用策略
protocol EAMainActorViewModel: ObservableObject {
    // 所有ViewModel必须在主线程运行
}

extension EAMainActorViewModel {
    // 提供统一的主线程执行保障
    @MainActor
    func updateUI<T>(_ operation: @escaping () -> T) -> T {
        return operation()
    }
}

// 具体实现
@MainActor
class EAHabitViewModel: EAMainActorViewModel {
    @Published var habits: [EAHabit] = []
    @Published var isLoading: Bool = false
    
    // 自动在主线程执行
    func loadHabits() async {
        isLoading = true
        defer { isLoading = false }
        
        // 后台数据获取
        let habits = await Task.detached {
            try await EAHabitService.shared.fetchHabits()
        }.value
        
        // 主线程UI更新
        self.habits = habits
    }
}
```

### 16.2 性能监控架构

**架构级性能监控**：
```swift
// 架构性能监控
class EAArchitectureMonitor {
    static let shared = EAArchitectureMonitor()
    
    private var performanceMetrics: [String: TimeInterval] = [:]
    
    func startMeasuring(_ operation: String) -> String {
        let measurementId = UUID().uuidString
        performanceMetrics[measurementId] = CFAbsoluteTimeGetCurrent()
        return measurementId
    }
    
    func endMeasuring(_ measurementId: String, operation: String) {
        guard let startTime = performanceMetrics.removeValue(forKey: measurementId) else {
            return
        }
        
        let duration = CFAbsoluteTimeGetCurrent() - startTime
        
        // 性能阈值检查
        let threshold: TimeInterval = 0.1 // 100ms
        if duration > threshold {
            print("⚠️ 性能警告: \(operation) 耗时 \(duration)秒")
        }
        
        // 记录性能数据
        recordPerformanceMetric(operation: operation, duration: duration)
    }
    
    private func recordPerformanceMetric(operation: String, duration: TimeInterval) {
        // 性能数据记录逻辑
        // 可以发送到分析服务
    }
}
```

---

## 架构演进规划

### 短期目标（1-3个月）
- 完善AI引擎集成和成本控制
- 优化SwiftData关系设计和性能
- 增强编译错误预防机制
- **完成数字宇宙主题社区功能上线**

### 中期目标（3-6个月）
- 多AI服务商支持和智能降级
- 高级个性化功能和情境感知
- 跨平台扩展准备和架构优化
- **挑战系统功能扩展和AI宇宙向导完善**
- **好友功能完整实现和AI增强聊天优化**

### 长期目标（6-12个月）
- 生态系统建设和社区功能完善
- 国际化支持和多语言AI
- 企业级功能扩展和性能监控
- **数字宇宙生态系统的全面建设和多平台扩展**
- **星际伙伴生态系统成熟化和跨平台好友功能**
