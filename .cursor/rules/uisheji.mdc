---
description: 
globs: 
alwaysApply: false
---
# Evolve - UI设计规范文档 v2.0

本文档定义了Evolve应用的视觉设计语言、交互模式和UI组件规范。所有界面设计和实现必须严格遵循本规范，确保产品体验的一致性、美观性和易用性。

## 一、设计哲学与核心概念

### 设计哲学：「自然启示」与「科技微光」的融合
- **自然启示**：从自然界汲取灵感，传递生命力与成长感
- **科技微光**：融入未来科技感，传递智能与进化

### 隐喻系统：「生态」与「能量流转」
- **习惯 = 生态元素**：每个习惯是一个成长的生态单元
- **进度 = 能量流**：完成习惯释放能量，形成可视化的能量流转
- **AI = 智慧核心**：AI助手为"智慧核心"，引导用户成长

## 二、色彩系统

### 主色调
- **深邃蓝绿色** `#002b20` - 主要背景色，营造神秘、沉静且富有生命力的氛围
- **背景渐变**：`linear-gradient(170deg, #0A2F51 0%, #005A4B 55%, #002b20 100%)`

### 强调色/能量色
- **荧光青色** `#40E0D0` - 代表AI的智慧、新生与活力
- **珊瑚粉/日落橙** `#FF7F50` - 代表用户的热情、行动力与成就

### 辅助色
- **中性灰**：浅灰 `#F5F5F7`、中灰 `#C7C7CC`、深灰 `#8A8A8F`、暗灰 `#48484A`
- **金属质感色**：银色 `#D9D9DF`、淡金 `#F0E6D2`

### 状态色
- **成功**: `#34C759` | **警告**: `#FF9500` | **错误**: `#FF3B30` | **信息**: `#007AFF`

### 特殊渐变
- **智慧核心渐变**：`radial-gradient(circle, rgba(56,239,125,0.8) 0%, rgba(0,200,200,0.7) 70%)`
- **能量流渐变**：`linear-gradient(45deg, #40E0D0 0%, #FF7F50 100%)`

## 三、字体系统

### 字体选择
- **主字体**：SF Pro Display (iOS系统字体)
- **数字字体**：SF Pro Rounded (圆润数字显示)

### 字体层级
| 层级 | 字号 | 字重 | 行高 | 用途 |
|------|------|------|------|------|
| H1 | 28pt | Bold | 34pt | 页面主标题 |
| H2 | 22pt | Semibold | 28pt | 区块标题 |
| H3 | 18pt | Semibold | 24pt | 卡片标题 |
| Body | 16pt | Regular | 22pt | 正文内容 |
| Caption | 14pt | Regular | 18pt | 辅助说明 |
| Small | 12pt | Regular | 16pt | 次要信息 |

## 四、间距与布局系统

### 基础间距单位
- **基础单位**：8pt
- **常用间距**：4pt, 8pt, 12pt, 16pt, 20pt, 24pt, 32pt, 40pt, 48pt

### 布局原则
- **安全区域**：严格遵守iOS安全区域
- **边距**：页面边距 16pt，卡片内边距 16pt
- **卡片间距**：垂直间距 12pt，水平间距 16pt
- **按钮高度**：主要按钮 48pt，次要按钮 40pt

## 五、核心UI组件规范

### 5.1 按钮系统
**主要按钮 (EAButton)**
- 高度：48pt，圆角：12pt
- 背景：能量流渐变
- 文字：16pt Semibold，白色
- 按下状态：透明度 0.8

**次要按钮**
- 高度：40pt，圆角：10pt
- 边框：1pt，荧光青色
- 文字：14pt Medium，荧光青色

### 5.2 卡片系统
**习惯卡片 (EAHabitCard)**
- 圆角：16pt，阴影：0 4pt 12pt rgba(0,0,0,0.1)
- 内边距：16pt
- 背景：半透明白色 rgba(255,255,255,0.1)

**社区帖子卡片 (EACommunityPostCard)**
- 圆角：12pt，内边距：16pt
- 用户头像：40pt 圆形
- 点赞按钮：心形图标，荧光青色

### 5.3 输入组件
**文本输入框 (EATextField)**
- 高度：48pt，圆角：12pt
- 边框：1pt，中性灰
- 聚焦状态：边框变为荧光青色
### 5.4 进度指示器
**能量流进度条 (EAEnergyFlowProgressBar)**
- 高度：8pt，圆角：4pt
- 背景：深灰，进度：能量流渐变
- 动画：流动光效

### 5.5 AI交互组件
**AI头像 (EAAIAvatarView)**
- 尺寸：64pt 圆形
- 背景：智慧核心渐变
- 呼吸动画：缩放 0.95-1.05，2秒周期

**对话气泡**
- 用户消息：右对齐，珊瑚粉背景
- AI消息：左对齐荧光青色背景
- 圆角：16pt，最大宽度：屏幕宽度的 75%

## 六、页面布局规范

### 6.1 导航结构
- **Tab Bar**：5个主要页面，图标 + 文字
- **导航栏**：透明背景，大标题样式
- **返回按钮**：系统样式，荧光青色

### 6.2 列表布局
- **垂直间距**：12pt
- **分组间距**：24pt
- **列表项高度**：最小 60pt

### 6.3 表单布局
- **字段间距**：16pt
- **标签与输入框间距**：8pt
- **提交按钮**：距离表单 32pt

## 七、交互与动效规范

### 7.1 基础动效
- **标准缓动**：ease-in-out
- **快速交互**：0.2秒
- **页面转场**：0.3秒
- **状态变化**：0.4秒

### 7.2 特殊动效
- **能量流动**：线性动画，1.5秒循环
- **AI呼吸**：缩放动画，2秒循环
- **完成庆祝**：粒子效果，1秒

### 7.3 手势交互
- **点击反馈**：轻微缩放 (0.95)
- **长按**：1秒触发，震动反馈
- **滑动操作**：左滑删除，右滑完成

## 八、无障碍设计规范

### 8.1 对比度要求
- **正文文字**：对比度 ≥ 4.5:1
- **大字体**：对比度 ≥ 3:1
- **图标按钮**：对比度 ≥ 3:1

### 8.2 字体支持
- **动态字体**：支持系统字体大小调整
- **最小字号**：12pt
- **最大缩放**：支持到 200%

### 8.3 交互支持
- **VoiceOver**：所有交互元素提供标签
- **按钮尺寸**：最小 44pt × 44pt
- **焦点指示**：清晰的焦点边框

## 九、社区功能UI规范

### 9.1 社区组件
**用户头像 (EAUserAvatarView)**
- 标准尺寸：40pt，大尺寸：64pt
- 在线状态：绿色圆点，4pt

**点赞按钮 (EALikeButton)**
- 图标：心形，24pt
- 未点赞：中性灰，已点赞：珊瑚粉
- 点击动画：缩放 + 颜色变化

**能量等级指示器 (EAEnergyLevelIndicator)**
- 显示方式：进度环 + 数字
- 颜色：根据等级从荧光青到珊瑚粉渐变

### 9.2 社区页面布局
- **帖子间距**：16pt
- **评论缩进**：32pt
- **操作按钮区域**：高度 48pt

## 十、设计资源与工具

### 10.1 设计文件
- **Figma设计稿**：包含所有组件和页面
- **图标库**：SF Symbols + 自定义图标
- **色彩库**：Assets.xcassets 中定义

### 10.2 开发资源
- **SwiftUI组件库**：UIComponents/ 目录
- **颜色定义**：AppColors.colorset
- **字体配置**：Typography.swift

### 10.3 设计检查清单
- [ ] 色彩符合规范
- [ ] 字体层级正确
- [ ] 间距使用基础单位
- [ ] 组件样式一致
- [ ] 无障碍支持完整
- [ ] 动效自然流畅

---

**版本信息**：v2.0 | **最后更新**：2024年 | **维护者**：Evolve设计团队
