
社区数据库设计审查
 你是一名资深的iOS架构师，专长为SwiftData和大型应用的架构设计，现在请你根据rules规范和IOS官方开发规范来进行代码审查：
🏗️ 架构层级审查（按优先级）
1️⃣ 数据模型层审查（最高优先级）
审查目标：SwiftData关系完整性和数据一致性
检查清单：
□ EAUser ↔ EAUserSettings 关系是否正确（一对一）
□ EAUser ↔ EAHabit 关系是否正确（一对多）
□ EAHabit ↔ EACompletion 关系是否正确（一对多）
□ EAUser ↔ EAUserSocialProfile 关系是否正确（一对一）
□ EAUser ↔ EAUserModerationProfile 关系是否正确（一对一）
□ EAUser ↔ EAUserDataProfile 关系是否正确（一对一）
□ EAPayment ↔ EAUserDataProfile 关系是否正确（多对一）
□ EAAIMessage ↔ EAUser 关系是否正确（多对一）
□ 所有关系是否遵循单端inverse规则
□ 是否存在外键模式违规（userId、habitId等字段）
□ 关系赋值顺序是否符合iOS 18+要求（当前项目需兼容ios17.0+以及IOS17.0以上）

2️⃣ Repository层审查（高优先级）
审查目标：数据访问层架构合规性
检查清单：
□ 所有Repository是否使用@ModelActor修饰
□ 是否通过EARepositoryContainer统一管理
□ 是否禁止直接ModelContext访问
□ Repository方法是否线程安全
□ 错误处理是否完整
□ 数据查询性能是否优化
□ 是否存在Context一致性问题

3️⃣ 服务层审查（中优先级）
审查目标：业务逻辑和依赖注入
检查清单：
□ EASessionManager是否正确管理用户会话
□ EAAuthService是否正确处理认证流程
□ EAPaymentService是否安全处理支付数据
□ EAAIService是否正确桥接AI数据
□ 是否避免单例模式，使用依赖注入
□ 服务间依赖关系是否清晰
□ 错误处理和降级策略是否完善

4️⃣ 数据流审查（中优先级）
审查目标：端到端数据流完整性
检查清单：
□ 用户注册→登录→会话恢复流程是否正常
□ 习惯创建→完成记录→统计分析流程是否正常
□ AI对话→数据存储→历史查询流程是否正常
□ 支付购买→记录保存→状态更新流程是否正常
□ 数据导出→隐私保护→格式正确性是否正常
□ 跨模块数据引用是否一致

5️⃣ 性能和稳定性审查（一般优先级）
审查目标：生产环境可靠性

检查清单：
□ 大数据量查询是否有分页机制
□ 缓存策略是否合理
□ 内存泄漏风险是否可控
□ 数据库迁移策略是否完善
□ 异常情况下的数据恢复机制
□ 并发访问的数据安全性
# 检测外键模式违规
grep -r "var.*Id: UUID" --include="*.swift" Evolve/Core/DataModels/
grep -r "userId\|habitId\|profileId" --include="*.swift" Evolve/

# 检测单例模式违规
grep -r "static let shared" --include="*.swift" Evolve/Core/Services/
grep -r "\.shared\." --include="*.swift" Evolve/

# 检测直接ModelContext使用
grep -r "@Environment(\.modelContext)" --include="*.swift" Evolve/Features/
grep -r "private.*modelContext" --include="*.swift" Evolve/Core/Services/

// 验证每个@Relationship是否有对应的inverse
// 验证关系两端类型是否匹配
// 验证关系赋值是否在正确的Context中执行

## 🔍 Evolve数据层审查报告

### 🔴 严重问题（必须立即修复）
- [具体问题] - 影响：[数据完整性/用户体验/系统稳定性]
- 涉及文件：[文件列表]

### 🟡 重要问题（影响功能正常性）
- [具体问题] - 影响：[性能/维护性]

### 🟢 优化建议（提升代码质量）
- [具体问题] - 建议：[优化方案]

### ✅ 验证通过的模块
- [模块名称] - 状态：[完全合规/基本正常]

### 📋 数据流验证结果
- 用户认证流程：[✅正常/❌异常]
- 习惯管理流程：[✅正常/❌异常]
- AI数据处理流程：[✅正常/❌异常]
- 支付系统流程：[✅正常/❌异常]

### 🎯 修复优先级建议
1. **立即修复**：[严重问题列表]
2. **本周修复**：[重要问题列表]
3. **后续优化**：[优化建议列表]







AI代码审查（移除重复文件）提示词：项目代码库整合与改革清理审计
角色：
你是一位资深的软件架构师，专长是代码重构（重构）和大型项目。你现在接手一个已经功能完善但可能存在代码开发的iOS项目。

核心任务：
对整个Evolve App的代码库进行一次全面的整合与快速清理审核。你的目标是识别并提出解决方案，消除重复的组件、废弃的代码和不一致的架构实现，最终目标是让项目代码更简洁、更丰富、更易于维护。

核心原则：

安全第一：在最终确认之前，您的所有操作都只是提出计划，而不是直接删除文件。
规范驱动：所有判断的最终参考项目的**《技术架构文档》和《开发规范文档》**。
择优劣汰：当发现功能重复的组件时，应保留功能更全、性能更优、更符合最新设计的规范版本。
请严格按照以下三阶段执行审计，并生成报告：

第一阶段：分析与发现（Analysis & Discovery）
在这个阶段，您只需扫描和分析代码，识别出所有潜在问题，并进行分类。

1.1. 组件冗余分析（组件冗余）

指令：深度扫描UIComponents/和Features/目录，查找相似或功能重叠的UI组件和功能组件。
审查线索：特别关注标有、、、V2等前后缀的文件名，例如和。NewOptimizedLegacyEACommunityPostCardEAOptimizedCommunityPostCard
简单来说：对于每一组疑似重复的组件，在内部进行代码比对，初步判断哪个是“推荐保留”的版本。
1.2. 代码资产使用率分析（资产和代码使用情况）

指令：执行一次项目级的交叉引用检查，查找所有未被任何活动代码调用的**“孤岛代码”和“僵尸资产”**。
审查范围：
Swift文件：从未被import或引用的class, struct, enum。
函数/方法：从未被调用的private或internal方法。
资源文件：在Assets.xcassets中存在，但从未在代码中被引用的Color Set或Image Set。
总量：列出所有疑似未使用代码和资产的清单。
1.3. 架构一致性检查（架构一致性）

指令：审查所有核心的视图（View）文件，确保它们使用的是在1.1中确定的“推荐保留”版本的组件。
审查案例：例如，检查EACommunityView.swift是否正在使用性能更优的EAOptimizedCommunityPostCard，而不是旧版本的EACommunityPostCard。
总量：列出所有使用过的“待荒废”组件的清单，明确指出哪个文件需要被修改。
第二阶段：整合与重构计划（Consolidation & Refactoring Plan）
这是最关键的一步。根据第一阶段的发现，你需要生成一份详细、安全、可执行的重构计划。在这一步，绝对不要修改任何代码。

请以Markdown格式输出你的计划：


## 代码库整合与重构计划报告

### 1. 冗余组件整合方案

---
#### **组件组 1: 帖子卡片 (Post Card)**
* **冗余文件**: 
    * `Evolve/UIComponents/EACommunityPostCard.swift`
    * `Evolve/UIComponents/EAOptimizedCommunityPostCard.swift`
* **推荐方案**: **保留 `EAOptimizedCommunityPostCard.swift`**，废弃 `EACommunityPostCard.swift`。
* **保留理由**: 该版本包含了滚动性能优化的逻辑，更符合高质量UI的要求。
* **需要修改的文件清单**:
    * `Evolve/Features/Community/EACommunityView.swift`: 将所有 `EACommunityPostCard` 的调用替换为 `EAOptimizedCommunityPostCard`。
    * `Evolve/Features/Me/EAPostHistoryView.swift` (假设存在): 同样需要替换。

---
#### **组件组 2: ... (其他冗余组件)**
* ...

### 2. 待废弃的孤岛代码与资产

* **文件**:
    * `Evolve/Common/Tools/OldLogger.swift` (原因: 已被新的日志系统替代，无任何引用)
    * `Evolve/Features/Onboarding/EAWelcomeView_V1.swift` (原因: 已被V2版本完全替代)
* **资产**:
    * `Assets.xcassets/Colors/OldThemeBlue` (原因: 已被新的色彩系统替代)

### 3. 架构一致性修复清单

* **问题**: `EACommunityView` 正在使用已过时的 `EACommunityPostCard` 组件。
* **文件**: `Evolve/Features/Community/EACommunityView.swift`
* **修复操作**: 将该文件中对 `EACommunityPostCard` 的引用，全部更新为 `EAOptimizedCommunityPostCard`。

---
**计划总结**: 本计划预计将移除 X 个冗余文件，更新 Y 个文件的组件引用，以提升代码一致性和性能。建议在执行前进行代码备份。
第三阶段：安全执行（Safe Execution）
指令：待第二阶段的计划被确认并批准后，你才能开始执行。
执行要求：
逐项执行：严格按照计划报告中的每一项进行修改。
小步工作：每完成一个组件的替换或一个文件的删除，都应单独进行一次编译测试，确保应用可以正常运行。
验证最终：在所有计划执行完毕后，运行一次完整的应用测试，确保所有功能，特别是被修改过的部分，行为与之前完全一致且无误。