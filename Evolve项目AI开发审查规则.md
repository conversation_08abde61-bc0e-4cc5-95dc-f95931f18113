# Evolve项目AI开发审查规则 v4.0

**更新日期：** 2025-06-21  
**适用范围：** Evolve iOS应用AI辅助开发的代码审查与质量控制  
**审查目标：** 确保AI开发产出符合项目规范、iOS设计标准和代码质量要求  
**文档依据：** 严格遵循《开发规范文档 v2.5》（.cursorrules）作为唯一权威标准  
**v4.0更新重点：** 新增性能稳定性、iOS版本兼容性、设备兼容性、代码质量深度审查

---

## 📝 SwiftData关系定义口诀（务必牢记！）
> **一对关系，一端inverse，另一端普通属性，SwiftData自动帮你搞定双向！**
> 
> 生活比喻：就像两个人结婚，户口本上只需要一方写"配偶是谁"，另一方自动就能查到，不用两边都写。

---

## 🎯 一、审查执行原则

### 核心要求
- **分层审查**：架构基础层→服务层→视图层，按优先级系统审查
- **一次性全面**：禁止"发现一批问题→修复→再发现新问题"的循环模式
- **问题分级**：🔴严重→🟡重要→🟢一般，优先处理架构性问题
- **严禁print()调试语句**：生产代码中严禁保留print()调试语句

### 使用方式
1. **开发完成后**：AI完成任何开发任务后，按本规则逐项检查
2. **问题修正**：发现问题必须立即修正，直到完全符合规范
3. **编译验证**：每次修复后必须完整编译验证

---

## 🔴 二、架构基础层审查（最高优先级）

### 2.1 SwiftData关系模式检查

**🚨 强制要求（绝对不可违反）**：
- [ ] **禁止外键模式**：不得使用`userId`、`habitId`等外键字段
- [ ] **关系模式强制**：必须使用标准SwiftData `@Relationship`模式
- [ ] **单端inverse规则**：每对关系只在一端用`@Relationship(inverse:)`，另一端用普通属性，SwiftData自动维护双向关系
- [ ] **关系完整性**：所有关系必须有对应的反向关系定义（但只需一端写@Relationship）
- [ ] **关系赋值顺序**：所有关系赋值必须遵循"插入Context→赋值关系→保存Context"，禁止在init中赋值关系
- [ ] **🚨 Context一致性（iOS 18+关键）**：所有关系赋值操作必须保证两端对象属于同一ModelContext，禁止跨Context赋值（防止iOS 18+崩溃）

**🔒 共享Container架构检查（新增关键要求）**：
- [ ] **共享ModelContainer**：所有Repository必须使用同一个ModelContainer实例
- [ ] **禁止独立Context**：不得在Repository中独立创建ModelContext
- [ ] **Container传递正确**：EARepositoryContainer必须传递共享Container给所有Repository
- [ ] **🚫 拒绝统一数据管理器**：严禁创建统一数据管理器替代Repository架构

**快速检测方法**：
```bash
# 外键违规检测
grep -r "var.*Id: UUID" --include="*.swift" Evolve/
grep -r "userId\|habitId\|profileId" --include="*.swift" Evolve/

# Context一致性检测
grep -r "ModelContext(" --include="*.swift" Evolve/
grep -r "static let shared.*Manager" --include="*.swift" Evolve/
```

**标准关系模式**：
```swift
// ✅ 正确：标准SwiftData关系
@Model final class EAUser {
    @Relationship(deleteRule: .cascade, inverse: \EAHabit.user)
    var habits: [EAHabit] // 只在这里写@Relationship
}
@Model final class EAHabit {
    var user: EAUser? // 只用普通属性，不用@Relationship
}

// ❌ 禁止：外键模式
@Model final class EAHabit {
    var userId: UUID? // 严格禁止
}

// ❌ 禁止：统一数据管理器
class EAUnifiedDataManager {
    static let shared = EAUnifiedDataManager() // 严格禁止
}
```

### 2.2 Repository模式完整性检查

**强制要求**：
- [ ] **数据访问统一**：所有数据访问必须通过Repository层
- [ ] **禁止直接ModelContext**：Service、ViewModel不得直接注入ModelContext
- [ ] **Repository协议**：每个Repository必须有对应的协议定义
- [ ] **Repository必须@ModelActor修饰**：所有Repository都必须用@ModelActor修饰，禁止普通class/struct实现Repository，保证线程安全

**检测方法**：
```bash
# 违规模式检测
grep -r "@Environment(\\.modelContext)" --include="*.swift" Evolve/
grep -r "private.*modelContext" --include="*.swift" Evolve/
```

### 2.3 数据模型复杂度控制

**限制要求**：
- [ ] **关系数量限制**：单个模型@Relationship不得超过5个
- [ ] **模型拆分**：超过限制的模型必须拆分为多个相关模型，必须通过一对一关系连接相关模型，禁止直接多对多关系
- [ ] **关系访问**：通过关系访问数据，禁止外键查询

**🔑 数据查询性能阈值检查（iOS平台优化）**：
- [ ] **单次查询限制**：单次查询返回记录数≤100条，超过必须分页处理
- [ ] **查询响应时间**：数据查询响应时间必须<100ms，复杂查询必须异步处理
- [ ] **关系深度限制**：避免深度关系遍历(>3层)，大量关系数据必须懒加载
- [ ] **批量操作限制**：单次批量插入/更新≤50条记录，必须使用事务包装

### 2.4 全局状态管理器设计审查（新增核心检查）

**🚨 强制要求（防止游离对象问题）**：
- [ ] **禁止持有模型对象**：全局管理器（如EASessionManager）严禁直接持有SwiftData模型对象
- [ ] **只持有稳定标识符**：全局管理器只能持有UUID等稳定标识符
- [ ] **异步安全接口**：必须提供异步接口通过Repository层获取最新对象
- [ ] **禁止模型对象缓存**：严禁在全局管理器中缓存SwiftData模型对象

**检测方法**：
```bash
# 全局管理器违规检测
grep -r "@Published.*: EA.*?" --include="*.swift" Evolve/Core/Services/
grep -r "var.*: EAUser\|var.*: EAHabit" --include="*Manager.swift" Evolve/
grep -r "private var cached.*: \[.*: EA" --include="*.swift" Evolve/
```

**标准模式验证**：
```swift
// ✅ 正确：只持有ID，提供异步接口
class EASessionManager: ObservableObject {
    @Published private(set) var currentUserID: UUID? = nil

    var safeCurrentUser: EAUser? {
        get async {
            // 通过Repository安全获取
        }
    }
}

// ❌ 禁止：持有模型对象
class EASessionManager: ObservableObject {
    @Published var currentUser: EAUser? = nil // 严禁
}
```

---

## 🟡 三、服务层架构审查（高优先级）

### 3.1 单例模式禁用检查

**强制要求**：
- [ ] **禁用单例**：不得使用`static let shared = XXX()`模式
- [ ] **依赖注入**：必须通过Environment传递服务
- [ ] **协议抽象**：服务必须有协议定义
- [ ] **禁止Environment默认值隐式单例**：Environment扩展禁止设置默认值，避免隐式单例

**检测方法**：
```bash
# 单例违规检测
grep -r "static let shared" --include="*.swift" Evolve/
grep -r "\\.shared\\." --include="*.swift" Evolve/
```

### 3.2 AI数据桥接架构检查

**要求**：
- [ ] **AI服务隔离**：AI服务不得直接访问Repository
- [ ] **数据桥接层**：通过专门的数据桥接层访问数据
- [ ] **格式转换统一**：AI数据格式转换集中处理
- [ ] **AI分析结果和缓存独立存储**：AI分析结果和缓存必须独立存储，桥接层需实现缓存和数据格式转换

### 3.3 业务服务层架构检查

**核心检查项**：
- [ ] **服务职责单一**：每个Service专注单一业务领域
- [ ] **错误处理统一**：使用统一的错误类型和处理机制
- [ ] **异步操作规范**：正确使用Swift Concurrency（async/await）
- [ ] **缓存策略合理**：实现适当的缓存机制，避免重复计算

**检测方法**：
```bash
# 服务层检查
grep -r "class.*Service" --include="*.swift" Evolve/Core/Services/
grep -r "protocol.*Service" --include="*.swift" Evolve/Core/Services/
```

---

## 🟢 四、视图层合规审查（中优先级）

### 4.1 MVVM架构检查

**必须检查项**：
- [ ] **@MainActor标记**：所有ViewModel必须标记@MainActor
- [ ] **状态管理**：正确使用@Published暴露状态
- [ ] **单向数据流**：View → ViewModel → Repository
- [ ] **依赖注入**：通过Environment注入依赖

### 4.2 SwiftUI实现检查

**核心检查项**：
- [ ] **Sheet状态管理**：使用统一的Sheet状态枚举
- [ ] **组件复用**：重复UI元素封装为组件
- [ ] **EA命名规范**：所有自定义类型必须用EA前缀
- [ ] **预览支持**：所有View必须包含SwiftUI预览

### 4.3 UI设计规范检查

**设计一致性**：
- [ ] **色彩系统**：使用Assets.xcassets中定义的颜色
- [ ] **间距系统**：使用8pt基础间距的倍数
- [ ] **字体系统**：使用系统字体和定义的层级
- [ ] **安全区域**：正确处理刘海屏和Home Indicator

### 4.4 状态管理统一性检查

**SwiftUI状态管理规范**：
- [ ] **@State使用正确**：仅用于单一视图本地状态
- [ ] **@Binding使用正确**：用于双向数据绑定
- [ ] **@ObservedObject/@StateObject使用正确**：ViewModel引用
- [ ] **@Environment使用正确**：全局依赖注入

**Sheet状态管理特殊检查**：
- [ ] **统一状态管理**：所有Sheet状态在单一ViewModel中集中管理
- [ ] **枚举驱动模式**：使用枚举定义所有可能的Sheet类型
- [ ] **避免多重Sheet冲突**：确保同时只有一个Sheet处于活跃状态

### 4.5 组件复用与设计系统检查

**组件库一致性**：
- [ ] **组件复用阈值**：3个或以上地方重复使用的UI元素已封装为组件
- [ ] **参数传递清晰**：使用明确的参数传递组件配置
- [ ] **组件接口设计**：定义清晰的组件API，隐藏内部实现
- [ ] **@ViewBuilder支持**：提供内容定制能力
- [ ] **EA前缀**：所有自定义类型必须用EA前缀，组件接口需清晰、参数传递明确

### 4.6 无障碍支持检查

**可访问性要求**：
- [ ] **VoiceOver支持**：所有交互元素提供合适的标签和提示
- [ ] **动态字体支持**：响应系统字体大小设置变化
- [ ] **对比度合规**：文本对比度遵循WCAG 2.1 AA标准
- [ ] **减少动画支持**：响应系统"减少动画"设置

---

## 🔧 五、集成与性能审查

### 5.1 AI服务集成检查

**AI集成架构要求**：
- [ ] **接口封装**：EAAIService处理API通信，EAAIManager管理会话
- [ ] **类型安全**：使用Codable协议，定义请求/响应模型
- [ ] **成本控制**：实现AI调用成本控制机制
- [ ] **缓存策略**：AI响应缓存管理，避免重复调用

### 5.2 通知系统集成检查

**通知系统要求**：
- [ ] **权限管理**：合适时机请求通知权限
- [ ] **智能调度**：基于用户设定时间和行为模式
- [ ] **个性化内容**：根据教练风格生成不同提醒内容
- [ ] **通知管理**：用户可查看和管理已安排的通知

### 5.3 性能优化检查

**性能指标要求**：
- [ ] **启动时间**：应用启动时间<1.5秒
- [ ] **UI流畅度**：保持60fps的UI流畅度
- [ ] **内存使用**：内存使用<200MB
- [ ] **网络优化**：合理的网络请求频率和缓存策略

### 5.4 内存管理检查

**内存安全要求**：
- [ ] **循环引用防范**：正确使用[weak self]
- [ ] **资源释放**：及时取消Task和移除监听器
- [ ] **大对象管理**：图片延迟加载和适当缓存
- [ ] **SwiftData内存管理**：及时释放大型查询结果

**🔑 iOS内存管理阈值检查**：
- [ ] **单个视图数据对象**：≤50个，避免视图内存过载
- [ ] **图片缓存限制**：≤20MB，超过自动清理最旧缓存
- [ ] **大文件处理**：>10MB文件必须分块处理，避免内存峰值
- [ ] **内存警告响应**：收到内存警告时强制清理缓存和临时数据
- [ ] **后台任务时间**：≤30秒，超时自动终止
- [ ] **后台资源释放**：应用进入后台时释放非必要资源

---

## ⚡ 六、性能与稳定性深度审查（新增）

### 6.1 应用性能基准检查

**启动性能要求**：
- [ ] **冷启动时间**：<2秒（从点击图标到首屏显示）
- [ ] **热启动时间**：<1秒（从后台切换回前台）
- [ ] **数据加载时间**：核心数据加载<1秒
- [ ] **页面切换响应**：页面切换延迟<300ms

**运行时性能要求**：
- [ ] **内存峰值控制**：正常使用<150MB，峰值<300MB
- [ ] **CPU使用率**：空闲状态<5%，操作时<30%
- [ ] **电池消耗**：后台运行每小时<2%电量
- [ ] **网络效率**：合理的请求频率，避免频繁轮询

### 6.2 SwiftUI性能优化检查

**渲染性能要求**：
- [ ] **避免过度重绘**：使用@State、@Binding、@Published正确管理状态
- [ ] **列表性能优化**：长列表使用LazyVStack/LazyHStack
- [ ] **图片性能**：AsyncImage配合合理的缓存策略
- [ ] **动画性能**：复杂动画使用withAnimation和合理的duration

**组件性能检查**：
- [ ] **组件层级深度**：避免超过10层的嵌套视图
- [ ] **计算属性优化**：避免在body中进行复杂计算
- [ ] **PreferenceKey使用**：正确使用PreferenceKey传递数据
- [ ] **ViewBuilder优化**：合理使用@ViewBuilder减少视图重建

### 6.3 SwiftData性能优化检查

**数据库性能要求**：
- [ ] **查询优化**：使用合适的谓词和排序，避免全表扫描
- [ ] **批量操作**：大量数据操作使用批量处理
- [ ] **关系加载**：合理使用懒加载，避免不必要的关系数据加载
- [ ] **索引策略**：为经常查询的字段考虑添加索引

**内存管理要求**：
- [ ] **上下文管理**：及时释放不再使用的ModelContext
- [ ] **大对象处理**：大型数据对象使用流式处理
- [ ] **缓存策略**：实现合理的本地缓存，避免重复查询
- [ ] **事务管理**：合理使用事务，避免长时间锁定

---

## 📱 七、iOS版本兼容性审查（新增）

### 7.1 iOS 17.0-18.5+兼容性检查

**核心兼容性要求**：
- [ ] **最低版本支持**：确保所有功能在iOS 17.0上正常运行
- [ ] **API可用性检查**：使用@available标记版本特定功能
- [ ] **降级策略**：新版本特性在旧版本上有合理降级
- [ ] **测试覆盖**：在iOS 17.0、17.4、18.0、18.5+上测试核心功能

**SwiftUI兼容性要求**：
- [ ] **新特性使用**：iOS 17.0+新特性正确使用@available包装
- [ ] **布局兼容**：确保布局在不同iOS版本上一致
- [ ] **动画兼容**：动画效果在不同版本上表现一致
- [ ] **导航兼容**：NavigationStack在iOS 16+上的兼容处理

### 7.2 SwiftData版本兼容性检查

**数据模型兼容性**：
- [ ] **Schema演进**：支持数据模型的版本升级
- [ ] **迁移策略**：定义清晰的数据迁移路径
- [ ] **向后兼容**：新版本数据模型不破坏旧版本数据
- [ ] **容错处理**：数据损坏时的恢复机制

**iOS 18+特殊要求**：
- [ ] **关系验证增强**：iOS 18+对SwiftData关系验证更严格
- [ ] **Context一致性**：严格的ModelContext一致性检查
- [ ] **内存管理优化**：iOS 18+内存管理策略调整
- [ ] **性能监控**：iOS 18+性能监控API集成

### 7.3 API兼容性检查

**系统API使用**：
- [ ] **弃用API避免**：不使用已弃用的API
- [ ] **新API适配**：合理使用新版本API提升体验
- [ ] **权限兼容**：不同版本的权限请求处理
- [ ] **通知兼容**：UserNotifications在不同版本的兼容性

---

## 📱 八、设备兼容性审查（新增）

### 8.1 iPhone/iPad布局兼容性检查

**响应式布局要求**：
- [ ] **屏幕尺寸适配**：支持iPhone SE到iPhone 15 Pro Max全尺寸
- [ ] **iPad布局优化**：iPad上的布局不是简单的iPhone放大
- [ ] **横竖屏适配**：支持设备旋转，布局合理调整
- [ ] **安全区域处理**：正确处理不同设备的安全区域

**iPad特殊要求**：
- [ ] **多窗口支持**：支持iPad的多窗口和分屏功能
- [ ] **触控优化**：触控区域大小适合iPad使用
- [ ] **键盘支持**：支持iPad外接键盘快捷键
- [ ] **Apple Pencil**：相关功能支持Apple Pencil输入
- [ ] **🚨 iPad崩溃预防**：特别检查Sheet、NavigationStack、List等组件在iPad上的表现
- [ ] **iPad布局验证**：每个页面必须在iPad模拟器上验证布局和功能正常

### 8.2 设备性能分级检查

**性能分级策略**：
- [ ] **设备检测**：检测设备性能等级（A12+、A15+、A17+）
- [ ] **功能分级**：根据设备性能调整功能复杂度
- [ ] **降级策略**：低性能设备的功能降级方案
- [ ] **内存限制**：不同设备的内存使用限制

### 8.3 设备特性兼容检查

**硬件特性适配**：
- [ ] **Face ID/Touch ID**：生物认证功能适配
- [ ] **摄像头功能**：不同设备摄像头能力适配
- [ ] **传感器支持**：加速度计、陀螺仪等传感器使用
- [ ] **网络能力**：5G、WiFi 6等网络特性适配

**无障碍功能**：
- [ ] **VoiceOver**：完整的屏幕阅读器支持
- [ ] **动态字体**：支持用户自定义字体大小
- [ ] **高对比度**：支持高对比度显示模式
- [ ] **减少动画**：响应系统减少动画设置

---

## 🧹 九、代码质量深度审查（新增）

### 9.1 废弃代码清理检查

**代码清理要求**：
- [ ] **临时文件清理**：删除所有测试文件（Test*.swift、*Test.swift）
- [ ] **注释代码清理**：删除大段注释掉的代码
- [ ] **TODO/FIXME清理**：处理或删除所有TODO、FIXME、XXX、HACK标记
- [ ] **调试代码清理**：删除所有print()语句（除非用#if DEBUG包装）

**快速检测方法**：
```bash
# 废弃代码检测
find . -name "*Test*.swift" -o -name "*test*.swift"
grep -r "TODO\|FIXME\|XXX\|HACK" --include="*.swift" .
grep -r "print(" --include="*.swift" . | grep -v "#if DEBUG"

# 性能问题检测
grep -r "DispatchQueue.main.async" --include="*.swift" .
grep -r "Task.detached" --include="*.swift" .
grep -r "\.background(" --include="*.swift" .

# 兼容性问题检测
grep -r "@available" --include="*.swift" .
grep -r "if #available" --include="*.swift" .
grep -r "UIDevice.current" --include="*.swift" .
```

### 9.2 代码重复度检查

**重复代码控制**：
- [ ] **重复逻辑提取**：3次以上重复的逻辑必须提取为函数
- [ ] **重复UI组件**：3次以上重复的UI必须封装为组件
- [ ] **重复常量定义**：统一的常量定义，避免魔法数字
- [ ] **重复错误处理**：统一的错误处理机制

### 9.3 代码复杂度控制

**复杂度限制**：
- [ ] **函数复杂度**：单个函数不超过50行
- [ ] **类复杂度**：单个类不超过500行
- [ ] **嵌套深度**：代码嵌套不超过4层
- [ ] **参数数量**：函数参数不超过5个

### 9.4 命名规范检查

**命名一致性**：
- [ ] **EA前缀**：所有自定义类型必须使用EA前缀
- [ ] **驼峰命名**：变量和函数使用小驼峰，类型使用大驼峰
- [ ] **语义化命名**：名称能清晰表达用途，避免缩写
- [ ] **布尔值命名**：布尔值使用is、has、can、should等前缀

### 9.5 文档和注释质量

**文档要求**：
- [ ] **公共API文档**：所有public接口必须有文档注释
- [ ] **复杂逻辑注释**：复杂业务逻辑必须有说明注释
- [ ] **TODO注释规范**：TODO必须包含负责人和截止时间
- [ ] **代码示例**：关键API提供使用示例

---

## 🔒 十、安全与隐私审查

### 10.1 数据安全检查

**安全存储要求**：
- [ ] **敏感数据保护**：API密钥、支付信息使用Keychain存储
- [ ] **本地存储安全**：非敏感数据使用UserDefaults
- [ ] **网络安全**：强制HTTPS，实现证书验证
- [ ] **数据传输加密**：敏感数据传输使用额外加密

### 10.2 用户隐私检查

**隐私保护要求**：
- [ ] **数据收集透明**：清晰说明收集的数据类型和用途
- [ ] **用户控制权**：提供数据导出和删除功能
- [ ] **匿名化处理**：行为分析数据匿名化处理
- [ ] **隐私政策**：提供简明的隐私政策

### 10.3 身份验证检查

**认证安全要求**：
- [ ] **多种认证方式**：支持密码、生物认证和第三方认证
- [ ] **会话管理**：合理的会话过期时间
- [ ] **敏感操作保护**：重要操作需额外验证
- [ ] **异常检测**：检测异常登录行为

---

## 🧪 十一、测试与质量保证审查

### 11.1 SwiftUI预览检查

**预览规范要求**：
- [ ] **预览完整性**：所有SwiftUI视图必须包含预览
- [ ] **预览数据**：使用PreviewData.swift中的模拟数据
- [ ] **多场景支持**：不同状态和设备尺寸的预览
- [ ] **预览配置**：正确配置ModelContainer用于预览

### 11.2 测试覆盖率检查

**测试要求**：
- [ ] **单元测试**：所有ViewModel和Service需有单元测试
- [ ] **测试覆盖率**：核心业务逻辑≥80%
- [ ] **依赖模拟**：使用协议和依赖注入便于测试
- [ ] **UI测试**：测试核心用户流程，使用可访问性标识符

### 11.3 错误处理检查

**错误处理标准**：
- [ ] **错误分类**：网络错误、数据错误、业务错误
- [ ] **用户体验**：友好的错误提示和恢复建议
- [ ] **降级策略**：缓存数据、离线模式
- [ ] **错误日志**：适当的错误日志记录

---

## 🚀 十二、社区功能专项审查

### 12.1 社区架构检查

**社区功能架构要求**：
- [ ] **独立Repository**：社区功能必须有独立的EACommunityRepository
- [ ] **模型独立管理**：社区相关模型不得与核心用户模型混合
- [ ] **权限独立控制**：社区功能的权限控制独立于核心功能

### 12.2 社区性能检查

**性能优化要求**：
- [ ] **分页加载**：社区内容加载必须分页，每页≤20条
- [ ] **图片懒加载**：社区图片必须实现懒加载机制
- [ ] **缓存策略**：社区内容必须有合理的本地缓存策略
- [ ] **加载性能**：页面加载<2秒

### 12.3 社区内容管理检查

**内容管理要求**：
- [ ] **审核机制**：关键词过滤、用户举报、AI辅助审核
- [ ] **质量保障**：鼓励真实分享、标识AI生成内容
- [ ] **隐私保护**：可选分享范围、匿名选项、用户数据控制
- [ ] **数据安全**：社区数据分离存储、遵循GDPR法规

---

## 🔧 十三、快速修复指导

### 13.1 常见问题快速修复

**SwiftData关系错误**：
1. 检查inverse参数的KeyPath是否正确
2. 确认关系两端的类型匹配
3. 验证属性名拼写和单复数
4. 检查关系赋值顺序和上下文一致性

**循环修复防范**：
1. **3次规则**：同一错误修复超过3次必须停止
2. **批量修复**：相关文件必须同时修改
3. **系统分析**：重新审查项目架构和依赖关系

### 13.2 编译错误处理的"第一性原则"原则（核心约束更新）

经验历史表明，AI在修复编译错误时，有时会为了让代码通过编译而牺牲其功能正确性（例如，删除#Predicate过滤条件）。为了根绝此类问题，所有编译错误的修复必须严格遵循以下四步工作流程，禁止任何形式的跳过或简化。

#### 第一步：停止并分析根本原因（停止并分析）

**1.1 完整错误日志分析**：必须完整读取并理解Xcode返回的所有错误信息，而不是只看第一行。

**1.2 上下文代码审查**：必须审查错误代码行的上下文，理解该行代码的原始业务含义是什么。例如，一条带predicate的FetchDescriptor，其含义是"从数据库中筛选数据"。

**1.3 修改声明根因假设**：在行为之前，必须用一句话明确说明您认为的编译错误根因。例如："错误根因是FetchDescriptor初始化器语法在 iOS 17+ 已更改，fetchLimit不再是初始化参数。"

#### 第二步：声明修复平原与"功能不变性" (Declare Intent & Invariants)

**2.1 声明修复策略**：必须用一句话说明你的修复策略。例如："我的策略是采用iOS 17+推荐的属性设置方式来配置fetchLimit，同时保持原有的#Predicate逻辑不变。"

**2.2 声明"功能不变性"**：这是最关键的一步。在修复后必须明确声明，哪些核心功能明白是绝对不能改变的。例如：

> **功能不变性声明**：本次修复必须保证fetchMessages方法的核心功能—— '仅获取指定友谊的消息' ——这一逻辑100%不变。过滤逻辑是该方法的核心，绝不能被移除或同等。

#### 第三步：应用"最小化且正确"的修复（Apply Minimal & Correct Fix）

**3.1 修改最小化修改原则**：只与错误直接相关的、最小化范围的代码。

**3.2 知识库/搜索优先**：如果遇到不熟悉的语法错误（如#Predicate或新版API），严禁猜测或简化。必须优先通过搜索官方文档（Apple Developer）或权威资料来找到正确的现代语法。

**3.3 🚫 严禁以下"破坏性修复"行为**：
- 严禁删除或注释掉核心逻辑代码，特别是where子句、if条件、predicate参数等过滤和判断逻辑。
- 严禁为了满足类型匹配而返回nil、[]、0或false等默认值，除非这确实是符合业务逻辑的。
- 严禁将一个有问题的复杂实现，替换为一个功能不完整的简单实现。

#### 第四步：提供功能验证方法（提供验证方法）

**4.1 声明验证方式**：在修复后，必须提供一个简单的功能验证说明，告诉用户应该如何从功能层面完成测试此修复是否正确，而不是编译通过。

**验证方法示例**：
> 为验证本修复，请在App中进入两个不同的好友聊天页面，并确认每个页面都只显示与当前好友的聊天记录，没有出现其他聊天的消息。同时，在消息数量多的聊天中，验证分页加载功能仍然正常。

---

## 📋 十四、审查报告标准格式

### 输出格式要求

```
## 🔍 系统性架构审查报告

### 🔴 严重问题（必须立即修复）
- [具体问题] - 影响：[架构/性能/安全]
- 涉及文件：[文件列表]

### 🟡 重要问题（影响架构一致性）
- [具体问题] - 影响：[维护性/扩展性]

### 🟢 一般问题（代码质量优化）
- [具体问题] - 影响：[代码风格/性能]

### 📋 系统性修复计划
**批次1（架构基础）**：[文件列表] - [修复内容]
**批次2（服务层）**：[文件列表] - [修复内容]
**批次3（视图层）**：[文件列表] - [修复内容]

### ✅ 合规验证结果
- [ ] 架构基础层合规
- [ ] 全局状态管理器设计合规（新增）
- [ ] 服务层架构合规
- [ ] 视图层实现合规
- [ ] 业务逻辑层合规
- [ ] 集成与性能合规
- [ ] 性能与稳定性合规（新增）
- [ ] iOS版本兼容性合规（新增）
- [ ] 设备兼容性合规（新增）
- [ ] 代码质量深度合规（新增）
- [ ] 安全与隐私合规
- [ ] 测试与质量合规
- [ ] 社区功能合规
- [ ] 整个项目编译无错误无警告
```

---

## ⚡ 十五、应急处理机制

### 触发条件
- 连续3次修复同一问题
- 发现严重架构违规
- 工具调用接近限制

### 处理步骤
1. **立即停止**：停止当前修复操作
2. **全面分析**：重新审查项目架构和依赖关系
3. **系统方案**：制定系统性解决方案
4. **请求指导**：向用户说明情况并请求具体指导

### 质量红线（绝对不可违反）
1. **架构红线**：禁止外键模式、禁止单例模式、禁止直接ModelContext、禁止全局管理器持有SwiftData模型对象
2. **修复红线**：禁止循环修复、禁止单点修复、禁止跳过验证
3. **审查红线**：禁止分批发现、禁止跳过层级、禁止忽略优先级

**⚠️ 最终保障**：当AI无法确保质量时，必须主动向用户说明情况并请求人工介入。 