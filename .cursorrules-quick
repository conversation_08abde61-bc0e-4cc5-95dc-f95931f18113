# Evolve iOS - 快速开发规范

## 🚨 绝对禁止 (违反=崩溃)

1. **外键字段**: `var userId: UUID` ❌ → 使用 `var user: EAUser?` ✅
2. **单例模式**: `static let shared` ❌ → 依赖注入 ✅
3. **直接ModelContext**: `@Environment(\.modelContext)` ❌ → Repository模式 ✅
4. **双端@Relationship**: 两端都用@Relationship ❌ → 单端inverse ✅
5. **print语句**: `print("...")` ❌ → 条件编译 ✅

## ✅ 标准模式

**SwiftData关系** (只在一端用@Relationship):
```swift
@Model class EAUser {
    @Relationship(inverse: \EAHabit.user) var habits: [EAHabit] = []
}
@Model class EAHabit {
    var user: EAUser? // 普通属性
}
```

**Repository模式**:
```swift
@ModelActor actor EARepository { }
@MainActor class ViewModel: ObservableObject {
    @Environment(\.repositoryContainer) private var repos
}
```

**依赖注入**:
```swift
@StateObject private var service = EAService()
.environment(\.service, service)
```

## 🔒 必须遵守

- 所有ViewModel标记`@MainActor`
- 所有Repository使用`@ModelActor`
- 关系赋值顺序: 插入Context → 赋值关系 → 保存
- Context一致性: 共享ModelContainer

**检查清单**: 关系✅ 单例✅ Repository✅ MainActor✅ Print✅ 