# Evolve - 开发步骤文档 v4.0 (架构优化版)

**📅 更新时间：2025-06-05 11:29:37**
**📍 当前项目状态：阶段五已完成，进入架构优化阶段**

## 引言

本文档旨在指导AI辅助开发Evolve iOS应用的整个过程。项目已完成基础功能开发（阶段一至五），当前进入架构优化与AI深度集成阶段。我们将采用"**架构先行，质量优先，AI深度集成，持续优化**"的策略。每个优化阶段和步骤都包含明确的AI执行指令，确保项目架构的稳定性和可扩展性。

**🎯 当前项目成果（已完成）**：
- ✅ 基础项目结构与SwiftData集成
- ✅ 用户认证与会话管理
- ✅ 习惯管理核心功能
- ✅ AI对话基础框架
- ✅ 社区功能完整实现
- ⚠️ **发现架构一致性问题，需要优化**

**当前阶段核心原则：**
*   **架构先行**：优先解决架构一致性问题，确保系统稳定性。
*   **质量优先**：重构现有代码，提升代码质量和可维护性。
*   **AI深度集成**：建立完善的AI数据桥接架构，实现真正的AI驱动功能。
*   **性能优化**：解决数据模型复杂度过高等性能问题。
*   **规范执行**：严格按照优化后的开发规范执行所有开发任务。
*   **持续集成**：确保每次优化后系统仍能正常运行。

**重要说明：iOS项目目录结构与命名**

*   项目根目录为 `Evolve/`。
*   Xcode项目文件为 `Evolve/Evolve.xcodeproj`。
*   主要源代码和资源位于内层 `Evolve/Evolve/` 目录。
*   所有文件路径均相对于内层源代码目录（即 `Evolve/Evolve/`）。
*   所有自定义类型（类、结构体、枚举、协议等）遵循 `EA` 前缀命名规范（详情见《Evolve - 开发规范文档 v2.1》）。

**🚨 开发规范核心约束（必须严格遵循）**

1. **SwiftData开发核心约束（iOS 17.0-18.5+兼容性）**：
   - **完整双向关系强制要求**：所有@Relationship必须有对应的反向关系(inverse)
   - **单端inverse规则**：双向关系只在一端使用@Relationship(inverse:)，另一端使用普通属性
   - **关系赋值顺序**：所有关系属性必须在对象插入ModelContext后再赋值
   - **Apple官方ModelContainer配置**：使用可变参数语法，如 `for: EAUser.self, EAHabit.self`

2. **AI开发约束与质量控制**：
   - **AI角色定位**：AI必须作为"私人教练、引导者、对话伙伴、赋能者"，而非简单的工具或助手
   - **对话优先原则**：所有核心功能优先采用对话式交互设计，AI回复必须具备情感温度和个性化特征
   - **情境感知要求**：AI必须能够识别用户当前的情绪状态，根据对话上下文调整回复风格和内容
   - **成本控制策略**：高优先级场景（用户主动求助、危机干预、重大里程碑）必须调用AI API，中优先级场景（每日洞察生成、习惯创建建议）缓存24小时后调用，低优先级场景（日常提醒、基础统计）优先本地处理
   - **缓存机制**：用户画像缓存7天，AI洞察缓存24小时，行为分析缓存3天，相似问题回复缓存1天
   - **降级策略**：AI服务不可用时必须有高质量备选方案，本地智能引擎能独立处理基础场景

3. **编译错误系统性修复规范**：
   - **禁止循环修复**：采用系统性分析和一次性修复策略，避免"修复A导致B错误"的无限循环
   - **SwiftData关系错误优先**：首先解决关系定义问题，绝不删除inverse参数来"解决"编译错误
   - **批量执行修复**：一次性修复所有相关文件，确保一致性和完整性
   - **根因分析要求**：按优先级排序，首先解决SwiftData关系问题，再解决基础依赖问题

4. **iOS 17.0+兼容性要求**：
   - **@MainActor架构**：所有ViewModel必须标记@MainActor确保UI更新在主线程
   - **Swift Concurrency**：使用async/await和@MainActor确保线程安全
   - **SwiftUI最新特性**：使用iOS 17.0+的新API和改进功能
   - **线程安全保障**：所有SwiftData操作必须在主线程执行，避免并发冲突

---

## 🚨 当前优先任务：架构优化与质量提升阶段

**⚠️ 重要说明**：项目已完成基础功能开发，但在架构审查中发现以下关键问题需要优先解决：

### 🔍 发现的架构问题

1. **架构不一致问题**：
   - 核心功能使用Repository模式
   - 社区功能直接使用Service+ModelContext模式
   - 导致代码风格不一致，维护困难

2. **数据模型复杂度过高**：
   - EAUser模型包含11个@Relationship关系
   - 可能导致性能问题和查询缓慢

3. **单例与依赖注入混用**：
   - SessionManager使用单例模式但需要依赖注入
   - 架构模式不清晰，状态管理困难

4. **AI数据桥接架构不明确**：
   - AI服务如何获取用户数据缺乏明确架构
   - AI与其他模块的数据交互规范缺失

### 📋 架构优化计划

#### 步骤6.1：社区功能Repository重构

**目标**：将社区功能统一到Repository架构模式。

**AI执行指令**:
```
1. 创建社区相关的Repository接口和实现：
   * `Evolve/Core/Services/EACommunityRepository.swift` - 社区数据Repository
   * 实现EACommunityPost、EACommunityComment、EACommunityLike等的CRUD操作
   * 使用@ModelActor确保线程安全

2. 重构EACommunityService：
   * 移除直接的ModelContext操作
   * 通过EACommunityRepository进行数据访问
   * 保持业务逻辑不变

3. 更新相关ViewModel：
   * 修改EACommunityViewModel等
   * 通过Repository而非直接注入ModelContext
   * 确保依赖注入一致性

4. 验证重构效果：
   * 确保社区功能正常运行
   * 检查架构一致性
   * 验证性能没有下降
```

#### 步骤6.2：EAUser模型复杂度优化

**目标**：解决EAUser模型关系过多的问题。

**AI执行指令**:
```
1. 分析EAUser的11个@Relationship关系：
   * 识别核心关系（用户设置、习惯）
   * 识别可分离的关系（社区相关）
   * 评估性能影响

2. 创建社区用户档案模型：
   * `EACommunityProfile.swift` - 专门处理社区相关数据
   * 包含关注、粉丝、帖子、评论等关系
   * 与EAUser建立一对一关系

3. 重构数据访问逻辑：
   * 更新相关Repository和Service
   * 确保数据一致性
   * 保持API接口不变

4. 性能测试：
   * 对比重构前后的查询性能
   * 验证内存使用情况
   * 确保用户体验无影响
```

#### 步骤6.3：SessionManager架构重构

**目标**：消除单例模式，实现纯依赖注入架构。

**AI执行指令**:
```
1. 重构SessionManager设计：
   * 移除单例模式（shared）
   * 改为通过Environment传递的服务
   * 保持会话管理功能不变

2. 更新依赖注入体系：
   * 在AppEntry中配置SessionManager
   * 通过Environment传递给需要的视图
   * 更新所有使用SessionManager的地方

3. 重构认证流程：
   * 确保登录、注册、会话恢复功能正常
   * 验证Keychain集成没有问题
   * 测试多用户切换场景

4. 清理旧代码：
   * 移除所有SessionManager.shared的调用
   * 统一使用依赖注入模式
   * 更新相关文档和注释
```

#### 步骤6.4：AI数据桥接架构建立

**目标**：建立清晰的AI数据访问架构。

**AI执行指令**:
```
1. 创建AI数据桥接服务：
   * `EAAIDataBridge.swift` - AI与数据层的桥接服务
   * 定义AI访问用户数据的标准接口
   * 实现数据格式转换和缓存机制

2. 建立AI分析结果存储：
   * `EAAIInsight.swift` - AI洞察结果模型
   * `EAAIAnalysisRepository.swift` - AI分析数据Repository
   * 支持洞察缓存和历史记录

3. 重构AI服务集成：
   * 更新EAAIService使用数据桥接服务
   * 实现智能缓存策略
   * 确保成本控制机制生效

4. 实现AI驱动功能：
   * 基于用户习惯数据的个性化洞察
   * 智能分享时机检测
   * 情绪状态分析和干预建议
```

### ✅ 验证标准

**架构一致性检查**：
- [ ] 所有数据访问都通过Repository层
- [ ] 不存在直接的Service+ModelContext模式
- [ ] 依赖注入模式统一使用Environment

**性能优化验证**：
- [ ] EAUser查询性能提升≥30%
- [ ] 应用启动时间<1.5秒
- [ ] 内存使用<200MB

**AI集成验证**：
- [ ] AI可以正确获取用户习惯数据
- [ ] AI洞察缓存机制工作正常
- [ ] AI成本控制策略生效

---

## 当前项目状态

**开发进度**：
- ✅ 阶段一（项目初始化与基础架构）已完成 - 2024-11-26
- ✅ 阶段二（用户引导与认证流程）已完成 - 2024-11-28  
- ✅ 阶段三（核心功能开发）已完成 - 2024-12-05
- ✅ 阶段四（高级功能与优化）已完成 - 2024-12-12
- ✅ 阶段五（社区功能开发）已完成 - 2024-12-19
- ✅ **架构优化阶段已完成** - 2025-06-05（社区Repository重构、EAUser模型优化、SessionManager架构统一）
- 🚀 **阶段六（AI深度集成与智能化系统-架构优化集成版）准备开始** - 2025-06-05 14:48:00
- ⚪ 阶段七（智能系统与数据分析）规划中 - 预计2025-06-10
- ⚪ 阶段八（最终优化与发布准备）规划中 - 预计2025-06-15

**架构优化成果**：
- ✅ Repository模式统一执行（社区功能完成重构）
- ✅ EAUser模型复杂度优化（11个关系→5个核心关系）
- ✅ SessionManager支持依赖注入（保持向后兼容）
- ⏳ AI数据桥接架构（即将在阶段六完成）

**项目质量保障**：
- 编译状态：✅ 成功（Exit code: 0）
- 模拟器测试：✅ 正常运行
- 架构一致性：✅ 符合开发规范文档
- 代码质量：✅ 无调试代码残留

---

## 历史开发阶段记录（已完成）

以下为项目已完成的开发阶段，保留作为历史记录和参考。

## 阶段一：项目初始化与基础设置（✅ 已完成）

此阶段为后续开发打下坚实基础。

### 步骤1.1：创建Xcode项目与基础目录结构

**目标**：创建符合规范的iOS项目骨架。

**AI执行指令**:
```
1.  使用Xcode创建新的iOS应用项目：
    *   项目名称：Evolve
    *   界面：SwiftUI
    *   生命周期：SwiftUI App
    *   语言：Swift
    *   最低部署目标：iOS 17.0
    *   **不勾选** "Use Core Data" （我们将手动集成SwiftData）。
2.  在项目成功创建后，确认项目根目录为 `Evolve`，Xcode项目文件为 `Evolve/Evolve.xcodeproj`。
3.  在内层源代码目录 `Evolve/Evolve/` 中，根据《Evolve - 技术架构文档 v2.0》第二节规划，创建以下核心目录结构（如果Xcode已自动生成部分，请校验并补充）：
    *   `AppEntry.swift` (将Xcode自动生成的 `ContentView.swift` 或类似入口文件重命名并移至 `Evolve/Evolve/` 根级，作为新的应用入口)
    *   `Assets.xcassets` (Xcode已自动创建，用于管理资源)
    *   `Preview Content/` (Xcode已自动创建或手动创建，用于存放SwiftUI预览用的模拟数据，如 `PreviewData.swift`)
    *   `Core/`
        *   `Constants/`
        *   `Config/`
        *   `DataModels/`
        *   `Persistence/`
        *   `Services/`
        *   `AIEngine/`
        *   `Notifications/`
        *   `Analytics/`
    *   `Common/`
        *   `Extensions/`
        *   `Tools/`
    *   `Features/`
        *   `Onboarding/`
        *   `Authentication/`
        *   `MainTab/`
        *   `Today/`
        *   `Atlas/`
        *   `AuraSpace/`
        *   `Me/`
    *   `UIComponents/`
4.  确保项目可以成功编译并在模拟器上运行一个空白页面。
5.  **README.md 更新**：记录项目已初始化，基本目录结构已按技术架构文档创建。
```

**验证标准**:
*   项目成功创建，名为 `Evolve`。
*   内层 `Evolve/Evolve/` 目录结构符合《技术架构文档 v2.0》的规划。
*   项目iOS部署目标为 17.0。
*   `AppEntry.swift` 作为应用入口存在。
*   项目能成功编译运行。
*   `README.md` 已更新。

### 步骤1.2：配置色彩系统

**目标**：在项目中定义UI设计规范中规划的颜色。

**AI执行指令**:
```
1.  打开 `Evolve/Evolve/Assets.xcassets`。
2.  在 `Assets.xcassets` 中创建一个名为 "AppColors" 的文件夹（如果偏好，或直接在根级别创建颜色集）。
3.  根据《Evolve - UI设计规范文档 v1.0》第二节"色彩系统"，在 "AppColors" 文件夹（或直接在Assets根级）中创建所有定义的颜色集 (Color Set)。
    *   例如主色调中的 `primaryBackgroundDeep` (#002b20), `primaryBackgroundMedium` (#005A4B) 等。
    *   确保每个颜色集的命名与UI设计文档中的语义化名称一致（如 `primaryBackgroundDeep`）。
4.  为每个颜色集配置好 "Any Appearance" (通用) 的色值。
5.  **重点**：根据UI设计文档，为需要适配深色模式的颜色，在对应的颜色集中添加 "Dark Appearance" 并设置其深色模式下的色值。如果文档中未明确指定深色模式的具体色值，可暂时使用与通用模式相同的颜色，或基于通用颜色进行合理调整（例如略微提高亮度以保证对比度），后续可由叶同学您指定精确值。
6.  **README.md 更新**：记录色彩系统已根据UI设计规范在Assets中配置完成。
```
**验证标准**:
*   `Assets.xcassets` 中包含了UI设计文档定义的所有颜色。
*   颜色命名与UI文档一致，便于代码中引用。
*   关键颜色已考虑并配置了深色模式（Dark Appearance）的色值。
*   `README.md` 已更新。

### 步骤1.3：配置Git版本控制

**目标**：建立Git仓库并配置忽略文件。

**AI执行指令**:
```
1.  在项目根目录 `Evolve/` 初始化Git仓库。
2.  在 `Evolve/` 根目录下创建一个名为 `.gitignore` 的文件。
3.  向 `.gitignore` 文件中添加适用于iOS Swift项目的忽略规则，至少应包括：
    *   Xcode特定的用户设置和状态文件（如 `*.xcuserdatad/`, `xcuserdata/`）
    *   macOS系统文件 (`.DS_Store`)
    *   构建产物目录（如 `Build/`, `DerivedData/`）
    *   Swift Package Manager 构建产物 (`.build/`, `*.xcodeproj`)
    *   CocoaPods依赖目录（如果未来使用，如 `Pods/`）
    *   临时文件和缓存
4.  将当前所有项目文件（包括 `.gitignore` 和已创建的目录结构）添加到Git暂存区，并进行首次提交 (Initial commit)。
5.  **README.md 更新**：记录Git仓库已初始化，`.gitignore` 文件已配置，并完成首次代码提交。
```
**验证标准**:
*   Git仓库在 `Evolve/` 根目录成功初始化。
*   `.gitignore` 文件配置合理，能有效忽略无需版本控制的文件。
*   项目文件已成功提交到本地Git仓库。
*   `README.md` 已更新。

### 步骤1.4：基础App入口与SwiftData初始化

**目标**：配置应用主入口，并初始化SwiftData容器。

**AI执行指令**:
```
1.  打开 `Evolve/Evolve/AppEntry.swift` 文件。
2.  修改其内容，使其成为一个标准的 SwiftUI App 入口，并遵循MVVM架构（如果适用）。
    ```swift
    import SwiftUI
    import SwiftData // 确保导入

    @main
    struct EvolveApp: App { // App 结构体名称应为 EvolveApp
        var body: some Scene {
            WindowGroup {
                // 初始视图，暂时可以是简单的Text或后续的OnboardingView/MainTabView
                Text("Evolve App 即将启航!") 
            }
            // 关键：在此处或合适的地方配置SwiftData的modelContainer
            // 参考《技术架构文档》中对SwiftData的集成描述
            // 注意：按照开发规范文档v2.1的要求，使用可变参数语法配置ModelContainer
            // 例如：.modelContainer(for: EAHabit.self, EACompletion.self, EAUser.self)
            // 此时这些模型可能尚未创建，可以先注释掉，后续创建模型后再补全
        }
    }
    ```
3.  根据《技术架构文档 v2.1》中定义的数据模型，在 `Evolve/Core/DataModels/` 目录下创建以下核心数据模型文件（所有文件名遵循 `EA` 前缀规范）：
    
    **核心习惯模型**：
    *   `EAHabit.swift` - 习惯实体模型，包含id, name, icon, frequency等基本属性
    *   `EACompletion.swift` - 习惯完成记录，包含habitId, completionDate, mood等（遵循EA简化命名规范）
    
    **用户与认证模型**：
    *   `EAUser.swift` - 用户档案，包含id, username, email, isPro, preferredCoachStyle等（遵循EA简化命名规范）
    
    **AI与对话模型**：
    *   `EAAIConversation.swift` - AI对话会话记录
    *   `EAAIMessage.swift` - 单条AI消息记录
    
    **支付与会员模型**：
    *   `EAPaymentRecord.swift` - 支付交易记录，支持StoreKit 2
    
    **内容管理模型**：
    *   `EAContent.swift` - 动力包、心理练习等内容实体
    
    **习惯路径模型**：
    *   `EAHabitPath.swift` - 习惯培养路径
    *   `EAHabitPathStage.swift` - 路径中的具体阶段
    
    **数据分析模型**：
    *   `EAUserAnalytics.swift` - 用户行为分析数据
    
    **系统设置模型**：
    *   `EANotificationSettings.swift` - 通知偏好设置
    
    **🚨 SwiftData实现要点（严格遵循开发规范文档v2.1）**：
    *   每个文件暂时包含空的模型结构：`@Model final class EAModelName { }`
    *   确保所有模型都使用 `@Model` 注解以便SwiftData识别
    *   **关系定义规范**：遵循单端inverse规则，双向关系只在一端使用@Relationship(inverse:)
    *   **Apple官方配置**：使用可变参数语法配置ModelContainer
    *   **iOS 17.0+兼容性**：确保关系定义符合最新SwiftData要求
    *   遵循《技术架构文档 v2.1》中定义的模型关系和属性
4.  在 `AppEntry.swift` 中，正确配置 `.modelContainer`，至少包含一个初始的、空的模型（如果上面创建了空模型文件）。建议使用《技术架构文档 v2.1》中定义的核心模型，包含所有必要的数据模型。如果暂时没有任何模型，可以先用一个临时的空模型占位。
    
    **🚨 Apple官方ModelContainer配置规范（WWDC 2023/2024确认）**：
    ```swift
    // ✅ 正确：多模型使用可变参数语法（Apple官方标准）
    let container = try ModelContainer(for: 
        EAUser.self,
        EAHabit.self,
        EACompletion.self,
        EANotificationSettings.self
    )
    ```
5.  确保应用能够编译并运行，显示 `AppEntry.swift` 中设置的初始视图。
6.  **README.md 更新**：记录 `AppEntry.swift` 已配置为应用入口，SwiftData的 `.modelContainer` 初始化点已设置，并列出已创建的核心数据模型文件。
```
**验证标准**:
*   `AppEntry.swift` 结构正确，应用可以从此入口启动。
*   SwiftData 的 `.modelContainer` 在 `AppEntry.swift` 中有明确的配置位置。
*   核心数据模型文件（EA前缀）已创建骨架。
*   即使是临时视图，应用也能成功编译并运行。
*   `README.md` 已更新。

---

## 阶段二：用户引导（Onboarding）与认证流程开发

此阶段完成新用户首次接触应用的核心路径。

### 步骤2.1：设计与实现用户引导（Onboarding）流程页面

**目标**：创建引导用户了解App核心价值并完成初始设置的界面。

**参考UI原型**：请参考项目根目录下的 `aura_onboarding.html` 文件。

**AI执行指令**:
```
1.  在 `Evolve/Features/Onboarding/` 目录下创建 SwiftUI View 文件 `EAOnboardingView.swift`。
2.  设计 `EAOnboardingView` 的基本布局，可以包含一个 `TabView` 用于多页滑动介绍，或者一个简单的垂直布局来展示信息。
    *   暂时使用占位文本和SF Symbols图标。
    *   页面内容应体现App核心价值，参考《产品需求文档.md》的核心功能模块。
    *   使用iOS 17.0+的最新SwiftUI特性，如 .scrollContentBackground(.hidden) 等。
3.  在 `AppEntry.swift` 中，将 `EAOnboardingView` 设置为首次启动时（或根据特定逻辑判断）显示的视图。
    *   你可能需要在 `AppEntry.swift` 或一个专门的 `EAAppStateManager` (后续创建) 中管理是否首次启动的状态。
4.  确保 `EAOnboardingView` 可以在模拟器上显示。
5.  **README.md 更新**：记录 `EAOnboardingView.swift` 骨架已创建，并可在App启动时显示。
```
**验证标准**:
*   `EAOnboardingView.swift` 创建成功。
*   应用启动后能展示 `EAOnboardingView` 的基本骨架。
*   `README.md` 已更新。

#### 步骤2.1.2：为 `EAOnboardingView` 开发所需UI组件 (按需)

**目标**：根据 `EAOnboardingView` 的具体设计，开发或完善其所需的自定义UI组件。

**AI执行指令**:
```
1.  分析 `EAOnboardingView` 的设计稿（或基于《UI设计规范文档 v1.0》的设想），确定是否需要新的、尚未创建的自定义UI组件。
    *   **优先复用**：检查《UI设计规范文档 v1.0》中定义的 `EAButton`、`EAProgressIndicator`、`EAAnimatedIllustration` 等组件是否适用。
2.  如果需要新组件：
    *   在 `Evolve/UIComponents/` 目录下创建该组件的SwiftUI View文件（遵循 `EA` 前缀命名，如 `EAOnboardingCard.swift`）。
    *   实现该组件的视觉样式和基本交互，严格遵循《UI设计规范文档 v1.0》。
    *   使用iOS 17.0+特性，如新的动画API、符号效果等。
    *   为新组件添加SwiftUI预览 (`#Preview`)。
3.  在 `EAOnboardingView.swift` 中集成并使用这些（新创建或已定义的）UI组件。
4.  **README.md 更新**：记录为 `EAOnboardingView` 开发或集成的自定义UI组件（名称和路径）。
```
**验证标准**:
*   `EAOnboardingView` 中用到的自定义UI组件已实现或从规范中正确引用。
*   新创建的组件符合UI设计规范，并有预览。
*   `README.md` 已更新。

#### 步骤2.1.3：创建 `EAOnboardingViewModel` 并处理基本逻辑

**AI执行指令**:
```
1.  在 `Evolve/Features/Onboarding/` 目录下创建 Swift 文件 `EAOnboardingViewModel.swift`。
2.  定义 `EAOnboardingViewModel` 类，使其遵循 `ObservableObject` 协议。
3.  在 ViewModel 中添加必要的 `@Published` 属性来管理引导流程的状态：
    *   `@Published var currentPageIndex: Int = 0`
    *   `@Published var isCompleted: Bool = false`
    *   `@Published var hasRequestedNotificationPermission: Bool = false`
4.  在 ViewModel 中添加方法来处理用户操作，例如"下一步"、"跳过"或"完成引导"。
5.  在 `EAOnboardingView.swift` 中，使用 `@StateObject` 创建 `EAOnboardingViewModel` 的实例。
6.  将 `EAOnboardingView` 中的用户交互（如按钮点击）连接到 ViewModel 中的相应方法。
7.  根据 ViewModel 的状态更新 `EAOnboardingView` 的显示内容。
8.  **README.md 更新**：记录 `EAOnboardingViewModel.swift` 已创建，并与 `EAOnboardingView` 完成初步绑定。
```
**验证标准**:
*   `EAOnboardingViewModel.swift` 创建成功并遵循 `ObservableObject`。
*   `EAOnboardingView` 与 ViewModel 绑定，可以响应用户操作并更新视图状态。
*   `README.md` 已更新。

#### 步骤2.1.4：实现引导流程与权限请求 (可选，按需)

**目标**：完成引导页的核心逻辑，包括页面切换和必要的权限请求。

**AI执行指令**:
```
1.  在 `EAOnboardingViewModel` 中完善页面切换逻辑，例如通过修改 `@Published` 的当前页索引。
2.  如果引导流程中需要请求权限（如通知权限、相机权限等）：
    *   **按需创建 `EANotificationService`**：如果在之前的步骤中尚未创建，则在 `Evolve/Core/Services/` 目录下创建 `EANotificationService.swift`。
        *   实现请求通知权限的核心方法 (`requestAuthorization`)。参考《技术架构文档 v2.0》对通知服务的描述。
        *   **README.md 更新**：记录 `EANotificationService.swift` 已创建并实现权限请求功能。
    *   在 `EAOnboardingViewModel` 中注入或调用 `EANotificationService` 来执行权限请求。
    *   在 `EAOnboardingView` 的适当位置（例如某个引导页的按钮点击后）触发权限请求。
3.  当引导流程完成时，更新应用状态：
    *   **按需创建用户设置存储**：如果在 `Core/Persistence/` 下的 `EAUserSettings.swift` 尚未创建，则创建它，用于存储"引导是否已完成"的标志。
    *   使用 `@AppStorage` 或 `UserDefaults` 标记引导已完成。
    *   **README.md 更新**：记录用户设置存储中增加了"引导完成"标志。
4.  **行为分析集成（可选）**：如果需要追踪用户行为，在引导流程的关键节点调用 `EABehaviorAnalyticsService` 记录事件（后续创建）。
5.  **测试**：完整测试引导流程，包括页面切换、权限请求（如果实现）以及流程完成后的状态变更。
6.  **README.md 更新**：记录 `EAOnboardingView` 引导流程逻辑完成，包括权限请求（若有）和完成状态处理。
```
**验证标准**:
*   引导页可以按预期进行多页展示和切换。
*   权限请求（如果实现）按预期触发和响应。
*   引导完成后，应用状态正确更新，下次启动不再显示引导页。
*   `README.md` 已更新相关条目。

### 步骤2.2：设计与实现登录、注册、忘记密码页面

**目标**：创建用户账户认证所需的标准界面。

**参考UI原型**：请参考项目根目录下的 `aura_auth_global_ui.html` 文件。

**AI执行指令**:
```
1.  在 `Evolve/Features/Authentication/` 目录下创建 SwiftUI View 文件 `EALoginView.swift` 和 `EARegistrationView.swift`。
2.  为这两个视图设计基本布局，包含输入框（用户名/邮箱、密码）、操作按钮等。
    *   暂时使用占位文本和简单的SF Symbols。
    *   参考《UI设计规范文档 v1.0》中关于表单元素、按钮的规范。
    *   使用iOS 17.0+的新特性，如改进的表单控件和键盘处理。
3.  在 `AppEntry.swift` 中，当引导流程完成后（或如果无需引导流程），将 `EALoginView` (或一个选择登录/注册的入口视图) 设置为显示的视图。
4.  确保 `EALoginView` 和 `EARegistrationView` 可以在模拟器上通过导航（如果需要导航逻辑）显示。
5.  **README.md 更新**：记录 `EALoginView.swift` 和 `EARegistrationView.swift` 骨架已创建。
```
**验证标准**:
*   `EALoginView.swift` 和 `EARegistrationView.swift` 创建成功。
*   应用在引导流程后（或直接）能展示登录/注册视图的骨架。
*   `README.md` 已更新。

#### 步骤2.2.2：为认证页面开发所需UI组件 (按需)

**AI执行指令**:
```
1.  分析 `EALoginView` 和 `EARegistrationView` 的设计，确定所需的自定义UI组件。
    *   **复用提示**：检查《UI设计规范文档 v1.0》中定义的 `EAButton`、`EATextField`、`EASecureField`、`EAValidationMessage` 等。这些通用组件应优先实现和复用。
    *   如果这些组件尚未在 `Evolve/UIComponents/` 目录下创建，请现在创建它们。
        *   严格按照《UI设计规范文档》实现其样式、状态和交互。
        *   使用iOS 17.0+的新特性，如改进的文本字段样式和验证反馈。
        *   为它们添加SwiftUI预览。
        *   **README.md 更新**：记录 `EAButton.swift`、`EATextField.swift` 等已创建。
2.  在 `EALoginView.swift` 和 `EARegistrationView.swift` 中集成并使用这些UI组件。
3.  **README.md 更新**：记录 `EALoginView` 和 `EARegistrationView` 已集成必要的UI组件。
```
**验证标准**:
*   认证页面中用到的 `EAButton`, `EATextField` 等组件已实现并正确集成。
*   组件符合UI设计规范，并有预览。
*   `README.md` 已更新。

#### 步骤2.2.3：创建 `EAAuthViewModel` 及用户数据模型

**AI执行指令**:
```
1.  在 `Evolve/Features/Authentication/` 目录下创建 Swift 文件 `EAAuthViewModel.swift`。
2.  定义 `EAAuthViewModel` 类，使其遵循 `ObservableObject`。
3.  在 ViewModel 中添加 `@Published` 属性来管理用户输入（如邮箱、密码）、认证状态（加载中、成功、失败）、错误消息等。
4.  **完善用户数据模型**：
    *   在 `Evolve/Core/DataModels/` 目录下完善 `EAUser.swift`，确保包含《技术架构文档 v2.1》中定义的所有属性：
        *   基本信息：id, username, email, creationDate
        *   会员信息：isPro, proExpirationDate
        *   AI设置：preferredCoachStyle
        *   确保其为 `@Model final class EAUser` 结构，以便与SwiftData集成。
        *   **🚨 SwiftData关系定义**：遵循单端inverse规则，正确定义与其他模型的关系
        *   **README.md 更新**：记录 `EAUser.swift` 数据模型已完善。
5.  在 `EAAuthViewModel` 中定义登录和注册的方法骨架（暂时为空或只打印日志）。
    *   **🚨 @MainActor要求**：确保 `EAAuthViewModel` 标记为 `@MainActor`，遵循iOS 17.0+架构规范
   **🚨 SwiftData关系操作**：用户数据保存必须遵循关系赋值顺序要求
6.  将 `EALoginView` 和 `EARegistrationView` 中的用户输入绑定到 `EAAuthViewModel` 的 `@Published` 属性，并将按钮操作连接到ViewModel的方法。
7.  **支付系统准备（可选）**：为后续Pro会员功能，预留与 `EAPaymentService` 的集成接口。
8.  **README.md 更新**：记录 `EAAuthViewModel.swift` 已创建，并与认证视图完成数据绑定；`EAUser.swift` 已完善。
```
**验证标准**:
*   `EAAuthViewModel.swift` 创建成功并能管理用户输入和认证状态。
*   `EAUser.swift` 已按《技术架构文档 v2.1》规范完善。
*   认证视图与ViewModel绑定，用户输入能反映到ViewModel，按钮操作能调用ViewModel方法。
*   `README.md` 已更新。

#### 步骤2.2.4：实现认证逻辑与网络服务 (按需)

**目标**：完成实际的用户注册和登录功能，包括与后端API的交互。

**AI执行指令**:
```
1.  **按需创建网络服务**：
    *   如果项目中尚未创建通用的网络服务 `EANetworkService.swift`，请现在在 `Evolve/Core/Services/` 目录下创建它。
        *   实现其基础 `fetch` 方法，使用 `URLSession` 和 `async/await`，支持GET/POST等，并包含基本的错误处理。
        *   确保支持iOS 17.0+的新网络特性。
        *   **README.md 更新**：记录 `EANetworkService.swift` 已创建并实现基础网络请求功能。
    *   基于 `EANetworkService`，在 `Evolve/Core/Services/` 目录下创建专门用于认证的 `EAAuthService.swift`。
        *   定义 `EAAuthServiceProtocol` 及其实现类。
        *   实现 `login(email:password:)` 和 `register(email:password:name:)` 等方法。
        *   处理API的请求参数和响应数据的编解码 (Codable)。
        *   **README.md 更新**：记录 `EAAuthService.swift` 已创建，并实现登录注册API的调用。
2.  在 `EAAuthViewModel` 中注入 `EAAuthServiceProtocol` 的依赖。
3.  在 `EAAuthViewModel` 的登录和注册方法中，调用 `EAAuthService` 的相应方法执行实际的认证操作。
    *   处理异步操作的状态（如 `isLoading`）。
    *   处理成功响应：保存用户信息到SwiftData的 `EAUser`，更新应用内登录状态。
    *   **🚨 SwiftData操作规范**：确保用户数据保存遵循关系赋值顺序要求，所有关系属性必须在对象插入ModelContext后再赋值
    *   处理失败响应：将错误信息展示给用户。
4.  **会话管理**：
    *   **按需创建 `EASessionManager`**：在 `Core/Services/` 下创建 `EASessionManager.swift`，用于管理用户会话状态。
    *   **🚨 @MainActor要求**：确保 `EASessionManager` 标记为 `@MainActor`
    *   使用Keychain存储敏感信息（如Token），使用 `@AppStorage` 存储简单会话标志。
    *   **README.md 更新**：记录会话管理机制已建立。
5.  在 `AppEntry.swift` 中，根据持久化的登录状态，决定是显示认证页面还是主应用界面。
6.  **行为分析集成（可选）**：记录用户登录、注册等关键事件到 `EAUserAnalytics`。
7.  **测试**：使用模拟的后端响应（或真实后端，如果可用）测试完整的登录和注册流程，包括错误处理。
8.  **README.md 更新**：记录用户认证逻辑（登录、注册）通过网络服务已实现，会话管理已建立。
```
**验证标准**:
*   用户可以通过界面完成注册和登录操作。
*   认证请求能正确发送到（模拟或真实的）后端。
*   能正确处理认证成功和失败的场景，并更新UI。
*   用户登录状态能被持久化，并在下次启动时恢复。
*   `README.md` 已更新相关条目。

---

## 阶段三：核心功能模块开发 - 习惯跟踪与内容呈现

此阶段构建App的核心用户价值。

### 步骤3.1：设计与实现核心功能 - 主Tab导航 (Main Tab Navigation)

**目标**：创建用户进行习惯打卡、查看进度的核心界面。

**参考UI原型**：请参考项目根目录下的 `aura_coach_home.html` 文件。

**AI执行指令**:
```
1.  在 `Evolve/Features/MainTab/` 目录下创建 SwiftUI View 文件 `EAMainTabView.swift`。
2.  在 `EAMainTabView` 中，使用 SwiftUI 的 `TabView` 实现底部导航栏。
3.  根据《产品需求文档.md》规划，创建四个主要的Tab：
    *   今日 (Today) - 日常习惯打卡和进度查看
    *   图鉴 (Atlas) - 习惯管理和统计分析
    *   灵境 (AuraSpace) - AI交互和智能建议
    *   我的 (Me) - 用户中心和设置
4.  为每个Tab设置图标（使用SF Symbols）和文字标签，遵循《UI设计规范文档 v1.0》中关于底部Tab导航栏的规范。
5.  **为每个Tab关联一个临时的占位符视图**。创建一个通用的 `EAPlaceholderView.swift` 在 `Evolve/UIComponents/` 目录下，该视图可以接受一个标题参数，用于显示当前是哪个Tab。
    *   **README.md 更新**：记录 `EAPlaceholderView.swift` 已创建。
6.  在 `AppEntry.swift` 中（或应用状态管理器），当用户认证成功后，将 `EAMainTabView` 设置为根视图。
7.  确保应用在登录后能显示 `EAMainTabView`，并且可以点击切换各个Tab（显示对应的占位符视图）。
8.  **README.md 更新**：记录 `EAMainTabView.swift` 骨架已创建，包含四个Tab，并能进行切换。
```
**验证标准**:
*   `EAMainTabView.swift` 创建成功。
*   应用登录后显示主Tab导航，包含四个Tab。
*   可以点击切换Tab，并显示对应的占位符内容。
*   Tab图标和文字符合UI设计规范。
*   `README.md` 已更新。

### 步骤3.2：设计与实现今日页面 (Today View)

**目标**：创建用户日常打卡和查看今日习惯进度的界面。

**AI执行指令**:
```
1.  在 `Evolve/Features/Today/` 目录下创建 `EATodayView.swift` 和 `EATodayViewModel.swift`。
2.  `EATodayView`:
    *   替换 `EAMainTabView` 中"今日"Tab的占位符视图为 `EATodayView`。
    *   设计布局：顶部显示日期和能量状态，中间显示今日习惯列表，每个习惯支持打卡操作。
    *   **按需创建/复用UI组件**：
        *   `EAHabitCard.swift` - 展示单个习惯的打卡卡片，支持不同状态显示
        *   `EAEnergyMeter.swift` - 显示用户当日能量状态，使用进度环形设计
        *   `EACompletionButton.swift` - 习惯完成打卡按钮，支持触觉反馈
        *   `EAStreakIndicator.swift` - 连续天数指示器，突出成就感
        *   `EADailyInsight.swift` - 每日AI洞察卡片，基于行为分析
    *   **iOS 17.0+特性应用**：
        *   使用新的交互式动画，如打卡成功的庆祝动效
        *   集成Widget刷新机制，同步桌面小组件状态
        *   应用改进的列表性能优化
3.  `EATodayViewModel` (`ObservableObject`):
    *   **🚨 @MainActor要求**：必须标记为 `@MainActor`，遵循iOS 17.0+架构规范
    *   注入依赖服务：`ModelContext`, `EABehaviorAnalyticsService`, `EASmartNotificationEngine`, `EAAIManager`
    *   核心状态管理：
        *   `@Published var todayHabits: [EAHabit] = []`
        *   `@Published var completedToday: Set<UUID> = []`
        *   `@Published var dailyProgress: Double = 0.0`
        *   `@Published var energyLevel: Int = 0`
        *   `@Published var todayInsight: String?`
    *   核心功能方法：
        *   `loadTodayHabits()`: 加载今日需要完成的习惯，应用智能排序
        *   `completeHabit(habit: EAHabit)`: 标记习惯为已完成，创建 `EACompletion`（遵循EA简化命名规范）
        *   `refreshDailyInsight()`: 从AI获取今日个性化洞察
        *   **🚨 AI成本控制**：优先使用本地分析，重要时机调用AI API
        *   `updateEnergyLevel()`: 基于完成情况计算能量值
    *   **智能系统集成**：
        *   行为分析：记录打卡时间、完成顺序等行为数据
        *   智能推送：基于完成情况调整后续提醒
        *   AI交互：生成个性化的完成反馈和鼓励
        *   **🚨 缓存策略**：AI洞察缓存24小时，行为分析缓存3天
4.  **数据同步与缓存**：
    *   实现本地缓存策略，确保离线可用性
    *   支持数据增量同步，优化性能
    *   集成Widget数据共享机制
5.  **测试重点**：
    *   验证今日习惯列表正确加载和显示
    *   确认打卡操作能正确保存到SwiftData
    *   测试能量值计算和UI更新的准确性
    *   验证AI洞察的获取和显示
6.  **README.md 更新**：记录 `EATodayView` 和 `EATodayViewModel` 已创建，支持完整的习惯打卡功能、智能洞察和行为分析集成。
```

### 步骤3.3：设计与实现习惯创建页面 (Habit Creation)

**目标**：创建添加和编辑习惯的界面，支持AI智能引导。

**参考UI原型**：请参考项目根目录下的 `aura_habit_details.html` 文件。

**AI执行指令**:
```
1.  在 `Evolve/Features/Atlas/` 创建 `EAHabitCreationView.swift` 和 `EAHabitCreationViewModel.swift`。
    *   **🚨 @MainActor要求**：确保 `EAHabitCreationViewModel` 标记为 `@MainActor`，遵循iOS 17.0+架构规范
2.  `EAHabitCreationView`:
    *   设计分步式表单界面，包含基本信息、目标设定、提醒配置等步骤。
    *   **核心UI组件创建/复用**：
        *   `EATextField` - 习惯名称输入，支持实时AI建议
        *   `EAButton` - 主要操作按钮，遵循设计规范
        *   `EAIconPicker` - 习惯图标选择器，提供分类图标库
        *   `EAFrequencySelector` - 频次选择组件，支持每日/每周/自定义
        *   `EAReminderTimePicker` - 提醒时间选择，集成智能推荐时段
        *   `EADifficultySlider` - 难度评估滑块，影响AI建议
        *   `EAGoalInput` - 目标设定输入，支持数量/时长等类型
    *   **AI引导功能集成**：
        *   AI建议引导按钮："让AI帮我设计这个习惯"
        *   实时智能提示：根据输入内容提供个性化建议
        *   习惯模板推荐：基于用户偏好推荐成功案例
        *   障碍预警：AI识别潜在困难并提供应对策略
    *   **iOS 17.0+特性应用**：
        *   使用新的表单控件，提升输入体验
        *   集成改进的键盘处理和自动填充
        *   应用新的导航API，优化页面流转
3.  `EAHabitCreationViewModel` (`ObservableObject`):
    *   **依赖注入**：`ModelContext`, `EAAIManager`, `EANotificationService`, `EAHabitPathService`, `EAContentService`
    *   **状态管理**：
        *   `@Published var habitName: String = ""`
        *   `@Published var selectedIcon: String = ""`
        *   `@Published var frequency: EAHabitFrequency = .daily`
        *   `@Published var reminderTime: Date?`
        *   `@Published var difficulty: Int = 1`
        *   `@Published var goalValue: Int = 1`
        *   `@Published var isAIGuided: Bool = false`
        *   `@Published var aiSuggestions: [EAHabitSuggestion] = []`
        *   `@Published var currentStep: Int = 0`
        *   `var habitToEdit: EAHabit?` - 用于编辑模式
    *   **核心功能方法**：
        *   `saveHabit()`: 保存习惯到SwiftData，集成路径规划和通知设置
        *   `generateAISuggestions()`: 调用AI生成个性化习惯建议
        *   `applyAISuggestion(suggestion: EAHabitSuggestion)`: 应用AI建议到表单
        *   `validateCurrentStep()`: 验证当前步骤输入的完整性
        *   `setupNotifications()`: 配置智能提醒，集成最佳时间推荐
        *   `createHabitPath()`: 为复杂习惯生成分阶段路径
    *   **🚨 SwiftData操作规范**：确保习惯保存遵循关系赋值顺序要求
    *   **🚨 AI集成**：调用AI生成个性化习惯建议（中优先级场景，缓存24小时）
4.  **智能系统深度集成**：
    *   **AI引导流程**：
        *   根据用户目标智能推荐习惯类型和参数
        *   分析用户历史数据，提供个性化建议
        *   生成习惯成功案例和激励内容
    *   **通知智能化**：
        *   基于行为分析确定最佳提醒时间
        *   个性化提醒文案，匹配用户偏好的教练风格
        *   动态调整提醒频率和内容
    *   **内容关联**：
        *   自动关联相关的动力包和心理练习
        *   推荐成功故事和专业指导内容
        *   为Pro用户提供高级内容推荐
    *   **路径规划**：
        *   为复杂习惯自动生成分阶段培养路径
        *   设定里程碑和成就解锁机制
        *   提供习惯升级和演进建议
5.  **数据验证与错误处理**：
    *   实现表单验证逻辑，确保数据完整性
    *   提供友好的错误提示和修正建议
    *   支持草稿保存和恢复功能
6.  **测试重点**：
    *   验证表单各步骤的输入验证和数据绑定
    *   测试AI建议生成和应用的完整流程
    *   确认习惯保存后的通知设置正确性
    *   验证编辑模式的数据加载和更新
7.  **README.md 更新**：记录 `EAHabitCreationView` 和 `EAHabitCreationViewModel` 已创建，支持完整的AI引导、智能通知设置、内容关联和路径规划功能。
```

### 步骤3.4：设计与实现内容展示页面 (Content Display)

**目标**：创建展示动力包、心理学练习等内容的界面。

**参考UI原型**：请参考项目根目录下的 `aura_mind_treasury.html` 文件。

**AI执行指令**:
```
1.  在 `Evolve/Features/Me/` 目录下创建 SwiftUI View 文件 `EAContentLibraryView.swift`。
2.  设计 `EAContentLibraryView` 的基本布局：
    *   顶部分类筛选（动力包、心理学练习、成功故事等）
    *   内容列表，支持Pro内容的差异化展示
    *   **复用UI组件**：`EAContentCard.swift`、`EAProBadge.swift`等
3.  在 `Evolve/Features/Me/` 目录下创建 `EAContentLibraryViewModel.swift`：
    *   注入 `EAContentService`（后续创建）和 `EAPaymentService`（用于Pro验证）
    *   `@Published var contents: [EAContent] = []`
    *   `@Published var selectedCategory: String = "all"`
    *   方法 `loadContents()`: 根据用户Pro状态加载相应内容
4.  **内容服务创建**：在 `Evolve/Core/Services/` 下创建 `EAContentService.swift`，实现内容的CRUD操作和推荐算法。
5.  **Pro会员验证**：集成支付系统验证，区分免费和Pro内容。
6.  **README.md 更新**：记录内容展示系统已创建，支持Pro会员差异化。
```

---

## 阶段四："我的"模块开发 - 用户中心与Pro会员系统

此阶段完善用户个性化管理和高级功能。

### 步骤4.1：设计与实现"我的"主页

**目标**：创建用户查看个人资料、成就、进行应用设置等的界面。

**参考UI原型**：请参考项目根目录下的 `aura_me_subpages.html` 文件。

**AI执行指令**:
```
1.  在 `Evolve/Features/Me/` 目录下完善 `EAMeView.swift`，使其成为完整的用户中心页面。
2.  设计 `EAMeView` 的布局：
    *   顶部用户信息区域（头像、昵称、Pro状态）
    *   统计概览（习惯数量、完成率、连续天数等）
    *   功能入口（设置、数据导出、Pro升级等）
    *   **复用UI组件**：`EAUserCard.swift`、`EAStatsCard.swift`、`EAMenuRow.swift`等
3.  创建 `EAMeViewModel.swift`：
    *   注入 `ModelContext`、`EAUserAnalytics`、`EAPaymentService`等服务
    *   `@Published var user: EAUser?`（遵循EA简化命名规范）
    *   `@Published var userStats: EAUserStats`（后续定义）
    *   方法 `loadUserData()`: 加载用户基本信息和统计数据
    *   方法 `exportUserData()`: 数据导出功能
    *   **🚨 支付安全**：使用StoreKit 2验证机制，安全存储交易信息
    *   **🚨 SwiftData操作规范**：确保用户数据操作遵循关系完整性要求
4.  **Pro会员系统集成**：
    *   创建 `EAProSubscriptionView.swift` 用于Pro会员购买
    *   集成StoreKit 2进行应用内购买
    *   **README.md 更新**：记录Pro会员购买流程已实现
5.  **数据导出功能**：
    *   创建 `EADataExportService.swift` 实现GDPR合规的数据导出
    *   支持导出用户习惯数据、AI对话记录等
6.  **README.md 更新**：记录完整的用户中心页面已实现，包含Pro会员和数据导出功能。
```

### 步骤4.2：实现支付系统

**目标**：集成StoreKit 2实现Pro会员购买功能。

**AI执行指令**:
```
1.  在 `Evolve/Core/Services/` 目录下创建 `EAPaymentService.swift`：
    *   遵循《开发规范文档 v2.0》中的支付安全规范
    *   使用StoreKit 2 API实现产品加载、购买、恢复等功能
    *   支持订阅验证和收据处理
    *   实现购买记录的本地存储（`EAPaymentRecord`）
2.  在 `Evolve/Features/Me/` 下创建 `EAProSubscriptionView.swift`：
    *   展示不同的订阅选项（月度、季度、年度、终身）
    *   使用《UI设计规范文档 v1.0》中定义的支付相关组件
    *   集成原生StoreKit界面
3.  **安全措施**：
    *   实现服务器端收据验证（如果有后端）
    *   本地购买状态验证
    *   防止重复购买的逻辑
4.  **测试**：
    *   沙盒环境测试购买流程
    *   验证Pro功能的正确解锁
5.  **README.md 更新**：记录StoreKit 2支付系统已实现，支持多种订阅选项。
```

---

## 阶段五：社区功能开发（能量共振社区）

该阶段将实现Evolve应用的社区功能，让用户可以分享习惯坚持成果、互相鼓励支持。通过"能量共振"的核心设计理念，打造有温度的习惯养成社区。

### 开发策略

**UI驱动的增量开发模式**：采用"导航入口→数据基础→页面框架→UI组件→服务集成→功能完善→AI集成→系统优化"的渐进式开发方式，每个步骤都可独立编译测试，确保与项目现有架构完全一致。

**核心设计原则**：
- **UI优先驱动**：先建立导航入口，再逐步完善内容，确保开发过程可视化
- **小步快跑**：每个步骤都能独立编译和测试，问题能够及时发现和解决
- **架构一致**：严格遵循项目MVVM和依赖注入模式，不使用单例模式
- **渐进式集成**：从简单到复杂，逐步增加功能，降低复杂性风险
- **规范遵循**：严格遵循《开发规范文档 v2.1》的所有要求，特别是SwiftData开发规范

### 步骤5.1：主Tab导航社区入口建立

**目标**：优先建立社区功能的入口，确保导航结构完整可测试。

**AI执行指令**:
```
1. 修改 `Evolve/Features/MainTab/EAMainTabView.swift`：
   * 检查现有Tab结构，确认是否已有4个Tab（Today、Atlas、AuraSpace、Me）
   * 在现有Tab基础上添加第5个Tab "社区"，使用中文标签"社区"
   * 如果使用枚举管理Tab，添加 .community case
   * 暂时使用简单占位视图：Text("社区功能开发中...").padding()
   * 使用SF Symbols图标："person.3.fill"作为社区Tab图标
   * 确保Tab切换逻辑完整，无编译错误

2. 遵循项目架构规范：
   * 保持与现有4个Tab相同的命名和结构模式
   * 确保符合项目的UI设计规范
   * 使用项目统一的颜色和字体设置

3. 立即验证测试：
   * 编译项目，确保无编译错误
   * 启动模拟器，检查5个Tab是否正常显示
   * 逐个点击每个Tab，确保切换正常
   * 特别测试社区Tab，确认显示占位内容
   * 检查内存使用情况，确保无异常

4. **README.md 更新**：在功能模块表格中添加Community模块条目，状态标记为开发中，记录主Tab导航已扩展为5个Tab。
```

**验证标准**:
* 应用编译成功，无任何编译错误或警告
* 主Tab导航栏显示5个Tab，布局美观
* 每个Tab切换响应灵敏，无卡顿
* 社区Tab点击显示占位内容，文字居中显示
* 模拟器运行稳定，内存使用正常
* README.md已更新，Community模块已记录

### 步骤5.2：社区数据模型创建与集成

**目标**：基于技术架构文档，创建社区功能的核心数据模型，确保SwiftData集成符合项目规范。

**AI执行指令**:
```
1. 在 `Evolve/Core/DataModels/` 目录下创建5个社区数据模型：
   * `EACommunityPost.swift` - 社区帖子模型
   * `EACommunityComment.swift` - 评论模型  
   * `EACommunityLike.swift` - 点赞模型
   * `EACommunityFollow.swift` - 用户关注模型
   * `EACommunityReport.swift` - 举报模型

2. 严格遵循SwiftData开发规范（《开发规范文档 v2.1》第二章第4节）：
   * **完整双向关系强制要求**：所有@Relationship必须有对应的反向关系(inverse)
   * **单端inverse规则**：双向关系只在一端使用@Relationship(inverse:)，另一端使用普通属性
   * **关系赋值顺序**：所有关系属性必须在对象插入ModelContext后再赋值
   * **Apple官方ModelContainer配置**：使用可变参数语法
   * **@MainActor兼容**：确保与项目架构完全一致

3. 数据模型设计要点：
   * EACommunityPost：包含用户ID、内容文本、创建时间、关联习惯ID等
   * EACommunityComment：关联帖子和用户，支持评论内容和时间
   * EACommunityLike：用户对帖子的点赞关系，防止重复点赞
   * EACommunityFollow：用户关注关系，支持互相关注
   * EACommunityReport：举报内容管理，支持不同举报类型
   * 所有模型都与现有EAUser模型建立正确关系

4. 更新数据库配置：
   * 在 `Evolve/Core/Persistence/EADatabaseManager.swift` 中添加新模型
   * 更新modelContainer配置，使用可变参数语法：for: EAUser.self, EAHabit.self, EACompletion.self, EACommunityPost.self, EACommunityComment.swift, EACommunityLike.self, EACommunityFollow.self, EACommunityReport.self
   * 确保Schema版本控制和迁移策略正确

5. 立即验证测试：
   * 编译项目，确保无编译错误
   * 启动模拟器，验证数据库创建成功
   * 检查ModelContainer配置是否正确
   * 验证SwiftData关系定义无冲突

6. **README.md 更新**：在数据模型表格中添加5个社区模型记录，状态标记为✅已创建，记录创建日期。
```

**验证标准**:
* 5个社区数据模型创建完成，文件结构清晰
* SwiftData关系定义正确，严格遵循单端inverse规则
* 应用编译成功，无编译错误和运行时崩溃
* 数据库启动正常，ModelContainer配置成功
* EADatabaseManager正确包含所有新模型
* README.md已更新，模型记录完整

### 步骤5.3：社区主页面框架搭建

**目标**：创建社区主页面的基础MVVM框架，建立完整的页面架构，确保与数据模型正确集成。

**AI执行指令**:
```
1. 创建社区页面目录和基础文件：
   * 创建 `Evolve/Features/Community/` 目录
   * 创建 `EACommunityView.swift` - 社区主页面
   * 创建 `EACommunityViewModel.swift` - 社区页面ViewModel

2. EACommunityViewModel实现（严格遵循项目架构规范）：
   * **🚨 @MainActor要求**：必须标记@MainActor，遵循项目MVVM架构规范
   * **依赖注入模式**：不使用单例，通过环境依赖注入ModelContext
   * 定义核心状态属性：
     - @Published var posts: [EACommunityPost] = []
     - @Published var isLoading: Bool = false
     - @Published var errorMessage: String?
     - @Published var hasNextPage: Bool = true
   * 实现基础方法框架：
     - init(modelContext: ModelContext) - 依赖注入构造器
     - loadPosts() - 从SwiftData加载帖子（使用真实查询）
     - refreshPosts() - 刷新帖子列表
     - createPost(content: String) - 创建新帖子（预留）
   * **SwiftData集成**：使用ModelContext进行数据查询，遵循关系赋值顺序要求

3. EACommunityView实现：
   * 遵循项目UI设计规范，使用生态隐喻设计
   * 建立基本页面结构：
     - NavigationView包装
     - 列表展示帖子（使用ForEach）
     - 下拉刷新功能
     - 浮动添加按钮（预留）
   * 使用@StateObject var viewModel: EACommunityViewModel初始化ViewModel
   * 适配深色模式和无障碍访问
   * 暂时使用简单的Text显示帖子内容

4. 更新主Tab导航：
   * 在EAMainTabView中将占位视图替换为EACommunityView()
   * 通过@Environment(\.modelContext)传递ModelContext给EACommunityView
   * 确保ViewModel正确初始化

5. 立即验证测试：
   * 编译项目，确保无编译错误
   * 启动模拟器，验证社区页面正常显示
   * 检查ViewModel状态管理是否正常
   * 测试页面导航和基础交互
   * 验证SwiftData查询功能（即使暂无数据）

6. **README.md 更新**：在功能模块表格的Community条目中更新状态，记录主页面框架已完成。
```

**验证标准**:
* 社区主页面显示正常，布局结构清晰
* EACommunityViewModel严格遵循@MainActor和依赖注入规范
* 页面与ViewModel正确绑定，状态管理正常
* Tab导航集成成功，从其他Tab切换到社区Tab无问题
* SwiftData集成就绪，数据查询代码结构正确
* 应用编译运行稳定，无崩溃和内存问题
* README.md已更新，开发进度记录完整

### 步骤5.4：按需社区UI组件开发

**目标**：根据页面实际需求，优先复用已有组件，渐进式开发必要的UI组件，避免过度设计和重复开发。

**AI执行指令**:
```
1. **优先复用现有组件检查**：
   * **🚨 严格禁止重复创建**：在创建任何新组件前，必须先检查项目中是否已有可复用的组件
   * **已有可复用组件清单**：
     - ✅ `EAAvatarView.swift` - 用户头像显示（支持系统头像和自定义头像）
     - ✅ `EAButton.swift` - 通用按钮组件（支持主要、次要、社交等多种样式）
     - ✅ `EATextField.swift` - 高级输入框组件（支持验证、状态显示等）
     - ✅ `EAHabitCard.swift` - 卡片设计模式参考（渐变背景、阴影、交互动效）
     - ✅ `EAPlaceholderView.swift` - 占位和空状态显示

2. **EACommunityPostCard组件设计要求**：
   * **🔑 复用EAHabitCard设计模式**：严格参考EAHabitCard的卡片设计风格
     - 复用相同的圆角半径(24)、阴影效果、渐变背景系统
     - 复用卡片的缩放动效和点击交互模式
     - 保持与项目其他卡片组件的设计一致性
   * **🔑 复用EAAvatarView显示用户头像**：
     - 直接使用`EAAvatarView(avatarData: post.author.avatarData, size: 32)`
     - 不要重新创建头像显示逻辑
   * 显示内容：帖子内容、发布时间、点赞数等
   * 严格遵循项目UI设计规范，体现"生态隐喻"设计理念
   * 支持深色模式和无障碍访问

3. **EALikeButton组件设计要求**：
   * **🔑 基于EAButton进行定制**：
     - 使用`EAButton`的`.icon`尺寸和基础架构
     - 复用EAButton的触觉反馈和动效系统
     - 扩展支持点赞状态切换（已点赞/未点赞）
   * 使用SF Symbols心形图标，点赞后显示填充样式
   * 包含点赞数量显示和适当的点击动效

4. **社区评论输入组件（如需要）**：
   * **🔑 直接复用EATextField**：使用`EATextField`配置为多行文本输入模式
   * 不要重新创建文本输入逻辑

5. **立即集成到EACommunityView**：
   * 在帖子列表中使用EACommunityPostCard显示每个帖子
   * 在帖子卡片中集成EALikeButton
   * 确保组件与EACommunityPost数据模型完全匹配
   * 验证组件在实际页面中的显示效果和交互

6. **组件质量保证**：
   * 每个新组件都包含完整的SwiftUI预览代码
   * 使用模拟数据展示不同状态（有数据/无数据/加载中）
   * 确保深色模式适配正确
   * 验证无障碍访问支持（VoiceOver标签）

7. **立即验证测试**：
   * 编译项目，确保组件创建无编译错误
   * 在SwiftUI预览中测试组件显示效果
   * 在实际页面中验证组件集成正常
   * 测试组件的交互功能和状态变化
   * **特别验证**：确认EAAvatarView和EAButton的正确复用

8. **README.md 更新**：在UI组件表格中添加EACommunityPostCard和EALikeButton两个组件记录，并标注复用了EAAvatarView、EAButton等已有组件。
```

**验证标准**:
* 2个核心UI组件创建完成，功能明确
* 组件设计完全符合项目UI规范和生态隐喻理念  
* 组件与EACommunityView无缝集成，显示效果良好
* SwiftUI预览正常工作，支持多种状态展示
* 组件交互功能正常，用户体验良好
* 深色模式和无障碍访问支持完整
* README.md已更新，组件记录准确

### 步骤5.5：数据层与UI层集成

**目标**：将真实数据模型与UI层连接，实现基本的数据展示和交互功能。

**AI执行指令**:
```
1. 更新EACommunityViewModel，集成真实SwiftData操作：
   * 在loadPosts()方法中实现真实的SwiftData查询：
     - 使用FetchDescriptor<EACommunityPost>
     - 按创建时间倒序排列
     - 实现分页加载（每页20条）
     - 包含关联的用户信息查询
   * 在refreshPosts()方法中实现列表刷新逻辑
   * 添加错误处理和加载状态管理
   * **🚨 SwiftData操作规范**：确保所有数据操作遵循关系赋值顺序要求

2. 实现基础数据操作方法：
   * createPost(content: String, habitId: UUID?) - 创建新帖子
   * toggleLike(postId: UUID) - 点赞/取消点赞
   * loadUserPosts(userId: UUID) - 加载用户发布的帖子
   * **依赖注入原则**：通过ModelContext参数传递，不使用单例模式

3. 创建测试数据支持：
   * 在EACommunityViewModel中添加createSampleData()方法
   * 创建几条示例帖子数据用于开发测试
   * 确保示例数据与真实数据模型结构完全一致
   * 支持一键清理测试数据的方法

4. 更新EACommunityView数据绑定：
   * 确保帖子列表正确显示ViewModel中的posts数组
   * 实现下拉刷新功能，调用refreshPosts()
   * 添加加载状态指示器（isLoading状态绑定）
   * 实现空状态页面（当posts为空时显示）

5. 完善用户资料管理与测试数据：
   * **用户信息集成**：
     - 在EACommunityPostCard中正确显示帖子作者信息
     - 使用关联的EAUser数据显示用户名和头像
     - 确保用户数据的正确加载和显示
   * **测试用户创建**：
     - 在createSampleData()中创建完整的测试用户资料（包含用户名、邮箱、头像、创建时间等）
     - 创建多个不同类型的测试用户（新用户、活跃用户、Pro用户等）
     - 确保测试用户与EAUser数据模型完全匹配
   * **用户状态模拟**：
     - 模拟用户登录状态，确保社区功能有正确的用户上下文
     - 验证用户权限控制（如只有作者能删除自己的帖子）
     - 测试用户资料的本地持久化和读取
   * **数据关联验证**：
     - 确保帖子与用户的关联关系正确
     - 验证点赞记录与用户的关联
     - 测试用户之间的关注关系

6. 立即验证测试：
   * 编译项目，确保SwiftData集成无编译错误
   * 启动模拟器，测试数据加载功能
   * 创建测试数据，验证帖子列表正常显示
   * 测试刷新功能和加载状态显示
   * 验证用户信息在帖子中正确显示

7. **README.md 更新**：记录数据层与UI层集成完成，支持基本的帖子展示和交互功能。
```

**验证标准**:
* EACommunityViewModel的SwiftData操作正确实现
* 帖子列表能够正确加载和显示真实数据
* 下拉刷新和加载状态功能正常工作
* **用户资料管理验证**：
  - 用户信息在帖子卡片中正确显示（头像、用户名、发布时间）
  - 测试用户数据创建完整，包含所有必要字段
  - 用户登录状态模拟正确，社区功能有正确的用户上下文
  - 用户权限控制有效（作者删除权限等）
* 数据创建和查询操作符合SwiftData规范
* 用户与帖子、点赞、关注等数据关联关系正确
* 空状态和错误状态处理完善
* 应用运行稳定，数据操作无崩溃，模拟器测试便利
* README.md已更新，开发进度记录完整

### 步骤5.6：社区服务层架构实现

**目标**：实现社区功能的服务层逻辑，遵循项目MVVM架构规范，最大化复用现有服务层基础设施。

**AI执行指令**:
```
1. **复用现有服务层基础设施**：
   * **🔑 基于EANetworkService.swift构建**：
     - EANetworkService已提供完整的网络请求架构（错误处理、重试机制、认证管理）
     - 不要重新创建网络请求逻辑，直接基于EANetworkService扩展
     - 复用已有的EANetworkError错误类型和处理机制
   * **🔑 参考EAAuthService.swift的架构模式**：
     - 复用相同的协议定义模式（Protocol + Implementation）
     - 复用相同的依赖注入和错误处理模式
     - 遵循相同的异步操作和状态管理规范

2. 创建 `Evolve/Core/Services/EACommunityService.swift`：
   * **🚨 架构规范遵循**：严格遵循项目MVVM架构和依赖注入模式，不使用单例模式
   * **依赖注入设计**：通过构造器注入ModelContext，与EACommunityViewModel完美配合
   * **网络操作基于EANetworkService**：
     ```swift
     // 示例：复用网络服务架构
     private let networkService: EANetworkService
     
     init(modelContext: ModelContext, networkService: EANetworkService = .shared) {
         self.modelContext = modelContext
         self.networkService = networkService
     }
     ```
   * **核心服务方法**：
     - fetchPosts(limit: Int, offset: Int) -> [EACommunityPost] - 分页获取帖子
     - createPost(content: String, authorId: UUID, habitId: UUID?) -> EACommunityPost - 创建新帖子
     - toggleLike(postId: UUID, userId: UUID) -> Bool - 点赞/取消点赞
     - deletePost(postId: UUID, userId: UUID) -> Bool - 删除帖子（仅作者可删除）
   * **SwiftData操作规范**：所有数据操作遵循关系赋值顺序要求和上下文一致性原则

3. **复用现有错误处理模式**：
   * **基于EANetworkError扩展**：定义CommunityError枚举，继承网络错误处理模式
   * **参考其他Service的错误处理**：复用EAAuthService和EAContentService的错误处理策略
   * 提供用户友好的错误信息和恢复建议
   * 支持离线模式的基础数据缓存

4. 实现内容安全与质量控制：
   * 创建 `EAContentModerationService.swift`：
     - 基于现有的验证和过滤模式设计
     - validateContent(content: String) -> ContentValidationResult - 内容验证
     - filterInappropriateContent(content: String) -> String - 基础内容过滤
     - reportContent(contentId: UUID, reporterId: UUID, reason: String) - 举报功能
   * 集成到EACommunityService中，确保发布内容符合社区规范

5. 集成到EACommunityViewModel：
   * **复用现有ViewModel模式**：参考EATodayViewModel的架构和状态管理
   * 更新ViewModel构造器，注入EACommunityService
   * 将现有的数据操作方法重构为调用Service层
   * 保持ViewModel专注于状态管理和UI逻辑
   * 确保Service调用的异步处理正确

6. 为AI功能预留接口：
   * 在Service中预留AI相关方法接口：
     - generateShareSuggestion(habitId: UUID) -> String? - AI分享建议生成（预留）
     - analyzePostSentiment(content: String) -> SentimentAnalysis? - 情感分析（预留）
   * 设计灵活的扩展架构，支持后续AI功能无缝集成

7. 立即验证测试：
   * 编译项目，确保服务层架构无编译错误
   * 测试EACommunityService与ViewModel的集成
   * 验证数据操作通过Service层正确执行
   * 测试错误处理机制的有效性
   * 确认服务层架构符合项目规范
   * **特别验证**：确认EANetworkService的正确复用和集成

8. **README.md 更新**：在核心服务表格中添加EACommunityService和EAContentModerationService记录，并标注基于EANetworkService构建。
```

**验证标准**:
* 社区服务层实现完成，架构清晰合理
* 服务严格遵循项目MVVM和依赖注入规范
* Service与ViewModel集成无缝，数据操作正确
* 错误处理机制完善，用户体验良好
* 内容安全和质量控制功能就绪
* AI功能预留接口设计合理，便于后续扩展
* 应用编译运行稳定，服务层功能正常
* README.md已更新，服务记录完整

### 步骤5.7：社区功能基础集成测试与优化

**目标**：对已开发的社区核心功能进行全面测试，优化用户体验，确保功能稳定可用。

**AI执行指令**:
```
1. 端到端功能验证：
   * **完整流程测试**：从Tab导航→帖子列表→数据展示→交互操作，验证整个用户流程
   * **数据一致性验证**：确保ViewModel状态与UI显示完全同步，数据更新及时反映
   * **SwiftData操作测试**：验证帖子创建、查询、更新操作的正确性和稳定性
   * **用户权限验证**：测试只有作者能删除自己帖子等权限控制逻辑

2. 性能优化实施：
   * **列表性能优化**：
     - 优化帖子列表的滚动性能，避免卡顿
     - 实现懒加载机制，分页加载帖子数据
     - 优化SwiftData查询，使用合适的FetchDescriptor
   * **内存管理优化**：
     - 检查并修复可能的内存泄漏（特别是ViewModel中的循环引用）
     - 优化图片加载和缓存策略
     - 确保页面切换时资源正确释放

3. 用户体验细节优化：
   * **加载状态改进**：
     - 添加优雅的loading状态指示器
     - 实现骨架屏效果，提升加载体验
     - 优化错误状态页面的设计和提示信息
   * **空状态优化**：
     - 设计友好的空状态页面（暂无帖子时）
     - 添加引导用户发布第一条帖子的提示
     - 确保空状态的视觉效果符合项目设计规范

4. 错误处理完善：
   * **网络错误处理**：实现网络连接失败时的友好提示和重试机制
   * **数据错误处理**：处理数据格式异常、关联数据缺失等情况
   * **用户操作错误**：如重复点赞、无权限操作等的友好提示
   * **崩溃预防**：添加必要的防护代码，避免nil访问和数组越界

5. 无障碍访问优化：
   * 为所有UI元素添加适当的VoiceOver标签
   * 确保组件支持动态字体大小调整
   * 验证颜色对比度符合无障碍标准
   * 测试键盘导航和辅助技术支持

6. 架构质量验证：
   * **代码质量检查**：确保所有组件严格遵循EA命名前缀规范
   * **架构一致性**：验证ViewModel的@MainActor标记和依赖注入模式
   * **文档同步性**：确保代码实现与技术架构文档描述一致
   * **清理调试代码**：移除所有print()调试语句和临时测试代码

7. 立即验证测试：
   * 在多种设备尺寸上测试社区功能
   * 验证深色模式和浅色模式的显示效果
   * 测试极端情况：网络断开、数据为空、大量数据等
   * 确保应用在各种情况下都能稳定运行

8. **README.md 更新**：更新Community模块状态为✅基础功能完成，记录已实现的核心功能和优化项。
```

**验证标准**:
* 社区功能端到端流程测试通过，无重大缺陷
* 应用性能表现良好，列表滚动流畅，加载速度快
* 错误处理机制完善，用户体验友好
* 无障碍访问支持完整，符合iOS标准
* 代码质量高，严格遵循项目架构规范
* 深色模式和多设备适配正常
* 应用稳定性良好，无崩溃和内存问题
* README.md已更新，功能状态记录准确

### 阶段五开发总结

**开发成果概览**：
- ✅ **主Tab导航扩展**：成功集成社区Tab，5个Tab导航体系完整
- ✅ **数据架构建立**：5个核心社区数据模型，严格遵循SwiftData规范
- ✅ **UI框架搭建**：EACommunityView主页面和EACommunityViewModel架构完成
- ✅ **组件系统构建**：EACommunityPostCard和EALikeButton核心组件就绪
- ✅ **服务层实现**：EACommunityService和EAContentModerationService架构完善
- ✅ **质量保证完成**：性能优化、错误处理、无障碍访问全面实施

**技术架构优势**：
- **严格规范遵循**：所有组件严格遵循项目MVVM架构和依赖注入模式，无单例模式
- **SwiftData规范实施**：完整双向关系、单端inverse规则、关系赋值顺序要求全面落实
- **渐进式开发模式**：采用"导航→数据→页面→组件→服务→优化"的科学开发顺序
- **边开发边验证**：每个步骤都能独立编译测试，问题能够及时发现和解决

**开发质量保障**：
- **无编译错误承诺**：每个开发步骤都确保应用能够成功编译和运行
- **架构一致性保证**：与项目现有Today、Atlas、AuraSpace、Me模块完全一致
- **性能表现优良**：列表滚动流畅、内存管理良好、用户体验优秀
- **可扩展性强**：为后续AI集成、高级功能等预留了良好的架构基础

**后续扩展方向**：
- **创建帖子功能**：基于现有架构，可无缝添加EACreatePostView和相关功能
- **帖子详情页面**：实现EAPostDetailView，支持评论和深度互动
- **AI智能集成**：分享时机检测、内容推荐、情感分析等智能功能
- **社区子功能**：用户关注、内容分类、搜索发现等高级社交功能

**开发经验总结**：
- **UI驱动开发的优势**：先建导航和框架，后完善功能，大幅降低开发风险
- **数据模型优先的重要性**：严格的SwiftData规范确保了数据层的稳定性
- **渐进式组件开发**：按需创建组件避免了过度设计和资源浪费
- **服务层架构的价值**：清晰的职责分离为功能扩展和维护提供了强大支撑

此阶段为Evolve应用建立了完整的社区功能基础，为用户提供了分享习惯成果、互相鼓励支持的平台，实现了"能量共振"的核心产品理念。

---

## 阶段六：AI深度集成与智能化系统（架构优化集成版）

### 步骤6.1：AI数据桥接架构建立（DeepSeek API集成版）

**目标**：建立AI数据桥接架构，集成DeepSeek API，为后续AI功能提供数据支持。

**重要说明**：本步骤对应《Evolve架构优化执行计划》的"阶段四：AI数据桥接架构建立"，完成此步骤后架构优化工作全部完成。

**AI执行指令**：
```
1. 【AI数据模型创建】
   在 Evolve/Core/DataModels/ 目录下创建5个AI相关数据模型：
   * `EAAIInsight.swift` - AI洞察结果模型（包含用户关系）
   * `EAAIUserProfile.swift` - AI用户画像模型（包含用户关系）  
   * `EAAIBehaviorPattern.swift` - AI行为模式模型
   * `EAAICostRecord.swift` - AI成本记录模型
   * `EAAIResponseCache.swift` - AI响应缓存模型

2. 【DeepSeek API配置】
   在 Evolve/Core/Config/ 目录下创建：
   * `EADeepSeekConfig.swift` - 参考API.md配置密钥、URL、模型参数
   * 实现成本计算、优先级管理、场景枚举

3. 【API服务层】
   在 Evolve/Core/Services/ 目录下创建：
   * `EADeepSeekAPIModels.swift` - 请求响应数据模型
   * `EADeepSeekAPIService.swift` - 网络服务实现，支持多种API场景

4. 【Repository层扩展】
   在 Evolve/Core/Repositories/ 目录下创建：
   * `EAAIInsightRepository.swift` - AI洞察数据仓库（@ModelActor）
   * `EAAIUserProfileRepository.swift` - AI用户画像数据仓库（@ModelActor）
   * 扩展 `EARepositoryContainer.swift`，添加AI Repository管理

5. 【AI数据桥接服务】
   在 Evolve/Core/Services/ 目录下创建：
   * `EAAIDataBridge.swift` - 数据格式转换和桥接主服务
   * 实现用户数据→AI格式转换，AI结果→应用数据存储

6. 【架构优化：通过userId关联而非直接关系】
   **重要**：为遵循≤5个关系性能限制，AI数据通过userId关联访问：
   * EAAIInsight模型包含userId字段，通过Repository查询
   * EAAIUserProfile模型包含userId字段，通过Repository查询
   * **不在EAUser中添加直接关系**，保持核心模型轻量化

7. 【数据库配置更新】
   更新 `EADatabaseManager.swift`，添加AI模型到ModelContainer

8. 【编译测试验证】
   * 编译项目确保无错误
   * 验证所有AI模型关系正确定义（遵循单端inverse规则）
   * 测试Repository和服务初始化成功
   * 验证DeepSeek API配置加载正确
```

**验证标准**:
* AI数据模型创建完成（5个文件）
* DeepSeek API配置正确加载
* Repository层扩展完成
* AI数据桥接服务创建完成
* AI数据通过userId关联架构实现（保持EAUser≤5个关系）
* ModelContainer配置包含所有AI模型
* 编译无错误，模拟器测试通过
* 遵循Repository模式强制执行规范
* 严格遵循SwiftData单端inverse规则
* **README.md更新完成**：记录新增的AI数据模型、Repository、服务等组件

### 步骤6.2：今日页面AI功能集成（DeepSeek API驱动）

**目标**：为今日页面集成AI驱动的个性化洞察、完成概率预测和智能建议功能。

**AI执行指令**：
```
1. 【DeepSeek API服务层】
   在 Evolve/Core/Services/ 目录下创建：
   * `EADeepSeekTodayService.swift` - 今日页面专用DeepSeek API服务
   * 实现每日洞察生成、完成概率预测、个性化建议

2. 【本地智能引擎】
   在 Evolve/Core/Services/ 目录下创建：
   * `EALocalIntelligenceEngine.swift` - 本地智能规则引擎
   * 实现基础统计分析、模式识别、成本控制下的智能决策

3. 【智能分析引擎】
   在 Evolve/Core/Services/ 目录下创建：
   * `EAIntelligentAnalysisEngine.swift` - 智能行为分析引擎
   * `EABehaviorPatternDetector.swift` - 行为模式检测器
   * 实现分层AI策略：缓存→本地→AI API

4. 【EATodayViewModel更新】
   更新 `Features/Today/EATodayViewModel.swift`：
   * 集成AI数据桥接服务
   * 添加每日洞察生成功能
   * 实现完成概率预测
   * 添加个性化建议展示

5. 【UI组件创建】
   在 UIComponents/ 目录下创建：
   * `EADailyInsightCard.swift` - 每日洞察卡片组件
   * `EACompletionProbabilityIndicator.swift` - 完成概率指示器
   * `EAPersonalizedSuggestionView.swift` - 个性化建议视图

6. 【EATodayView更新】
   更新 `Features/Today/EATodayView.swift`：
   * 集成新的AI驱动组件
   * 添加每日洞察展示
   * 显示完成概率和建议

7. 【环境值配置】
   更新环境值配置，支持AI服务依赖注入

8. 【编译测试验证】
   验证编译无错误、AI功能正常工作、成本控制有效
```

**验证标准**:
* DeepSeek API服务正常工作
* 本地智能引擎功能完成
* 分层AI调用策略实现（缓存→本地→AI API）
* EATodayViewModel集成AI功能成功
* UI组件创建完成（3个新组件）
* EATodayView展示AI功能正确
* 成本控制机制有效（24小时缓存等）
* 降级策略正常工作
* 编译无错误，模拟器测试通过
* 每日洞察生成正确
* 完成概率预测准确
* **README.md更新完成**：记录今日页面新增的AI服务和UI组件

### 步骤6.3：灵境页面AI对话核心功能（DeepSeek多轮对话）

**目标**：实现灵境页面的AI对话核心功能，支持多轮对话、情境感知和个性化回复。

**AI执行指令**：
```
1. 【AI对话管理器】
   在 Evolve/Core/Services/ 目录下创建：
   * `EAAIConversationManager.swift` - AI对话管理器
   * 实现多轮对话、上下文记忆、情境感知

2. 【情绪状态分析器】
   在 Evolve/Core/Services/ 目录下创建：
   * `EAEmotionalStateAnalyzer.swift` - 情绪状态分析器
   * 实现基于关键词和语境的情绪识别

3. 【EAAuraSpaceViewModel更新】
   更新 `Features/AuraSpace/EAAuraSpaceViewModel.swift`：
   * 集成AI对话管理器
   * 实现情绪状态分析
   * 支持多轮对话和上下文记忆

4. 【UI组件扩展】
   在 UIComponents/ 目录下创建：
   * `EAEmotionalStateIndicator.swift` - 情绪状态指示器
   * `EAConversationContextView.swift` - 对话上下文视图

5. 【EAAuraSpaceView更新】
   更新 `Features/AuraSpace/EAAuraSpaceView.swift`：
   * 集成新的AI对话功能
   * 支持情绪状态显示

6. 【编译测试验证】
   验证AI对话功能、多轮对话、情绪识别等功能正常
```

**验证标准**:
* AI对话管理器实现完成
* 多轮对话功能正常工作
* 情绪状态分析准确
* EAAuraSpaceViewModel集成成功
* 新UI组件创建完成
* 对话上下文记忆功能正常
* 编译无错误，模拟器测试通过
* **README.md更新完成**：记录灵境页面AI对话系统的核心组件

### 步骤6.4：图鉴页面AI习惯创建助手（DeepSeek智能推荐）

**目标**：为图鉴页面添加AI驱动的智能习惯创建助手和个性化推荐功能。

**AI执行指令**：
```
1. 【智能习惯分析器】
   在 Evolve/Core/Services/ 目录下创建：
   * `EAIntelligentHabitAnalyzer.swift` - 智能习惯分析器
   * 基于用户画像提供习惯创建建议

2. 【EAAtlasViewModel更新】
   更新 `Features/Atlas/EAAtlasViewModel.swift`：
   * 集成AI习惯分析器
   * 实现智能推荐功能

3. 【EAHabitCreationViewModel更新】
   更新 `Features/Atlas/EAHabitCreationViewModel.swift`：
   * 添加AI助手功能
   * 集成智能推荐建议

4. 【UI组件创建】
   在 UIComponents/ 目录下创建：
   * `EAAIRecommendationCard.swift` - AI推荐卡片

5. 【页面更新】
   更新相关View文件，集成AI推荐功能

6. 【编译测试验证】
   验证AI推荐功能正常工作
```

**验证标准**:
* 智能习惯分析器实现完成
* EAAtlasViewModel集成AI功能
* EAHabitCreationViewModel支持AI助手
* AI推荐卡片组件创建完成
* 智能推荐功能正常工作
* 编译无错误，模拟器测试通过

### 步骤6.5：智能推送与时机优化系统（DeepSeek驱动）

**目标**：建立基于AI的智能推送和最佳时机分析系统。

**AI执行指令**：
```
1. 【最佳时机分析器】
   在 Evolve/Core/Services/ 目录下创建：
   * `EAOptimalTimingAnalyzer.swift` - 最佳时机分析器
   * 实现用户行为模式分析和最佳推送时机预测

2. 【行为模式检测器】
   在 Evolve/Core/Services/ 目录下创建：
   * `EABehaviorPatternDetector.swift` - 行为模式检测器（本地分析）
   * 实现用户习惯完成规律检测

3. 【个性化消息生成器】
   在 Evolve/Core/Services/ 目录下创建：
   * `EAPersonalizedMessageGenerator.swift` - 个性化消息生成器
   * `EAMessageTemplateManager.swift` - 消息模板管理器

4. 【智能推送调度器】
   在 Evolve/Core/Services/ 目录下创建：
   * `EAAdaptivePushScheduler.swift` - 自适应推送调度器

5. 【EANotificationService集成】
   更新 `Core/Notifications/EANotificationService.swift`：
   * 集成智能推送系统
   * 支持AI驱动的时机优化

6. 【编译测试验证】
   验证智能推送功能正常工作
```

**验证标准**:
* 最佳时机分析器实现完成
* 行为模式检测功能正常
* 个性化消息生成系统完成
* 智能推送调度器正常工作
* EANotificationService集成成功
* 编译无错误，模拟器测试通过

### 步骤6.6：社区AI智能化增强（DeepSeek内容分析）

**目标**：为社区功能添加AI驱动的内容分析、分享时机检测和智能推荐。

**AI执行指令**：
```
1. 【社区内容智能分析器】
   在 Evolve/Core/Services/ 目录下创建：
   * `EACommunityContentAnalyzer.swift` - 社区内容智能分析器
   * 实现内容质量分析、情感分析、话题分析

2. 【分享时机检测器】
   在 Evolve/Core/Services/ 目录下创建：
   * `EASharingOpportunityDetector.swift` - 分享时机检测器
   * 基于用户行为和成就检测最佳分享时机

3. 【EACommunityViewModel集成】
   更新 `Features/Community/EACommunityViewModel.swift`：
   * 集成AI内容分析
   * 添加智能推荐功能

4. 【UI组件创建】
   在 UIComponents/ 目录下创建：
   * `EAContentQualityIndicator.swift` - 内容质量指示器

5. 【社区页面更新】
   更新社区相关View文件，集成AI分析功能

6. 【编译测试验证】
   验证社区AI功能正常工作
```

**验证标准**:
* 社区内容分析器实现完成
* 分享时机检测功能正常
* EACommunityViewModel集成AI功能
* 内容质量指示器创建完成
* 社区AI智能化功能正常
* 编译无错误，模拟器测试通过

### 步骤6.7：AI系统监控与持续优化（DeepSeek全面监控）

**目标**：建立AI系统监控和持续优化机制，确保AI功能稳定高效运行。

**AI执行指令**：
```
1. 【AI性能监控器】
   在 Evolve/Core/Analytics/ 目录下创建：
   * `EAAIPerformanceMonitor.swift` - AI性能监控器
   * 监控API调用响应时间、成功率、错误类型

2. 【AI成本跟踪器】
   在 Evolve/Core/Analytics/ 目录下创建：
   * `EAAICostTracker.swift` - AI成本跟踪器
   * 实现预算管理、成本预警、使用统计

3. 【系统健康检查】
   在 Evolve/Core/Analytics/ 目录下创建：
   * `EAAISystemHealthMonitor.swift` - AI系统健康监控器
   * 检测异常模式、性能降级、服务可用性

4. 【EADeepSeekAPIService监控集成】
   更新 `EADeepSeekAPIService.swift`：
   * 集成性能监控和成本跟踪
   * 添加健康检查机制

5. 【我的页面AI统计】
   更新 `Features/Me/EAMeViewModel.swift`：
   * 添加AI使用统计展示
   * 显示AI效果分析

6. 【编译测试验证】
   验证监控系统功能正常、成本跟踪准确
```

**验证标准**:
* AI性能监控器正常工作
* 成本跟踪系统准确记录
* 系统健康检查机制有效
* EADeepSeekAPIService监控集成成功
* 我的页面AI统计显示正确
* 监控系统对应用性能影响最小
* 编译无错误，模拟器测试通过

---

## 阶段七：高级数据分析与系统优化

### 步骤7.1：高级数据分析架构（基于Repository模式）

**目标**：建立高级数据分析架构，基于现有Repository模式提供深度用户行为洞察。

**AI执行指令**：
```
1. 【数据分析Repository扩展】
   在 Evolve/Core/Repositories/ 目录下创建：
   * `EAAnalyticsDataRepository.swift` - 分析数据仓库
   * 基于现有Repository模式，提供分析数据的统一访问

2. 【高级分析引擎】
   在 Evolve/Core/Analytics/ 目录下创建：
   * `EAAdvancedAnalyticsEngine.swift` - 高级分析引擎
   * `EAUserBehaviorAnalyzer.swift` - 用户行为分析器
   * `EAPredictiveModelBuilder.swift` - 预测模型构建器

3. 【DeepSeek分析服务】
   在 Evolve/Core/Services/ 目录下创建：
   * `EADeepSeekAnalyticsService.swift` - 基于DeepSeek的深度分析服务
   * 实现复杂模式识别和预测分析

4. 【分析数据模型】
   在 Evolve/Core/DataModels/ 目录下创建：
   * `EAUserAnalyticsProfile.swift` - 用户分析档案模型
   * `EABehaviorInsight.swift` - 行为洞察模型

5. 【EARepositoryContainer扩展】
   更新 `EARepositoryContainer.swift`，添加分析Repository管理

6. 【集成验证】
   确保所有分析功能通过Repository模式访问数据，避免直接ModelContext操作
```

**验证标准**:
* 分析Repository创建完成
* 高级分析引擎功能正常
* DeepSeek分析服务集成成功
* 分析数据模型创建完成
* 严格遵循Repository模式
* 编译无错误，模拟器测试通过

### 步骤7.2：系统优化与性能监控增强

**目标**：基于阶段六的监控基础，进一步优化系统性能和用户体验。

**AI执行指令**：
```
1. 【性能优化服务】
   在 Evolve/Core/Services/ 目录下创建：
   * `EAPerformanceOptimizer.swift` - 系统性能优化器
   * 基于监控数据自动优化系统参数

2. 【缓存策略优化】
   在 Evolve/Core/Services/ 目录下创建：
   * `EAIntelligentCacheManager.swift` - 智能缓存管理器
   * 优化AI响应和数据查询的缓存策略

3. 【用户体验分析器】
   在 Evolve/Core/Analytics/ 目录下创建：
   * `EAUserExperienceAnalyzer.swift` - 用户体验分析器
   * 分析用户交互模式，优化界面响应

4. 【系统扩展预留】
   在 Evolve/Core/Services/ 目录下创建：
   * `EASystemExtensionManager.swift` - 为未来功能扩展做准备
   * 预留Apple Watch、macOS扩展接口

5. 【最终集成测试】
   进行全面的系统集成测试，确保所有优化不影响现有功能
```

**验证标准**:
* 性能优化器正常工作
* 缓存策略优化有效
* 用户体验分析功能完成
* 系统扩展接口预留完成
* 系统整体性能提升
* 编译无错误，模拟器测试通过
* 所有功能模块正常协作

---

## 项目开发总结

### 🎯 最终实现目标

**核心功能完整性**：
- ✅ 完整的用户引导与认证系统
- ✅ 全功能习惯管理与智能追踪
- ✅ AI深度集成的对话和分析系统
- ✅ 完善的用户中心与会员系统
- ✅ 智能化的社区分享与互动

**技术架构稳定性**：
- ✅ MVVM架构模式完全实施
- ✅ Repository模式强制执行规范
- ✅ SwiftData关系优化完成（≤5个关系限制）
- ✅ AI分层调用策略和成本控制
- ✅ iOS 17.0+完全兼容性保障

**产品体验优秀性**：
- ✅ 生态隐喻设计理念完全贯穿
- ✅ AI私人教练角色深度定位
- ✅ 能量流转与社区共振体验
- ✅ 个性化与智能化深度交互

### 📊 开发完成检查清单

**架构一致性检查**：
- [ ] 所有数据访问通过Repository层完成
- [ ] 没有Service+ModelContext直接操作
- [ ] AI服务通过数据桥接层访问数据
- [ ] SessionManager支持依赖注入
- [ ] 社区功能有独立Repository

**代码质量检查**：
- [ ] 所有类型使用EA前缀命名
- [ ] SwiftData关系遵循单端inverse规则
- [ ] 所有ViewModel标记@MainActor
- [ ] 清理所有print()调试语句
- [ ] 错误处理和降级策略完整

**功能完整性检查**：
- [ ] 今日页面AI功能正常
- [ ] 灵境页面对话系统完整
- [ ] 图鉴页面智能推荐工作
- [ ] 社区AI智能化功能完成
- [ ] 我的页面统计显示正确

**性能与监控检查**：
- [ ] AI成本控制机制有效
- [ ] 系统性能监控正常
- [ ] 缓存策略优化完成
- [ ] 用户体验流畅度达标

通过以上完整的开发步骤，Evolve AI应用将成为一个技术先进、功能完整、用户体验优秀的习惯养成应用，为用户提供真正智能化的个人成长支持。

## 总结

本开发步骤文档为Evolve iOS应用提供了完整、精确的开发路线图。每个阶段都有明确的目标、详细的执行指令和具体的验证标准，确保开发过程有序进行，最终实现一个高质量的AI驱动习惯养成应用。

**开发成功的关键要素**：
- 严格遵循架构规范和开发约束
- 每个步骤都进行编译和功能验证
- 保持与产品设计理念的一致性
- 确保AI功能的智能化和个性化
- 维护代码质量和项目可维护性

祝叶同学开发顺利！🚀