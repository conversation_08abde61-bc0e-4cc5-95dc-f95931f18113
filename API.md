# Evolve项目 - DeepSeek API 集成方案文档 v2.0

> **更新时间**: 2025-06-02 19:01:44  
> **文档版本**: v2.0  
> **项目架构**: SwiftUI + SwiftData + MVVM  

## 项目概述

Evolve是一款基于SwiftUI和SwiftData构建的AI驱动习惯养成应用，采用MVVM架构模式。本文档基于项目现有的EAAIService架构，为DeepSeek API的集成提供完整的技术方案和实施指南。

## 一、DeepSeek API 基础配置

### 1.1 核心配置参数

| 参数 | 值 | 说明 |
|------|-----|------|
| **base_url** | `https://api.deepseek.com` | 主要端点 |
| **api_key** | `***********************************` | 项目专用API密钥 |
| **主模型** | `deepseek-chat` | 日常对话模型 |
| **推理模型** | `deepseek-reasoner` | 复杂分析模型 |

### 1.2 项目适配的模型选择策略

**基于Evolve功能场景的智能模型分配：**

| 功能场景 | 推荐模型 | 原因 | 示例用途 |
|---------|----------|------|---------|
| **日常AI对话** | `deepseek-chat` | 响应快速、成本低、适合高频交互 | 用户问问题、寻求鼓励 |
| **习惯建议生成** | `deepseek-chat` | 平衡质量与速度 | generateHabitSuggestions |
| **社区分享内容** | `deepseek-chat` | 创意输出、用户友好 | generateShareContent |
| **复杂分析洞察** | `deepseek-reasoner` | 深度推理、高质量输出 | 习惯模式分析、长期规划 |
| **危机干预** | `deepseek-reasoner` | 需要深思熟虑的回应 | 用户情绪低落时的专业指导 |

## 二、成本控制与优化策略

### 2.1 定价分析（人民币）

| 模型 | 输入（缓存命中）| 输入（缓存未命中）| 输出价格 | 优惠时段 |
|------|-----|-----|-----|-----|
| **deepseek-chat** | ¥0.5/百万tokens | ¥2/百万tokens | ¥8/百万tokens | 00:30-08:30 (5折) |
| **deepseek-reasoner** | ¥1/百万tokens | ¥4/百万tokens | ¥16/百万tokens | 00:30-08:30 (2.5折) |

### 2.2 基于项目的分层调用策略

```swift
/// 基于Evolve业务场景的AI调用优先级
enum EAAICallPriority {
    case critical    // 必须实时调用AI API
    case standard    // 可缓存24小时
    case low         // 优先本地处理
}

/// Evolve功能场景到优先级映射
private func getCallPriority(for scenario: EAAIScenario) -> EAAICallPriority {
    switch scenario {
    // 🔴 高优先级 - 用户体验关键
    case .userActiveHelp:           // 用户主动求助
        return .critical
    case .crisisIntervention:       // 情绪危机干预
        return .critical
    case .majorMilestone:           // 重大成就庆祝
        return .critical
        
    // 🟡 中优先级 - 可以延迟或缓存
    case .dailyInsight:             // 每日洞察生成
        return .standard
    case .habitCreationSuggestion:  // 习惯创建建议
        return .standard
    case .communityContentGeneration: // 社区分享内容
        return .standard
        
    // 🟢 低优先级 - 本地处理优先
    case .dailyReminder:            // 日常提醒
        return .low
    case .basicStatistics:          // 基础统计
        return .low
    case .quickEncouragement:       // 快速鼓励
        return .low
    }
}
```

### 2.3 Context Caching 成本优化实现

```swift
// 利用DeepSeek的硬盘缓存技术实现90%成本节省
private func buildOptimizedRequest(for scenario: EAAIScenario) -> DeepSeekChatRequest {
    var messages: [ChatMessage] = []
    
    // 🎯 系统提示词（会被自动缓存）
    messages.append(ChatMessage(
        role: "system", 
        content: getSystemPrompt(for: scenario)
    ))
    
    // 🏷️ 用户画像（缓存7天，命中率高）
    if let userProfile = getCachedUserProfile() {
        messages.append(ChatMessage(
            role: "system", 
            content: buildUserProfileContext(userProfile)
        ))
    }
    
    // 📝 对话历史（前缀缓存）
    messages.append(contentsOf: getOptimizedConversationHistory())
    
    return DeepSeekChatRequest(
        model: selectOptimalModel(for: scenario),
        messages: messages,
        maxTokens: getOptimalTokenLimit(for: scenario),
        temperature: getOptimalTemperature(for: scenario),
        stream: shouldUseStreaming(for: scenario)
    )
}
```

## 三、EAAIService 升级实现

### 3.1 DeepSeek API 数据模型

```swift
// MARK: - DeepSeek API数据模型（完全兼容OpenAI格式）
struct DeepSeekChatRequest: Codable {
    let model: String
    let messages: [ChatMessage]
    let maxTokens: Int?
    let temperature: Double?
    let stream: Bool
    
    enum CodingKeys: String, CodingKey {
        case model, messages, temperature, stream
        case maxTokens = "max_tokens"
    }
}

struct ChatMessage: Codable {
    let role: String  // "system", "user", "assistant"
    let content: String
}

struct DeepSeekChatResponse: Codable {
    let id: String
    let object: String
    let created: Int
    let model: String
    let choices: [ChatChoice]
    let usage: TokenUsage
    
    // 成本追踪关键信息
    struct TokenUsage: Codable {
        let promptTokens: Int
        let completionTokens: Int
        let totalTokens: Int
        let promptCacheHitTokens: Int    // 缓存命中tokens
        let promptCacheMissTokens: Int   // 缓存未命中tokens
        
        enum CodingKeys: String, CodingKey {
            case totalTokens = "total_tokens"
            case promptTokens = "prompt_tokens" 
            case completionTokens = "completion_tokens"
            case promptCacheHitTokens = "prompt_cache_hit_tokens"
            case promptCacheMissTokens = "prompt_cache_miss_tokens"
        }
        
        // 成本计算方法
        func calculateCost(for model: String) -> Double {
            let prices = DeepSeekPricing.getPricing(for: model)
            let inputCost = (Double(promptCacheHitTokens) * prices.inputCached + 
                           Double(promptCacheMissTokens) * prices.inputUncached) / 1_000_000
            let outputCost = Double(completionTokens) * prices.output / 1_000_000
            return inputCost + outputCost
        }
    }
    
    struct ChatChoice: Codable {
        let index: Int
        let message: ChatMessage
        let finishReason: String
        
        enum CodingKeys: String, CodingKey {
            case index, message
            case finishReason = "finish_reason"
        }
    }
}
```

### 3.2 升级的EAAIService实现

```swift
/// 升级版AI服务 - 集成DeepSeek API
class EAAIService: ObservableObject {
    
    // MARK: - DeepSeek配置
    private struct DeepSeekConfig {
        static let baseURL = "https://api.deepseek.com"
        static let apiKey = "***********************************"
        static let chatModel = "deepseek-chat"
        static let reasonerModel = "deepseek-reasoner"
        static let timeout: TimeInterval = 30
    }
    
    // MARK: - 成本控制器
    private let costController = EAAICostController()
    private let cacheManager = EAAIResponseCache()
    
    // MARK: - 现有属性保持不变
    private let networkService: EANetworkService
    private var conversationHistory: [EAAIMessage] = []
    private let maxHistoryLength = 20
    
    // MARK: - 升级的发送消息方法
    func sendMessage(_ message: String, context: [String: Any]? = nil) async throws -> EAAIResponse {
        let startTime = Date()
        
        // 🔍 分析场景类型
        let scenario = analyzeScenario(from: message, context: context)
        
        // 🎯 检查调用优先级
        let priority = getCallPriority(for: scenario)
        
        // 💰 成本控制检查
        guard await costController.shouldAllowCall(priority: priority) else {
            return try await generateLocalResponse(for: message, scenario: scenario)
        }
        
        // 📦 检查缓存
        if let cachedResponse = await cacheManager.getCachedResponse(for: message, scenario: scenario) {
            return cachedResponse
        }
        
        // 🚀 构建和发送请求
        let request = buildOptimizedRequest(for: scenario, message: message, context: context)
        
        do {
            let response = try await sendDeepSeekRequest(request)
            
            // 📊 记录成本和性能
            await recordAPICall(response: response, scenario: scenario, duration: Date().timeIntervalSince(startTime))
            
            // 💾 缓存响应
            let aiResponse = EAAIResponse(
                message: response.choices.first?.message.content ?? "",
                suggestions: extractSuggestions(from: response),
                confidence: 0.9,
                responseTime: Date().timeIntervalSince(startTime)
            )
            
            await cacheManager.cacheResponse(aiResponse, for: message, scenario: scenario)
            
            return aiResponse
            
        } catch {
            // 🛡️ 降级到本地处理
            print("⚠️ DeepSeek API调用失败，降级到本地处理: \(error)")
            return try await generateLocalResponse(for: message, scenario: scenario)
        }
    }
    
    // MARK: - DeepSeek API调用核心方法
    private func sendDeepSeekRequest(_ request: DeepSeekChatRequest) async throws -> DeepSeekChatResponse {
        var urlRequest = URLRequest(url: URL(string: "\(DeepSeekConfig.baseURL)/chat/completions")!)
        urlRequest.httpMethod = "POST"
        urlRequest.addValue("Bearer \(DeepSeekConfig.apiKey)", forHTTPHeaderField: "Authorization")
        urlRequest.addValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.timeoutInterval = DeepSeekConfig.timeout
        
        let requestData = try JSONEncoder().encode(request)
        urlRequest.httpBody = requestData
        
        let (data, response) = try await URLSession.shared.data(for: urlRequest)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw EAAIError.networkError("无效的响应类型")
        }
        
        guard httpResponse.statusCode == 200 else {
            let errorMessage = String(data: data, encoding: .utf8) ?? "未知错误"
            throw EAAIError.apiError("HTTP \(httpResponse.statusCode): \(errorMessage)")
        }
        
        return try JSONDecoder().decode(DeepSeekChatResponse.self, from: data)
    }
    
    // MARK: - 智能场景分析
    private func analyzeScenario(from message: String, context: [String: Any]?) -> EAAIScenario {
        let lowercased = message.lowercased()
        
        // 🚨 危机干预检测
        if lowercased.contains("放弃") || lowercased.contains("失败") || lowercased.contains("痛苦") {
            return .crisisIntervention
        }
        
        // 🎉 重大里程碑检测
        if let streak = context?["streak"] as? Int, streak >= 30 {
            return .majorMilestone
        }
        
        // ❓ 用户主动求助
        if lowercased.contains("怎么") || lowercased.contains("如何") || lowercased.contains("帮助") {
            return .userActiveHelp
        }
        
        // 📝 习惯创建相关
        if lowercased.contains("创建") || lowercased.contains("新习惯") {
            return .habitCreationSuggestion
        }
        
        // 🏆 每日洞察
        if lowercased.contains("今天") || lowercased.contains("进度") {
            return .dailyInsight
        }
        
        return .userActiveHelp // 默认高优先级
    }
    
    // MARK: - 模型选择策略
    private func selectOptimalModel(for scenario: EAAIScenario) -> String {
        switch scenario {
        case .crisisIntervention, .majorMilestone:
            return DeepSeekConfig.reasonerModel  // 需要深度推理
        case .userActiveHelp, .habitCreationSuggestion, .dailyInsight:
            return DeepSeekConfig.chatModel      // 平衡速度与质量
        default:
            return DeepSeekConfig.chatModel
        }
    }
    
    // MARK: - 本地降级处理（保持现有逻辑）
    private func generateLocalResponse(for message: String, scenario: EAAIScenario) async throws -> EAAIResponse {
        // 保持现有的 generateContextualResponse 逻辑
        let response = generateContextualResponse(for: message)
        return EAAIResponse(
            message: response.message,
            suggestions: response.suggestions,
            confidence: 0.7,  // 本地处理置信度较低
            responseTime: 0.1
        )
    }
}

// MARK: - 场景枚举定义
enum EAAIScenario: String, CaseIterable {
    case userActiveHelp = "用户主动求助"
    case crisisIntervention = "危机干预"
    case majorMilestone = "重大里程碑"
    case dailyInsight = "每日洞察"
    case habitCreationSuggestion = "习惯创建建议"
    case communityContentGeneration = "社区内容生成"
    case dailyReminder = "日常提醒"
    case basicStatistics = "基础统计"
    case quickEncouragement = "快速鼓励"
}
```

## 四、成本控制实现

### 4.1 EAAICostController 实现

```swift
/// AI调用成本控制器
@MainActor
class EAAICostController: ObservableObject {
    
    @Published var dailyCost: Double = 0.0
    @Published var monthlyCost: Double = 0.0
    @Published var dailyCallCount: Int = 0
    
    // 成本限制配置
    private let dailyCostLimit: Double = 10.0    // 每日10元限制
    private let monthlyCostLimit: Double = 200.0  // 每月200元限制
    private let dailyCallLimit: Int = 500        // 每日500次调用限制
    
    // 用户分级成本配额
    private func getCostQuota(for userTier: UserTier) -> (daily: Double, monthly: Double) {
        switch userTier {
        case .free:     return (daily: 2.0, monthly: 20.0)
        case .pro:      return (daily: 10.0, monthly: 200.0)
        case .premium:  return (daily: 50.0, monthly: 1000.0)
        }
    }
    
    func shouldAllowCall(priority: EAAICallPriority) async -> Bool {
        // 高优先级调用总是允许（关键用户体验）
        if priority == .critical {
            return true
        }
        
        // 检查成本限制
        if dailyCost >= dailyCostLimit || monthlyCost >= monthlyCostLimit {
            return false
        }
        
        // 检查调用频率
        if dailyCallCount >= dailyCallLimit {
            return false
        }
        
        return true
    }
    
    func recordCost(_ cost: Double, for scenario: EAAIScenario) {
        dailyCost += cost
        monthlyCost += cost
        dailyCallCount += 1
        
        // 记录详细的成本分析
        print("💰 AI调用成本: ¥\(String(format: "%.4f", cost)) - 场景: \(scenario.rawValue)")
        print("📊 今日累计: ¥\(String(format: "%.2f", dailyCost))/¥\(dailyCostLimit)")
    }
}

enum UserTier {
    case free, pro, premium
}
```

### 4.2 缓存管理器实现

```swift
/// AI响应缓存管理器
actor EAAIResponseCache {
    
    private var cache: [String: CachedResponse] = [:]
    
    struct CachedResponse {
        let response: EAAIResponse
        let timestamp: Date
        let scenario: EAAIScenario
        
        var isExpired: Bool {
            let expirationInterval: TimeInterval
            switch scenario {
            case .dailyInsight, .habitCreationSuggestion:
                expirationInterval = 24 * 3600  // 24小时
            case .communityContentGeneration:
                expirationInterval = 12 * 3600  // 12小时
            default:
                expirationInterval = 3600       // 1小时
            }
            
            return Date().timeIntervalSince(timestamp) > expirationInterval
        }
    }
    
    func getCachedResponse(for message: String, scenario: EAAIScenario) async -> EAAIResponse? {
        let key = generateCacheKey(message: message, scenario: scenario)
        
        guard let cached = cache[key], !cached.isExpired else {
            cache.removeValue(forKey: key)
            return nil
        }
        
        print("🎯 缓存命中: \(scenario.rawValue)")
        return cached.response
    }
    
    func cacheResponse(_ response: EAAIResponse, for message: String, scenario: EAAIScenario) async {
        let key = generateCacheKey(message: message, scenario: scenario)
        cache[key] = CachedResponse(response: response, timestamp: Date(), scenario: scenario)
    }
    
    private func generateCacheKey(message: String, scenario: EAAIScenario) -> String {
        return "\(scenario.rawValue)_\(message.lowercased().prefix(50).replacingOccurrences(of: " ", with: "_"))"
    }
}
```

## 五、集成部署指南

### 5.1 现有代码升级步骤

1. **保持现有接口不变** - 确保UI层无需修改
2. **渐进式启用** - 通过配置开关控制DeepSeek调用
3. **降级策略** - 确保API不可用时的优雅降级
4. **成本监控** - 实时监控和告警机制

### 5.2 配置开关实现

```swift
// 在 EAFeatureManager 中添加
@Published var isDeepSeekEnabled: Bool = true
@Published var useReasonerForComplexTasks: Bool = true
@Published var enableCostOptimization: Bool = true

// 在 EAAIService 中使用
private func shouldUseDeepSeekAPI() -> Bool {
    return EAFeatureManager.shared.isDeepSeekEnabled && 
           EAFeatureManager.shared.checkAIFeatures()
}
```

### 5.3 错误处理和监控

```swift
// 增强的错误处理
private func handleDeepSeekError(_ error: Error) -> EAAIError {
    switch error {
    case let urlError as URLError:
        if urlError.code == .timedOut {
            return .serviceUnavailable
        }
        return .networkError(urlError.localizedDescription)
    case let decodingError as DecodingError:
        return .invalidResponse
    default:
        return .apiError(error.localizedDescription)
    }
}

// 性能监控
private func recordAPICall(response: DeepSeekChatResponse, scenario: EAAIScenario, duration: TimeInterval) async {
    let usage = response.usage
    let cost = usage.calculateCost(for: response.model)
    
    await costController.recordCost(cost, for: scenario)
    
    print("""
    📈 API调用完成:
       模型: \(response.model)
       场景: \(scenario.rawValue)
       耗时: \(String(format: "%.2f", duration))秒
       Tokens: \(usage.totalTokens) (缓存命中: \(usage.promptCacheHitTokens))
       成本: ¥\(String(format: "%.4f", cost))
    """)
}
```

## 六、最佳实践建议

### 6.1 成本优化最佳实践

1. **充分利用缓存机制** - 系统提示词和用户画像重复使用
2. **错峰调用** - 夜间批处理非紧急任务
3. **智能降级** - 本地处理简单查询
4. **模型选择** - 根据任务复杂度选择合适模型

### 6.2 用户体验优化

1. **流式输出** - 长回复使用流式传输提升体验
2. **快速响应** - 缓存常见问题的回复
3. **降级透明** - 本地处理时保持用户体验一致
4. **个性化** - 利用用户画像提供个性化服务

### 6.3 安全和隐私

1. **API密钥安全** - 使用Keychain存储，服务器端轮换
2. **数据脱敏** - 敏感信息本地处理，不传输给API
3. **请求限制** - 实施频率限制防止滥用
4. **用户同意** - 明确告知AI功能的数据使用方式

---

## 技术支持

- **DeepSeek官方文档**: @https://api-docs.deepseek.com/zh-cn/
- **项目技术架构**: 参考 `技术架构文档.md`
- **开发规范**: 参考 `开发规范文档.md`

---

*本文档基于Evolve项目实际架构制定，确保DeepSeek API集成的最佳实践和成本效益。*

注意！注意！完成修复优化任务的时候禁止更新README.md，只在执行新建开发的任务才可以更新到README.md！所有的修复都不准采取临时方案，必须制定永久性解决方案，无法永久解决的问题请告知我。
注意！每次调试的代码在调试完记得清理，确保符合开发规范文档。
注意！在Swiftdata数据库方面必须按照开发文档规范执行，也不得随意修改Swiftdata设计框架和底层模型，需要修改和调整需要经过我同意！例如数据模型开发、状态管理、关系赋值、关系定义必须符合开发规范文档.md和IOS移动端开发规范的要求！
注意！所有操作都要遵守严格遵守“开发规范文档”！
每次执行代码修改或开发，都要告诉我做了哪些地方的修改或开发，是否符合@开发规范文档.md 
提示：在某些问题修复超过两次都没有解决，可以启用搜索功能，全网搜索和IOS开发者社区寻找参考资料，在使用搜索功能搜索全网资料的时候，如果要用年份去搜索请记得增加2025年，因为现在是2025年了。

