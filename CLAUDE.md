# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**Evolve** is a sophisticated AI-driven habit-forming iOS application built with native SwiftUI and SwiftData. The app uses an "ecological metaphor" design philosophy where habit development is visualized as ecosystem growth, featuring AI-powered personal coaching and a digital universe theme for community features.

## Core Architecture

### Technology Stack
- **Language**: Swift 5.9+ (iOS 17.0+ minimum)
- **UI Framework**: SwiftUI with modern iOS 17+ features
- **Architecture**: MVVM + Repository Pattern
- **Data Layer**: SwiftData for all persistence
- **Concurrency**: Swift Concurrency (async/await, @MainActor)
- **Dependencies**: Pure iOS native (no external package managers)

### Key Design Principles
- **Single Responsibility**: Each component focuses on one function
- **Repository Pattern**: All data access through Repository layer
- **Dependency Injection**: Dependencies passed through Environment objects
- **Reactive Programming**: @Published for data binding
- **Error Handling**: Unified error handling and user feedback

## Development Commands

Since this is a standard Xcode project without external dependencies:

```bash
# Open project in Xcode
open Evolve.xcodeproj

# Build from command line
xcodebuild -project Evolve.xcodeproj -scheme Evolve -configuration Debug

# Run tests
xcodebuild test -project Evolve.xcodeproj -scheme Evolve -destination 'platform=iOS Simulator,name=iPhone 15'

# Archive for distribution
xcodebuild archive -project Evolve.xcodeproj -scheme Evolve -archivePath Evolve.xcarchive
```

## Project Structure

```
Evolve/
├── AppEntry.swift                   # SwiftUI App entry point
├── Core/                           # Core services and shared functionality
│   ├── DataModels/                 # SwiftData @Model definitions
│   ├── Repositories/               # Data access layer with @ModelActor
│   ├── Services/                   # Business logic services
│   ├── AI/                         # AI engine core modules
│   └── Persistence/                # Database management
├── Features/                       # Feature modules by business domain
│   ├── Today/                      # Daily habit tracking
│   ├── Atlas/                      # Habit library and management
│   ├── AuraSpace/                  # AI interaction space
│   ├── Community/                  # Social features with digital universe theme
│   └── Me/                         # User profile and settings
├── UIComponents/                   # 60+ reusable SwiftUI components
└── Common/                         # Tools and extensions
```

## Critical Development Standards

### SwiftData Requirements (MUST FOLLOW)

**Single-End Inverse Rule** - The most important SwiftData rule:
- Each relationship pair uses `@Relationship(inverse:)` on **ONE END ONLY**
- The other end uses a **plain property** (no @Relationship annotation)
- SwiftData automatically maintains the bidirectional relationship

```swift
// ✅ CORRECT: One-to-many relationship
@Model
final class EAUser {
    @Relationship(deleteRule: .cascade, inverse: \EAHabit.user)
    var habits: [EAHabit]  // Only this side has @Relationship
    
    init(username: String) {
        self.username = username
        self.habits = []  // Initialize in init, not as default value
    }
}

@Model
final class EAHabit {
    var user: EAUser?  // Plain property, no @Relationship needed
    
    init(name: String) {
        self.name = name
    }
}

// ❌ FORBIDDEN: Both sides with @Relationship
@Model final class EAHabit {
    @Relationship(inverse: \EAUser.habits) var user: EAUser?  // Don't do this!
}
```

**Relationship Assignment Order (iOS 18+ Critical)**:
1. Create objects
2. Insert into ModelContext  
3. Assign relationships
4. Save context

```swift
// ✅ CORRECT: Safe relationship assignment
let user = EAUser(username: "John")
let habit = EAHabit(name: "Exercise")
modelContext.insert(user)
modelContext.insert(habit)
habit.user = user  // Assign relationship after insertion
try modelContext.save()
```

### Repository Pattern (MANDATORY)

**All data access MUST go through Repository layer**:
- ViewModels access data through Repository protocols
- Services access data through Repository protocols  
- NO direct ModelContext injection in ViewModels/Services
- Repository implementations use @ModelActor for thread safety

```swift
// ✅ CORRECT: Repository pattern
@MainActor
class EAHabitViewModel: ObservableObject {
    private let habitRepository: EAHabitRepositoryProtocol
    
    init(habitRepository: EAHabitRepositoryProtocol) {
        self.habitRepository = habitRepository
    }
    
    func loadHabits() async {
        habits = await habitRepository.fetchUserHabits()
    }
}

// ❌ FORBIDDEN: Direct ModelContext access
@MainActor
class EAHabitViewModel: ObservableObject {
    @Environment(\.modelContext) private var modelContext  // Don't do this!
}
```

### Naming Convention

**EA Prefix Rule**: All custom types must use "EA" prefix:
- Models: `EAHabit`, `EAUser`, `EACompletion`
- Views: `EAHabitDetailView`, `EAButtonView`
- ViewModels: `EAHabitViewModel`, `EAUserViewModel`
- Services: `EANetworkService`, `EAAIService`
- Components: `EAButton`, `EATextField`

### Architecture Constraints

**FORBIDDEN Patterns**:
- ❌ Singleton pattern (`static let shared`)
- ❌ Foreign key fields (`userId`, `habitId`)
- ❌ Direct ModelContext injection in ViewModels
- ❌ Global managers holding SwiftData model objects
- ❌ Multiple ModelContainer instances

**REQUIRED Patterns**:
- ✅ Repository pattern for all data access
- ✅ @MainActor for all ViewModels
- ✅ Dependency injection through Environment
- ✅ SwiftData relationships (no foreign keys)
- ✅ Shared ModelContainer across all repositories

## AI Architecture

The app features a sophisticated multi-layered AI system:

### AI Service Layers
1. **Core AI Layer**: Complex analysis, emotional recognition, crisis intervention
2. **Intelligent Rules Layer**: Local behavior pattern matching  
3. **Cache Optimization Layer**: AI result caching, user profiling
4. **Cost Control Layer**: Smart AI call management

### AI Features
- Personalized habit coaching
- Emotional state analysis
- Predictive insights
- AI-enhanced friend chat
- Content recommendation

## Common Issues & Solutions

### SwiftData Compilation Errors
**"circular reference resolving attached macro 'Relationship'"**
- Check inverse KeyPath is correct: `\TargetModel.propertyName`
- Verify property names match exactly (case-sensitive)
- Ensure only ONE end uses @Relationship(inverse:)

**"Cannot find type 'XXX' in scope"**
- Check all models are in same module
- Verify @Model annotation is present
- Check for typos in class names

### Context Consistency Issues (iOS 18+)
- All repositories must share the same ModelContainer
- Avoid independent ModelContext creation
- Use shared container passed from app entry point

### Performance Requirements
- Single query returns ≤100 records (use pagination)
- Query response time <100ms
- Memory usage <200MB during normal operation
- App startup time <1.5 seconds

## Key Files to Understand

### Core Architecture
- `Evolve/Core/Persistence/EADatabaseManager.swift` - Database management
- `Evolve/Core/Repositories/EARepositoryContainer.swift` - Repository container
- `Evolve/AppEntry.swift` - App entry and ModelContainer setup

### Data Models
- `Evolve/Core/DataModels/EAUser.swift` - User data model
- `Evolve/Core/DataModels/EAHabit.swift` - Habit data model
- `Evolve/Core/DataModels/EACompletion.swift` - Completion records

### AI Integration
- `Evolve/Core/AI/EAAIService.swift` - AI service integration
- `Evolve/Core/Services/EAAIDataBridge.swift` - AI data bridge

## Testing & Validation

### Before Committing
1. Remove all `print()` debug statements
2. Verify all ViewModels have @MainActor
3. Check SwiftData relationships follow single-end inverse rule
4. Ensure Repository pattern is used for all data access
5. Validate EA naming prefix is used consistently
6. Test on both iPhone and iPad simulators

### Code Quality Checks
- No external dependencies (pure iOS native)
- All custom types use EA prefix
- Repository pattern for data access
- @MainActor for thread safety
- Proper error handling throughout

## Development Guidelines

### When Adding New Features
1. Create Repository interface and implementation
2. Add to EARepositoryContainer
3. Use dependency injection in ViewModels
4. Follow MVVM pattern with @MainActor
5. Implement proper error handling
6. Add SwiftUI previews for all views
7. Update this CLAUDE.md file

### When Modifying SwiftData Models
1. Review all relationships carefully
2. Follow single-end inverse rule
3. Test relationship assignment order
4. Verify Context consistency
5. Test on iOS 17.0+ and 18.0+

This project represents a production-ready iOS application with enterprise-level architecture. Always prioritize data integrity, performance, and maintainability when making changes.