<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>compressed-paths</key>
	<array>
		<string>currentRun/core/stores/indexed-store-12</string>
		<string>currentRun/core/stores/indexed-store-15</string>
		<string>currentRun/core/stores/indexed-store-18</string>
		<string>currentRun/core/stores/indexed-store-2</string>
		<string>currentRun/core/stores/indexed-store-5</string>
		<string>currentRun/core/stores/indexed-store-8</string>
		<string>currentRun/core/stores/indexed-store-32</string>
		<string>currentRun/core/stores/indexed-store-35</string>
		<string>run1/core/table-manager</string>
		<string>currentRun/core/stores/indexed-store-20</string>
		<string>currentRun/core/stores/indexed-store-23</string>
		<string>currentRun/core/stores/indexed-store-26</string>
		<string>run1/core/stores/indexed-store-0</string>
		<string>run1/core/core-config</string>
		<string>currentRun/core/stores/indexed-store-29</string>
		<string>run1/core/stores/indexed-store-1</string>
		<string>currentRun/core/stores/indexed-store-11</string>
		<string>run1/core/stores/indexed-store-2</string>
		<string>run1/core/stores/indexed-store-3</string>
		<string>run1/core/stores/indexed-store-4</string>
		<string>run1/core/stores/indexed-store-5</string>
		<string>currentRun/core/stores/indexed-store-14</string>
		<string>run1/core/stores/indexed-store-6</string>
		<string>run1/core/stores/indexed-store-10</string>
		<string>run1/core/stores/indexed-store-7</string>
		<string>currentRun/core/stores/indexed-store-17</string>
		<string>run1/core/stores/indexed-store-8</string>
		<string>run1/core/stores/indexed-store-9</string>
		<string>run1/core/stores/indexed-store-11</string>
		<string>currentRun/core/stores/indexed-store-0</string>
		<string>run1/core/stores/indexed-store-12</string>
		<string>run1/core/stores/indexed-store-13</string>
		<string>currentRun/core/stores/indexed-store-3</string>
		<string>run1/core/stores/indexed-store-14</string>
		<string>currentRun/core/stores/indexed-store-6</string>
		<string>run1/core/stores/indexed-store-15</string>
		<string>run1/core/stores/indexed-store-16</string>
		<string>currentRun/core/stores/indexed-store-9</string>
		<string>currentRun/core/stores/indexed-store-31</string>
		<string>run1/core/stores/indexed-store-17</string>
		<string>run1/core/stores/indexed-store-18</string>
		<string>run1/core/stores/indexed-store-19</string>
		<string>run1/core/stores/indexed-store-20</string>
		<string>currentRun/core/stores/indexed-store-34</string>
		<string>run1/core/stores/indexed-store-21</string>
		<string>run1/core/stores/indexed-store-22</string>
		<string>run1/core/stores/indexed-store-23</string>
		<string>run1/core/stores/indexed-store-24</string>
		<string>run1/core/stores/indexed-store-25</string>
		<string>run1/core/stores/indexed-store-26</string>
		<string>run1/core/stores/indexed-store-27</string>
		<string>run1/core/stores/indexed-store-28</string>
		<string>run1/core/stores/indexed-store-29</string>
		<string>currentRun/core/stores/indexed-store-22</string>
		<string>run1/core/stores/indexed-store-30</string>
		<string>run1/core/stores/indexed-store-31</string>
		<string>currentRun/core/stores/indexed-store-25</string>
		<string>currentRun/core/stores/indexed-store-28</string>
		<string>currentRun/core/uniquing</string>
		<string>currentRun/core/uniquing</string>
		<string>currentRun/core/uniquing</string>
		<string>currentRun/core/stores/indexed-store-10</string>
		<string>currentRun/core/stores/indexed-store-13</string>
		<string>currentRun/core/stores/indexed-store-16</string>
		<string>run1/core/uniquing</string>
		<string>run1/core/uniquing</string>
		<string>run1/core/uniquing</string>
		<string>currentRun/core/stores/indexed-store-19</string>
		<string>currentRun/core/stores/indexed-store-1</string>
		<string>currentRun/core/stores/indexed-store-4</string>
		<string>currentRun/core/stores/indexed-store-7</string>
		<string>currentRun/core/stores/indexed-store-30</string>
		<string>currentRun/core/stores/indexed-store-33</string>
		<string>currentRun/core/stores/indexed-store-21</string>
		<string>currentRun/core/stores/indexed-store-24</string>
		<string>currentRun/core/stores/indexed-store-27</string>
	</array>
	<key>paths</key>
	<array>
		<string>currentRun/core/stores/indexed-store-12</string>
		<string>currentRun/core/stores/indexed-store-15</string>
		<string>currentRun/core/stores/indexed-store-18</string>
		<string>currentRun/core/stores/indexed-store-2</string>
		<string>currentRun/core/stores/indexed-store-5</string>
		<string>currentRun/core/stores/indexed-store-8</string>
		<string>currentRun/core/stores/indexed-store-32</string>
		<string>currentRun/core/stores/indexed-store-35</string>
		<string>run1/core/table-manager</string>
		<string>currentRun/core/stores/indexed-store-20</string>
		<string>currentRun/core/stores/indexed-store-23</string>
		<string>currentRun/core/stores/indexed-store-26</string>
		<string>run1/core/stores/indexed-store-0</string>
		<string>run1/core/core-config</string>
		<string>currentRun/core/stores/indexed-store-29</string>
		<string>run1/core/stores/indexed-store-1</string>
		<string>currentRun/core/stores/indexed-store-11</string>
		<string>run1/core/stores/indexed-store-2</string>
		<string>run1/core/stores/indexed-store-3</string>
		<string>run1/core/stores/indexed-store-4</string>
		<string>run1/core/stores/indexed-store-5</string>
		<string>currentRun/core/stores/indexed-store-14</string>
		<string>run1/core/stores/indexed-store-6</string>
		<string>run1/core/stores/indexed-store-10</string>
		<string>run1/core/stores/indexed-store-7</string>
		<string>currentRun/core/stores/indexed-store-17</string>
		<string>run1/core/stores/indexed-store-8</string>
		<string>run1/core/stores/indexed-store-9</string>
		<string>run1/core/stores/indexed-store-11</string>
		<string>currentRun/core/stores/indexed-store-0</string>
		<string>run1/core/stores/indexed-store-12</string>
		<string>run1/core/stores/indexed-store-13</string>
		<string>currentRun/core/stores/indexed-store-3</string>
		<string>run1/core/stores/indexed-store-14</string>
		<string>currentRun/core/stores/indexed-store-6</string>
		<string>run1/core/stores/indexed-store-15</string>
		<string>run1/core/stores/indexed-store-16</string>
		<string>currentRun/core/stores/indexed-store-9</string>
		<string>currentRun/core/stores/indexed-store-31</string>
		<string>run1/core/stores/indexed-store-17</string>
		<string>run1/core/stores/indexed-store-18</string>
		<string>run1/core/stores/indexed-store-19</string>
		<string>run1/core/stores/indexed-store-20</string>
		<string>currentRun/core/stores/indexed-store-34</string>
		<string>run1/core/stores/indexed-store-21</string>
		<string>run1/core/stores/indexed-store-22</string>
		<string>run1/core/stores/indexed-store-23</string>
		<string>run1/core/stores/indexed-store-24</string>
		<string>run1/core/stores/indexed-store-25</string>
		<string>run1/core/stores/indexed-store-26</string>
		<string>run1/core/stores/indexed-store-27</string>
		<string>run1/core/stores/indexed-store-28</string>
		<string>run1/core/stores/indexed-store-29</string>
		<string>currentRun/core/stores/indexed-store-22</string>
		<string>run1/core/stores/indexed-store-30</string>
		<string>run1/core/stores/indexed-store-31</string>
		<string>currentRun/core/stores/indexed-store-25</string>
		<string>currentRun/core/stores/indexed-store-28</string>
		<string>currentRun/core/uniquing</string>
		<string>currentRun/core/stores/indexed-store-10</string>
		<string>currentRun/core/stores/indexed-store-13</string>
		<string>currentRun/core/stores/indexed-store-16</string>
		<string>run1/core/uniquing</string>
		<string>currentRun/core/stores/indexed-store-19</string>
		<string>currentRun/core/stores/indexed-store-1</string>
		<string>currentRun/core/stores/indexed-store-4</string>
		<string>currentRun/core/stores/indexed-store-7</string>
		<string>currentRun/core/stores/indexed-store-30</string>
		<string>currentRun/core/stores/indexed-store-33</string>
		<string>currentRun/core/stores/indexed-store-21</string>
		<string>currentRun/core/stores/indexed-store-24</string>
		<string>currentRun/core/stores/indexed-store-27</string>
	</array>
</dict>
</plist>
