# Evolve AI - 智能习惯养成应用

<div align="center">

![iOS](https://img.shields.io/badge/iOS-17.0+-blue.svg)
![Swift](https://img.shields.io/badge/Swift-5.9+-orange.svg)
![SwiftUI](https://img.shields.io/badge/SwiftUI-5.0+-green.svg)
![SwiftData](https://img.shields.io/badge/SwiftData-1.0+-purple.svg)
![License](https://img.shields.io/badge/License-Private-red.svg)

**基于AI驱动的个人习惯养成应用，采用"生态隐喻"和"能量流转"设计理念**

</div>

## 📋 项目概述

Evolve AI是一款创新的iOS习惯养成应用，通过深度AI集成和数字宇宙主题设计，为用户提供个性化的习惯培养体验。应用采用现代化的SwiftUI + SwiftData技术栈，遵循MVVM架构模式，实现了完整的用户认证、习惯管理、AI对话、社区互动和好友系统。

### 🎯 核心特性

- **🤖 AI深度集成**：AI作为私人教练，提供个性化指导和情境感知
- **🌱 生态化设计**：习惯养成过程可视化为生态系统的成长
- **⚡ 能量流转系统**：通过完成习惯获得星际能量，提升用户等级
- **👥 星际伙伴系统**：好友功能融入数字宇宙主题，支持AI增强聊天
- **🌌 社区共振**：用户分享习惯成果，形成能量共振效应
- **📊 数据驱动**：基于用户行为数据提供智能分析和建议

## 🏗️ 技术架构

### 核心技术栈

- **编程语言**：Swift 5.9+
- **UI框架**：SwiftUI（iOS 17.0+）
- **架构模式**：MVVM + Repository Pattern
- **数据持久化**：SwiftData
- **并发处理**：Swift Concurrency (async/await, @MainActor)
- **AI集成**：DeepSeek API
- **依赖注入**：Environment-based DI

### 架构设计原则

- **Repository模式强制执行**：所有数据访问通过Repository层
- **SwiftData关系设计**：遵循单端inverse规则，避免外键模式
- **依赖注入优先**：禁止单例模式，使用Environment注入
- **Context一致性**：共享ModelContainer确保数据一致性
- **线程安全**：所有ViewModel标记@MainActor

## 📱 功能模块

### ✅ 已完成功能

#### 🔐 用户认证系统
- [x] 用户注册/登录（手机号+密码）
- [x] 会话管理和状态持久化
- [x] 用户引导流程（Onboarding）
- [x] 密码重置功能
- [x] 自动登录和会话恢复

#### 📊 习惯管理系统
- [x] 习惯创建和编辑（AI引导式）
- [x] 多种频率模式（每日/每周/自定义）
- [x] 图标分类选择（8个分类，丰富图标库）
- [x] 习惯完成打卡
- [x] 进度统计和可视化
- [x] 习惯详情和历史记录

#### 🤖 AI对话系统
- [x] AI私人教练对话
- [x] 个性化习惯建议
- [x] 情境感知回复
- [x] 对话历史管理
- [x] AI头像和动画效果
- [x] 快速回复功能

#### 🌌 社区功能
- [x] 帖子发布和浏览
- [x] 点赞和评论系统
- [x] 用户关注功能
- [x] 内容推荐算法
- [x] 图片上传和展示
- [x] 社区内容管理

#### 👥 好友系统
- [x] 好友请求发送/接收
- [x] 好友列表管理
- [x] 好友搜索功能
- [x] 好友聊天（AI增强）
- [x] 好友屏蔽功能
- [x] 星际伙伴主题UI

#### ⚙️ 系统功能
- [x] 用户设置和偏好
- [x] 通知系统
- [x] 数据缓存策略
- [x] 错误处理机制
- [x] 性能监控
- [x] 深色模式支持

### 🚧 开发中功能

- [ ] AI成本优化和缓存策略完善
- [ ] 社区挑战系统
- [ ] 高级数据分析
- [ ] 付费功能集成（StoreKit 2）
- [ ] 推送通知优化

## 📁 项目结构

```
Evolve/
├── Evolve/
│   ├── AppEntry.swift                 # 应用入口和依赖注入配置
│   ├── Core/                          # 核心模块
│   │   ├── DataModels/               # SwiftData数据模型
│   │   ├── Services/                 # 业务服务层
│   │   ├── Persistence/              # 数据持久化管理
│   │   └── Repository/               # Repository层实现
│   ├── Features/                      # 功能模块
│   │   ├── Authentication/           # 用户认证
│   │   ├── MainTab/                  # 主Tab导航
│   │   ├── Today/                    # 今日页面
│   │   ├── Atlas/                    # 习惯图鉴
│   │   ├── AuraSpace/                # AI灵境
│   │   ├── Community/                # 社区功能
│   │   ├── Friends/                  # 好友系统
│   │   └── Profile/                  # 用户中心
│   ├── UIComponents/                 # 可复用UI组件
│   └── Utils/                        # 工具类和扩展
├── 技术架构文档.md                    # 详细技术架构说明
├── 开发规范文档.md                    # 开发规范和约束
├── .cursorrules                      # 核心开发规范（AI必读）
└── API.md                           # DeepSeek API集成文档
```

## 🔧 开发环境配置

### 系统要求

- macOS 14.0+
- Xcode 15.0+
- iOS 17.0+ 部署目标
- Swift 5.9+

### 快速开始

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd Evolve
   ```

2. **打开项目**
   ```bash
   open Evolve.xcodeproj
   ```

3. **配置API密钥**
   - 在`API.md`中查看DeepSeek API配置
   - 确保API密钥正确配置

4. **编译运行**
   - 选择iOS模拟器或真机
   - 按⌘+R运行项目

## 📋 开发规范

### 核心约束（必须严格遵循）

1. **SwiftData关系设计**
   - 使用@Relationship而非外键字段
   - 遵循单端inverse规则
   - 关系赋值在对象插入ModelContext后进行

2. **架构模式**
   - 所有数据访问通过Repository层
   - 禁止Service直接操作ModelContext
   - ViewModel必须标记@MainActor

3. **依赖注入**
   - 严格禁止单例模式
   - 使用Environment进行依赖注入
   - 共享ModelContainer确保Context一致性

4. **代码质量**
   - 所有类型使用EA前缀命名
   - 清理所有print()调试语句
   - 完整的错误处理和降级策略

详细规范请参考：`.cursorrules`和`开发规范文档.md`

## 🎯 开发进度

### 已完成里程碑

- ✅ **阶段一**：基础项目结构与SwiftData集成
- ✅ **阶段二**：用户认证与会话管理
- ✅ **阶段三**：习惯管理核心功能
- ✅ **阶段四**：AI对话基础框架
- ✅ **阶段五**：社区功能完整实现
- ✅ **阶段六**：好友系统集成

### 当前状态

项目已完成核心功能开发，正在进行架构优化和性能提升阶段。主要关注：

- 🔧 架构一致性优化
- 🔧 AI成本控制和缓存策略
- 🔧 用户体验优化
- 🔧 性能监控和优化

## 🐛 已知问题

### 已解决问题

- ✅ SwiftData Context冲突导致的崩溃
- ✅ 用户会话管理竞态条件
- ✅ 好友功能ID使用规范问题
- ✅ 社区功能性能优化
- ✅ AI对话历史管理

### 待优化项目

- 🔄 AI API调用成本优化
- 🔄 大数据量场景下的性能优化
- 🔄 网络异常处理增强
- 🔄 UI动画性能优化

## 📚 相关文档

- [技术架构文档](技术架构文档.md) - 详细的技术架构设计
- [开发规范文档](开发规范文档.md) - 完整的开发规范和约束
- [API集成文档](API.md) - DeepSeek API集成指南
- [好友功能文档](社区功能好友功能集成文档.md) - 好友系统设计文档
- [ID使用规范](Evolve项目ID使用规范文档.md) - 项目ID使用规范

## 🤝 开发团队

本项目采用AI辅助开发模式，严格遵循项目规范和架构约束，确保代码质量和系统稳定性。

## 📄 许可证

本项目为私有项目，版权所有。

---

**最后更新时间**：2025-07-13
**文档版本**：v1.0
**项目状态**：架构优化阶段