/* 
  Localizable.strings
  Evolve

  Created by AI Assistant on 2025-07-13.
  通知系统本地化字符串
*/

// MARK: - 习惯提醒通知
"notification.habit.title" = "计划提醒";
"notification.habit.body.format" = "该完成「%@」了！坚持就是胜利 💪";
"notification.habit.body.motivational.1" = "该完成「%@」了！我相信你能做到 💪";
"notification.habit.body.motivational.2" = "「%@」时间到啦！让我们一起坚持下去 ✨";
"notification.habit.body.motivational.3" = "提醒：记得完成今天的「%@」哦 🌟";
"notification.habit.body.motivational.4" = "「%@」正在等待你的行动！加油 🚀";
"notification.habit.body.motivational.5" = "时间提醒：「%@」，每一次坚持都是成长 🌱";

// MARK: - 好友通知
"notification.friend.request.title" = "新的好友请求";
"notification.friend.request.body.with_message" = "%@ 想要添加您为好友：%@";
"notification.friend.request.body.without_message" = "%@ 想要添加您为好友";
"notification.friend.message.title" = "新消息";
"notification.friend.accepted.title" = "好友请求已接受";
"notification.friend.rejected.title" = "好友请求被拒绝";

// MARK: - 权限相关
"notification.permission.denied.title" = "通知权限被拒绝";
"notification.permission.denied.message" = "请在设置中开启通知权限以接收提醒";
"notification.permission.settings.button" = "前往设置";

// MARK: - 错误消息
"notification.error.schedule_failed" = "提醒设置失败，请稍后重试";
"notification.error.permission_required" = "需要通知权限才能设置提醒";
