//
//  EASheetManager.swift
//  Evolve
//
//  Created by AI Assistant on 2025-01-27.
//  统一Sheet状态管理系统，符合.cursorrules规范
//

import SwiftUI
import SwiftData

// MARK: - Sheet类型枚举（遵循EA命名规范）

enum EASheetType: Identifiable {
    // 习惯相关Sheet
    case habitCreation
    case habitEdit(EAHabit)
    case habitReminderSettings(EAHabit)
    
    // 时间选择相关Sheet
    case timePicker
    case multiTimeReminder(EAHabit)
    
    // 内容库相关Sheet
    case contentLibrary
    case proUpgrade
    
    // 分享相关Sheet
    case shareSheet
    case achievementDetail
    
    // 成就和庆祝相关Sheet
    case eventDetail
    
    var id: String {
        switch self {
        case .habitCreation:
            return "habitCreation"
        case .habitEdit(let habit):
            return "habitEdit_\(habit.id)"
        case .habitReminderSettings(let habit):
            return "habitReminderSettings_\(habit.id)"
        case .timePicker:
            return "timePicker"
        case .multiTimeReminder(let habit):
            return "multiTimeReminder_\(habit.id)"
        case .contentLibrary:
            return "contentLibrary"
        case .proUpgrade:
            return "proUpgrade"
        case .shareSheet:
            return "shareSheet"
        case .achievementDetail:
            return "achievementDetail"
        case .eventDetail:
            return "eventDetail"
        }
    }
}

// MARK: - Alert类型枚举

enum EAAlertType: Identifiable {
    case deleteHabit(EAHabit)
    case levelUpCelebration(oldLevel: Int, newLevel: Int, newTitle: String)
    case badgeEarned(title: String, description: String)
    case error(message: String)
    case success(message: String)
    
    var id: String {
        switch self {
        case .deleteHabit(let habit):
            return "deleteHabit_\(habit.id)"
        case .levelUpCelebration:
            return "levelUpCelebration"
        case .badgeEarned:
            return "badgeEarned"
        case .error:
            return "error"
        case .success:
            return "success"
        }
    }
}

// MARK: - Sheet管理器（遵循@MainActor线程安全要求）

@MainActor
class EASheetManager: ObservableObject {
    
    // MARK: - 核心状态（单一状态源）
    
    @Published var activeSheet: EASheetType?
    @Published var activeAlert: EAAlertType?
    
    // ✅ 修复：添加nonisolated init，解决EnvironmentKey初始化问题
    nonisolated init() {
        // 初始化时不需要设置值，@Published属性自动为nil
    }
    
    // MARK: - Sheet操作方法
    
    /// 显示Sheet
    @MainActor
    func presentSheet(_ sheetType: EASheetType) {
        activeSheet = sheetType
    }
    
    /// 关闭Sheet
    @MainActor
    func dismissSheet() {
        activeSheet = nil
    }
    
    /// 显示Alert
    @MainActor
    func presentAlert(_ alertType: EAAlertType) {
        activeAlert = alertType
    }
    
    /// 关闭Alert
    @MainActor
    func dismissAlert() {
        activeAlert = nil
    }
    
    // MARK: - 便捷方法
    
    /// 显示计划编辑Sheet
    @MainActor
    func showHabitEdit(_ habit: EAHabit) {
        presentSheet(.habitEdit(habit))
    }
    
    /// 显示计划提醒设置Sheet
    @MainActor
    func showHabitReminderSettings(_ habit: EAHabit) {
        presentSheet(.habitReminderSettings(habit))
    }
    
    /// 显示删除确认Alert
    @MainActor
    func showDeleteConfirmation(_ habit: EAHabit) {
        presentAlert(.deleteHabit(habit))
    }
    
    /// 显示等级提升庆祝
    @MainActor
    func showLevelUpCelebration(oldLevel: Int, newLevel: Int, newTitle: String) {
        presentAlert(.levelUpCelebration(oldLevel: oldLevel, newLevel: newLevel, newTitle: newTitle))
    }
    
    /// 显示徽章获得庆祝
    @MainActor
    func showBadgeEarned(title: String, description: String) {
        presentAlert(.badgeEarned(title: title, description: description))
    }
}

// MARK: - Environment扩展

extension EnvironmentValues {
    private struct EASheetManagerKey: EnvironmentKey {
        static let defaultValue = EASheetManager()
    }
    
    var sheetManager: EASheetManager {
        get { self[EASheetManagerKey.self] }
        set { self[EASheetManagerKey.self] = newValue }
    }
}

// MARK: - 便捷访问扩展

extension View {
    /// 添加统一的Sheet管理支持
    func withSheetManager(_ sheetManager: EASheetManager) -> some View {
        self
            .environment(\.sheetManager, sheetManager)
            .sheet(item: Binding<EASheetType?>(
                get: { sheetManager.activeSheet },
                set: { _ in sheetManager.dismissSheet() }
            )) { (sheetType: EASheetType) in
                sheetContent(for: sheetType)
            }
            .alert(item: Binding<EAAlertType?>(
                get: { sheetManager.activeAlert },
                set: { _ in sheetManager.dismissAlert() }
            )) { (alertType: EAAlertType) in
                alertContent(for: alertType, sheetManager: sheetManager)
            }
    }
}

// MARK: - Sheet内容构建器

@ViewBuilder
private func sheetContent(for sheetType: EASheetType) -> some View {
    switch sheetType {
    case .habitCreation:
        // ✅ 关键修复：暂时显示占位内容，需要传入正确的依赖
        VStack {
            Text("创建新计划")
                .font(.title2)
                .padding()
            Text("请通过图鉴页面创建计划")
                .foregroundColor(.secondary)
            Spacer()
        }
        .padding()
    
    case .habitEdit(let habit):
        // 简化的计划编辑界面
        VStack {
            Text("编辑计划: \(habit.name)")
                .font(.title2)
                .padding()
            Text("计划编辑功能开发中...")
                .foregroundColor(.secondary)
            Spacer()
        }
        .padding()
    
    case .habitReminderSettings(let habit):
        // 使用现有的提醒设置视图
        EAHabitReminderSettingsView(habit: habit)
    
    case .timePicker:
        Text("时间选择器")
            .padding()
    
    case .multiTimeReminder:
        Text("多时间提醒设置")
            .padding()
    
    case .contentLibrary:
        // 使用现有的内容库视图
        EAContentLibraryView()
    
    case .proUpgrade:
        // 使用现有的Pro会员视图
        EAProMembershipView()
    
    case .shareSheet:
        Text("分享功能")
            .padding()
    
    case .achievementDetail:
        Text("成就详情")
            .padding()
    
    case .eventDetail:
        Text("事件详情")
            .padding()
    }
}

// MARK: - Alert内容构建器

private func alertContent(for alertType: EAAlertType, sheetManager: EASheetManager) -> Alert {
    switch alertType {
    case .deleteHabit(let habit):
        return Alert(
            title: Text("确认删除"),
            message: Text("您确定要删除计划\"\(habit.name)\"吗？此操作不可逆。"),
            primaryButton: .destructive(Text("删除")) {
                // 发送删除通知
                NotificationCenter.default.post(
                    name: NSNotification.Name("ConfirmHabitDelete"),
                    object: habit
                )
            },
            secondaryButton: .cancel(Text("取消"))
        )
    
    case .levelUpCelebration(let oldLevel, let newLevel, let newTitle):
        return Alert(
            title: Text("🎉 等级提升！"),
            message: Text("恭喜您从等级\(oldLevel)提升至等级\(newLevel)！\n现在您是\(newTitle)了！"),
            dismissButton: .default(Text("太棒了！"))
        )
    
    case .badgeEarned(let title, let description):
        return Alert(
            title: Text("🏆 获得徽章！"),
            message: Text("恭喜获得徽章：\(title)\n\(description)"),
            primaryButton: .default(Text("查看详情")),
            secondaryButton: .cancel(Text("稍后查看"))
        )
    
    case .error(let message):
        return Alert(
            title: Text("错误"),
            message: Text(message),
            dismissButton: .default(Text("确定"))
        )
    
    case .success(let message):
        return Alert(
            title: Text("成功"),
            message: Text(message),
            dismissButton: .default(Text("确定"))
        )
    }
} 