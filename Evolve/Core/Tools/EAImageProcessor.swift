import UIKit
import Foundation

/// 图片处理工具类
/// 提供图片压缩、格式转换、存储管理等功能
/// 遵循社交APP图片上传通用规范
@MainActor
final class EAImageProcessor {
    
    // MARK: - 常量定义
    
    /// 图片上传限制
    enum ImageLimits {
        static let maxImageCount = 9        // 最多9张图片
        static let maxFileSize = 10 * 1024 * 1024  // 10MB
        static let maxDimension: CGFloat = 1920     // 最大尺寸1920px
        static let minDimension: CGFloat = 200      // 最小尺寸200px
        static let compressionQuality: CGFloat = 0.8 // 压缩质量
    }
    
    /// 支持的图片格式
    enum SupportedFormat: String, CaseIterable {
        case jpeg = "jpeg"
        case png = "png"
        case heic = "heic"
        
        var fileExtension: String {
            switch self {
            case .jpeg: return "jpg"
            case .png: return "png"
            case .heic: return "heic"
            }
        }
    }
    
    /// 图片处理错误
    enum ImageProcessingError: LocalizedError {
        case invalidFormat
        case fileTooLarge
        case imageTooSmall
        case compressionFailed
        case saveFailed
        case invalidImageData
        
        var errorDescription: String? {
            switch self {
            case .invalidFormat:
                return "不支持的图片格式，请选择 JPG、PNG 或 HEIC 格式"
            case .fileTooLarge:
                return "图片文件过大，请选择小于 10MB 的图片"
            case .imageTooSmall:
                return "图片尺寸过小，最小支持 200x200 像素"
            case .compressionFailed:
                return "图片压缩失败，请重试"
            case .saveFailed:
                return "图片保存失败，请检查存储空间"
            case .invalidImageData:
                return "图片数据无效，请选择其他图片"
            }
        }
    }
    
    // MARK: - 初始化
    
    init() {}
    
    // MARK: - 公共方法
    
    /// 处理并保存图片
    /// - Parameter imageData: 原始图片数据
    /// - Returns: 保存后的图片相对路径
    func processAndSaveImage(_ imageData: Data) async throws -> String {
        // 1. 验证图片格式和大小
        try validateImageData(imageData)
        
        // 2. 创建UIImage对象
        guard let originalImage = UIImage(data: imageData) else {
            throw ImageProcessingError.invalidImageData
        }
        
        // 3. 验证图片尺寸
        try validateImageSize(originalImage)
        
        // 4. 压缩处理图片
        let processedImage = try await processImage(originalImage)
        
        // 5. 转换为JPEG数据
        guard let compressedData = processedImage.jpegData(compressionQuality: ImageLimits.compressionQuality) else {
            throw ImageProcessingError.compressionFailed
        }
        
        // 6. 保存到本地
        let relativePath = try await saveImageToLocal(compressedData)
        
        return relativePath
    }
    
    /// 批量处理图片
    /// - Parameter imagesData: 图片数据数组
    /// - Returns: 保存后的图片相对路径数组
    func processBatchImages(_ imagesData: [Data]) async throws -> [String] {
        // 验证图片数量
        guard imagesData.count <= ImageLimits.maxImageCount else {
            throw ImageProcessingError.fileTooLarge
        }
        
        var imagePaths: [String] = []
        
        for imageData in imagesData {
            let path = try await processAndSaveImage(imageData)
            imagePaths.append(path)
        }
        
        return imagePaths
    }
    
    /// 从本地路径加载图片
    /// - Parameter relativePath: 相对路径
    /// - Returns: UIImage对象
    func loadImageFromPath(_ relativePath: String) -> UIImage? {
        let fullPath = getFullImagePath(relativePath)
        return UIImage(contentsOfFile: fullPath)
    }
    
    /// 删除本地图片
    /// - Parameter relativePath: 相对路径
    func deleteImage(at relativePath: String) {
        let fullPath = getFullImagePath(relativePath)
        try? FileManager.default.removeItem(atPath: fullPath)
    }
    
    /// 批量删除图片
    /// - Parameter relativePaths: 相对路径数组
    func deleteBatchImages(_ relativePaths: [String]) {
        for path in relativePaths {
            deleteImage(at: path)
        }
    }
    
    /// 清理无效图片（清理机制）
    func cleanupOrphanedImages(validPaths: [String]) {
        let imagesDir = getImagesDirectory()
        
        do {
            let allFiles = try FileManager.default.contentsOfDirectory(atPath: imagesDir)
            let validFileNames = Set(validPaths.compactMap { URL(string: $0)?.lastPathComponent })
            
            for fileName in allFiles {
                if !validFileNames.contains(fileName) {
                    let fullPath = (imagesDir as NSString).appendingPathComponent(fileName)
                    try? FileManager.default.removeItem(atPath: fullPath)
                }
            }
        } catch {
            // 静默处理清理错误
        }
    }
    
    // MARK: - 私有方法
    
    /// 验证图片数据
    private func validateImageData(_ data: Data) throws {
        // 检查文件大小
        guard data.count <= ImageLimits.maxFileSize else {
            throw ImageProcessingError.fileTooLarge
        }
        
        // 检查图片格式（通过文件头）
        guard isValidImageFormat(data) else {
            throw ImageProcessingError.invalidFormat
        }
    }
    
    /// 验证图片格式
    private func isValidImageFormat(_ data: Data) -> Bool {
        guard data.count >= 4 else { return false }
        
        let bytes = data.prefix(4)
        
        // JPEG: FF D8 FF
        if bytes[0] == 0xFF && bytes[1] == 0xD8 && bytes[2] == 0xFF {
            return true
        }
        
        // PNG: 89 50 4E 47
        if bytes[0] == 0x89 && bytes[1] == 0x50 && bytes[2] == 0x4E && bytes[3] == 0x47 {
            return true
        }
        
        // HEIC格式检测需要更复杂的逻辑，暂时通过UIImage创建来验证
        return UIImage(data: data) != nil
    }
    
    /// 验证图片尺寸
    private func validateImageSize(_ image: UIImage) throws {
        let size = image.size
        let minDim = min(size.width, size.height)
        
        guard minDim >= ImageLimits.minDimension else {
            throw ImageProcessingError.imageTooSmall
        }
    }
    
    /// 处理图片（压缩和调整尺寸）
    private func processImage(_ image: UIImage) async throws -> UIImage {
        return await Task.detached {
            let originalSize = image.size
            let maxDim = ImageLimits.maxDimension
            
            // 如果图片尺寸超过限制，进行等比缩放
            if max(originalSize.width, originalSize.height) > maxDim {
                let scale = maxDim / max(originalSize.width, originalSize.height)
                let newSize = CGSize(
                    width: originalSize.width * scale,
                    height: originalSize.height * scale
                )
                
                return image.resized(to: newSize) ?? image
            }
            
            return image
        }.value
    }
    
    /// 保存图片到本地
    private func saveImageToLocal(_ data: Data) async throws -> String {
        let fileName = "\(UUID().uuidString).jpg"
        let imagesDir = getImagesDirectory()
        let fullPath = (imagesDir as NSString).appendingPathComponent(fileName)
        
        // 确保目录存在
        try createImagesDirectoryIfNeeded()
        
        // 保存文件
        let success = FileManager.default.createFile(atPath: fullPath, contents: data, attributes: nil)
        
        guard success else {
            throw ImageProcessingError.saveFailed
        }
        
        return "Images/\(fileName)"
    }
    
    /// 获取图片存储目录
    private func getImagesDirectory() -> String {
        let documentsPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)[0]
        return (documentsPath as NSString).appendingPathComponent("Images")
    }
    
    /// 创建图片目录
    private func createImagesDirectoryIfNeeded() throws {
        let imagesDir = getImagesDirectory()
        if !FileManager.default.fileExists(atPath: imagesDir) {
            try FileManager.default.createDirectory(atPath: imagesDir, withIntermediateDirectories: true, attributes: nil)
        }
    }
    
    /// 获取图片完整路径
    private func getFullImagePath(_ relativePath: String) -> String {
        let documentsPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)[0]
        return (documentsPath as NSString).appendingPathComponent(relativePath)
    }
}

 