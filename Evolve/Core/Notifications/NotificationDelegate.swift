import Foundation
import UserNotifications

// MARK: - 通知代理
class NotificationDelegate: NSObject, UNUserNotificationCenterDelegate {
    // 移除单例模式
    // static let shared = NotificationDelegate()
    
    // 改为公开初始化器，支持依赖注入
    override init() {
        super.init()
    }
    
    // MARK: - UNUserNotificationCenterDelegate
    
    /// 应用在前台时收到通知的处理
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // 在前台显示通知横幅和声音
        completionHandler([.banner, .sound])
    }
    
    /// 用户点击通知的处理
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        let userInfo = response.notification.request.content.userInfo
        
        // 处理不同类型的通知
        if let notificationType = userInfo["type"] as? String {
            switch notificationType {
            case "habit_reminder":
                handleHabitReminderTap(userInfo: userInfo)
            case "intervention":
                handleInterventionTap(userInfo: userInfo)
            default:
                break
            }
        }
        
        completionHandler()
    }
    
    // MARK: - 私有方法
    
    /// 处理习惯提醒通知点击
    private func handleHabitReminderTap(userInfo: [AnyHashable: Any]) {
        guard let habitIdString = userInfo["habitId"] as? String,
              let habitId = UUID(uuidString: habitIdString) else {
            return
        }
        
        // 发送通知，让应用导航到对应的习惯
        NotificationCenter.default.post(
            name: NSNotification.Name("NavigateToHabit"),
            object: nil,
            userInfo: ["habitId": habitId]
        )
    }
    
    /// 处理干预通知点击
    private func handleInterventionTap(userInfo: [AnyHashable: Any]) {
        // 导航到AI对话页面
        NotificationCenter.default.post(
            name: NSNotification.Name("NavigateToAuraSpace"),
            object: nil
        )
    }
} 