//
//  EANotificationLocalizer.swift
//  Evolve
//
//  Created by AI Assistant on 2025-07-13.
//

import Foundation

/// 通知内容本地化管理器
/// 负责生成本地化的通知标题和内容
struct EANotificationLocalizer {
    
    // MARK: - 习惯提醒本地化
    
    /// 生成习惯提醒通知标题
    static func habitReminderTitle() -> String {
        return NSLocalizedString("notification.habit.title", comment: "习惯提醒通知标题")
    }
    
    /// 生成习惯提醒通知内容（随机激励语句）
    static func habitReminderBody(for habitName: String) -> String {
        let motivationalKeys = [
            "notification.habit.body.motivational.1",
            "notification.habit.body.motivational.2",
            "notification.habit.body.motivational.3",
            "notification.habit.body.motivational.4",
            "notification.habit.body.motivational.5"
        ]
        
        let randomKey = motivationalKeys.randomElement() ?? motivationalKeys[0]
        let template = NSLocalizedString(randomKey, comment: "习惯提醒激励语句")
        
        return String(format: template, habitName)
    }
    
    /// 生成基础习惯提醒内容
    static func basicHabitReminderBody(for habitName: String) -> String {
        let template = NSLocalizedString("notification.habit.body.format", comment: "基础习惯提醒内容")
        return String(format: template, habitName)
    }
    
    // MARK: - 好友通知本地化
    
    /// 生成好友请求通知标题
    static func friendRequestTitle() -> String {
        return NSLocalizedString("notification.friend.request.title", comment: "好友请求通知标题")
    }
    
    /// 生成好友请求通知内容
    static func friendRequestBody(senderName: String, message: String?) -> String {
        if let message = message, !message.isEmpty {
            let template = NSLocalizedString("notification.friend.request.body.with_message", comment: "带消息的好友请求")
            return String(format: template, senderName, message)
        } else {
            let template = NSLocalizedString("notification.friend.request.body.without_message", comment: "无消息的好友请求")
            return String(format: template, senderName)
        }
    }
    
    /// 生成新消息通知标题
    static func newMessageTitle() -> String {
        return NSLocalizedString("notification.friend.message.title", comment: "新消息通知标题")
    }
    
    // MARK: - 权限相关本地化
    
    /// 生成权限被拒绝提示标题
    static func permissionDeniedTitle() -> String {
        return NSLocalizedString("notification.permission.denied.title", comment: "权限被拒绝标题")
    }
    
    /// 生成权限被拒绝提示内容
    static func permissionDeniedMessage() -> String {
        return NSLocalizedString("notification.permission.denied.message", comment: "权限被拒绝消息")
    }
    
    /// 生成前往设置按钮文本
    static func settingsButtonText() -> String {
        return NSLocalizedString("notification.permission.settings.button", comment: "前往设置按钮")
    }
    
    // MARK: - 错误消息本地化
    
    /// 生成调度失败错误消息
    static func scheduleFailedMessage() -> String {
        return NSLocalizedString("notification.error.schedule_failed", comment: "调度失败错误")
    }
    
    /// 生成权限需要错误消息
    static func permissionRequiredMessage() -> String {
        return NSLocalizedString("notification.error.permission_required", comment: "权限需要错误")
    }
}
