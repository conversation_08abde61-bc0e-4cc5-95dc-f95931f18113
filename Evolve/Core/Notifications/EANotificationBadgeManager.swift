//
//  EANotificationBadgeManager.swift
//  Evolve
//
//  Created by AI Assistant on 2025-07-13.
//

import Foundation
import UIKit
import UserNotifications

/// 通知Badge统一管理器
/// 负责应用图标Badge数字的统一管理和更新
@MainActor
class EANotificationBadgeManager: ObservableObject {
    
    // MARK: - 单例模式
    
    static let shared = EANotificationBadgeManager()
    
    // MARK: - 状态管理
    
    @Published var totalBadgeCount: Int = 0
    @Published var habitReminderCount: Int = 0
    @Published var friendNotificationCount: Int = 0
    @Published var messageCount: Int = 0
    
    private let notificationCenter = UNUserNotificationCenter.current()
    
    // MARK: - 初始化
    
    private init() {
        // 启动时更新Badge计数
        Task {
            await updateTotalBadgeCount()
        }
    }
    
    // MARK: - Badge计数管理
    
    /// 更新习惯提醒Badge计数
    func updateHabitReminderCount(_ count: Int) {
        habitReminderCount = count
        Task {
            await updateTotalBadgeCount()
        }
    }
    
    /// 更新好友通知Badge计数
    func updateFriendNotificationCount(_ count: Int) {
        friendNotificationCount = count
        Task {
            await updateTotalBadgeCount()
        }
    }
    
    /// 更新消息Badge计数
    func updateMessageCount(_ count: Int) {
        messageCount = count
        Task {
            await updateTotalBadgeCount()
        }
    }
    
    /// 增加Badge计数
    func incrementBadgeCount(for type: BadgeType) {
        switch type {
        case .habitReminder:
            habitReminderCount += 1
        case .friendNotification:
            friendNotificationCount += 1
        case .message:
            messageCount += 1
        }
        
        Task {
            await updateTotalBadgeCount()
        }
    }
    
    /// 减少Badge计数
    func decrementBadgeCount(for type: BadgeType) {
        switch type {
        case .habitReminder:
            habitReminderCount = max(0, habitReminderCount - 1)
        case .friendNotification:
            friendNotificationCount = max(0, friendNotificationCount - 1)
        case .message:
            messageCount = max(0, messageCount - 1)
        }
        
        Task {
            await updateTotalBadgeCount()
        }
    }
    
    /// 清除特定类型的Badge计数
    func clearBadgeCount(for type: BadgeType) {
        switch type {
        case .habitReminder:
            habitReminderCount = 0
        case .friendNotification:
            friendNotificationCount = 0
        case .message:
            messageCount = 0
        }
        
        Task {
            await updateTotalBadgeCount()
        }
    }
    
    /// 清除所有Badge计数
    func clearAllBadges() {
        habitReminderCount = 0
        friendNotificationCount = 0
        messageCount = 0
        
        Task {
            await updateTotalBadgeCount()
        }
    }
    
    // MARK: - 系统Badge更新
    
    /// 更新总Badge计数并同步到系统
    private func updateTotalBadgeCount() async {
        let newTotal = habitReminderCount + friendNotificationCount + messageCount
        totalBadgeCount = newTotal
        
        // 更新应用图标Badge
        UIApplication.shared.applicationIconBadgeNumber = newTotal
        
        #if DEBUG
        print("🔢 [BadgeManager] Badge计数更新: 总计=\(newTotal), 习惯=\(habitReminderCount), 好友=\(friendNotificationCount), 消息=\(messageCount)")
        #endif
    }
    
    /// 从系统获取当前Badge计数
    func getCurrentBadgeCount() -> Int {
        return UIApplication.shared.applicationIconBadgeNumber
    }
    
    // MARK: - Badge类型枚举
    
    enum BadgeType {
        case habitReminder
        case friendNotification
        case message
        
        var displayName: String {
            switch self {
            case .habitReminder:
                return "习惯提醒"
            case .friendNotification:
                return "好友通知"
            case .message:
                return "消息"
            }
        }
    }
}
