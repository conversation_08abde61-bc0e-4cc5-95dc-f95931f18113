import SwiftUI
import UIKit

/// 应用全局常量定义
/// 包含尺寸规范、字体规范、动画参数等（颜色系统使用Assets.xcassets）
struct EAAppConstants {
    
    // MARK: - 尺寸规范
    
    /// 尺寸常量，遵循iOS设计规范
    struct Dimensions {
        
        // MARK: - Tab Bar规范
        
        /// Tab Bar标准高度（不含安全区域）
        static let tabBarHeight: CGFloat = 49
        
        /// Tab Bar字体大小
        static let tabBarFontSize: CGFloat = 10
        
        // MARK: - 间距系统
        
        /// 基础间距单位
        static let baseSpacing: CGFloat = 8
        
        /// 标准内边距
        static let standardPadding: CGFloat = 16
        
        /// 大间距
        static let largeSpacing: CGFloat = 24
        
        /// 超大间距
        static let extraLargeSpacing: CGFloat = 32
        
        // MARK: - 圆角规范
        
        /// 标准圆角半径
        static let standardCornerRadius: CGFloat = 12
        
        /// 小圆角半径
        static let smallCornerRadius: CGFloat = 8
        
        /// 大圆角半径
        static let largeCornerRadius: CGFloat = 20
    }
    
    // MARK: - 字体规范
    
    /// 字体常量
    struct Fonts {
        
        /// Tab Bar标题字体（选中状态）
        static let tabBarTitleSelected = UIFont.systemFont(ofSize: 10, weight: .medium)
        
        /// Tab Bar标题字体（未选中状态）
        static let tabBarTitleNormal = UIFont.systemFont(ofSize: 10, weight: .regular)
        
        /// 标题字体
        static let title = UIFont.systemFont(ofSize: 24, weight: .bold)
        
        /// 副标题字体
        static let subtitle = UIFont.systemFont(ofSize: 16, weight: .medium)
        
        /// 正文字体
        static let body = UIFont.systemFont(ofSize: 14, weight: .regular)
        
        /// 小字体
        static let caption = UIFont.systemFont(ofSize: 12, weight: .regular)
    }
    
    // MARK: - 动画参数
    
    /// 动画常量
    struct Animation {
        
        /// 标准动画时长
        static let standardDuration: TimeInterval = 0.3
        
        /// 快速动画时长
        static let fastDuration: TimeInterval = 0.15
        
        /// 慢速动画时长
        static let slowDuration: TimeInterval = 0.5
        
        /// 弹簧动画参数
        static let springResponse: Double = 0.6
        static let springDampingFraction: Double = 0.8
    }
    
    // MARK: - 阴影规范
    
    /// 阴影常量
    struct Shadow {
        
        /// 标准阴影
        static let standard = (color: Color.black.opacity(0.1), radius: CGFloat(4), x: CGFloat(0), y: CGFloat(2))
        
        /// 轻微阴影
        static let light = (color: Color.black.opacity(0.05), radius: CGFloat(2), x: CGFloat(0), y: CGFloat(1))
        
        /// 重阴影
        static let heavy = (color: Color.black.opacity(0.2), radius: CGFloat(8), x: CGFloat(0), y: CGFloat(4))
    }
    
    // MARK: - AuraSpace界面规范
    
    /// AuraSpace界面相关常量
    struct AuraSpace {
        
        /// 头像尺寸
        static let avatarSizeSmall: CGFloat = 30
        static let avatarSizeMedium: CGFloat = 50
        static let avatarSizeLarge: CGFloat = 80
        
        /// 输入框最小高度
        static let inputMinHeight: CGFloat = 44
        
        /// 输入框最大高度
        static let inputMaxHeight: CGFloat = 120
        
        /// 聊天气泡间距
        static let chatBubbleSpacing: CGFloat = 16
        
        /// 底部安全区域额外间距
        static let bottomSafeAreaPadding: CGFloat = 20
        
        /// 输入区域背景透明度
        static let inputBackgroundOpacity: Double = 0.3
        
        /// 输入区域模糊半径
        static let inputBackgroundBlurRadius: CGFloat = 10
        
        /// 欢迎消息顶部间距
        static let welcomeMessageTopPadding: CGFloat = 40
        
        /// 快速回复按钮顶部间距
        static let quickReplyTopPadding: CGFloat = 8
        
        /// 聊天内容底部间距
        static let chatContentBottomSpacing: CGFloat = 20
    }
    
    // MARK: - ContentLibrary界面规范
    
    /// ContentLibrary界面相关常量
    struct ContentLibrary {
        
        /// 内容区域标准间距
        static let contentPadding: CGFloat = 20
        
        /// 搜索框内边距
        static let searchFieldPadding: CGFloat = 16
        
        /// 搜索框垂直内边距
        static let searchFieldVerticalPadding: CGFloat = 12
        
        /// 分类按钮内边距
        static let categoryButtonPadding: CGFloat = 12
        
        /// 分类按钮垂直内边距
        static let categoryButtonVerticalPadding: CGFloat = 8
        
        /// 分类按钮间距
        static let categoryButtonSpacing: CGFloat = 12
        
        /// 分类按钮圆角
        static let categoryButtonCornerRadius: CGFloat = 16
        
        /// 推荐内容卡片宽度
        static let recommendedCardWidth: CGFloat = 200
        
        /// 推荐内容卡片内边距
        static let recommendedCardPadding: CGFloat = 12
        
        /// 推荐内容卡片圆角
        static let recommendedCardCornerRadius: CGFloat = 12
        
        /// 内容列表间距
        static let contentListSpacing: CGFloat = 16
        
        /// 空状态图标尺寸
        static let emptyStateIconSize: CGFloat = 48
        
        /// Pro升级图标尺寸
        static let proUpgradeIconSize: CGFloat = 60
        
        /// 内容描述最大字符数
        static let contentDescriptionMaxLength: Int = 100
        
        /// 分类标签内边距
        static let categoryTagPadding: CGFloat = 6
        
        /// 分类标签垂直内边距
        static let categoryTagVerticalPadding: CGFloat = 2
        
        /// 分类标签圆角
        static let categoryTagCornerRadius: CGFloat = 6
        
        /// Pro升级按钮内边距
        static let proUpgradeButtonPadding: CGFloat = 40
        
        /// Pro升级按钮垂直内边距
        static let proUpgradeButtonVerticalPadding: CGFloat = 16
        
        /// Pro升级按钮圆角
        static let proUpgradeButtonCornerRadius: CGFloat = 25
    }
    
    // MARK: - 图标选择器界面规范
    
    /// 图标选择器相关常量
    struct IconSelector {
        
        /// 图标网格列数
        static let gridColumns: Int = 6
        
        /// 图标按钮尺寸
        static let iconButtonSize: CGFloat = 44
        
        /// 图标显示尺寸
        static let iconDisplaySize: CGFloat = 20
        
        /// 图标网格间距
        static let gridSpacing: CGFloat = 16
        
        /// 分类按钮内边距
        static let categoryButtonHorizontalPadding: CGFloat = 16
        static let categoryButtonVerticalPadding: CGFloat = 8
        
        /// 分类按钮间距
        static let categoryButtonSpacing: CGFloat = 12
        
        /// 选中图标缩放倍数
        static let selectedIconScale: CGFloat = 1.1
        
        /// 动画时长
        static let animationDuration: TimeInterval = 0.2
        static let categoryAnimationDuration: TimeInterval = 0.3
        
        /// 每个分类的图标数量
        static let iconsPerCategory: Int = 18
        
        /// 总分类数量
        static let totalCategories: Int = 8
    }
    
    // MARK: - Today页面界面规范
    
    /// Today页面相关常量
    struct Today {
        
        // MARK: - UI Layout Constants
        
        /// 主要间距
        static let mainSpacing: CGFloat = 20
        static let sectionSpacing: CGFloat = 16
        static let cardSpacing: CGFloat = 12
        
        /// 内边距
        static let containerPadding: CGFloat = 20
        static let cardPadding: CGFloat = 16
        static let smallPadding: CGFloat = 8
        
        /// AI头像相关
        static let aiAvatarSize: CGFloat = 60
        static let aiAvatarLargeSize: CGFloat = 80
        static let messageIndicatorSize: CGFloat = 16
        
        /// 动画时长
        static let breathingAnimationDuration: TimeInterval = 2.0
        static let glowAnimationDuration: TimeInterval = 3.0
        static let scaleAnimationDuration: TimeInterval = 0.2
        static let particleAnimationInterval: TimeInterval = 0.1
        
        /// 圆角半径
        static let cardCornerRadius: CGFloat = 16
        static let smallCornerRadius: CGFloat = 8
        
        /// 阴影设置
        static let shadowRadius: CGFloat = 8
        static let shadowOpacity: Double = 0.2
        static let shadowOffset: CGSize = CGSize(width: 0, height: 4)
        
        // MARK: - Notification Names
        
        /// 通知名称常量
        struct Notifications {
            static let habitDataChanged = "HabitDataChanged"
            static let habitDeleted = "HabitDeleted"
            static let habitEdited = "HabitEdited"
            static let databaseReset = "DatabaseReset"
            static let navigateToAuraSpace = "NavigateToAuraSpace"
        }
        
        // MARK: - Text Constants
        
        /// 文本常量
        struct Text {
            static let emptyStateTitle = "暂无习惯"
            static let emptyStateSubtitle = "去图鉴创建第一个习惯"
            static let aiAccessibilityLabel = "AI助手"
            static let aiAccessibilityHintWithMessage = "有新消息，点击查看"
            static let aiAccessibilityHintNoMessage = "点击与AI助手对话"
            static let messageIndicatorAccessibilityLabel = "有%d条AI消息"
            static let noMessageAccessibilityLabel = "无AI消息"
            
            /// 每日洞察文案
            static let dailyInsights = [
                "今日专注于内心的平静，让每一个习惯都成为成长的种子。",
                "每一步都是进步，每一次坚持都让你更接近理想的自己。",
                "习惯是时间的复利，今天的小小行动将带来明天的巨大改变。",
                "在忙碌中保持初心，在坚持中找到力量。",
                "成长不在于速度，而在于持续的方向和不变的坚持。"
            ]
        }
        
        // MARK: - Particle Effect Constants
        
        /// 粒子效果相关常量
        struct ParticleEffect {
            static let particleCount: Int = 4
            static let maxParticleSize: Int = 6
            static let minParticleSize: Int = 3
            static let floatAmplitude: CGFloat = 15
            static let horizontalFloatAmplitude: CGFloat = 8
            static let scaleVariation: Double = 0.08
            static let opacityVariation: Double = 0.1
            static let baseOpacity: Double = 0.6
            static let particleOpacity: Double = 0.4
            static let particleAnimationInterval: TimeInterval = 0.1
        }
    }
    
    // MARK: - Community社区功能界面规范
    
    /// Community社区功能相关常量
    struct Community {
        
        // MARK: - Data Constants
        
        /// 数据相关常量
        struct Data {
            static let defaultPageSize: Int = 20
            static let defaultCategory: String = "general"
            static let defaultEnergyLevel: Int = 5
            static let defaultUserEnergyLevel: Int = 5
            static let maxContentLength: Int = 500
            static let maxCommentLength: Int = 200
        }
        
        // MARK: - Timing Constants
        
        /// 时间相关常量
        struct Timing {
            static let debounceTime: TimeInterval = 1.0
            static let searchDebounceTime: TimeInterval = 0.5
            static let refreshDelayTime: TimeInterval = 0.1
            static let animationDuration: TimeInterval = 0.3
            static let cardAnimationDuration: TimeInterval = 0.1
            static let longPressDuration: TimeInterval = 0.2
        }
        
        // MARK: - PostCard Layout Constants
        
        /// 帖子卡片布局常量
        struct PostCard {
            // 主要间距
            static let containerPadding: CGFloat = 16
            static let cardSpacing: CGFloat = 16
            static let userInfoSpacing: CGFloat = 12
            static let userTextSpacing: CGFloat = 2
            static let energyLevelSpacing: CGFloat = 4
            static let habitInfoSpacing: CGFloat = 8
            
            // 圆角设置
            static let cardCornerRadius: CGFloat = 16
            static let habitTagCornerRadius: CGFloat = 12
            
            // 阴影设置
            static let shadowOpacity: Double = 0.15
            static let shadowRadius: CGFloat = 8
            static let shadowOffset: CGFloat = 4
            
            // 动画和交互
            static let pressedScale: CGFloat = 0.98
            static let normalScale: CGFloat = 1.0
            
            // 用户头像
            static let avatarSize: CGFloat = 40
            
            // 边框宽度
            static let borderWidth: CGFloat = 1
        }
        
        // MARK: - Typography Constants
        
        /// 字体大小常量
        struct Typography {
            static let userNameFontSize: CGFloat = 16
            static let userMetaFontSize: CGFloat = 12
            static let timeFontSize: CGFloat = 12
            static let contentFontSize: CGFloat = 15
            static let habitTagFontSize: CGFloat = 13
            static let actionButtonFontSize: CGFloat = 14
            static let energyIconFontSize: CGFloat = 10
            static let habitIconFontSize: CGFloat = 14
            static let lineSpacing: CGFloat = 4
        }
        
        // MARK: - Color Opacity Constants
        
        /// 颜色透明度常量
        struct ColorOpacity {
            static let primaryWhite: Double = 1.0
            static let secondaryWhite: Double = 0.7
            static let tertiaryWhite: Double = 0.6
            static let habitTagBackground: Double = 0.15
            static let habitTagBorder: Double = 0.3
            static let actionButtonBackground: Double = 0.1
        }
        
        // MARK: - Action Bar Constants
        
        /// 操作栏常量
        struct ActionBar {
            static let spacing: CGFloat = 20
            static let buttonSpacing: CGFloat = 4
            static let iconSize: CGFloat = 16
            static let buttonPadding: CGFloat = 8
            static let buttonHeight: CGFloat = 32
            static let buttonCornerRadius: CGFloat = 8
        }
        
        // MARK: - Comment System Constants
        
        /// 评论系统常量
        struct Comment {
            static let cellPadding: CGFloat = 16
            static let avatarSize: CGFloat = 32
            static let contentSpacing: CGFloat = 12
            static let metaSpacing: CGFloat = 8
            static let replyIndentation: CGFloat = 40
            static let maxNestingLevel: Int = 3
        }
        
        // MARK: - Like Button Constants
        
        /// 点赞按钮常量
        struct LikeButton {
            static let iconSize: CGFloat = 20
            static let animationDuration: TimeInterval = 0.2
            static let scaleAnimationDuration: TimeInterval = 0.1
            static let pulseScale: CGFloat = 1.2
            static let normalScale: CGFloat = 1.0
        }
        
        // MARK: - Community View Constants
        
        /// 社区主视图常量
        struct ListView {
            static let headerPadding: CGFloat = 20
            static let listSpacing: CGFloat = 16
            static let searchBarHeight: CGFloat = 44
            static let categoryFilterHeight: CGFloat = 40
            static let loadingIndicatorSize: CGFloat = 20
            static let refreshControlOffset: CGFloat = -50
        }
        
        // MARK: - Post Detail Constants
        
        /// 帖子详情页常量
        struct PostDetail {
            static let headerSpacing: CGFloat = 16
            static let contentPadding: CGFloat = 20
            static let commentSectionSpacing: CGFloat = 24
            static let inputAreaHeight: CGFloat = 60
            static let inputAreaMaxHeight: CGFloat = 120
        }
        
        // MARK: - Error Messages
        
        /// 错误消息常量
        struct ErrorMessages {
            static let userNotLoggedIn = "请先登录后再进行操作"
            static let postNotFound = "帖子不存在，可能已被删除"
            static let insufficientPermissions = "权限不足，无法执行此操作"
            static let networkConnectionFailed = "网络连接失败，请检查网络后重试"
            static let dataSaveFailed = "数据保存失败，请重试"
            static let likeOperationFailed = "点赞操作失败"
            static let commentOperationFailed = "评论操作失败"
            static let createPostFailed = "发布帖子失败"
            static let deletePostFailed = "删除帖子失败"
        }
        
        // MARK: - Success Messages
        
        /// 成功消息常量
        struct SuccessMessages {
            static let postCreated = "帖子发布成功"
            static let postDeleted = "帖子删除成功"
            static let commentAdded = "评论添加成功"
            static let likeAdded = "点赞成功"
            static let likeRemoved = "取消点赞成功"
        }
        
        // MARK: - Test Data Constants
        
        /// 测试数据常量
        struct TestData {
            static let minPostsPerUser: Int = 1
            static let maxPostsPerUser: Int = 3
            static let minLikeCount: Int = 0
            static let maxLikeCount: Int = 15
            static let minCommentCount: Int = 0
            static let maxCommentCount: Int = 8
            static let minEnergyLevel: Int = 3
            static let maxEnergyLevel: Int = 8
            static let maxTimeOffsetDays: Int = 90
            static let maxTimeOffsetHours: Int = 720
            static let maxTimeOffsetMinutes: Int = 1440
        }
    }
    
    // MARK: - 字体规范
    
    /// 字体常量
    struct FontSizes {
        
        // MARK: - 基础字体大小
        
        /// 超大标题字体
        static let extraLargeTitle: CGFloat = 34
        
        /// 大标题字体
        static let largeTitle: CGFloat = 28
        
        /// 标题字体
        static let title: CGFloat = 22
        
        /// 副标题字体
        static let subtitle: CGFloat = 18
        
        /// 正文字体
        static let body: CGFloat = 16
        
        /// 说明文字字体
        static let caption: CGFloat = 14
        
        /// 小字体
        static let small: CGFloat = 12
    }
    
    // MARK: - 🔑 设备适配规范
    
    /// 设备适配常量
    struct DeviceAdaptation {
        
        // MARK: - 设备类型检测
        
        /// 是否为iPad
        static var isIPad: Bool {
            UIDevice.current.userInterfaceIdiom == .pad
        }
        
        /// 是否为iPhone
        static var isIPhone: Bool {
            UIDevice.current.userInterfaceIdiom == .phone
        }
        
        /// 是否为横屏模式
        static var isLandscape: Bool {
            guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                  let window = windowScene.windows.first else {
                return false
            }
            return window.bounds.width > window.bounds.height
        }
        
        /// 屏幕宽度
        static var screenWidth: CGFloat {
            UIScreen.main.bounds.width
        }
        
        /// 屏幕高度
        static var screenHeight: CGFloat {
            UIScreen.main.bounds.height
        }
        
        // MARK: - 布局适配参数
        
        /// iPad内容最大宽度
        static let iPadMaxContentWidth: CGFloat = 800
        
        /// iPad最小内容宽度
        static let iPadMinContentWidth: CGFloat = 400
        
        /// iPhone内容边距
        static let iPhoneContentMargin: CGFloat = 16
        
        /// iPad内容边距比例
        static let iPadContentMarginRatio: CGFloat = 0.125 // 12.5%
        
        /// 获取适配的内容宽度
        static func adaptiveContentWidth() -> CGFloat {
            if isIPad {
                let screenWidth = Self.screenWidth
                let dynamicWidth = screenWidth * (1 - iPadContentMarginRatio * 2)
                return min(max(dynamicWidth, iPadMinContentWidth), iPadMaxContentWidth)
            } else {
                return screenWidth - (iPhoneContentMargin * 2)
            }
        }
        
        /// 获取适配的水平边距
        static func adaptiveHorizontalMargin() -> CGFloat {
            if isIPad {
                let screenWidth = Self.screenWidth
                let contentWidth = adaptiveContentWidth()
                return max((screenWidth - contentWidth) / 2, 40)
            } else {
                return iPhoneContentMargin
            }
        }
        
        // MARK: - TabBar适配参数
        
        /// 获取适配的TabBar高度
        static func adaptiveTabBarHeight() -> CGFloat {
            if isIPad {
                return 65
            } else {
                return EAAppConstants.Dimensions.tabBarHeight
            }
        }
        
        /// 获取适配的字体大小
        static func adaptiveFontSize(base: CGFloat, scaleFactor: CGFloat = 0.2) -> CGFloat {
            if isIPad {
                return base + (base * scaleFactor) // iPad字体放大20%
            } else {
                return base
            }
        }
    }
} 