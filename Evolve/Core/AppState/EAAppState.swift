//
//  EAAppState.swift
//  Evolve
//
//  Created by AI Assistant on 2025-07-12.
//

import SwiftUI
import SwiftData
import Foundation
import Combine
import UserNotifications

// MARK: - 统一应用状态管理器

/// 🔑 架构改进：统一的应用状态管理系统
/// 将原本散布在AppEntry中的20+个@State变量统一管理
/// 提供完整的数据栈生命周期管理和状态同步机制
@MainActor
class EAAppState: ObservableObject {
    
    // MARK: - 核心状态管理
    
    /// 会话ID：用于强制视图树重建
    @Published var sessionID: UUID = UUID()
    
    /// 数据栈状态
    @Published var isDataStackReady: Bool = false
    @Published var dataStackError: String? = nil
    
    // MARK: - 核心服务实例
    
    /// 数据库管理器
    @Published var databaseManager: EADatabaseManager?
    
    /// Repository容器
    @Published var repositoryContainer: EARepositoryContainerImpl?
    
    /// 会话管理器
    @Published var sessionManager: EASessionManager?
    
    // MARK: - AI和提醒服务
    
    /// AI应用内提醒管理器
    @Published var aiInAppReminderManager: EAAIInAppReminderManager
    
    /// Repository性能监控器
    @Published var repositoryPerformanceMonitor: EARepositoryPerformanceMonitor
    
    /// 表单管理器
    @Published var sheetManager: EASheetManager
    
    /// 通知代理
    @Published var notificationDelegate: NotificationDelegate
    
    /// 图像缓存服务
    @Published var imageCacheService: EAImageCacheService
    
    // MARK: - 依赖服务状态
    
    /// 好友关系服务
    @Published var friendshipService: EAFriendshipService?
    
    /// 好友通知服务
    @Published var friendNotificationService: EAFriendNotificationService?
    
    /// 好友聊天服务
    @Published var friendChatService: EAFriendChatService?
    
    /// 用户完整性守护服务
    @Published var userIntegrityGuard: EAUserIntegrityGuard?
    
    /// 异步档案初始化器
    @Published var asyncProfileInitializer: EAAsyncProfileInitializer?
    
    /// 用户身份监控器
    @Published var userIdentityMonitor: EAUserIdentityMonitor?
    
    /// 用户友好错误服务
    @Published var userFriendlyErrorService: EAUserFriendlyErrorService?
    
    /// 降级策略管理器
    @Published var gracefulDegradationManager: EAGracefulDegradationManager?
    
    /// 性能阈值管理器
    @Published var performanceThresholdManager: EAPerformanceThresholdManager?
    
    // MARK: - 初始化
    
    init() {
        // 🔑 初始化不依赖外部服务的组件
        self.aiInAppReminderManager = EAAIInAppReminderManager()
        self.repositoryPerformanceMonitor = EARepositoryPerformanceMonitor()
        self.sheetManager = EASheetManager()
        self.notificationDelegate = NotificationDelegate()
        self.imageCacheService = EAImageCacheService()

        // 🔑 修复：注册通知代理到系统
        setupNotificationDelegate()

        #if DEBUG
        print("✅ [AppState] 应用状态管理器初始化完成")
        #endif
    }

    // MARK: - 🔑 通知系统配置

    /// 配置通知代理
    private func setupNotificationDelegate() {
        UNUserNotificationCenter.current().delegate = notificationDelegate

        #if DEBUG
        print("✅ [AppState] 通知代理注册完成")
        #endif
    }
    
    // MARK: - 🔑 核心数据栈生命周期管理
    
    /// 创建核心数据栈
    func createCoreDataStack() {
        #if DEBUG
        print("🔧 [AppState] 开始创建核心数据栈")
        #endif
        
        dataStackError = nil
        
        // 第一步：创建数据库管理器
        let dbManager = EADatabaseManager(forTesting: false)
        
        // 第二步：创建Repository容器
        guard let repoContainer = dbManager.getRepositoryContainer() as? EARepositoryContainerImpl else {
            dataStackError = "Repository容器创建失败"
            #if DEBUG
            print("❌ [AppState] Repository容器创建失败")
            #endif
            return
        }
        
        // 第三步：创建SessionManager
        let sessManager = EASessionManager()
        sessManager.setDatabaseManager(dbManager)
        sessManager.setRepositoryContainer(repoContainer)
        
        // 第四步：创建依赖服务
        let tempAIDataBridge = EACommunityAIDataBridge(repositoryContainer: repoContainer)
        let tempUserIntegrityGuard = EAUserIntegrityGuard(repositoryContainer: repoContainer)
        let tempUserFriendlyErrorService = EAUserFriendlyErrorService()
        
        // 第五步：原子性更新所有状态
        updateCoreServices(
            databaseManager: dbManager,
            repositoryContainer: repoContainer,
            sessionManager: sessManager,
            aiDataBridge: tempAIDataBridge,
            userIntegrityGuard: tempUserIntegrityGuard,
            userFriendlyErrorService: tempUserFriendlyErrorService
        )
        
        // 标记数据栈就绪
        isDataStackReady = true
        
        #if DEBUG
        print("✅ [AppState] 核心数据栈创建完成")
        #endif
    }
    
    /// 原子性更新核心服务
    private func updateCoreServices(
        databaseManager: EADatabaseManager,
        repositoryContainer: EARepositoryContainerImpl,
        sessionManager: EASessionManager,
        aiDataBridge: EACommunityAIDataBridge,
        userIntegrityGuard: EAUserIntegrityGuard,
        userFriendlyErrorService: EAUserFriendlyErrorService
    ) {
        // 原子性更新核心服务引用
        self.databaseManager = databaseManager
        self.repositoryContainer = repositoryContainer
        self.sessionManager = sessionManager
        self.userIntegrityGuard = userIntegrityGuard
        self.asyncProfileInitializer = EAAsyncProfileInitializer(repositoryContainer: repositoryContainer)
        self.userIdentityMonitor = EAUserIdentityMonitor(repositoryContainer: repositoryContainer)
        
        // 创建好友相关服务
        self.friendshipService = EAFriendshipService(
            repositoryContainer: repositoryContainer,
            sessionManager: sessionManager,
            integrityGuard: userIntegrityGuard
        )
        self.friendNotificationService = EAFriendNotificationService(repositoryContainer: repositoryContainer)
        self.friendChatService = EAFriendChatService(
            repositoryContainer: repositoryContainer,
            sessionManager: sessionManager,
            aiDataBridge: aiDataBridge
        )
        
        // 创建错误处理和性能管理服务
        self.userFriendlyErrorService = userFriendlyErrorService
        self.gracefulDegradationManager = EAGracefulDegradationManager(userFriendlyErrorService: userFriendlyErrorService)
        self.performanceThresholdManager = EAPerformanceThresholdManager(
            performanceMonitor: repositoryPerformanceMonitor
        )
    }
    
    /// 🔑 销毁核心数据栈（基于ARC）
    func destroyCoreDataStack() {
        #if DEBUG
        print("🔧 [AppState] 开始销毁核心数据栈")
        #endif
        
        // 停止所有服务
        sessionManager?.stopIdentityMonitoring()
        
        // 🔑 关键：让ARC自动销毁，不强制清理文件
        self.databaseManager = nil
        self.repositoryContainer = nil
        self.sessionManager = nil
        self.userIntegrityGuard = nil
        self.asyncProfileInitializer = nil
        self.userIdentityMonitor = nil
        self.friendshipService = nil
        self.friendNotificationService = nil
        self.friendChatService = nil
        self.userFriendlyErrorService = nil
        self.gracefulDegradationManager = nil
        self.performanceThresholdManager = nil
        
        // 标记数据栈未就绪
        isDataStackReady = false
        
        #if DEBUG
        print("✅ [AppState] 核心数据栈销毁完成，ARC将处理内存清理")
        #endif
    }
    
    /// 🔑 用户切换时重建数据栈
    func rebuildDataStackForUserSwitch() {
        #if DEBUG
        print("🔄 [AppState] 用户切换，重建数据栈")
        #endif
        
        // 第一步：销毁现有数据栈
        destroyCoreDataStack()
        
        // 第二步：生成新的sessionID，强制SwiftUI重建整个视图树
        self.sessionID = UUID()
        
        // 第三步：创建新的数据栈
        createCoreDataStack()
        
        #if DEBUG
        print("✅ [AppState] 数据栈重建完成，sessionID: \(sessionID)")
        #endif
    }
    
    // MARK: - 🔑 性能优化配置
    
    /// 配置性能监控
    func configurePerformanceMonitoring() async {
        #if DEBUG
        print("🔧 [AppState] 开始配置性能监控")
        #endif
        
        guard let repositoryContainer = repositoryContainer,
              let performanceManager = performanceThresholdManager else {
            #if DEBUG
            print("⚠️ [AppState] 性能监控配置跳过：缺少必要组件")
            #endif
            return
        }
        
        // 配置Repository性能监控器
        await repositoryContainer.configurePerformanceMonitoring(monitor: repositoryPerformanceMonitor)
        
        // 配置用户身份服务的性能阈值管理器
        userIntegrityGuard?.configure(performanceThresholdManager: performanceManager)
        asyncProfileInitializer?.configure(performanceThresholdManager: performanceManager)
        
        #if DEBUG
        print("✅ [AppState] 性能监控配置完成")
        #endif
    }
    
    // MARK: - 状态查询方法
    
    /// 检查数据栈是否就绪
    var isCoreServicesReady: Bool {
        return isDataStackReady && 
               sessionManager != nil && 
               repositoryContainer != nil
    }
    
    /// 获取核心服务状态描述
    func getCoreServicesStatus() -> String {
        var status = "📱 核心服务状态:\n"
        status += "- 数据栈就绪: \(isDataStackReady ? "✅" : "❌")\n"
        status += "- 数据库管理器: \(databaseManager != nil ? "✅" : "❌")\n"
        status += "- Repository容器: \(repositoryContainer != nil ? "✅" : "❌")\n"
        status += "- 会话管理器: \(sessionManager != nil ? "✅" : "❌")\n"
        status += "- 好友服务: \(friendshipService != nil ? "✅" : "❌")\n"
        status += "- 性能监控: \(performanceThresholdManager != nil ? "✅" : "❌")\n"
        
        if let error = dataStackError {
            status += "- 错误信息: ❌ \(error)\n"
        }
        
        return status
    }
    
    // MARK: - 清理方法
    
    /// 执行完整清理
    func performFullCleanup() {
        #if DEBUG
        print("🧹 [AppState] 执行完整清理")
        #endif
        
        destroyCoreDataStack()
        dataStackError = nil
        sessionID = UUID()
        
        #if DEBUG
        print("✅ [AppState] 完整清理完成")
        #endif
    }
}

// MARK: - 扩展：便捷访问方法

extension EAAppState {
    
    /// 获取安全的Repository容器
    var safeRepositoryContainer: EARepositoryContainer? {
        return repositoryContainer
    }
    
    /// 获取安全的会话管理器
    var safeSessionManager: EASessionManager? {
        return sessionManager
    }
    
    /// 检查是否需要重建数据栈
    var needsDataStackRebuild: Bool {
        return !isDataStackReady || sessionManager == nil || repositoryContainer == nil
    }
}

#if DEBUG
// MARK: - 调试扩展

extension EAAppState {
    
    /// 打印完整状态信息
    func printFullStatus() {
        print("""
        📊 ===== AppState 完整状态报告 =====
        SessionID: \(sessionID)
        数据栈就绪: \(isDataStackReady)
        
        核心服务:
        \(getCoreServicesStatus())
        
        错误信息: \(dataStackError ?? "无")
        =====================================
        """)
    }
    
    /// 模拟数据栈故障
    func simulateDataStackFailure() {
        dataStackError = "模拟的数据栈故障"
        isDataStackReady = false
        print("🚨 [AppState] 模拟数据栈故障")
    }
}
#endif