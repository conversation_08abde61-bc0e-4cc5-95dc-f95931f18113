//
//  EAUniverseGuideModels.swift
//  Evolve
//
//  Created by AI Assistant on 2025-06-06.
//  Phase 2 Day 5: AI宇宙向导集成 - 数据模型定义
//

import Foundation
import SwiftUI

// MARK: - 宇宙向导消息模型

/// 宇宙向导消息
struct EAGuideMessage: Identifiable, Codable {
    let id: UUID
    let role: EAMessageRole
    let content: String
    let timestamp: Date
    let sessionId: UUID
    var guidePersonality: EAGuidePersonality?
    var contextTags: [String] = []
    var isHighlighted: Bool = false
    
    init(
        id: UUID = UUID(),
        role: EAMessageRole,
        content: String,
        timestamp: Date = Date(),
        sessionId: UUID,
        guidePersonality: EAGuidePersonality? = nil,
        contextTags: [String] = []
    ) {
        self.id = id
        self.role = role
        self.content = content
        self.timestamp = timestamp
        self.sessionId = sessionId
        self.guidePersonality = guidePersonality
        self.contextTags = contextTags
    }
}

/// 消息角色
enum EAMessageRole: String, Codable, CaseIterable {
    case user = "user"
    case guide = "guide"
    case system = "system"
    
    var displayName: String {
        switch self {
        case .user: return "探索者"
        case .guide: return "宇宙向导"
        case .system: return "系统"
        }
    }
    
    var icon: String {
        switch self {
        case .user: return "person.circle.fill"
        case .guide: return "sparkles.rectangle.stack.fill"
        case .system: return "gear.circle.fill"
        }
    }
}

/// 宇宙向导个性类型
enum EAGuidePersonality: String, Codable, CaseIterable {
    case wise = "wise"           // 智慧型：深思熟虑，富有洞察
    case encouraging = "encouraging"  // 鼓励型：积极正面，充满活力
    case gentle = "gentle"       // 温和型：温暖耐心，善解人意
    case adventurous = "adventurous"  // 冒险型：大胆探索，勇于尝试
    
    var displayName: String {
        switch self {
        case .wise: return "智慧向导"
        case .encouraging: return "激励向导"
        case .gentle: return "温和向导"
        case .adventurous: return "冒险向导"
        }
    }
    
    var description: String {
        switch self {
        case .wise: return "深思熟虑，富有智慧的宇宙向导，提供深刻的洞察和建议"
        case .encouraging: return "充满活力，积极正面的宇宙向导，激励你勇敢前行"
        case .gentle: return "温暖耐心，善解人意的宇宙向导，给予贴心的关怀和支持"
        case .adventurous: return "大胆探索，勇于尝试的宇宙向导，带你发现未知的可能"
        }
    }
    
    var icon: String {
        switch self {
        case .wise: return "brain.head.profile"
        case .encouraging: return "flame.fill"
        case .gentle: return "heart.fill"
        case .adventurous: return "rocket.fill"
        }
    }
    
    var primaryColor: Color {
        switch self {
        case .wise: return .blue
        case .encouraging: return .orange
        case .gentle: return .pink
        case .adventurous: return .purple
        }
    }
}

// MARK: - 触发和上下文模型

/// 宇宙向导触发原因
enum EAGuideTriggerReason: String, Codable, CaseIterable {
    case userInitiated = "user_initiated"
    case milestoneAchieved = "milestone_achieved"
    case strugglingDetected = "struggling_detected"
    case newFeatureIntroduction = "new_feature"
    case dailyCheckin = "daily_checkin"
    
    var displayName: String {
        switch self {
        case .userInitiated: return "用户主动"
        case .milestoneAchieved: return "里程碑达成"
        case .strugglingDetected: return "困难检测"
        case .newFeatureIntroduction: return "新功能介绍"
        case .dailyCheckin: return "每日签到"
        }
    }
}

/// 宇宙向导引导上下文
enum EAGuideContext: String, Codable, CaseIterable {
    case firstTimeUser = "first_time_user"
    case habitCreation = "habit_creation"
    case streakBreaking = "streak_breaking"
    case milestoneApproaching = "milestone_approaching"
    case lowActivity = "low_activity"
    case socialSharing = "social_sharing"
    
    var displayName: String {
        switch self {
        case .firstTimeUser: return "新用户引导"
        case .habitCreation: return "计划创建"
        case .streakBreaking: return "连击中断"
        case .milestoneApproaching: return "里程碑临近"
        case .lowActivity: return "活跃度低"
        case .socialSharing: return "社交分享"
        }
    }
}

/// 用户上下文信息
struct EAUserContext: Codable {
    let userId: UUID
    let stellarLevel: Int
    let totalStellarEnergy: Int
    let habitsCount: Int
    let lastActiveDate: Date
    let preferredGuideStyle: EAGuidePersonality
    
    /// 用户活跃度评分 (0-1)
    var activityScore: Double {
        let daysSinceLastActive = Date().timeIntervalSince(lastActiveDate) / 86400
        return max(0, 1 - (daysSinceLastActive / 7)) // 7天内线性衰减
    }
    
    /// 星际等级称号
    var stellarLevelTitle: String {
        switch stellarLevel {
        case 1...3: return "新手探索者"
        case 4...6: return "星际旅者"
        case 7...10: return "宇宙领航员"
        default: return "传奇探索者"
        }
    }
}

// MARK: - 配置模型

/// 宇宙向导配置
struct EAUniverseGuideConfig {
    
    // MARK: - 对话配置
    
    /// 最大对话轮数
    let maxConversationRounds: Int = 20
    
    /// 响应超时时间（秒）
    let responseTimeoutSeconds: TimeInterval = 30
    
    /// 会话过期时间（小时）
    let sessionExpirationHours: TimeInterval = 24
    
    // MARK: - AI调用配置
    
    /// 高优先级触发场景（必须调用AI）
    let highPriorityTriggers: Set<EAGuideTriggerReason> = [
        .userInitiated,
        .milestoneAchieved,
        .strugglingDetected
    ]
    
    /// 中优先级触发场景（缓存24小时后调用）
    let mediumPriorityTriggers: Set<EAGuideTriggerReason> = [
        .newFeatureIntroduction
    ]
    
    /// 低优先级触发场景（优先本地处理）
    let lowPriorityTriggers: Set<EAGuideTriggerReason> = [
        .dailyCheckin
    ]
    
    // MARK: - 引导策略配置
    
    /// 主动引导间隔（小时）
    let proactiveGuidanceIntervalHours: TimeInterval = 24
    
    /// 最大每日引导次数
    let maxDailyGuidanceCount: Int = 3
    
    /// 引导消息最小间隔（分钟）
    let guidanceMinIntervalMinutes: TimeInterval = 30
    
    // MARK: - 个性化配置
    
    /// 默认向导个性
    let defaultPersonality: EAGuidePersonality = .wise
    
    /// 个性自动调整启用
    let enablePersonalityAdaptation: Bool = true
    
    /// 上下文感知启用
    let enableContextAwareness: Bool = true
    
    // MARK: - 成本控制配置
    
    /// AI调用降级阈值（毫秒）
    let aiCallTimeoutMs: Int = 10000
    
    /// 缓存过期检查间隔（分钟）
    let cacheExpirationCheckMinutes: TimeInterval = 15
    
    /// 启用本地智能回复
    let enableLocalIntelligentReplies: Bool = true
    
    // MARK: - 方法
    
    /// 获取触发优先级
    func getTriggerPriority(for trigger: EAGuideTriggerReason) -> EAGuidePriority {
        if highPriorityTriggers.contains(trigger) {
            return .high
        } else if mediumPriorityTriggers.contains(trigger) {
            return .medium
        } else {
            return .low
        }
    }
    
    /// 是否需要AI调用
    func requiresAICall(for trigger: EAGuideTriggerReason) -> Bool {
        return highPriorityTriggers.contains(trigger)
    }
    
    /// 获取缓存有效期
    func getCacheValidityDuration(for priority: EAGuidePriority) -> TimeInterval {
        switch priority {
        case .high:
            return 0 // 不缓存，实时调用
        case .medium:
            return 24 * 3600 // 24小时
        case .low:
            return 7 * 24 * 3600 // 7天
        }
    }
}

/// 宇宙向导优先级
enum EAGuidePriority: String, Codable, CaseIterable {
    case high = "high"
    case medium = "medium"
    case low = "low"
    
    var displayName: String {
        switch self {
        case .high: return "高优先级"
        case .medium: return "中优先级"
        case .low: return "低优先级"
        }
    }
    
    var aiCallRequired: Bool {
        return self == .high
    }
    
    var cacheValidityHours: TimeInterval {
        switch self {
        case .high: return 0     // 不缓存
        case .medium: return 24  // 24小时
        case .low: return 168    // 7天
        }
    }
}

// MARK: - 智能引导模型

/// 智能引导建议
struct EAIntelligentGuidance: Identifiable, Codable {
    let id: UUID
    let userId: UUID
    let context: EAGuideContext
    let priority: EAGuidePriority
    let title: String
    let content: String
    let actionItems: [EAGuidanceAction]
    let createdAt: Date
    let expiresAt: Date
    
    init(
        userId: UUID,
        context: EAGuideContext,
        priority: EAGuidePriority,
        title: String,
        content: String,
        actionItems: [EAGuidanceAction] = [],
        validityHours: TimeInterval = 24
    ) {
        self.id = UUID()
        self.userId = userId
        self.context = context
        self.priority = priority
        self.title = title
        self.content = content
        self.actionItems = actionItems
        self.createdAt = Date()
        self.expiresAt = Date().addingTimeInterval(validityHours * 3600)
    }
    
    /// 是否已过期
    var isExpired: Bool {
        return Date() > expiresAt
    }
    
    /// 剩余有效时间（小时）
    var remainingValidityHours: TimeInterval {
        let remaining = expiresAt.timeIntervalSince(Date()) / 3600
        return max(0, remaining)
    }
}

/// 引导行动项
struct EAGuidanceAction: Identifiable, Codable {
    let id: UUID
    let title: String
    let actionType: EAGuidanceActionType
    let targetId: UUID?
    let isCompleted: Bool
    
    init(
        title: String,
        actionType: EAGuidanceActionType,
        targetId: UUID? = nil,
        isCompleted: Bool = false
    ) {
        self.id = UUID()
        self.title = title
        self.actionType = actionType
        self.targetId = targetId
        self.isCompleted = isCompleted
    }
}

/// 引导行动类型
enum EAGuidanceActionType: String, Codable, CaseIterable {
    case createHabit = "create_habit"
    case completeHabit = "complete_habit"
    case shareAchievement = "share_achievement"
    case exploreFeature = "explore_feature"
    case connectWithCommunity = "connect_community"
    case reviewProgress = "review_progress"
    
    var displayName: String {
        switch self {
        case .createHabit: return "创建习惯"
        case .completeHabit: return "完成习惯"
        case .shareAchievement: return "分享成就"
        case .exploreFeature: return "探索功能"
        case .connectWithCommunity: return "社区互动"
        case .reviewProgress: return "回顾进展"
        }
    }
    
    var icon: String {
        switch self {
        case .createHabit: return "plus.circle.fill"
        case .completeHabit: return "checkmark.circle.fill"
        case .shareAchievement: return "share.circle.fill"
        case .exploreFeature: return "sparkles.rectangle.stack.fill"
        case .connectWithCommunity: return "person.2.circle.fill"
        case .reviewProgress: return "chart.line.uptrend.xyaxis.circle.fill"
        }
    }
}

// MARK: - 统计和分析模型

/// 宇宙向导使用统计
struct EAGuideUsageStatistics: Codable {
    var totalConversations: Int = 0
    var totalMessages: Int = 0
    var averageConversationLength: Double = 0
    var mostUsedPersonality: EAGuidePersonality = .wise
    var mostCommonTrigger: EAGuideTriggerReason = .userInitiated
    var userSatisfactionScore: Double = 0
    var lastInteractionDate: Date?
    
    /// 更新统计数据
    mutating func updateStatistics(
        conversationLength: Int,
        personality: EAGuidePersonality,
        trigger: EAGuideTriggerReason
    ) {
        totalConversations += 1
        totalMessages += conversationLength
        averageConversationLength = Double(totalMessages) / Double(totalConversations)
        lastInteractionDate = Date()
        
        // 这里可以添加更复杂的统计逻辑
        // 例如：跟踪最常用的个性类型和触发原因
    }
    
    /// 获取互动频率（次/天）
    func getInteractionFrequency() -> Double {
        guard let lastDate = lastInteractionDate else { return 0 }
        let daysSinceFirst = Date().timeIntervalSince(lastDate) / 86400
        return daysSinceFirst > 0 ? Double(totalConversations) / daysSinceFirst : Double(totalConversations)
    }
} 