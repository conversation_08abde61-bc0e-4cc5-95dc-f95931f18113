//
//  EAUniverseGuideService.swift
//  Evolve
//
//  Created by AI Assistant on 2025-06-06.
//  Phase 2 Day 5: AI宇宙向导集成 - 宇宙向导对话功能
//

import Foundation
import SwiftUI

/// 宇宙向导服务
/// AI扮演数字宇宙向导，提供个性化引导和对话支持
/// 遵循开发规范文档的"Repository模式强制执行规范"和"AI成本控制开发规范"
@MainActor
class EAUniverseGuideService: ObservableObject {
    
    // MARK: - 发布属性
    
    @Published var conversationHistory: [EAGuideMessage] = []
    @Published var isGuideActive: Bool = false
    @Published var isProcessingResponse: Bool = false
    @Published var guidePersonality: EAGuidePersonality = .wise
    @Published var lastInteractionTime: Date?
    
    // MARK: - 依赖注入
    
    private var repositoryContainer: EARepositoryContainer?
    private var aiDataBridge: EACommunityAIDataBridge?
    private var cacheManager: EAAICacheManager?
    
    // MARK: - 对话管理
    
    private var sessionId: UUID = UUID()
    private let maxConversationHistory = 20 // 保持最近20轮对话
    
    // MARK: - 宇宙向导配置
    
    private let guideConfig = EAUniverseGuideConfig()
    
    // MARK: - 初始化
    
    /// ✅ 修复：支持延迟依赖注入的初始化
    init() {
        setupDefaultPersonality()
    }
    
    /// ✅ 新增：设置依赖注入
    /// - Parameters:
    ///   - repositoryContainer: Repository容器
    ///   - aiDataBridge: AI数据桥接服务
    func setupDependencies(
        repositoryContainer: EARepositoryContainer,
        aiDataBridge: EACommunityAIDataBridge
    ) {
        self.repositoryContainer = repositoryContainer
        self.aiDataBridge = aiDataBridge
        self.cacheManager = EAAICacheManager()
    }
    
    /// 兼容性初始化方法（保留用于其他地方的直接初始化）
    convenience init(repositoryContainer: EARepositoryContainer, aiDataBridge: EACommunityAIDataBridge, cacheManager: EAAICacheManager) {
        self.init()
        self.repositoryContainer = repositoryContainer
        self.aiDataBridge = aiDataBridge
        self.cacheManager = cacheManager
    }
    
    // MARK: - 核心对话接口
    
    /// 开始与宇宙向导的对话
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - triggerReason: 触发对话的原因
    func startConversation(userId: UUID, triggerReason: EAGuideTriggerReason = .userInitiated) async {
        sessionId = UUID()
        isGuideActive = true
        lastInteractionTime = Date()
        
        // 获取用户上下文
        let userContext = await buildUserContext(userId: userId)
        
        // 生成欢迎消息
        let welcomeMessage = generateWelcomeMessage(context: userContext, trigger: triggerReason)
        
        // 添加到对话历史
        conversationHistory = [welcomeMessage]
    }
    
    /// 发送消息给宇宙向导并获取回复
    /// - Parameters:
    ///   - userMessage: 用户消息
    ///   - userId: 用户ID
    /// - Returns: 向导回复
    func sendMessage(_ userMessage: String, userId: UUID) async -> EAGuideMessage? {
        guard isGuideActive else { return nil }
        
        isProcessingResponse = true
        defer { isProcessingResponse = false }
        
        // 添加用户消息到历史
        let userMsg = EAGuideMessage(
            id: UUID(),
            role: .user,
            content: userMessage,
            timestamp: Date(),
            sessionId: sessionId
        )
        conversationHistory.append(userMsg)
        
        // 生成AI回复
        let guideResponse = await generateGuideResponse(
            userMessage: userMessage,
            userId: userId,
            conversationContext: conversationHistory
        )
        
        // 添加向导回复到历史
        conversationHistory.append(guideResponse)
        
        // 维护对话历史长度
        maintainConversationHistory()
        
        lastInteractionTime = Date()
        
        return guideResponse
    }
    
    /// 结束与宇宙向导的对话
    func endConversation() {
        isGuideActive = false
        
        // 保存对话历史到缓存
        cacheManager?.cacheGuideConversation(conversationHistory, sessionId: sessionId)
        
        // 清理当前会话
        conversationHistory.removeAll()
    }
    
    // MARK: - 智能引导功能
    
    /// 提供上下文感知的智能引导
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - context: 当前上下文
    /// - Returns: 引导建议
    func provideIntelligentGuidance(userId: UUID, context: EAGuideContext) async -> EAGuideMessage? {
        do {
            // 分析用户当前状态
            let userSummary = try await aiDataBridge?.getUserSocialSummary(userId: userId)
            
            // 基于上下文生成引导
            let guidance = generateContextualGuidance(context: context, userSummary: userSummary)
            
            return guidance
            
        } catch {
            return generateGenericGuidance(context: context)
        }
    }
    
    /// 检测并提供主动引导时机
    /// - Parameter userId: 用户ID
    /// - Returns: 主动引导消息（如果需要）
    func detectProactiveGuidanceOpportunity(userId: UUID) async -> EAGuideMessage? {
        // 检查是否需要主动引导
        let shouldProvideGuidance = await shouldProvideProactiveGuidance(userId: userId)
        
        guard shouldProvideGuidance else { return nil }
        
        // 生成主动引导消息
        return await generateProactiveGuidance(userId: userId)
    }
    
    // MARK: - 私有方法
    
    /// 构建用户上下文
    private func buildUserContext(userId: UUID) async -> EAUserContext {
        do {
            let userSummary = try await aiDataBridge?.getUserSocialSummary(userId: userId)
            _ = await repositoryContainer?.userRepository.fetchUser(by: userId)
            
            return EAUserContext(
                userId: userId,
                stellarLevel: userSummary?.stellarLevel ?? 1,
                totalStellarEnergy: userSummary?.totalStellarEnergy ?? 0,
                habitsCount: userSummary?.postsCount ?? 0, // 使用postsCount作为活跃度指标
                lastActiveDate: Date(),
                preferredGuideStyle: guidePersonality
            )
            
        } catch {
            // 降级处理：使用默认上下文
            return EAUserContext(
                userId: userId,
                stellarLevel: 1,
                totalStellarEnergy: 0,
                habitsCount: 0,
                lastActiveDate: Date(),
                preferredGuideStyle: .wise
            )
        }
    }
    
    /// 生成欢迎消息
    private func generateWelcomeMessage(context: EAUserContext, trigger: EAGuideTriggerReason) -> EAGuideMessage {
        let welcomeContent = generateWelcomeContent(context: context, trigger: trigger)
        
        return EAGuideMessage(
            id: UUID(),
            role: .guide,
            content: welcomeContent,
            timestamp: Date(),
            sessionId: sessionId,
            guidePersonality: guidePersonality,
            contextTags: ["welcome", trigger.rawValue]
        )
    }
    
    /// 生成欢迎内容
    private func generateWelcomeContent(context: EAUserContext, trigger: EAGuideTriggerReason) -> String {
        let levelTitle = getStellarLevelTitle(level: context.stellarLevel)
        
        switch trigger {
        case .userInitiated:
            return "🌟 欢迎来到数字宇宙，\(levelTitle)！我是你的宇宙向导。在这片星际空间中，我将陪伴你探索习惯养成的奥秘。有什么我可以帮助你的吗？"
            
        case .milestoneAchieved:
            return "✨ 恭喜你，\(levelTitle)！我感受到了你刚刚释放的强大星际能量。让我们一起庆祝这个重要的里程碑吧！"
            
        case .strugglingDetected:
            return "💫 \(levelTitle)，我察觉到你在星际旅程中遇到了一些挑战。别担心，每位探索者都会经历这样的时刻。让我来帮助你重新找到前进的方向。"
            
        case .newFeatureIntroduction:
            return "🚀 \(levelTitle)，数字宇宙又有新的发现了！我迫不及待想要与你分享这些令人兴奋的新功能。准备好开启新的探索之旅了吗？"
            
        case .dailyCheckin:
            return "🌅 早安，\(levelTitle)！新的一天在数字宇宙中开始了。让我看看今天你想要在哪个方向上释放你的星际能量。"
        }
    }
    
    /// 生成AI回复
    private func generateGuideResponse(
        userMessage: String,
        userId: UUID,
        conversationContext: [EAGuideMessage]
    ) async -> EAGuideMessage {
        // 在实际项目中，这里会调用AI API生成回复
        // 现在使用基于规则的智能回复系统
        
        let responseContent = await generateIntelligentResponse(
            userMessage: userMessage,
            userId: userId,
            context: conversationContext
        )
        
        return EAGuideMessage(
            id: UUID(),
            role: .guide,
            content: responseContent,
            timestamp: Date(),
            sessionId: sessionId,
            guidePersonality: guidePersonality,
            contextTags: extractContextTags(from: userMessage)
        )
    }
    
    /// 生成智能回复内容
    private func generateIntelligentResponse(
        userMessage: String,
        userId: UUID,
        context: [EAGuideMessage]
    ) async -> String {
        let message = userMessage.lowercased()
        
        // 基于关键词的智能回复
        if message.contains("帮助") || message.contains("怎么") {
            return "🌟 我很乐意帮助你！在数字宇宙中，每个问题都是新发现的起点。请告诉我具体遇到了什么挑战，我会为你提供最适合的指导。"
        }
        
        if message.contains("习惯") || message.contains("坚持") {
            return "💫 习惯养成就像在宇宙中播种星辰，需要时间和耐心。记住，每一次坚持都在为你的星际能量充电。小步前进，终将抵达遥远的星系！"
        }
        
        if message.contains("累") || message.contains("难") || message.contains("放弃") {
            return "🌙 我理解你的感受，星际探索者。疲惫是旅程的一部分，但请记住，你已经走了这么远。让我们一起找到重新点燃星际能量的方法。"
        }
        
        if message.contains("成就") || message.contains("完成") || message.contains("成功") {
            return "🎉 太棒了！你的成就让整个数字宇宙都在为你闪耀！这些星际能量将成为你继续前行的强大动力。"
        }
        
        // 默认智能回复
        return "✨ 谢谢你与我分享这些。作为你的宇宙向导，我会一直在这里支持你的星际探索之旅。还有什么想要深入探讨的吗？"
    }
    
    /// 生成降级回复
    private func generateFallbackResponse(userMessage: String) -> EAGuideMessage {
        let fallbackContent = "🌟 星际网络似乎有些不稳定，但我依然在这里陪伴你。请稍后再试，或者告诉我你当前最需要的帮助。"
        
        return EAGuideMessage(
            id: UUID(),
            role: .guide,
            content: fallbackContent,
            timestamp: Date(),
            sessionId: sessionId,
            guidePersonality: guidePersonality,
            contextTags: ["fallback"]
        )
    }
    
    /// 维护对话历史长度
    private func maintainConversationHistory() {
        if conversationHistory.count > maxConversationHistory {
            conversationHistory = Array(conversationHistory.suffix(maxConversationHistory))
        }
    }
    
    /// 设置默认个性
    private func setupDefaultPersonality() {
        guidePersonality = .wise
    }
    
    /// 获取星际等级称号
    private func getStellarLevelTitle(level: Int) -> String {
        switch level {
        case 1...3: return "新手探索者"
        case 4...6: return "星际旅者"
        case 7...10: return "宇宙领航员"
        default: return "传奇探索者"
        }
    }
    
    /// 提取上下文标签
    private func extractContextTags(from message: String) -> [String] {
        var tags: [String] = []
        let lowercasedMessage = message.lowercased()
        
        if lowercasedMessage.contains("习惯") { tags.append("habit") }
        if lowercasedMessage.contains("帮助") { tags.append("help") }
        if lowercasedMessage.contains("成就") { tags.append("achievement") }
        if lowercasedMessage.contains("难") || lowercasedMessage.contains("累") { tags.append("struggle") }
        
        return tags
    }
    
    // MARK: - 引导逻辑
    
    /// 生成上下文感知的引导
    private func generateContextualGuidance(context: EAGuideContext, userSummary: EAAISocialSummary?) -> EAGuideMessage {
        let guidanceContent = generateGuidanceContent(context: context, userSummary: userSummary)
        
        return EAGuideMessage(
            id: UUID(),
            role: .guide,
            content: guidanceContent,
            timestamp: Date(),
            sessionId: sessionId,
            guidePersonality: guidePersonality,
            contextTags: [context.rawValue]
        )
    }
    
    /// 生成引导内容
    private func generateGuidanceContent(context: EAGuideContext, userSummary: EAAISocialSummary?) -> String {
        let levelTitle = getStellarLevelTitle(level: userSummary?.stellarLevel ?? 1)
        
        switch context {
        case .firstTimeUser:
            return "🚀 欢迎加入数字宇宙，\(levelTitle)！让我来为你介绍这个神奇的星际空间。在这里，每个习惯都会转化为星际能量，帮助你不断成长。"
            
        case .habitCreation:
            return "✨ 准备创建新的习惯了吗？\(levelTitle)！记住，最强大的习惯往往从最简单的行动开始。让我们一起为你的新星际任务制定完美的计划。"
            
        case .streakBreaking:
            return "💫 我注意到你的连击中断了，\(levelTitle)。但请记住，真正的探索者从不因为一次跌倒而停止前进。让我们重新点燃你的星际能量！"
            
        case .milestoneApproaching:
            return "🌟 我感受到强大的星际能量正在聚集！你即将达成一个重要里程碑，\(levelTitle)。坚持下去，胜利就在前方！"
            
        case .lowActivity:
            return "🌙 \(levelTitle)，我发现你最近的星际活动有所减少。也许是时候重新审视你的探索目标了？我在这里帮助你重新找到方向。"
            
        case .socialSharing:
            return "🎊 你的成就值得被整个宇宙知道！\(levelTitle)，分享你的星际能量可以激励其他探索者。让我们一起创造这个激动人心的时刻。"
        }
    }
    
    /// 生成通用引导
    private func generateGenericGuidance(context: EAGuideContext) -> EAGuideMessage {
        let genericContent = "🌟 在星际探索的旅程中，我始终与你同在。无论遇到什么挑战，记住你都有无限的潜能。"
        
        return EAGuideMessage(
            id: UUID(),
            role: .guide,
            content: genericContent,
            timestamp: Date(),
            sessionId: sessionId,
            guidePersonality: guidePersonality,
            contextTags: [context.rawValue, "generic"]
        )
    }
    
    /// 判断是否需要主动引导
    private func shouldProvideProactiveGuidance(userId: UUID) async -> Bool {
        // 简化的主动引导逻辑
        guard let lastInteraction = lastInteractionTime else { return true }
        
        // 如果超过24小时没有互动，提供主动引导
        let hoursSinceLastInteraction = Date().timeIntervalSince(lastInteraction) / 3600
        return hoursSinceLastInteraction > 24
    }
    
    /// 生成主动引导
    private func generateProactiveGuidance(userId: UUID) async -> EAGuideMessage {
        let proactiveContent = "🌅 探索者，新的一天开始了！数字宇宙中有新的冒险等待着你。今天想要在哪个方向上释放你的星际能量？"
        
        return EAGuideMessage(
            id: UUID(),
            role: .guide,
            content: proactiveContent,
            timestamp: Date(),
            sessionId: UUID(),
            guidePersonality: guidePersonality,
            contextTags: ["proactive", "daily"]
        )
    }
} 