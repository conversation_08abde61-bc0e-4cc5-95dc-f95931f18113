import SwiftData
import Foundation

/// 数据库健康检查器（iOS 17.0-18.2+兼容版本）
/// 负责检查和修复数据库完整性问题，避免暴力重置
@MainActor
class EADatabaseHealthChecker {
    
    // MARK: - 初始化
    init() {}
    
    // MARK: - 公共接口
    
    /// 执行全面的数据库健康检查
    /// - Parameter modelContext: SwiftData模型上下文
    /// - Returns: 检查结果，包含发现的问题和修复状态
    func performHealthCheck(modelContext: ModelContext) async -> HealthCheckResult {
        var result = HealthCheckResult()
        
        do {
            // 1. 基础数据访问检查
            try await checkBasicDataAccess(modelContext: modelContext)
            result.basicAccessPassed = true
            
            // 2. 数据一致性检查
            let consistencyIssues = try await checkDataConsistency(modelContext: modelContext)
            result.consistencyIssues = consistencyIssues
            
            // 3. 孤立数据检查
            let orphanedDataIssues = try await checkOrphanedData(modelContext: modelContext)
            result.orphanedDataIssues = orphanedDataIssues
            
            // 4. 计算总体健康状态
            result.isHealthy = consistencyIssues.isEmpty && orphanedDataIssues.isEmpty
            
        } catch {
            result.error = error
            result.isHealthy = false
        }
        
        return result
    }
    
    /// 智能修复数据库问题
    /// - Parameter modelContext: SwiftData模型上下文
    /// - Returns: 修复结果
    func performIntelligentRepair(modelContext: ModelContext) async -> RepairResult {
        var result = RepairResult()
        
        do {
            // 1. 修复孤立的完成记录
            let orphanedRecordsFixed = try await repairOrphanedCompletionRecords(modelContext: modelContext)
            result.orphanedRecordsFixed = orphanedRecordsFixed
            
            // 2. 修复用户数据不一致
            let userDataFixed = try await repairUserDataInconsistency(modelContext: modelContext)
            result.userDataInconsistenciesFixed = userDataFixed
            
            // 3. 修复提醒数量不匹配
            try await repairReminderCountMismatch(modelContext: modelContext)
            result.reminderCountFixed = true
            
            result.success = true
            
        } catch {
            result.error = error
            result.success = false
        }
        
        return result
    }
    
    // MARK: - 私有检查方法
    
    /// 检查基础数据访问能力
    private func checkBasicDataAccess(modelContext: ModelContext) async throws {
        // 尝试基础的数据访问操作
        var habitDescriptor = FetchDescriptor<EAHabit>()
        habitDescriptor.fetchLimit = 1
        _ = try modelContext.fetch(habitDescriptor)
        
        var userDescriptor = FetchDescriptor<EAUser>()
        userDescriptor.fetchLimit = 1
        _ = try modelContext.fetch(userDescriptor)
        
        var completionDescriptor = FetchDescriptor<EACompletion>()
        completionDescriptor.fetchLimit = 1
        _ = try modelContext.fetch(completionDescriptor)
    }
    
    /// 检查数据一致性
    private func checkDataConsistency(modelContext: ModelContext) async throws -> [String] {
        var issues: [String] = []
        
        // 检查提醒数量一致性
        let habitDescriptor = FetchDescriptor<EAHabit>()
        let habits = try modelContext.fetch(habitDescriptor)
        
        let actualReminderCount = habits.reduce(0) { total, habit in
            return total + habit.reminderTimes.count
        }
        
        let storedReminderCount = UserDefaults.standard.integer(forKey: "ai_reminder_count")
        
        if actualReminderCount != storedReminderCount {
            issues.append("提醒数量不匹配: 实际\(actualReminderCount), 存储\(storedReminderCount)")
        }
        
        return issues
    }
    
    /// 检查孤立数据
    private func checkOrphanedData(modelContext: ModelContext) async throws -> [String] {
        var issues: [String] = []
        
        // 检查孤立的完成记录（使用关系检查）
        let completionDescriptor = FetchDescriptor<EACompletion>()
        let allCompletions = try modelContext.fetch(completionDescriptor)
        
        let orphanedCompletions = allCompletions.filter { completion in
            // 检查是否有关联的习惯
            return completion.habit == nil
        }
        
        if !orphanedCompletions.isEmpty {
            issues.append("发现 \(orphanedCompletions.count) 个孤立的完成记录")
        }
        
        return issues
    }
    
    // MARK: - 私有修复方法
    
    /// 修复孤立的完成记录
    private func repairOrphanedCompletionRecords(modelContext: ModelContext) async throws -> Int {
        let completionDescriptor = FetchDescriptor<EACompletion>()
        let allCompletions = try modelContext.fetch(completionDescriptor)
        
        var fixedCount = 0
        
        for completion in allCompletions {
            // 检查是否有关联的习惯
            if completion.habit == nil {
                // 删除孤立记录
                modelContext.delete(completion)
                fixedCount += 1
            }
        }
        
        if fixedCount > 0 {
            try modelContext.save()
        }
        
        return fixedCount
    }
    
    /// 修复用户数据不一致
    private func repairUserDataInconsistency(modelContext: ModelContext) async throws -> Int {
        // 确保所有习惯都有用户关系
        let habitDescriptor = FetchDescriptor<EAHabit>()
        let allHabits = try modelContext.fetch(habitDescriptor)
        
        let userDescriptor = FetchDescriptor<EAUser>()
        let allUsers = try modelContext.fetch(userDescriptor)
        
        var fixedCount = 0
        
        for habit in allHabits {
            if habit.user == nil {
                fixedCount += 1
                if allUsers.isEmpty {
                    // 创建默认用户
                    let defaultUser = EAUser(username: "默认用户")
                    modelContext.insert(defaultUser)
                    // fetch一次，确保上下文一致
                    let defaultUserId = defaultUser.id
                    let userDescriptor = FetchDescriptor<EAUser>(predicate: #Predicate { $0.id == defaultUserId })
                    guard let contextUser = try? modelContext.fetch(userDescriptor).first else { continue }
                    habit.user = contextUser
                } else {
                    // fetch一次，确保上下文一致
                    let firstUserId = allUsers.first!.id
                    let userDescriptor = FetchDescriptor<EAUser>(predicate: #Predicate { $0.id == firstUserId })
                    guard let contextUser = try? modelContext.fetch(userDescriptor).first else { continue }
                    habit.user = contextUser
                }
            }
        }
        
        if fixedCount > 0 {
            try modelContext.save()
        }
        
        return fixedCount
    }
    
    /// 修复提醒数量不匹配
    private func repairReminderCountMismatch(modelContext: ModelContext) async throws {
        let habitDescriptor = FetchDescriptor<EAHabit>()
        let habits = try modelContext.fetch(habitDescriptor)
        
        let actualReminderCount = habits.reduce(0) { total, habit in
            return total + habit.reminderTimes.count
        }
        
        // 更新UserDefaults中的数据
        UserDefaults.standard.set(actualReminderCount, forKey: "ai_reminder_count")
        
        // 通知相关服务更新
        NotificationCenter.default.post(
            name: NSNotification.Name("ReminderCountUpdated"),
            object: actualReminderCount
        )
    }
}

// MARK: - 结果数据结构

/// 健康检查结果
struct HealthCheckResult {
    var isHealthy: Bool = false
    var basicAccessPassed: Bool = false
    var consistencyIssues: [String] = []
    var orphanedDataIssues: [String] = []
    var error: Error?
    
    var totalIssues: Int {
        return consistencyIssues.count + orphanedDataIssues.count
    }
}

/// 修复结果
struct RepairResult {
    var success: Bool = false
    var orphanedRecordsFixed: Int = 0
    var userDataInconsistenciesFixed: Int = 0
    var reminderCountFixed: Bool = false
    var error: Error?
    
    var totalItemsFixed: Int {
        return orphanedRecordsFixed + userDataInconsistenciesFixed + (reminderCountFixed ? 1 : 0)
    }
} 