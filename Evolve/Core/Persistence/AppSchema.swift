import Foundation
import SwiftData

/// SwiftData模型的"唯一真相源" - 统一管理所有数据模型
/// 解决ModelContainer多点初始化和模型列表不一致问题
/// 严格遵循《Evolve项目AI开发审查规则.md》的容器统一化要求
struct EAAppSchema {
    
    /// 🔑 唯一真相源：所有SwiftData模型的完整列表
    /// 按字母顺序排列，确保一致性和可维护性
    static let allModels: [any PersistentModel.Type] = [
        EAAIMessage.self,
        EAAnalytics.self,
        EABlockedUser.self,
        EACommunityComment.self,
        EACommunityFollow.self,
        EACommunityLike.self,
        EACommunityPost.self,
        EACommunityReport.self,
        EACompletion.self,
        EAContent.self,
        EAFriendMessage.self,
        EAFriendNotification.self,
        EAFriendRequest.self,
        EAFriendship.self,
        EAHabit.self,
        EAPath.self,
        EAPayment.self,
        EAUniverseChallenge.self,
        EAUniverseChallengeParticipation.self,
        EAUser.self,
        EAUserAuthInfo.self,
        EAUserDataProfile.self,
        EAUserModerationProfile.self,
        EAUserSettings.self,
        EAUserSocialProfile.self
    ]
    
    /// 🔑 生产环境Container配置
    static func createProductionContainer() throws -> ModelContainer {
        let configuration = ModelConfiguration(
            isStoredInMemoryOnly: false,   // 🔑 关键：必须保存到磁盘文件
            allowsSave: true,              // 🔑 关键：允许保存操作
            groupContainer: .none,         // 使用应用默认容器
            cloudKitDatabase: .none        // 暂不启用iCloud同步
        )
        
        return try ModelContainer(
            for: EAAIMessage.self,
                 EAAnalytics.self,
                 EABlockedUser.self,
                 EACommunityComment.self,
                 EACommunityFollow.self,
                 EACommunityLike.self,
                 EACommunityPost.self,
                 EACommunityReport.self,
                 EACompletion.self,
                 EAContent.self,
                 EAFriendMessage.self,
                 EAFriendNotification.self,
                 EAFriendRequest.self,
                 EAFriendship.self,
                 EAHabit.self,
                 EAPath.self,
                 EAPayment.self,
                 EAUniverseChallenge.self,
                 EAUniverseChallengeParticipation.self,
                 EAUser.self,
                 EAUserAuthInfo.self,
                 EAUserDataProfile.self,
                 EAUserModerationProfile.self,
                 EAUserSettings.self,
                 EAUserSocialProfile.self,
            configurations: configuration
        )
    }
    
    /// 🔑 Preview环境Container配置（内存存储）
    static func createPreviewContainer() throws -> ModelContainer {
        let configuration = ModelConfiguration(
            isStoredInMemoryOnly: true
        )
        
        return try ModelContainer(
            for: EAAIMessage.self,
                 EAAnalytics.self,
                 EABlockedUser.self,
                 EACommunityComment.self,
                 EACommunityFollow.self,
                 EACommunityLike.self,
                 EACommunityPost.self,
                 EACommunityReport.self,
                 EACompletion.self,
                 EAContent.self,
                 EAFriendMessage.self,
                 EAFriendNotification.self,
                 EAFriendRequest.self,
                 EAFriendship.self,
                 EAHabit.self,
                 EAPath.self,
                 EAPayment.self,
                 EAUniverseChallenge.self,
                 EAUniverseChallengeParticipation.self,
                 EAUser.self,
                 EAUserAuthInfo.self,
                 EAUserDataProfile.self,
                 EAUserModerationProfile.self,
                 EAUserSettings.self,
                 EAUserSocialProfile.self,
            configurations: configuration
        )
    }
    
    /// 🔑 测试环境Container配置（内存存储）
    static func createTestContainer() throws -> ModelContainer {
        let configuration = ModelConfiguration(
            isStoredInMemoryOnly: true
        )
        
        return try ModelContainer(
            for: EAAIMessage.self,
                 EAAnalytics.self,
                 EABlockedUser.self,
                 EACommunityComment.self,
                 EACommunityFollow.self,
                 EACommunityLike.self,
                 EACommunityPost.self,
                 EACommunityReport.self,
                 EACompletion.self,
                 EAContent.self,
                 EAFriendMessage.self,
                 EAFriendNotification.self,
                 EAFriendRequest.self,
                 EAFriendship.self,
                 EAHabit.self,
                 EAPath.self,
                 EAPayment.self,
                 EAUniverseChallenge.self,
                 EAUniverseChallengeParticipation.self,
                 EAUser.self,
                 EAUserAuthInfo.self,
                 EAUserDataProfile.self,
                 EAUserModerationProfile.self,
                 EAUserSettings.self,
                 EAUserSocialProfile.self,
            configurations: configuration
        )
    }
    
    /// 验证模型列表完整性
    static func validateModelListIntegrity() -> Bool {
        // 确保列表不为空
        guard !allModels.isEmpty else { return false }
        
        // 确保没有重复的模型类型
        let uniqueModelCount = Set(allModels.map { ObjectIdentifier($0) }).count
        return uniqueModelCount == allModels.count
    }
    
    /// 获取模型数量统计
    static func getModelStatistics() -> (total: Int, coreModels: Int, socialModels: Int, friendModels: Int) {
        let coreModels = ["EAUser", "EAUserSettings", "EAHabit", "EACompletion", "EAContent", "EAPath", "EAPayment", "EAAnalytics", "EAAIMessage"]
        let socialModels = ["EACommunityPost", "EACommunityComment", "EACommunityLike", "EACommunityFollow", "EACommunityReport", "EAUserSocialProfile", "EAUserModerationProfile"]
        let friendModels = ["EAFriendship", "EAFriendRequest", "EAFriendMessage", "EAFriendNotification"]
        
        let coreCount = allModels.filter { model in
            let typeName = String(describing: model)
            return coreModels.contains(typeName)
        }.count
        
        let socialCount = allModels.filter { model in
            let typeName = String(describing: model)
            return socialModels.contains(typeName)
        }.count
        
        let friendCount = allModels.filter { model in
            let typeName = String(describing: model)
            return friendModels.contains(typeName)
        }.count
        
        return (
            total: allModels.count,
            coreModels: coreCount,
            socialModels: socialCount,
            friendModels: friendCount
        )
    }
} 