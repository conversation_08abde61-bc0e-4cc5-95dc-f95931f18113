import Foundation
import SwiftData

@Model
final class EAContent: @unchecked Sendable {
    var id: UUID = UUID()
    var title: String
    var content: String
    var contentType: String // "motivation", "psychology_exercise", "success_story"
    var isPro: Bool = false
    var creationDate: Date = Date()
    
    private var tagsString: String = ""
    
    init(title: String, content: String, contentType: String, isPro: Bool = false) {
        self.title = title
        self.content = content
        self.contentType = contentType
        self.isPro = isPro
    }
    
    var tags: [String] {
        get {
            guard !tagsString.isEmpty else { return [] }
            return tagsString.split(separator: ",")
                .map { $0.trimmingCharacters(in: .whitespaces) }
        }
        set {
            tagsString = newValue.joined(separator: ",")
        }
    }
} 