//
//  EAUserModerationProfile.swift
//  Evolve
//
//  Created by AI Assistant on 2025-06-05.
//

import Foundation
import SwiftData

/// 用户管理档案模型
/// 负责管理用户的举报、审核等管理相关数据，从EAUser模型中分离出来以降低复杂度
/// 遵循开发规范文档的"数据模型复杂度控制规范"
@Model
final class EAUserModerationProfile: @unchecked Sendable {
    var id: UUID = UUID()
    var creationDate: Date = Date()
    var lastUpdateDate: Date = Date()
    
    // MARK: - 核心关系
    
    /// 关联的用户（SwiftData标准关系）
    var user: EAUser?
    
    /// 用户提交的举报（一对多）
    @Relationship(deleteRule: .cascade, inverse: \EACommunityReport.reporter)
    var submittedReports: [EACommunityReport]
    
    /// 针对用户的举报（一对多）
    @Relationship(deleteRule: .cascade, inverse: \EACommunityReport.reportedUser)
    var receivedReports: [EACommunityReport]
    
    // MARK: - 管理统计属性
    
    /// 提交举报数量
    var submittedReportsCount: Int = 0
    
    /// 被举报数量
    var receivedReportsCount: Int = 0
    
    /// 信誉分数（0-100）
    var reputationScore: Double = 100.0
    
    /// 是否被禁言
    var isMuted: Bool = false
    
    /// 禁言到期时间
    var muteExpiryDate: Date?
    
    /// 是否被封禁
    var isBanned: Bool = false
    
    /// 封禁到期时间
    var banExpiryDate: Date?
    
    /// 警告次数
    var warningCount: Int = 0
    
    /// 最后警告时间
    var lastWarningDate: Date?
    
    // MARK: - 初始化
    
    init() {
        self.submittedReports = []
        self.receivedReports = []
    }
    
    // MARK: - 管理功能便捷方法
    
    /// 获取有效的提交举报数量
    func getActiveSubmittedReportsCount() -> Int {
        return submittedReports.filter { $0.status != "resolved" }.count
    }
    
    /// 获取有效的被举报数量
    func getActiveReceivedReportsCount() -> Int {
        return receivedReports.filter { $0.status != "resolved" }.count
    }
    
    /// 检查用户是否当前被禁言
    func isCurrentlyMuted() -> Bool {
        guard isMuted else { return false }
        if let expiryDate = muteExpiryDate {
            return Date() < expiryDate
        }
        return true // 永久禁言
    }
    
    /// 检查用户是否当前被封禁
    func isCurrentlyBanned() -> Bool {
        guard isBanned else { return false }
        if let expiryDate = banExpiryDate {
            return Date() < expiryDate
        }
        return true // 永久封禁
    }
    
    /// 更新管理统计数据
    func updateModerationStats() {
        submittedReportsCount = submittedReports.count
        receivedReportsCount = receivedReports.count
        lastUpdateDate = Date()
        
        // 根据被举报情况调整信誉分数
        let activeReports = getActiveReceivedReportsCount()
        if activeReports > 0 {
            reputationScore = max(0, 100.0 - Double(activeReports) * 10.0)
        }
    }
    
    /// 添加警告
    func addWarning() {
        warningCount += 1
        lastWarningDate = Date()
        reputationScore = max(0, reputationScore - 5.0)
        updateModerationStats()
    }
    
    /// 设置禁言
    func setMute(duration: TimeInterval? = nil) {
        isMuted = true
        if let duration = duration {
            muteExpiryDate = Date().addingTimeInterval(duration)
        } else {
            muteExpiryDate = nil // 永久禁言
        }
        reputationScore = max(0, reputationScore - 15.0)
        updateModerationStats()
    }
    
    /// 解除禁言
    func removeMute() {
        isMuted = false
        muteExpiryDate = nil
        updateModerationStats()
    }
    
    /// 设置封禁
    func setBan(duration: TimeInterval? = nil) {
        isBanned = true
        if let duration = duration {
            banExpiryDate = Date().addingTimeInterval(duration)
        } else {
            banExpiryDate = nil // 永久封禁
        }
        reputationScore = 0
        updateModerationStats()
    }
    
    /// 解除封禁
    func removeBan() {
        isBanned = false
        banExpiryDate = nil
        reputationScore = max(50.0, reputationScore) // 恢复基础信誉
        updateModerationStats()
    }
    
    /// 获取最近的举报记录
    func getRecentSubmittedReports(limit: Int = 10) -> [EACommunityReport] {
        return submittedReports
            .sorted { $0.creationDate > $1.creationDate }
            .prefix(limit)
            .map { $0 }
    }
    
    /// 获取最近被举报记录
    func getRecentReceivedReports(limit: Int = 10) -> [EACommunityReport] {
        return receivedReports
            .sorted { $0.creationDate > $1.creationDate }
            .prefix(limit)
            .map { $0 }
    }
    
    /// 检查是否可以发布内容
    func canPostContent() -> Bool {
        return !isCurrentlyBanned() && !isCurrentlyMuted()
    }
    
    /// 检查是否可以评论
    func canComment() -> Bool {
        return !isCurrentlyBanned() && !isCurrentlyMuted()
    }
    
    /// 检查是否可以点赞
    func canLike() -> Bool {
        return !isCurrentlyBanned()
    }
    
    // MARK: - 安全关系操作方法
    
    /// 安全添加提交的举报
    @MainActor
    func safelyAddSubmittedReport(_ report: EACommunityReport, in context: ModelContext) throws {
        guard report.modelContext == context, self.modelContext == context else {
            throw DataModelError.contextMismatch
        }
        self.submittedReports.append(report)
        updateModerationStats()
        try context.save()
    }
    
    /// 安全添加被举报记录
    @MainActor
    func safelyAddReceivedReport(_ report: EACommunityReport, in context: ModelContext) throws {
        guard report.modelContext == context, self.modelContext == context else {
            throw DataModelError.contextMismatch
        }
        self.receivedReports.append(report)
        updateModerationStats()
        try context.save()
    }
} 