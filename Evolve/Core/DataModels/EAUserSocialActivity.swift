//
//  EAUserSocialActivity.swift
//  Evolve
//
//  Created by AI Assistant on 2025-07-01.
//  第四阶段重构：从EAUserSocialProfile分离出的社交活动模型
//

import Foundation
import SwiftData

/// 用户社交活动档案模型
/// 🔑 第四阶段优化：从EAUserSocialProfile分离出消息和通知相关功能
/// 遵循《Evolve项目AI开发审查规则.md》的"数据模型复杂度控制规范"
/// 单个模型@Relationship不超过5个的限制
@Model
final class EAUserSocialActivity: @unchecked Sendable {
    var id: UUID = UUID()
    var creationDate: Date = Date()
    var lastUpdateDate: Date = Date()
    
    // MARK: - 核心关系
    
    /// 🔑 关联的用户社交档案（一对一关系）
    /// 通过这个关系连接到主要的社交档案
    /// 注意：这是普通属性，inverse在EAUserSocialProfile端定义
    var socialProfile: EAUserSocialProfile?
    
    // MARK: - 消息相关关系（从EAUserSocialProfile迁移）
    
    /// 发送的消息（一对多）
    @Relationship(deleteRule: .cascade, inverse: \EAFriendMessage.senderActivity)
    var sentMessages: [EAFriendMessage]
    
    /// 接收的消息（一对多）
    @Relationship(deleteRule: .cascade, inverse: \EAFriendMessage.receiverActivity)
    var receivedMessages: [EAFriendMessage]
    
    // MARK: - 通知相关关系（从EAUserSocialProfile迁移）
    
    /// 接收的通知（一对多）- 作为通知接收者
    @Relationship(deleteRule: .cascade, inverse: \EAFriendNotification.receiverActivity)
    var receivedNotifications: [EAFriendNotification]
    
    /// 触发的通知（一对多）- 作为通知触发者
    @Relationship(deleteRule: .cascade, inverse: \EAFriendNotification.senderActivity)
    var triggeredNotifications: [EAFriendNotification]
    
    // MARK: - 活动统计属性
    
    /// 发送消息总数
    var sentMessagesCount: Int = 0
    
    /// 接收消息总数
    var receivedMessagesCount: Int = 0
    
    /// 未读消息数量
    var unreadMessagesCount: Int = 0
    
    /// 未读通知数量
    var unreadNotificationsCount: Int = 0
    
    /// 最后消息时间
    var lastMessageDate: Date?
    
    /// 最后通知时间
    var lastNotificationDate: Date?
    
    // MARK: - 初始化方法
    
    /// 初始化社交活动档案
    /// 🚨 关键修复：遵循SwiftData原子性操作原则，不在init中建立关系
    init() {
        // 🔑 修复：关系建立将在Repository的原子性操作中完成
        self.socialProfile = nil
        self.sentMessages = []
        self.receivedMessages = []
        self.receivedNotifications = []
        self.triggeredNotifications = []
    }
    
    // MARK: - 便利方法
    
    /// 获取总消息数量
    var totalMessagesCount: Int {
        return sentMessagesCount + receivedMessagesCount
    }
    
    /// 获取总通知数量
    var totalNotificationsCount: Int {
        return receivedNotifications.count + triggeredNotifications.count
    }
    
    /// 是否有未读消息
    var hasUnreadMessages: Bool {
        return unreadMessagesCount > 0
    }
    
    /// 是否有未读通知
    var hasUnreadNotifications: Bool {
        return unreadNotificationsCount > 0
    }
    
    /// 更新消息统计
    func updateMessageStats() {
        sentMessagesCount = sentMessages.count
        receivedMessagesCount = receivedMessages.count
        unreadMessagesCount = receivedMessages.filter { !$0.isRead }.count
        lastMessageDate = [sentMessages.last?.creationDate, receivedMessages.last?.creationDate]
            .compactMap { $0 }
            .max()
        lastUpdateDate = Date()
    }
    
    /// 更新通知统计
    func updateNotificationStats() {
        unreadNotificationsCount = receivedNotifications.filter { !$0.isRead }.count
        lastNotificationDate = [receivedNotifications.last?.createdAt, triggeredNotifications.last?.createdAt]
            .compactMap { $0 }
            .max()
        lastUpdateDate = Date()
    }
    
    /// 标记所有消息为已读
    func markAllMessagesAsRead() {
        for message in receivedMessages {
            message.isRead = true
        }
        unreadMessagesCount = 0
        lastUpdateDate = Date()
    }
    
    /// 标记所有通知为已读
    func markAllNotificationsAsRead() {
        for notification in receivedNotifications {
            notification.isRead = true
        }
        unreadNotificationsCount = 0
        lastUpdateDate = Date()
    }
}

// MARK: - 扩展：数据验证

extension EAUserSocialActivity {
    
    /// 验证数据完整性
    /// - Returns: 是否通过验证
    func validateData() -> Bool {
        // 基本验证
        guard socialProfile != nil else {
            return false
        }
        
        // 统计数据一致性验证
        let actualSentCount = sentMessages.count
        let actualReceivedCount = receivedMessages.count
        let actualUnreadCount = receivedMessages.filter { !$0.isRead }.count
        
        return sentMessagesCount == actualSentCount &&
               receivedMessagesCount == actualReceivedCount &&
               unreadMessagesCount == actualUnreadCount
    }
    
    /// 修复数据不一致问题
    func repairDataInconsistency() {
        updateMessageStats()
        updateNotificationStats()
    }
}

// MARK: - 扩展：查询便利方法

extension EAUserSocialActivity {
    
    /// 获取最近的消息
    /// - Parameter limit: 限制数量
    /// - Returns: 最近的消息列表
    func getRecentMessages(limit: Int = 10) -> [EAFriendMessage] {
        let allMessages = (sentMessages + receivedMessages)
            .sorted(by: { $0.creationDate > $1.creationDate })
        return Array(allMessages.prefix(limit))
    }
    
    /// 获取最近的通知
    /// - Parameter limit: 限制数量
    /// - Returns: 最近的通知列表
    func getRecentNotifications(limit: Int = 10) -> [EAFriendNotification] {
        let allNotifications = (receivedNotifications + triggeredNotifications)
            .sorted(by: { $0.createdAt > $1.createdAt })
        return Array(allNotifications.prefix(limit))
    }
    
    /// 获取未读消息
    /// - Returns: 未读消息列表
    func getUnreadMessages() -> [EAFriendMessage] {
        return receivedMessages.filter { !$0.isRead }
            .sorted(by: { $0.creationDate > $1.creationDate })
    }
    
    /// 获取未读通知
    /// - Returns: 未读通知列表
    func getUnreadNotifications() -> [EAFriendNotification] {
        return receivedNotifications.filter { !$0.isRead }
            .sorted(by: { $0.createdAt > $1.createdAt })
    }
}
