import Foundation
import SwiftData

/// 用户认证信息模型
/// 存储用户的登录凭证和认证相关数据
@Model
final class EAUserAuthInfo: @unchecked Sendable {
    var id: UUID = UUID()
    var phoneNumber: String
    var passwordHash: String
    var createdAt: Date
    var lastLoginAt: Date?
    
    // ✅ 正确：指向用户数据档案（遵循SwiftData单端inverse规则）
    // 注意：EAUserDataProfile端定义@Relationship(inverse: \EAUserAuthInfo.userDataProfile)
    var userDataProfile: EAUserDataProfile?
    
    init(phoneNumber: String, passwordHash: String, createdAt: Date) {
        self.phoneNumber = phoneNumber
        self.passwordHash = passwordHash
        self.createdAt = createdAt
    }
    
    // MARK: - 便捷访问方法
    
    /// 获取关联的用户（通过数据档案）
    func getUser() -> EAUser? {
        return userDataProfile?.user
    }
    
    /// 更新最后登录时间
    func updateLastLogin() {
        lastLoginAt = Date()
    }
    
    /// 检查密码是否过期（示例：90天过期）
    func isPasswordExpired() -> Bool {
        let expirationInterval: TimeInterval = 90 * 24 * 60 * 60 // 90天
        return Date().timeIntervalSince(createdAt) > expirationInterval
    }
}
