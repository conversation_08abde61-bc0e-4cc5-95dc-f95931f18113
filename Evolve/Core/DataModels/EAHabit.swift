import Foundation
import SwiftData

@Model
final class EAHabit: @unchecked Sendable {
    // MARK: - Core Properties（已优化索引性能）
    
    @Attribute(.unique) var id: UUID = UUID()
    var name: String
    @Attribute var creationDate: Date = Date() // 🔑 性能优化：常用于排序，建议数据库优化
    var iconName: String
    var targetFrequency: Int
    var preferredTimeSlot: String?
    var isActive: Bool = true // 🔑 性能优化：常用于过滤，建议数据库优化
    
    // �� 关系的另一端：使用普通属性，避免循环引用
    // SwiftData会自动维护inverse关系
    var user: EAUser?
    
    // 🔗 核心关系3：习惯-完成记录（一对多）- 完整双向inverse关系
    @Relationship(deleteRule: .cascade, inverse: \EACompletion.habit)
    var completions: [EACompletion]
    
    // 业务逻辑需要的属性
    var frequencyType: String = "daily"
    
    // ✅ 修复iOS 18.2+兼容性：使用字符串存储数组，避免序列化问题
    private var selectedWeekdaysString: String = "1,3,5" // 默认周一、周三、周五
    
    var dailyTarget: Int = 1
    var monthlyTarget: Int = 15
    var monthlyMode: String = "target"
    
    // ✅ 修复iOS 18.2+兼容性：使用字符串存储数组
    private var selectedMonthlyDatesString: String = "1,15" // 默认每月1号和15号
    
    // ✅ 新增：一次性计划日期存储（遵循现有字符串存储数组模式）
    private var selectedOneTimeDatesString: String = ""
    
    var category: String = "健康"
    var difficulty: String = "简单"
    
    // 提醒相关属性
    var reminderEnabled: Bool = false
    
    // ✅ 修复iOS 18.2+兼容性：使用字符串存储数组
    private var reminderTimesString: String = "" // 空字符串表示无提醒
    
    // 显示相关属性
    var habitDescription: String = ""
    var color: String = "#14B8A6"
    
    init(name: String, iconName: String, targetFrequency: Int, preferredTimeSlot: String? = nil) {
        self.name = name
        self.iconName = iconName
        self.targetFrequency = targetFrequency
        self.preferredTimeSlot = preferredTimeSlot
        
        // ✅ 关键修复：根据开发规范文档第416行要求
        // "关系集合初始化：所有集合类型的关系属性必须在init中初始化，而非设置默认值"
        self.completions = []
    }
    
    // ✅ 计算属性：维护API兼容性 - selectedWeekdays
    var selectedWeekdays: [Int] {
        get {
            guard !selectedWeekdaysString.isEmpty else { return [1, 3, 5] }
            return selectedWeekdaysString.split(separator: ",")
                .compactMap { Int($0.trimmingCharacters(in: .whitespaces)) }
        }
        set {
            selectedWeekdaysString = newValue.map { String($0) }.joined(separator: ",")
        }
    }
    
    // ✅ 计算属性：维护API兼容性 - selectedMonthlyDates
    var selectedMonthlyDates: [Int] {
        get {
            guard !selectedMonthlyDatesString.isEmpty else { return [1, 15] }
            return selectedMonthlyDatesString.split(separator: ",")
                .compactMap { Int($0.trimmingCharacters(in: .whitespaces)) }
        }
        set {
            selectedMonthlyDatesString = newValue.map { String($0) }.joined(separator: ",")
        }
    }
    
    // ✅ 计算属性：维护API兼容性 - reminderTimes
    var reminderTimes: [String] {
        get {
            guard !reminderTimesString.isEmpty else { return [] }
            return reminderTimesString.split(separator: ",")
                .map { $0.trimmingCharacters(in: .whitespaces) }
        }
        set {
            reminderTimesString = newValue.joined(separator: ",")
        }
    }
    
    // ✅ 新增：计算属性 - selectedOneTimeDates（一次性计划日期访问接口）
    var selectedOneTimeDates: [String] {
        get {
            guard !selectedOneTimeDatesString.isEmpty else { return [] }
            return selectedOneTimeDatesString.split(separator: ",")
                .map { $0.trimmingCharacters(in: .whitespaces) }
        }
        set {
            selectedOneTimeDatesString = newValue.joined(separator: ",")
        }
    }
    
    // ✅ 新增：计算属性 - isOneTimeCompleted（一次性计划完成状态）
    var isOneTimeCompleted: Bool {
        get {
            guard frequencyType == "oneTime" else { return false }
            let selectedDates = Set(selectedOneTimeDates)
            let completedDates = Set(completions.compactMap { completion in
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM-dd"
                formatter.timeZone = TimeZone.current
                return formatter.string(from: completion.date)
            })
            return selectedDates.isSubset(of: completedDates) && !selectedDates.isEmpty
        }
    }
    
    // 业务逻辑方法（暂时返回模拟数据）
    func getTodayCompletion() -> EACompletion? {
        // 暂时返回nil，后续通过Repository模式实现
        return nil
    }
    
    func removeCompletion(_ completion: EACompletion) {
        // 暂时空实现，后续通过Repository模式实现
    }
    
    func addCompletion(note: String? = nil, energyLevel: Int = 5) -> EACompletion {
        // 暂时返回新的完成记录，后续通过Repository模式实现
        return EACompletion(completionNote: note, energyLevel: energyLevel)
    }
    
    var currentStreak: Int {
        // 暂时返回0，后续通过Repository模式实现
        return 0
    }
} 