//
//  EAAIFriendChatContext.swift
//  Evolve
//
//  Created by AI Assistant on 2024-12-19.
//  好友聊天AI上下文数据结构
//

import Foundation

/// AI好友聊天上下文数据
/// 用于AI增强聊天功能的上下文信息
struct EAAIFriendChatContext {
    /// 好友关系ID
    let friendshipId: UUID
    
    /// 关系强度（0.0-1.0）
    let relationshipStrength: Double
    
    /// 共同兴趣列表
    let commonInterests: [String]
    
    /// 发起者社交摘要
    let initiatorSummary: EAAISocialSummary
    
    /// 好友社交摘要
    let friendSummary: EAAISocialSummary
    
    /// 最后互动时间
    let lastInteractionDate: Date?
    
    /// 总能量共振值
    let totalEnergyResonance: Int
    
    /// 上下文创建时间
    let contextTimestamp: Date
    
    init(friendshipId: UUID, relationshipStrength: Double, commonInterests: [String], 
         initiatorSummary: EAAISocialSummary, friendSummary: EAAISocialSummary,
         lastInteractionDate: Date?, totalEnergyResonance: Int) {
        self.friendshipId = friendshipId
        self.relationshipStrength = relationshipStrength
        self.commonInterests = commonInterests
        self.initiatorSummary = initiatorSummary
        self.friendSummary = friendSummary
        self.lastInteractionDate = lastInteractionDate
        self.totalEnergyResonance = totalEnergyResonance
        self.contextTimestamp = Date()
    }
}

/// AI好友推荐数据
/// 用于智能好友推荐功能
struct EAAIFriendRecommendation {
    /// 推荐用户ID
    let userId: UUID
    
    /// 用户名
    let username: String
    
    /// 共同兴趣
    let commonInterests: [String]
    
    /// 星际等级
    let stellarLevel: Int
    
    /// 匹配分数（0.0-1.0）
    let matchScore: Double
    
    /// 推荐理由
    let reason: String
    
    /// 推荐时间
    let recommendationTimestamp: Date
    
    init(userId: UUID, username: String, commonInterests: [String], 
         stellarLevel: Int, matchScore: Double, reason: String) {
        self.userId = userId
        self.username = username
        self.commonInterests = commonInterests
        self.stellarLevel = stellarLevel
        self.matchScore = matchScore
        self.reason = reason
        self.recommendationTimestamp = Date()
    }
}
