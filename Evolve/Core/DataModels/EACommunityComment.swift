import Foundation
import SwiftData

/// 社区评论模型 - 支持对帖子的评论和回复
/// 严格遵循SwiftData单端inverse规则，只在必要的一端使用@Relationship
@Model
final class EACommunityComment: @unchecked Sendable {
    var id: UUID = UUID()
    var content: String                 // 评论内容
    var creationDate: Date = Date()     // 创建时间
    var lastEditDate: Date = Date()     // 最后编辑时间
    var isVisible: Bool = true          // 是否可见（用于软删除）
    var likeCount: Int = 0             // 点赞数量
    var replyCount: Int = 0            // 回复数量
    
    // 回复功能
    var replyToUsername: String?       // 回复对象的用户名
    var isReply: Bool = false          // 是否为回复评论
    var replyLevel: Int = 0            // 回复层级（0为顶级评论，1为一级回复等）
    
    // 内容审核
    var moderationStatus: String = "approved"  // 审核状态：pending、approved、rejected
    var flagCount: Int = 0                     // 被举报次数
    
    // AI增强功能
    var sentimentScore: Double = 0.0    // 情感分析分数 (-1.0 到 1.0)
    var isEncouraging: Bool = false     // 是否为鼓励性评论（AI识别）
    var toxicityScore: Double = 0.0     // 毒性检测分数 (0.0-1.0)
    
    // 🔗 SwiftData关系：与帖子的关系（普通属性，EACommunityPost端定义inverse）
    var post: EACommunityPost?
    
    // 🔗 SwiftData关系：与用户的关系（普通属性，EAUser端定义inverse）
    var author: EAUser?
    
    // 🔗 SwiftData关系：父子评论关系（自引用，保留此@Relationship因为是核心功能）
    var parentComment: EACommunityComment?    // 父评论（普通属性）
    
    @Relationship(deleteRule: .cascade, inverse: \EACommunityComment.parentComment)
    var replies: [EACommunityComment] = []    // 子回复评论
    
    // 注意：点赞和举报关系通过普通属性引用，不使用@Relationship避免循环依赖
    // 这些关系通过查询来访问，如：let likes = fetchLikes(for: commentId)
    
    init(
        content: String,
        replyToUsername: String? = nil
    ) {
        self.content = content
        self.replyToUsername = replyToUsername
        
        // ✅ 初始化关系数组，遵循iOS 18.2要求
        self.replies = []
    }
    
    // MARK: - 便捷方法
    
    /// 增加点赞数
    func incrementLikeCount() {
        likeCount += 1
    }
    
    /// 减少点赞数
    func decrementLikeCount() {
        if likeCount > 0 {
            likeCount -= 1
        }
    }
    
    /// 增加回复数（当有人回复此评论时调用）
    func incrementReplyCount() {
        replyCount += 1
    }
    
    /// 软删除评论
    func softDelete() {
        isVisible = false
        content = "[评论已删除]"
        lastEditDate = Date()
    }
    
    /// 检查是否需要内容审核
    func needsModeration() -> Bool {
        return flagCount >= 2 || moderationStatus == "pending" || toxicityScore > 0.7
    }
    
    /// 检查是否为顶级评论
    func isTopLevel() -> Bool {
        return parentComment == nil && replyLevel == 0
    }
    
    /// 获取评论简短预览（用于通知）
    func getPreviewText(maxLength: Int = 50) -> String {
        if content.count <= maxLength {
            return content
        }
        let endIndex = content.index(content.startIndex, offsetBy: maxLength)
        return String(content[..<endIndex]) + "..."
    }
    
    /// 计算评论质量分数（用于排序）
    func getQualityScore() -> Double {
        var score = Double(likeCount)
        
        // 鼓励性评论加分
        if isEncouraging {
            score += 5.0
        }
        
        // 毒性内容扣分
        score -= toxicityScore * 10.0
        
        // 被举报过多扣分
        score -= Double(flagCount) * 2.0
        
        return max(0.0, score)
    }
    
    /// 获取作者用户名（通过关系访问）
    func getAuthorUsername() -> String {
        return author?.username ?? "星际探索者"
    }
    
    /// 获取所属帖子标题（通过关系访问）
    func getPostTitle() -> String {
        return post?.content ?? "未知帖子"
    }
    
    /// 获取有效点赞数量（通过查询访问，避免循环关系）
    /// 注意：实际实现时需要通过ModelContext查询
    func getActiveLikesCount() -> Int {
        // 这个方法需要在ViewModel中实现，通过查询获取
        // 避免在模型层建立复杂的@Relationship网络
        return likeCount // 临时返回缓存的计数
    }
    
    /// 获取可见回复数量（通过关系访问）
    func getVisibleRepliesCount() -> Int {
        return replies.filter { $0.isVisible }.count
    }
} 