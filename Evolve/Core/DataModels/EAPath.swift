import Foundation
import SwiftData

@Model
final class EAPath: @unchecked Sendable {
    var id: UUID = UUID()
    var goalTitle: String
    var currentStage: Int = 0
    var totalStages: Int
    var creationDate: Date = Date()
    
    var userDataProfile: EAUserDataProfile?
    
    private var associatedHabitIDsString: String = "" // 关联习惯ID列表
    
    init(goalTitle: String, totalStages: Int) {
        self.goalTitle = goalTitle
        self.totalStages = totalStages
    }
    
    var associatedHabitIDs: [String] {
        get {
            guard !associatedHabitIDsString.isEmpty else { return [] }
            return associatedHabitIDsString.split(separator: ",")
                .map { $0.trimmingCharacters(in: .whitespaces) }
        }
        set {
            associatedHabitIDsString = newValue.joined(separator: ",")
        }
    }
    
    // MARK: - 便捷访问方法
    
    /// 获取关联的用户（通过数据档案）
    func getUser() -> EAUser? {
        return userDataProfile?.user
    }
    
    /// 计算完成进度百分比
    func getProgressPercentage() -> Double {
        guard totalStages > 0 else { return 0.0 }
        return Double(currentStage) / Double(totalStages) * 100.0
    }
    
    /// 检查是否已完成
    func isCompleted() -> Bool {
        return currentStage >= totalStages
    }
} 