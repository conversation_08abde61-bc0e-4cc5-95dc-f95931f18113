import Foundation
import SwiftData

/// 好友通知模型 - 星际消息系统
/// 管理好友相关的所有通知，包括请求、消息、系统通知等
@Model
final class EAFriendNotification: @unchecked Sendable {
    var id: UUID = UUID()
    var createdAt: Date = Date()
    var updatedAt: Date = Date()
    
    /// 通知类型
    enum NotificationType: String, Codable, CaseIterable {
        case friendRequest = "friend_request"           // 收到好友请求
        case requestAccepted = "request_accepted"       // 好友请求被接受
        case requestRejected = "request_rejected"       // 好友请求被拒绝
        case requestExpired = "request_expired"         // 好友请求过期
        case newMessage = "new_message"                 // 收到新消息
        case friendOnline = "friend_online"             // 好友上线
        case systemNotice = "system_notice"             // 系统通知
        case cooldownNotice = "cooldown_notice"         // 冷却期通知
        case rateLimitNotice = "rate_limit_notice"      // 频率限制通知
    }
    
    /// 通知优先级
    enum Priority: String, Codable, CaseIterable {
        case low = "low"
        case normal = "normal"
        case high = "high"
        case urgent = "urgent"
    }
    
    var type: NotificationType
    var priority: Priority = Priority.normal
    var title: String
    var content: String
    var isRead: Bool = false
    var isDeleted: Bool = false
    var expiresAt: Date?
    
    // 推送通知相关
    var shouldPush: Bool = true
    var pushSent: Bool = false
    var pushSentAt: Date?
    
    // 🔑 第四阶段优化：SwiftData关系 - 指向新的活动档案模型
    var receiverActivity: EAUserSocialActivity?  // 接收通知的用户活动档案（普通属性）
    var senderActivity: EAUserSocialActivity?    // 触发通知的用户活动档案（普通属性，可选）

    /// 🔑 兼容性：接收者档案（计算属性，保持API兼容性）
    var receiverProfile: EAUserSocialProfile? {
        return receiverActivity?.socialProfile
    }

    /// 🔑 兼容性：发送者档案（计算属性，保持API兼容性）
    var senderProfile: EAUserSocialProfile? {
        return senderActivity?.socialProfile
    }
    
    // 新增：关系属性替代外键
    var relatedRequest: EAFriendRequest?       // 关联的好友请求（普通属性）
    var relatedMessage: EAFriendMessage?       // 关联的消息（普通属性）
    var relatedFriendship: EAFriendship?       // 关联的好友关系（普通属性）
    
    init(
        type: NotificationType,
        title: String,
        content: String,
        priority: Priority = .normal,
        expiresAt: Date? = nil,
        shouldPush: Bool = true
    ) {
        self.type = type
        self.title = title
        self.content = content
        self.priority = priority
        self.expiresAt = expiresAt
        self.shouldPush = shouldPush
    }
    
    /// 标记为已读
    func markAsRead() {
        isRead = true
        updatedAt = Date()
    }
    
    /// 标记为已删除
    func markAsDeleted() {
        isDeleted = true
        updatedAt = Date()
    }
    
    /// 检查是否已过期
    func isExpired() -> Bool {
        guard let expiresAt = expiresAt else { return false }
        return Date() > expiresAt
    }
    
    /// 标记推送已发送
    func markPushSent() {
        pushSent = true
        pushSentAt = Date()
        updatedAt = Date()
    }
    
    /// 检查是否需要发送推送
    func needsPush() -> Bool {
        return shouldPush && !pushSent && !isExpired()
    }
    
    /// 获取通知的显示图标
    func getDisplayIcon() -> String {
        switch type {
        case .friendRequest:
            return "person.badge.plus"
        case .requestAccepted:
            return "person.badge.checkmark"
        case .requestRejected:
            return "person.badge.minus"
        case .requestExpired:
            return "person.badge.clock"
        case .newMessage:
            return "message.badge"
        case .friendOnline:
            return "person.badge.clock.fill"
        case .systemNotice:
            return "info.circle"
        case .cooldownNotice:
            return "clock.badge.exclamationmark"
        case .rateLimitNotice:
            return "exclamationmark.triangle"
        }
    }
    
    /// 获取通知的显示颜色
    func getDisplayColor() -> String {
        switch priority {
        case .low:
            return "gray"
        case .normal:
            return "blue"
        case .high:
            return "orange"
        case .urgent:
            return "red"
        }
    }
    
    /// 获取相对时间显示文本
    func getRelativeTimeText() -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.dateTimeStyle = .named
        return formatter.localizedString(for: createdAt, relativeTo: Date())
    }
    
    /// 创建好友请求通知
    static func createFriendRequestNotification(
        senderProfile: EAUserSocialProfile,
        receiverProfile: EAUserSocialProfile,
        request: EAFriendRequest,
        message: String?
    ) -> EAFriendNotification {
        let senderName = senderProfile.user?.username ?? "未知用户"
        let title = "新的好友请求"
        let content = message?.isEmpty == false ?
            "\(senderName) 想要添加您为好友：\(message!)" :
            "\(senderName) 想要添加您为好友"

        let notification = EAFriendNotification(
            type: .friendRequest,
            title: title,
            content: content,
            priority: .normal,
            expiresAt: Calendar.current.date(byAdding: .day, value: 30, to: Date())
        )

        // 🔑 第四阶段优化：使用活动档案而非直接档案
        notification.senderActivity = senderProfile.socialActivity
        notification.receiverActivity = receiverProfile.socialActivity
        notification.relatedRequest = request

        return notification
    }
    
    /// 创建请求被接受通知
    static func createRequestAcceptedNotification(
        accepterProfile: EAUserSocialProfile,
        requesterProfile: EAUserSocialProfile,
        friendship: EAFriendship
    ) -> EAFriendNotification {
        let accepterName = accepterProfile.user?.username ?? "未知用户"
        let title = "好友请求已接受"
        let content = "\(accepterName) 接受了您的好友请求，你们现在是星际伙伴了！"
        
        let notification = EAFriendNotification(
            type: .requestAccepted,
            title: title,
            content: content,
            priority: .high,
            expiresAt: Calendar.current.date(byAdding: .day, value: 7, to: Date())
        )
        
        notification.senderActivity = accepterProfile.socialActivity
        notification.receiverActivity = requesterProfile.socialActivity
        notification.relatedFriendship = friendship
        
        return notification
    }
    
    /// 创建请求被拒绝通知
    static func createRequestRejectedNotification(
        rejecterProfile: EAUserSocialProfile,
        requesterProfile: EAUserSocialProfile,
        reason: String?
    ) -> EAFriendNotification {
        let title = "好友请求未通过"
        let content = reason?.isEmpty == false ?
            "您的好友请求未通过：\(reason!)" :
            "您的好友请求未通过，请尊重对方的选择"
        
        let notification = EAFriendNotification(
            type: .requestRejected,
            title: title,
            content: content,
            priority: .low,
            expiresAt: Calendar.current.date(byAdding: .day, value: 3, to: Date()),
            shouldPush: false  // 拒绝通知不推送，避免打扰
        )
        
        notification.senderActivity = rejecterProfile.socialActivity
        notification.receiverActivity = requesterProfile.socialActivity
        
        return notification
    }
    
    /// 创建新消息通知
    static func createNewMessageNotification(
        senderProfile: EAUserSocialProfile,
        receiverProfile: EAUserSocialProfile,
        message: EAFriendMessage
    ) -> EAFriendNotification {
        let senderName = senderProfile.user?.username ?? "未知用户"
        let title = "新消息"
        let content = "\(senderName): \(message.preview)"
        
        let notification = EAFriendNotification(
            type: .newMessage,
            title: title,
            content: content,
            priority: .normal,
            expiresAt: Calendar.current.date(byAdding: .day, value: 7, to: Date())
        )
        
        notification.senderActivity = senderProfile.socialActivity
        notification.receiverActivity = receiverProfile.socialActivity
        notification.relatedMessage = message
        
        return notification
    }
    
    /// 创建冷却期通知
    static func createCooldownNotification(
        userProfile: EAUserSocialProfile,
        remainingHours: Int
    ) -> EAFriendNotification {
        let title = "请求发送受限"
        let content = "由于之前的请求被拒绝，您需要等待 \(remainingHours) 小时后才能再次发送好友请求"
        
        let notification = EAFriendNotification(
            type: .cooldownNotice,
            title: title,
            content: content,
            priority: .normal,
            shouldPush: false
        )
        
        notification.receiverActivity = userProfile.socialActivity
        
        return notification
    }
    
    /// 创建频率限制通知
    static func createRateLimitNotification(
        userProfile: EAUserSocialProfile,
        limitType: String
    ) -> EAFriendNotification {
        let title = "发送频率受限"
        let content = limitType == "daily" ?
            "您今天已发送5个好友请求，请明天再试" :
            "您这个小时已发送2个好友请求，请稍后再试"
        
        let notification = EAFriendNotification(
            type: .rateLimitNotice,
            title: title,
            content: content,
            priority: .normal,
            shouldPush: false
        )
        
        notification.receiverActivity = userProfile.socialActivity
        
        return notification
    }
}
