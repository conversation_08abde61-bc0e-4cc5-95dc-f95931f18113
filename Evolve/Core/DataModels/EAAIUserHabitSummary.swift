//
//  EAAIUserHabitSummary.swift
//  Evolve
//
//  Created by AI Assistant on 2025-06-06.
//

import Foundation

/// AI用户习惯摘要数据模型
/// 用于AI数据桥接，将用户习惯数据转换为AI可理解的格式
struct EAAIUserHabitSummary: Codable {
    let userId: UUID
    let totalHabits: Int
    let activeHabits: Int
    let habits: [EAAIHabitData]
    let recentCompletions: [EAAICompletionData]
    let completionRate: Double
    let streakData: [String: Int]
    let analysisTimestamp: Date
    
    /// 创建空的摘要（用于错误情况）
    static func empty(userId: UUID) -> EAAIUserHabitSummary {
        return EAAIUserHabitSummary(
            userId: userId,
            totalHabits: 0,
            activeHabits: 0,
            habits: [],
            recentCompletions: [],
            completionRate: 0.0,
            streakData: [:],
            analysisTimestamp: Date()
        )
    }
}

/// AI习惯数据格式
struct EAAIHabitData: Codable {
    let id: UUID
    let name: String
    let category: String
    let targetFrequency: Int
    let timeOfDay: String?
    let isActive: Bool
    let creationDate: Date
    let iconName: String
    
    /// 从EAHabit转换为AI格式
    init(from habit: EAHabit) {
        self.id = habit.id
        self.name = habit.name
        self.category = habit.category
        self.targetFrequency = habit.targetFrequency
        self.timeOfDay = habit.preferredTimeSlot
        self.isActive = habit.isActive
        self.creationDate = habit.creationDate
        self.iconName = habit.iconName
    }
}

/// AI完成数据格式
struct EAAICompletionData: Codable {
    let id: UUID
    let habitName: String
    let date: Date
    let timestamp: Date
    let energyLevel: Int
    let timeOfDay: String
    let note: String?
    
    /// 从EACompletion转换为AI格式
    init(from completion: EACompletion) {
        self.id = completion.id
        self.habitName = completion.habit?.name ?? "Unknown"
        self.date = completion.date
        self.timestamp = completion.date
        self.energyLevel = completion.energyLevel
        self.timeOfDay = EAAICompletionData.determineTimeOfDay(from: completion.date)
        self.note = completion.completionNote
    }
    
    /// 根据时间确定时段
    private static func determineTimeOfDay(from date: Date) -> String {
        let hour = Calendar.current.component(.hour, from: date)
        switch hour {
        case 5..<12:
            return "morning"
        case 12..<17:
            return "afternoon"
        case 17..<21:
            return "evening"
        default:
            return "night"
        }
    }
}

/// AI用户档案数据模型
/// 用于存储AI分析的用户行为模式和偏好
struct EAAIUserProfile: Codable {
    let userId: UUID
    let username: String
    let creationDate: Date
    let preferredCoachStyle: String
    let timeZone: String
    let habitPreferences: EAAIHabitPreferences
    let behaviorPatterns: EAAIBehaviorPatterns
    let analysisTimestamp: Date
    
    /// 创建空的档案（用于错误情况）
    static func empty(userId: UUID) -> EAAIUserProfile {
        return EAAIUserProfile(
            userId: userId,
            username: "未知用户",
            creationDate: Date(),
            preferredCoachStyle: "supportive",
            timeZone: TimeZone.current.identifier,
            habitPreferences: EAAIHabitPreferences.empty(),
            behaviorPatterns: EAAIBehaviorPatterns.empty(),
            analysisTimestamp: Date()
        )
    }
}

/// AI习惯偏好分析
struct EAAIHabitPreferences: Codable {
    let preferredCategories: [String]
    let preferredDifficulty: String
    let preferredTimeSlots: [String]
    let averageTargetFrequency: Double
    let mostActiveWeekdays: [Int]
    
    static func empty() -> EAAIHabitPreferences {
        return EAAIHabitPreferences(
            preferredCategories: [],
            preferredDifficulty: "简单",
            preferredTimeSlots: [],
            averageTargetFrequency: 1.0,
            mostActiveWeekdays: []
        )
    }
}

/// AI行为模式分析
struct EAAIBehaviorPatterns: Codable {
    let peakActivityHours: [Int]
    let completionRateByTimeOfDay: [String: Double]
    let streakPatterns: [String: Int]
    let energyLevelPatterns: [String: Double]
    let consistencyScore: Double
    let motivationTriggers: [String]
    
    static func empty() -> EAAIBehaviorPatterns {
        return EAAIBehaviorPatterns(
            peakActivityHours: [],
            completionRateByTimeOfDay: [:],
            streakPatterns: [:],
            energyLevelPatterns: [:],
            consistencyScore: 0.0,
            motivationTriggers: []
        )
    }
}

/// AI行为分析数据模型
struct EAAIBehaviorAnalysis: Codable {
    let userId: UUID
    let completionPatterns: [String]
    let timePreferences: [String]
    let motivationTriggers: [String]
    let challengeAreas: [String]
    let successFactors: [String]
    let analysisTimestamp: Date
}

// MARK: - EAHabit扩展：AI格式转换

extension EAHabit {
    /// 转换为AI格式
    func toAIFormat() -> EAAIHabitData {
        return EAAIHabitData(from: self)
    }
}

// MARK: - EACompletion扩展：AI格式转换

extension EACompletion {
    /// 转换为AI格式
    func toAIFormat() -> EAAICompletionData {
        return EAAICompletionData(from: self)
    }
} 