import Foundation
import SwiftData

/// 社区举报模型 - 管理用户举报不当内容
/// 严格遵循SwiftData双向关系规范，使用@Relationship建立数据关系
@Model
final class EACommunityReport: @unchecked Sendable {
    var id: UUID = UUID()
    var creationDate: Date = Date()     // 举报时间
    var status: String = "pending"      // 处理状态：pending、reviewing、resolved、dismissed
    var priority: String = "normal"     // 优先级：low、normal、high、urgent
    
    // 被举报目标类型（互斥，只能是帖子、评论或用户之一）
    var targetType: String              // 举报目标类型："post"、"comment"、"user"
    
    // 举报详情
    var reportReason: String            // 举报原因：spam、harassment、inappropriate、hate_speech、misinformation、other
    var reportDescription: String?      // 详细描述（可选）
    var category: String = "content"    // 举报分类：content、behavior、technical
    
    // 审核信息
    var reviewDate: Date?               // 审核时间
    var reviewDecision: String?         // 审核决定：valid、invalid、partial
    var reviewNotes: String?            // 审核备注
    var actionTaken: String?            // 采取的行动：none、warning、content_removal、account_suspension
    
    // AI辅助审核
    var aiConfidenceScore: Double = 0.0 // AI检测置信度 (0.0-1.0)
    var aiRecommendation: String?       // AI推荐处理方式
    var toxicityScore: Double = 0.0     // 毒性检测分数 (0.0-1.0)
    
    // 统计信息
    var similarReportsCount: Int = 0    // 相似举报数量
    var reporterReliabilityScore: Double = 0.8  // 举报者可靠性分数 (0.0-1.0)
    
    // 🔗 SwiftData关系：举报者（通过管理档案）
    var reporter: EAUserModerationProfile?  // 举报者的管理档案（普通属性，EAUserModerationProfile端定义inverse）
    
    // 🔗 SwiftData关系：被举报用户（通过管理档案）
    var reportedUser: EAUserModerationProfile?  // 被举报用户的管理档案（普通属性，EAUserModerationProfile端定义inverse）
    
    // 🔗 SwiftData关系：被举报内容（互斥关系）
    var targetPost: EACommunityPost?    // 被举报帖子（普通属性，EACommunityPost端定义inverse）
    var targetComment: EACommunityComment?  // 被举报评论（普通属性，EACommunityComment端定义inverse）
    
    // ✅ 修复：移除外键模式计算属性，遵循.cursorrules规范
    // 通过关系访问用户数据，而非外键模式
    // 使用方式：report.reporter?.user?.id（通过关系链访问）
    
    // 审核员（直接关联用户，因为审核员不需要通过管理档案）
    var reviewer: EAUser?
    
    init(
        reportReason: String,
        targetType: String,
        reportDescription: String? = nil
    ) {
        self.reportReason = reportReason
        self.targetType = targetType
        self.reportDescription = reportDescription
        
        // 根据举报原因设置初始优先级
        setPriorityBasedOnReason()
    }
    
    // MARK: - 便捷方法
    
    /// 根据举报原因设置优先级
    private func setPriorityBasedOnReason() {
        switch reportReason {
        case "hate_speech", "harassment":
            priority = "urgent"
        case "spam", "misinformation":
            priority = "high"
        case "inappropriate":
            priority = "normal"
        default:
            priority = "low"
        }
    }
    
    /// 开始审核
    func startReview(reviewer: EAUser) {
        self.reviewer = reviewer
        self.reviewDate = Date()
        self.status = "reviewing"
    }
    
    /// 完成审核
    func completeReview(
        decision: String,
        actionTaken: String,
        notes: String? = nil
    ) {
        self.reviewDecision = decision
        self.actionTaken = actionTaken
        self.reviewNotes = notes
        self.status = "resolved"
    }
    
    /// 驳回举报
    func dismissReport(reason: String? = nil) {
        self.status = "dismissed"
        self.reviewNotes = reason
        self.reviewDate = Date()
    }
    
    /// 获取目标ID（无论是帖子、评论还是用户）
    /// ✅ 修复：使用关系链访问，而非外键模式
    func getTargetId() -> UUID? {
        switch targetType {
        case "post":
            return targetPost?.id
        case "comment":
            return targetComment?.id
        case "user":
            return reportedUser?.user?.id
        default:
            return nil
        }
    }
    
    /// 检查是否为紧急举报
    func isUrgent() -> Bool {
        return priority == "urgent" || toxicityScore > 0.8
    }
    
    /// 检查是否需要AI辅助审核
    func needsAIReview() -> Bool {
        return aiConfidenceScore == 0.0 && status == "pending"
    }
    
    /// 检查是否为有效举报（基于AI和人工审核）
    func isLikelyValid() -> Bool {
        if let decision = reviewDecision {
            return decision == "valid" || decision == "partial"
        }
        // 如果还未人工审核，基于AI判断
        return aiConfidenceScore > 0.7 || toxicityScore > 0.6
    }
    
    /// 获取举报年龄（小时）
    func getAgeInHours() -> Double {
        return Date().timeIntervalSince(creationDate) / 3600.0
    }
    
    /// 检查是否超时未处理
    func isOverdue() -> Bool {
        let maxHours: Double = switch priority {
        case "urgent": 2.0
        case "high": 24.0
        case "normal": 72.0
        default: 168.0  // 7 days for low priority
        }
        
        return status == "pending" && getAgeInHours() > maxHours
    }
    
    /// 更新相似举报数量
    func updateSimilarReportsCount(_ count: Int) {
        similarReportsCount = count
        
        // 如果有多个相似举报，提高优先级
        if count >= 3 && priority != "urgent" {
            priority = "high"
        }
    }
    
    /// 获取举报者用户名（通过管理档案访问）
    /// ✅ 修复：使用关系链访问，而非外键模式
    func getReporterUsername() -> String {
        // 通过关系链访问用户信息
        if let userId = reporter?.user?.id {
            return "用户ID: \(userId.uuidString.prefix(8))"
        }
        return "未知用户"
    }
    
    /// 获取审核员用户名（通过关系访问）
    func getReviewerUsername() -> String {
        return reviewer?.username ?? "未分配"
    }
    
    /// 获取目标内容描述（通过关系访问）
    func getTargetDescription() -> String {
        switch targetType {
        case "post":
            if let post = targetPost {
                return "帖子: \(String(post.content.prefix(30)))"
            }
        case "comment":
            if let comment = targetComment {
                return "评论: \(String(comment.content.prefix(30)))"
            }
        case "user":
            if let userId = reportedUser?.user?.id {
                return "用户ID: \(userId.uuidString.prefix(8))"
            }
        default:
            break
        }
        return "未知内容"
    }
    
    /// 设置帖子目标（确保互斥性）
    func setPostTarget(_ post: EACommunityPost) {
        self.targetPost = post
        self.targetComment = nil
        self.reportedUser = nil
        self.targetType = "post"
    }
    
    /// 设置评论目标（确保互斥性）
    func setCommentTarget(_ comment: EACommunityComment) {
        self.targetComment = comment
        self.targetPost = nil
        self.reportedUser = nil
        self.targetType = "comment"
    }
    
    /// 设置用户目标（确保互斥性）
    func setUserTarget(_ userProfile: EAUserModerationProfile) {
        self.reportedUser = userProfile
        self.targetPost = nil
        self.targetComment = nil
        self.targetType = "user"
    }
} 