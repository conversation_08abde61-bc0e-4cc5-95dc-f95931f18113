import Foundation
import SwiftData

@Model
final class EAUserSettings: @unchecked Sendable {
    var id: UUID = UUID()
    var preferredCoachStyle: String = "温柔鼓励型"
    var notificationsEnabled: Bool = true
    var smartTimingEnabled: Bool = true
    var reminderStyle: String = "gentle"
    
    // 🔑 修复：SwiftData数组序列化问题 - 使用字符串存储
    private var preferredReminderTimesString: String = "09:00,18:00"
    
    // 🔗 关系的另一端：使用普通属性，避免循环引用
    // SwiftData会自动维护inverse关系
    var user: EAUser?
    
    init() {
        // 默认初始化
    }
    
    // 🔑 计算属性：维护API兼容性
    var preferredReminderTimes: [String] {
        get {
            guard !preferredReminderTimesString.isEmpty else { return ["09:00", "18:00"] }
            return preferredReminderTimesString.split(separator: ",")
                .map { $0.trimmingCharacters(in: .whitespaces) }
        }
        set {
            preferredReminderTimesString = newValue.joined(separator: ",")
        }
    }
} 