import Foundation
import SwiftData

@Model
final class EAPayment: @unchecked Sendable {
    var id: UUID = UUID()
    var productId: String
    var transactionId: String
    var purchaseDate: Date = Date()
    var expirationDate: Date?
    var isActive: Bool = true
    
    var userDataProfile: EAUserDataProfile?
    
    init(productId: String, transactionId: String, expirationDate: Date? = nil) {
        self.productId = productId
        self.transactionId = transactionId
        self.expirationDate = expirationDate
    }
    
    // MARK: - 便捷访问方法
    
    /// 获取关联的用户（通过数据档案）
    func getUser() -> EAUser? {
        return userDataProfile?.user
    }
    
    /// 检查支付是否已过期
    func isExpired() -> Bool {
        guard let expirationDate = expirationDate else { return false }
        return Date() > expirationDate
    }
} 