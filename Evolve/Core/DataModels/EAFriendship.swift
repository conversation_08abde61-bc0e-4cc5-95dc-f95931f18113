import Foundation
import SwiftData

/// 好友关系模型 - 星际伙伴系统
/// 记录用户之间的好友关系，支持星际能量共振和友谊等级
@Model
final class EAFriendship: @unchecked Sendable {
    var id: UUID = UUID()
    var creationDate: Date = Date()
    var lastInteractionDate: Date = Date()
    
    /// 好友关系状态
    /// 🔑 优化：移除废弃的blocked状态，统一使用EABlockedUser管理屏蔽
    enum FriendshipStatus: String, Codable, CaseIterable {
        case active = "active"      // 活跃好友
        case deleted = "deleted"    // 已删除（软删除）
    }
    
    var status: FriendshipStatus
    var isCloseFriend: Bool = false
    var friendNickname: String?
    
    // 星际能量系统（数字宇宙主题）
    var sharedStellarEnergy: Int = 0    // 共同产生的星际能量
    var friendshipLevel: Int = 1        // 友谊等级 (1-10)
    var totalMessagesCount: Int = 0     // 总消息数量
    var lastMessageDate: Date?          // 最后消息时间
    
    // ✅ 修复：SwiftData关系 - 严格遵循单端inverse规则
    // 🔑 在EAUserSocialProfile端定义@Relationship，EAFriendship端使用普通属性

    /// 🚨 致命修复：发起者档案（普通属性，遵循SwiftData单端inverse规则）
    /// 注意：EAUserSocialProfile端定义@Relationship(inverse: \EAFriendship.initiatorProfile)，这里必须是普通属性
    var initiatorProfile: EAUserSocialProfile?

    /// 🚨 致命修复：好友档案（普通属性，遵循SwiftData单端inverse规则）
    /// 注意：EAUserSocialProfile端定义@Relationship(inverse: \EAFriendship.friendProfile)，这里必须是普通属性
    var friendProfile: EAUserSocialProfile?

    /// 消息关系 - 🔑 标准化：升级为@Relationship，与EAFriendMessage的inverse对应
    /// 友谊关系的所有消息（一对多关系）
    @Relationship(deleteRule: .cascade, inverse: \EAFriendMessage.friendship)
    var messages: [EAFriendMessage]
    
    init() {
        self.status = .active  // 在初始化器中设置默认值
        self.messages = []
        // ✅ 修复：不在init中赋值关系属性，遵循SwiftData最佳实践
        // 关系赋值将在Repository层的"插入→赋值→保存"流程中完成
    }

    /// 🔑 新增：正确设置好友关系的方法
    /// 用于原子性操作中建立关系
    func setupRelationship(initiator: EAUserSocialProfile, friend: EAUserSocialProfile) {
        self.initiatorProfile = initiator
        self.friendProfile = friend
    }
    
    /// 🔑 关键修复：验证好友关系是否有效（新用户友好版本）
    func isValidFriendship() -> Bool {
        // 基础状态检查
        guard status == .active else { return false }

        // 关系属性存在性检查
        guard let initiator = initiatorProfile, let friend = friendProfile else { return false }

        // 🚨 关键修复：简化用户完整性验证，避免用户切换场景下的过度验证
        // 原问题：严格验证user对象存在，但在用户切换后Context中可能缺失
        // 新方案：只验证核心ID存在，允许用户对象在Context中延迟加载
        guard !initiator.id.uuidString.isEmpty, !friend.id.uuidString.isEmpty else { return false }

        // 确保两个档案不是同一个用户
        guard initiator.id != friend.id else { return false }

        return true
    }
    
    /// 执行能量共振 - 增加星际能量和友谊等级
    func performEnergyResonance(amount: Int) {
        sharedStellarEnergy += amount
        friendshipLevel = min(10, 1 + sharedStellarEnergy / 1000)
        lastInteractionDate = Date()
    }
    
    /// 🚨 关键修复：线程安全的获取对方用户档案（解决Context冲突问题）
    /// 注意：此方法必须在正确的Context中调用，避免跨线程访问
    @MainActor
    func getOtherProfile(currentProfile: EAUserSocialProfile) -> EAUserSocialProfile? {
        // 🚨 关键修复：首先验证好友关系的基础完整性
        guard let initiator = initiatorProfile, let friend = friendProfile else {
            return nil
        }

        // 🔑 新增：验证当前用户档案的完整性
        guard currentProfile.user != nil, currentProfile.stellarLevel != nil else {
            return nil
        }

        // 🚨 关键修复：安全的ID比较，避免Context冲突
        let currentProfileId = currentProfile.id
        let initiatorId = initiator.id
        let friendId = friend.id

        // 确定对方档案
        let otherProfile: EAUserSocialProfile?
        if initiatorId == currentProfileId {
            otherProfile = friend
        } else if friendId == currentProfileId {
            otherProfile = initiator
        } else {
            // 当前用户不是此好友关系的参与者
            return nil
        }

        // 🔑 新增：验证对方档案的数据完整性
        guard let other = otherProfile,
              other.user != nil,
              other.stellarLevel != nil else {
            return nil
        }

        return other
    }

    /// 🔑 新增：线程安全的获取对方用户ID（避免Context访问）
    func getOtherProfileId(currentProfileId: UUID) -> UUID? {
        // 🚨 关键修复：只访问ID属性，避免完整对象访问
        guard let initiator = initiatorProfile, let friend = friendProfile else {
            return nil
        }

        let initiatorId = initiator.id
        let friendId = friend.id

        if initiatorId == currentProfileId {
            return friendId
        } else if friendId == currentProfileId {
            return initiatorId
        } else {
            return nil
        }
    }
    
    /// 更新最后消息时间和计数
    func updateMessageStats() {
        lastMessageDate = Date()
        totalMessagesCount += 1
        lastInteractionDate = Date()
    }
}
