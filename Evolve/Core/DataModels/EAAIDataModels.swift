import Foundation

/// AI数据桥接模型 - 用于Repository和AI服务之间的数据传递
/// 遵循开发规范文档的AI成本控制和数据格式转换要求

// MARK: - AI习惯数据摘要

/// AI习惯数据摘要
/// 为AI分析用户习惯数据提供结构化格式
struct EAAIHabitSummary: Codable {
    let userId: UUID
    let totalHabits: Int
    let activeHabits: Int
    let recentCompletionRate: Double // 最近7天完成率
    let mostSuccessfulHabit: String? // 最成功的习惯名称
    let strugglingHabits: [String] // 困难习惯列表
    let analysisTimestamp: Date
    
    /// 转换为AI可理解的文本格式
    func toAIPromptContext() -> String {
        let completionPercentage = Int(recentCompletionRate * 100)
        let successHabit = mostSuccessfulHabit ?? "无"
        let strugglingList = strugglingHabits.isEmpty ? "无" : strugglingHabits.joined(separator: ", ")
        
        return """
        用户习惯分析：
        - 总习惯数量：\(totalHabits)个
        - 活跃习惯：\(activeHabits)个
        - 近7天完成率：\(completionPercentage)%
        - 表现最佳习惯：\(successHabit)
        - 需要支持的习惯：\(strugglingList)
        - 分析时间：\(analysisTimestamp.formatted(.dateTime.month().day().hour().minute()))
        """
    }
}

// MARK: - AI社区活动摘要

/// AI社区活动摘要
/// 为AI分析用户社区参与度提供结构化格式
struct EAAICommunityActivitySummary: Codable {
    let userId: UUID
    let totalPosts: Int
    let recentPostsCount: Int // 今日发帖数
    let averageLikesPerPost: Double
    let communityEngagementScore: Double
    let analysisTimestamp: Date
    
    /// 转换为AI可理解的文本格式
    func toAIPromptContext() -> String {
        let likesPerPost = String(format: "%.1f", averageLikesPerPost)
        let engagementScore = String(format: "%.1f", communityEngagementScore)
        
        return """
        用户社区活动分析：
        - 总发帖数：\(totalPosts)篇
        - 今日发帖：\(recentPostsCount)篇
        - 平均每帖获赞：\(likesPerPost)个
        - 社区参与度得分：\(engagementScore)分
        - 分析时间：\(analysisTimestamp.formatted(.dateTime.month().day().hour().minute()))
        """
    }
}

// MARK: - AI会话上下文

/// AI会话上下文
/// 为AI对话提供必要的上下文信息
struct EAAIConversationContext: Codable {
    let conversationId: UUID
    let userId: UUID
    let sessionType: String // daily_checkin, habit_coaching, crisis_support
    let habitSummary: EAAIHabitSummary?
    let communitySummary: EAAICommunityActivitySummary?
    let recentMessages: [String] // 最近的对话内容
    let contextTimestamp: Date
    
    /// 生成AI提示词前缀
    func generateAIPromptPrefix() -> String {
        var prefix = """
        你是Evolve应用的AI教练，专注于帮助用户培养好习惯。
        
        会话类型：\(sessionType)
        当前时间：\(contextTimestamp.formatted(.dateTime.month().day().hour().minute()))
        
        """
        
        if let habit = habitSummary {
            prefix += habit.toAIPromptContext() + "\n\n"
        }
        
        if let community = communitySummary {
            prefix += community.toAIPromptContext() + "\n\n"
        }
        
        if !recentMessages.isEmpty {
            prefix += "最近对话：\n"
            for (index, message) in recentMessages.enumerated() {
                prefix += "[\(index + 1)] \(message)\n"
            }
            prefix += "\n"
        }
        
        prefix += """
        请基于以上用户信息，以温暖、鼓励、专业的语气回应用户。
        回复应该个性化、具体，避免模板化的建议。
        """
        
        return prefix
    }
}

// MARK: - AI响应缓存

/// AI响应缓存项
/// 用于实现AI成本控制的缓存机制
struct EAAIResponseCacheItem: Codable {
    let cacheId: UUID
    let questionHash: String // 问题的哈希值，用于匹配相似问题
    let response: String
    let contextType: String
    let userId: UUID?
    let createdAt: Date
    let expiresAt: Date
    let useCount: Int
    
    /// 检查缓存是否有效
    func isValid() -> Bool {
        return Date() < expiresAt
    }
    
    /// 检查是否匹配特定问题
    func matches(questionHash: String, userId: UUID?) -> Bool {
        guard isValid() else { return false }
        
        if let cacheUserId = self.userId, let userId = userId {
            return self.questionHash == questionHash && cacheUserId == userId
        } else {
            return self.questionHash == questionHash && self.userId == nil
        }
    }
}

// MARK: - AI数据格式转换工具

/// 社区AI数据摘要（Phase 2新增）
/// 为AI社区功能分析提供结构化格式
struct EAAISocialSummary: Codable {
    let userId: UUID
    let postsCount: Int
    let likesReceived: Int
    let commentsReceived: Int
    let stellarLevel: Int
    let totalStellarEnergy: Int
    let analysisTimestamp: Date
    
    /// 转换为AI可理解的文本格式
    func toAIPromptContext() -> String {
        return """
        用户星际社区档案：
        - 用户ID：\(userId)
        - 发布信标数：\(postsCount)篇
        - 获得能量共振：\(likesReceived)次
        - 收到时空对话：\(commentsReceived)次
        - 星际等级：Lv.\(stellarLevel)
        - 累计星际能量：\(totalStellarEnergy)点
        - 分析时间：\(analysisTimestamp.formatted(.dateTime.month().day().hour().minute()))
        """
    }
}

/// AI推荐上下文（Phase 2新增）
/// 为AI内容推荐提供用户偏好和行为数据
struct EAAIRecommendationContext: Codable {
    let userId: UUID
    let interests: [String]
    let socialConnections: [UUID]
    let recentInteractions: [EACommunityInteraction]
    let contentPreferences: [String]
    let stellarLevel: Int
    let analysisTimestamp: Date
    
    /// 生成推荐上下文提示词
    func generateRecommendationPrompt() -> String {
        let interestsText = interests.isEmpty ? "暂无" : interests.joined(separator: ", ")
        let preferencesText = contentPreferences.isEmpty ? "暂无" : contentPreferences.joined(separator: ", ")
        
        return """
        星际探索者推荐上下文：
        - 探索者等级：Lv.\(stellarLevel)
        - 兴趣领域：\(interestsText)
        - 内容偏好：\(preferencesText)
        - 社交连接数：\(socialConnections.count)
        - 近期互动数：\(recentInteractions.count)
        - 分析时间：\(analysisTimestamp.formatted(.dateTime.month().day().hour().minute()))
        
        请基于以上信息推荐适合的宇宙信标内容和星际探索者。
        """
    }
}

/// 社区交互记录（Phase 2新增）
/// 记录用户在社区中的交互行为
struct EACommunityInteraction: Codable {
    let type: String        // "like", "comment", "share", "post"
    let timestamp: Date
    let contentId: UUID
    let targetUserId: UUID?
    
    /// 转换为可读的描述
    func toDescription() -> String {
        let timeString = timestamp.formatted(.dateTime.hour().minute())
        switch type {
        case "like":
            return "\(timeString) 进行了能量共振"
        case "comment":
            return "\(timeString) 发起了时空对话"
        case "share":
            return "\(timeString) 进行了星际传送"
        case "post":
            return "\(timeString) 发布了宇宙信标"
        default:
            return "\(timeString) 进行了未知交互"
        }
    }
}

/// AI数据格式转换工具
/// 提供各种数据格式之间的转换功能
struct EAAIDataFormatter {
    
    /// 将习惯列表转换为AI摘要
    static func formatHabitsForAI(_ habits: [EAHabit], completions: [EACompletion]) -> String {
        var summary = "用户习惯概览：\n"
        
        for habit in habits {
            let habitCompletions = completions.filter { $0.habit?.id == habit.id }
            let last7DaysCompletions = habitCompletions.filter { 
                Calendar.current.isDate($0.date, inSameDayAs: Date()) ||
                abs($0.date.timeIntervalSinceNow) < 7 * 24 * 3600
            }
            
            summary += "• \(habit.name)：近7天完成\(last7DaysCompletions.count)次"
            if habit.isActive {
                summary += "（活跃）"
            }
            summary += "\n"
        }
        
        return summary
    }
    
    /// 生成问题哈希值（用于缓存匹配）
    static func generateQuestionHash(_ question: String, context: String? = nil) -> String {
        let combined = question + (context ?? "")
        return String(combined.hash)
    }
    
    /// 创建AI会话上下文
    static func createConversationContext(
        userId: UUID,
        sessionType: String,
        habitSummary: EAAIHabitSummary?,
        communitySummary: EAAICommunityActivitySummary?,
        recentMessages: [String] = []
    ) -> EAAIConversationContext {
        return EAAIConversationContext(
            conversationId: UUID(),
            userId: userId,
            sessionType: sessionType,
            habitSummary: habitSummary,
            communitySummary: communitySummary,
            recentMessages: Array(recentMessages.suffix(5)), // 只保留最近5条消息
            contextTimestamp: Date()
        )
    }
}

// MARK: - AI缓存数据模型

/// AI洞察缓存数据
struct EAAIInsight: Codable {
    let id: UUID
    let userId: UUID
    let insightType: String
    let content: String
    let confidence: Double
    let generatedAt: Date
    let expiresAt: Date
    let metadata: [String: String]
}

/// 相似场景回复缓存数据
struct EAAISimilarScenarioResponse: Codable {
    let originalScenario: String
    let response: String
    let confidence: Double
    let responseType: String
    let generatedAt: Date
    let usageCount: Int
}

// MARK: - AI推荐结果数据模型

/// AI推荐结果
/// 包含个性化推荐的帖子和用户
struct EARecommendationResult {
    let posts: [EACommunityPost]
    let users: [EAUser]
    let generatedAt: Date

    /// 获取推荐摘要
    func getSummary() -> String {
        return "推荐了 \(posts.count) 篇信标和 \(users.count) 位探索者"
    }
}

// MARK: - AI增强功能统计数据模型

/// AI增强功能统计信息
struct EAAIEnhancementStatistics: Codable {
    var successfulEnhancements: Int = 0
    var failedEnhancements: Int = 0
    var aiEnabledCount: Int = 0
    var aiDisabledCount: Int = 0
    var cacheHits: Int = 0
    var cacheMisses: Int = 0
    var manualRefreshCount: Int = 0
    var cacheStatistics: EAAICacheStatistics?

    /// 获取缓存命中率
    func getCacheHitRate() -> Double {
        let totalRequests = cacheHits + cacheMisses
        return totalRequests > 0 ? Double(cacheHits) / Double(totalRequests) : 0.0
    }

    /// 计算成功率
    var successRate: Double {
        let total = successfulEnhancements + failedEnhancements
        guard total > 0 else { return 0.0 }
        return Double(successfulEnhancements) / Double(total)
    }
}

/// AI缓存统计信息
struct EAAICacheStatistics: Codable {
    var userProfileHits: Int = 0
    var userProfileSets: Int = 0
    var userProfileCacheSize: Int = 0

    var aiInsightHits: Int = 0
    var aiInsightSets: Int = 0
    var aiInsightCacheSize: Int = 0

    var behaviorAnalysisHits: Int = 0
    var behaviorAnalysisSets: Int = 0
    var behaviorAnalysisCacheSize: Int = 0

    var similarScenarioHits: Int = 0
    var similarScenarioSets: Int = 0
    var similarScenarioCacheSize: Int = 0

    var sharingMomentHits: Int = 0
    var sharingMomentSets: Int = 0
    var sharingMomentCacheSize: Int = 0

    var recommendationHits: Int = 0
    var recommendationSets: Int = 0
    var recommendationCacheSize: Int = 0

    var guideConversationHits: Int = 0
    var guideConversationSets: Int = 0
    var guideConversationCacheSize: Int = 0

    var lastCleanupTime: Date = Date()

    /// 获取总体缓存效率
    func getOverallEfficiency() -> Double {
        let totalHits = userProfileHits + aiInsightHits + behaviorAnalysisHits +
                        similarScenarioHits + sharingMomentHits + recommendationHits + guideConversationHits
        let totalSets = userProfileSets + aiInsightSets + behaviorAnalysisSets +
                        similarScenarioSets + sharingMomentSets + recommendationSets + guideConversationSets
        return totalSets > 0 ? Double(totalHits) / Double(totalSets) : 0.0
    }

    /// 计算总缓存大小
    var totalCacheSize: Int {
        return userProfileCacheSize + aiInsightCacheSize + behaviorAnalysisCacheSize +
               similarScenarioCacheSize + sharingMomentCacheSize + recommendationCacheSize + guideConversationCacheSize
    }
}

// MARK: - 扩展工具

/// Array扩展：提供去重功能
extension Array where Element: Hashable {
    /// 返回数组中的唯一元素
    func unique() -> [Element] {
        return Array(Set(self))
    }
} 