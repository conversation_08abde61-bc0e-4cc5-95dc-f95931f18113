import Foundation
import SwiftUI

/// 头像类型枚举
/// 定义用户可选择的头像类型，包括系统预设头像和自定义头像
enum EAAvatarType: String, CaseIterable, Codable {
    // 系统预设头像 - 使用SF Symbol图标
    case systemPerson = "person.circle.fill"
    case systemStar = "star.circle.fill"
    case systemHeart = "heart.circle.fill"
    case systemLeaf = "leaf.circle.fill"
    case systemSun = "sun.max.circle.fill"
    case systemMoon = "moon.circle.fill"
    case systemBolt = "bolt.circle.fill"
    case systemFlame = "flame.circle.fill"
    case systemDrop = "drop.circle.fill"
    case systemSnowflake = "snowflake.circle.fill"
    case systemMountain = "mountain.2.circle.fill"
    case systemTree = "tree.circle.fill"
    case systemPaw = "pawprint.circle.fill"
    case systemButterfly = "ladybug.circle.fill"
    case systemTurtle = "tortoise.circle.fill"
    case systemRabbit = "hare.circle.fill"
    case systemCat = "cat.circle.fill"
    case systemDog = "dog.circle.fill"
    
    // 自定义头像
    case custom = "custom"
    
    /// 头像显示名称
    var displayName: String {
        switch self {
        case .systemPerson: return "人物"
        case .systemStar: return "星星"
        case .systemHeart: return "爱心"
        case .systemLeaf: return "叶子"
        case .systemSun: return "太阳"
        case .systemMoon: return "月亮"
        case .systemBolt: return "闪电"
        case .systemFlame: return "火焰"
        case .systemDrop: return "水滴"
        case .systemSnowflake: return "雪花"
        case .systemMountain: return "山峰"
        case .systemTree: return "树木"
        case .systemPaw: return "爪印"
        case .systemButterfly: return "瓢虫"
        case .systemTurtle: return "乌龟"
        case .systemRabbit: return "兔子"
        case .systemCat: return "猫咪"
        case .systemDog: return "小狗"
        case .custom: return "自定义"
        }
    }
    
    /// 是否为系统预设头像
    var isSystemAvatar: Bool {
        return self != .custom
    }
    
    /// 获取系统预设头像列表
    static var systemAvatars: [EAAvatarType] {
        return allCases.filter { $0.isSystemAvatar }
    }
    
    /// 头像颜色
    var avatarColor: Color {
        switch self {
        case .systemPerson: return .blue
        case .systemStar: return .yellow
        case .systemHeart: return .red
        case .systemLeaf: return .green
        case .systemSun: return .orange
        case .systemMoon: return .purple
        case .systemBolt: return .cyan
        case .systemFlame: return .red
        case .systemDrop: return .blue
        case .systemSnowflake: return .cyan
        case .systemMountain: return .brown
        case .systemTree: return .green
        case .systemPaw: return .brown
        case .systemButterfly: return .pink
        case .systemTurtle: return .green
        case .systemRabbit: return .gray
        case .systemCat: return .orange
        case .systemDog: return .brown
        case .custom: return .accentColor
        }
    }
}

/// 头像数据模型
/// 用于存储和传递头像相关信息
struct EAAvatarData: Codable, Equatable {
    let type: EAAvatarType
    let customImageData: Data?
    let backgroundColorHex: String?
    
    init(type: EAAvatarType, customImageData: Data? = nil, backgroundColorHex: String? = nil) {
        self.type = type
        self.customImageData = customImageData
        self.backgroundColorHex = backgroundColorHex
    }
    
    /// 是否有效的头像数据
    var isValid: Bool {
        if type == .custom {
            return customImageData != nil
        }
        return true
    }
    
    // MARK: - Equatable实现
    static func == (lhs: EAAvatarData, rhs: EAAvatarData) -> Bool {
        return lhs.type == rhs.type && lhs.customImageData == rhs.customImageData && lhs.backgroundColorHex == rhs.backgroundColorHex
    }
} 