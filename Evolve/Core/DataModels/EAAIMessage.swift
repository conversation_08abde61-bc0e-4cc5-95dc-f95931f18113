import Foundation
import SwiftData

@Model
final class EAAIMessage: @unchecked Sendable {
    var id: UUID = UUID()
    var userMessage: String
    var aiResponse: String
    var timestamp: Date = Date()
    var conversationType: String = "general_chat"
    var emotionalContext: String? // 情绪上下文
    
    // ✅ 正确：通过EAUserDataProfile建立与用户的关系
    var userDataProfile: EAUserDataProfile?
    
    init(userMessage: String, aiResponse: String, conversationType: String = "general_chat") {
        self.userMessage = userMessage
        self.aiResponse = aiResponse
        self.conversationType = conversationType
    }
    
    // MARK: - 便捷访问方法
    
    /// 获取关联的用户（通过数据档案）
    func getUser() -> EAUser? {
        return userDataProfile?.user
    }
    
    /// 获取消息年龄（小时）
    func getAgeInHours() -> Double {
        return Date().timeIntervalSince(timestamp) / 3600.0
    }
    
    /// 检查是否为最近的消息
    func isRecent(withinHours hours: Double = 24.0) -> Bool {
        return getAgeInHours() <= hours
    }
} 