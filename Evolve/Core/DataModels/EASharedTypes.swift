import Foundation
import SwiftData

// MARK: - 共享枚举类型定义
// 统一管理所有SwiftData模型中使用的枚举类型，避免重复定义

/// 提醒风格枚举
/// 用于定义用户偏好的提醒方式
enum EAReminderStyle: String, CaseIterable, Codable {
    case gentle = "gentle"
    case motivational = "motivational"
    case strict = "strict"
    
    var description: String {
        switch self {
        case .gentle:
            return "温柔提醒"
        case .motivational:
            return "激励提醒"
        case .strict:
            return "严格提醒"
        }
    }
    
    var detailDescription: String {
        switch self {
        case .gentle:
            return "温和友善的提醒方式"
        case .motivational:
            return "充满动力的激励方式"
        case .strict:
            return "严格督促的提醒方式"
        }
    }
}

/// 习惯频率类型
enum EAHabitFrequency: String, CaseIterable, Codable {
    case daily = "daily"
    case weekly = "weekly"
    case monthly = "monthly"
    case custom = "custom"
    
    var description: String {
        switch self {
        case .daily:
            return "每日"
        case .weekly:
            return "每周"
        case .monthly:
            return "每月"
        case .custom:
            return "自定义"
        }
    }
}

/// 习惯状态枚举
enum EAHabitStatus: String, CaseIterable, Codable {
    case active = "active"
    case paused = "paused"
    case completed = "completed"
    case archived = "archived"
    
    var description: String {
        switch self {
        case .active:
            return "进行中"
        case .paused:
            return "已暂停"
        case .completed:
            return "已完成"
        case .archived:
            return "已归档"
        }
    }
}

/// 对话类型枚举
enum EAConversationType: String, CaseIterable, Codable {
    case dailyCheckin = "daily_checkin"
    case obstacleSupport = "obstacle_support"
    case generalChat = "general_chat"
    case motivationBoost = "motivation_boost"
    case progressReview = "progress_review"
    
    var description: String {
        switch self {
        case .dailyCheckin:
            return "每日签到"
        case .obstacleSupport:
            return "障碍支持"
        case .generalChat:
            return "日常对话"
        case .motivationBoost:
            return "动力提升"
        case .progressReview:
            return "进度回顾"
        }
    }
}

/// 内容类型枚举
enum EAContentType: String, CaseIterable, Codable {
    case motivation = "motivation"
    case psychologyExercise = "psychology_exercise"
    case successStory = "success_story"
    case guideline = "guideline"
    
    var description: String {
        switch self {
        case .motivation:
            return "动力包"
        case .psychologyExercise:
            return "心理练习"
        case .successStory:
            return "成功故事"
        case .guideline:
            return "指导方针"
        }
    }
}

/// 用户分析事件类型
enum EAAnalyticsEventType: String, CaseIterable, Codable {
    case habitCompleted = "habit_completed"
    case habitSkipped = "habit_skipped"
    case aiInteraction = "ai_interaction"
    case appOpened = "app_opened"
    case settingsChanged = "settings_changed"
    
    var description: String {
        switch self {
        case .habitCompleted:
            return "习惯完成"
        case .habitSkipped:
            return "习惯跳过"
        case .aiInteraction:
            return "AI交互"
        case .appOpened:
            return "应用打开"
        case .settingsChanged:
            return "设置变更"
        }
    }
}

// MARK: - 共享结构体类型

/// 时间段结构体
/// 用于表示习惯的执行时间
struct EATimeSlot: Codable, Hashable {
    let hour: Int
    let minute: Int
    
    init(hour: Int, minute: Int) {
        self.hour = max(0, min(23, hour))
        self.minute = max(0, min(59, minute))
    }
    
    /// 从Date创建时间段
    init(from date: Date) {
        let calendar = Calendar.current
        let components = calendar.dateComponents([.hour, .minute], from: date)
        self.hour = components.hour ?? 0
        self.minute = components.minute ?? 0
    }
    
    /// 转换为Date（今天的指定时间）
    func toDate() -> Date {
        let calendar = Calendar.current
        let now = Date()
        return calendar.date(bySettingHour: hour, minute: minute, second: 0, of: now) ?? now
    }
    
    /// 格式化显示
    var formatted: String {
        return String(format: "%02d:%02d", hour, minute)
    }
}

// MARK: - 类型别名
// 为了向后兼容，提供类型别名

typealias ReminderStyle = EAReminderStyle 