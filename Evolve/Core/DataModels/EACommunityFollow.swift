import Foundation
import SwiftData

/// 用户关注模型 - 管理用户之间的关注关系
/// 严格遵循SwiftData单端inverse规则，通过普通属性连接EAUserSocialProfile
@Model
final class EACommunityFollow: @unchecked Sendable {
    var id: UUID = UUID()
    var creationDate: Date = Date()     // 关注时间
    var isActive: Bool = true           // 是否有效（用于取消关注）
    
    // 关注类型和原因
    var followReason: String = "manual"  // 关注原因：manual(手动)、ai_suggested(AI推荐)、mutual_habit(共同习惯)
    var followSource: String = "profile" // 关注来源：profile(个人页面)、post(帖子)、comment(评论)、recommendation(推荐)
    
    // 互动统计
    var mutualInteractionCount: Int = 0  // 相互互动次数
    var sharedHabitsCount: Int = 0      // 共同习惯数量
    var energyResonanceScore: Double = 0.0  // 能量共振分数 (0.0-1.0)
    
    // 通知设置
    var notifyOnPost: Bool = true       // 是否通知新帖子
    var notifyOnAchievement: Bool = true // 是否通知成就达成
    var notifyOnMilestone: Bool = true   // 是否通知里程碑
    
    // 🔗 SwiftData关系：关注者和被关注者（通过社交档案）
    var followerProfile: EAUserSocialProfile?  // 关注者的社交档案（普通属性，EAUserSocialProfile端定义inverse）
    var followeeProfile: EAUserSocialProfile?  // 被关注者的社交档案（普通属性，EAUserSocialProfile端定义inverse）
    
    // ✅ 修复：移除外键模式计算属性，遵循.cursorrules规范
    // 通过关系访问用户数据，而非外键模式
    // 使用方式：follow.followerProfile?.user?.id（通过关系链访问）
    
    init(
        followReason: String = "manual",
        followSource: String = "profile"
    ) {
        self.followReason = followReason
        self.followSource = followSource
    }
    
    // MARK: - 便捷方法
    
    /// 取消关注
    func unfollow() {
        isActive = false
    }
    
    /// 重新关注
    func refollow() {
        isActive = true
        creationDate = Date()  // 更新关注时间
    }
    
    /// 增加互动计数
    func incrementInteraction() {
        mutualInteractionCount += 1
        updateEnergyResonance()
    }
    
    /// 更新共同习惯数量
    func updateSharedHabits(count: Int) {
        sharedHabitsCount = count
        updateEnergyResonance()
    }
    
    /// 更新能量共振分数
    private func updateEnergyResonance() {
        // 基于互动次数和共同习惯计算能量共振分数
        let interactionFactor = min(1.0, Double(mutualInteractionCount) / 50.0)
        let habitFactor = min(1.0, Double(sharedHabitsCount) / 10.0)
        let timeFactor = max(0.1, 1.0 - (getFollowAgeInDays() / 365.0))  // 时间衰减因子
        
        energyResonanceScore = (interactionFactor * 0.5 + habitFactor * 0.3 + timeFactor * 0.2)
    }
    
    /// 检查是否为相互关注
    /// ✅ 修复：使用关系链访问，而非外键模式
    func isMutualFollow(with otherFollow: EACommunityFollow) -> Bool {
        return self.followerProfile?.user?.id == otherFollow.followeeProfile?.user?.id && 
               self.followeeProfile?.user?.id == otherFollow.followerProfile?.user?.id &&
               self.isActive && otherFollow.isActive
    }
    
    /// 获取关注年龄（天数）
    func getFollowAgeInDays() -> Double {
        return Date().timeIntervalSince(creationDate) / (24 * 3600)
    }
    
    /// 检查是否为新关注（7天内）
    func isNewFollow() -> Bool {
        return getFollowAgeInDays() <= 7.0
    }
    
    /// 检查是否应该推荐内容
    func shouldRecommendContent() -> Bool {
        return isActive && energyResonanceScore > 0.3
    }
    
    /// 检查是否应该发送通知
    func shouldNotify(for eventType: String) -> Bool {
        guard isActive else { return false }
        
        switch eventType {
        case "post":
            return notifyOnPost
        case "achievement":
            return notifyOnAchievement
        case "milestone":
            return notifyOnMilestone
        default:
            return false
        }
    }
    
    /// 获取关注者用户ID
    /// ✅ 修复：使用关系链访问，而非外键模式
    func getFollowerUserId() -> UUID? {
        return followerProfile?.user?.id
    }
    
    /// 获取被关注者用户ID
    /// ✅ 修复：使用关系链访问，而非外键模式
    func getFolloweeUserId() -> UUID? {
        return followeeProfile?.user?.id
    }
    
    /// 获取关注关系描述（需要通过Repository获取用户名）
    /// ✅ 修复：使用关系链访问，而非外键模式
    func getFollowDescription() -> String {
        let followerUserId = followerProfile?.user?.id
        let followeeUserId = followeeProfile?.user?.id
        return "关注关系: \(followerUserId?.uuidString.prefix(8) ?? "未知") -> \(followeeUserId?.uuidString.prefix(8) ?? "未知")"
    }
} 