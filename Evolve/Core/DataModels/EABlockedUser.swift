//
//  EABlockedUser.swift
//  Evolve
//
//  Created by AI Assistant on 2025-01-27.
//  好友屏蔽功能数据模型 - 批次一：地基加固
//

import Foundation
import SwiftData

/// 屏蔽用户关系模型
/// 🔑 批次一：地基加固 - 专门用于管理用户屏蔽关系的详细信息
/// 遵循《Evolve项目AI开发审查规则.md》的数据模型设计规范
@Model
final class EABlockedUser: @unchecked Sendable {
    var id: UUID = UUID()
    var creationDate: Date = Date()
    var lastUpdateDate: Date = Date()
    
    // MARK: - 核心关系
    
    /// 🔑 屏蔽发起者的社交档案（普通属性，遵循SwiftData单端inverse规则）
    /// 注意：EAUserSocialProfile端定义@Relationship(inverse:)
    var blockerProfile: EAUserSocialProfile?
    
    /// 🔑 被屏蔽用户的社交档案（普通属性，遵循SwiftData单端inverse规则）
    /// 注意：EAUserSocialProfile端定义@Relationship(inverse:)
    var blockedProfile: EAUserSocialProfile?
    
    // MARK: - 屏蔽详情
    
    /// 屏蔽时间（您建议的字段）
    var blockedAt: Date = Date()
    
    /// 屏蔽原因（您建议的字段）
    var blockReason: String?
    
    /// 屏蔽类型
    enum BlockType: String, Codable, CaseIterable {
        case manual = "manual"           // 手动屏蔽
        case automatic = "automatic"     // 自动屏蔽（系统检测）
        case reported = "reported"       // 举报后屏蔽
        case friendship = "friendship"   // 好友关系屏蔽
    }
    
    var blockType: BlockType = BlockType.manual
    
    /// 是否活跃状态（用于软删除）
    var isActive: Bool = true
    
    /// 屏蔽级别
    enum BlockLevel: String, Codable, CaseIterable {
        case basic = "basic"             // 基础屏蔽（消息、好友请求）
        case complete = "complete"       // 完全屏蔽（所有互动）
        case community = "community"     // 社区屏蔽（帖子、评论）
    }
    
    var blockLevel: BlockLevel = BlockLevel.basic
    
    /// 到期时间（可选，用于临时屏蔽）
    var expiresAt: Date?
    
    /// 屏蔽来源页面
    var sourceContext: String?  // "friend_list", "chat", "profile", "post", "comment"
    
    /// 附加数据（JSON字符串）
    var additionalData: String?
    
    // MARK: - 统计信息
    
    /// 屏蔽前的互动次数
    var previousInteractionCount: Int = 0
    
    /// 屏蔽前的好友关系持续时间（秒）
    var previousFriendshipDuration: TimeInterval = 0
    
    /// 系统检测到的风险分数（0.0-1.0）
    var riskScore: Double = 0.0
    
    // MARK: - 初始化
    
    init(
        blockType: BlockType = .manual,
        blockLevel: BlockLevel = .basic,
        blockReason: String? = nil,
        sourceContext: String? = nil,
        expiresAt: Date? = nil
    ) {
        self.blockType = blockType
        self.blockLevel = blockLevel
        self.blockReason = blockReason
        self.sourceContext = sourceContext
        self.expiresAt = expiresAt
        self.blockedAt = Date()
    }
    
    // MARK: - 便捷方法
    
    /// 检查屏蔽是否已过期
    var isExpired: Bool {
        guard let expiresAt = expiresAt else { return false }
        return Date() > expiresAt
    }
    
    /// 检查屏蔽是否有效
    var isEffective: Bool {
        return isActive && !isExpired
    }
    
    /// 获取屏蔽持续时间
    var blockDuration: TimeInterval {
        return Date().timeIntervalSince(blockedAt)
    }
    
    /// 获取屏蔽原因的显示文本
    var blockReasonDisplayText: String {
        return blockReason ?? blockType.displayText
    }
    
    /// 更新最后修改时间
    func updateLastModified() {
        lastUpdateDate = Date()
    }
    
    /// 设置屏蔽过期时间
    func setExpiration(after duration: TimeInterval) {
        expiresAt = Date().addingTimeInterval(duration)
        updateLastModified()
    }
    
    /// 移除屏蔽过期时间（永久屏蔽）
    func removePermanentBlock() {
        expiresAt = nil
        updateLastModified()
    }
    
    /// 取消屏蔽（软删除）
    func unblock() {
        isActive = false
        updateLastModified()
    }
    
    /// 恢复屏蔽
    func reactivate() {
        isActive = true
        updateLastModified()
    }
    
    /// 获取屏蔽用户的ID（安全访问）
    var blockedUserId: UUID? {
        return blockedProfile?.user?.id
    }
    
    /// 获取屏蔽发起者的ID（安全访问）
    var blockerUserId: UUID? {
        return blockerProfile?.user?.id
    }
    
    /// 获取屏蔽用户的用户名（安全访问）
    var blockedUsername: String? {
        return blockedProfile?.user?.username
    }
    
    /// 获取屏蔽发起者的用户名（安全访问）
    var blockerUsername: String? {
        return blockerProfile?.user?.username
    }
}

// MARK: - 屏蔽类型扩展

extension EABlockedUser.BlockType {
    var displayText: String {
        switch self {
        case .manual:
            return "手动屏蔽"
        case .automatic:
            return "系统自动屏蔽"
        case .reported:
            return "举报后屏蔽"
        case .friendship:
            return "好友关系屏蔽"
        }
    }
    
    var description: String {
        switch self {
        case .manual:
            return "用户主动选择屏蔽"
        case .automatic:
            return "系统检测到不当行为自动屏蔽"
        case .reported:
            return "用户举报后执行屏蔽"
        case .friendship:
            return "从好友关系中屏蔽"
        }
    }
}

// MARK: - 屏蔽级别扩展

extension EABlockedUser.BlockLevel {
    var displayText: String {
        switch self {
        case .basic:
            return "基础屏蔽"
        case .complete:
            return "完全屏蔽"
        case .community:
            return "社区屏蔽"
        }
    }
    
    var description: String {
        switch self {
        case .basic:
            return "阻止消息和好友请求"
        case .complete:
            return "阻止所有形式的互动"
        case .community:
            return "阻止在社区中看到该用户的内容"
        }
    }
    
    /// 检查是否包含特定权限
    func includes(_ permission: BlockPermission) -> Bool {
        switch self {
        case .basic:
            return [.blockMessages, .blockFriendRequests].contains(permission)
        case .complete:
            return true // 完全屏蔽包含所有权限
        case .community:
            return [.blockPosts, .blockComments, .blockLikes].contains(permission)
        }
    }
}

// MARK: - 屏蔽权限枚举

enum BlockPermission: String, CaseIterable {
    case blockMessages = "block_messages"
    case blockFriendRequests = "block_friend_requests"
    case blockPosts = "block_posts"
    case blockComments = "block_comments"
    case blockLikes = "block_likes"
    case blockProfile = "block_profile"
    case blockSearch = "block_search"
    
    var displayText: String {
        switch self {
        case .blockMessages:
            return "阻止消息"
        case .blockFriendRequests:
            return "阻止好友请求"
        case .blockPosts:
            return "阻止帖子显示"
        case .blockComments:
            return "阻止评论显示"
        case .blockLikes:
            return "阻止点赞互动"
        case .blockProfile:
            return "阻止查看个人资料"
        case .blockSearch:
            return "阻止搜索结果显示"
        }
    }
} 