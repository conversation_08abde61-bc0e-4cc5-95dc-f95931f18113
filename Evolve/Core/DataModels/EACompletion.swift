import Foundation
import SwiftData

@Model
final class EACompletion: @unchecked Sendable {
    var id: UUID = UUID()
    var date: Date = Date()
    var completionNote: String?
    var energyLevel: Int = 5 // 1-10
    
    // 🔗 关系的另一端：使用普通属性，避免循环引用
    // SwiftData会自动维护inverse关系
    var habit: EAHabit?
    
    // ✅ 主构造器：支持显式设置date参数（解决链接错误）
    init(date: Date = Date(), completionNote: String? = nil, energyLevel: Int = 5) {
        self.date = date
        self.completionNote = completionNote
        self.energyLevel = energyLevel
    }
    
    // ✅ 便捷构造器：保持向后兼容性
    convenience init(completionNote: String? = nil, energyLevel: Int = 5) {
        self.init(date: Date(), completionNote: completionNote, energyLevel: energyLevel)
    }
} 