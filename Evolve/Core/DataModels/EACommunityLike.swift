import Foundation
import SwiftData

/// 社区点赞模型 - 记录用户对帖子和评论的点赞行为
/// 严格遵循SwiftData双向关系规范，使用@Relationship建立数据关系
@Model
final class EACommunityLike: @unchecked Sendable {
    var id: UUID = UUID()
    var creationDate: Date = Date()     // 点赞时间
    var isActive: Bool = true           // 是否有效（用于撤销点赞）
    
    // 点赞目标类型（互斥，只能是帖子或评论之一）
    var targetType: String              // 目标类型："post" 或 "comment"
    
    // 互动分析
    var userEnergyLevel: Int = 5        // 用户点赞时的能量等级
    var interactionContext: String = "timeline"  // 互动上下文：timeline、detail、notification
    
    // 🔗 SwiftData关系：与用户的双向关系
    var user: EAUser?                   // 点赞用户（普通属性，EAUser端定义inverse）
    
    // 🔗 SwiftData关系：与帖子的双向关系（可选，与评论互斥）
    var targetPost: EACommunityPost?    // 目标帖子（普通属性，EACommunityPost端定义inverse）
    
    // 🔗 SwiftData关系：与评论的双向关系（可选，与帖子互斥）
    var targetComment: EACommunityComment?  // 目标评论（普通属性，EACommunityComment端定义inverse）
    
    init(
        targetType: String = "post",
        userEnergyLevel: Int = 5,
        interactionContext: String = "timeline"
    ) {
        self.targetType = targetType
        self.userEnergyLevel = userEnergyLevel
        self.interactionContext = interactionContext
    }
    
    // MARK: - 便捷方法
    
    /// 撤销点赞
    func unlike() {
        isActive = false
    }
    
    /// 重新点赞
    func relike() {
        isActive = true
        creationDate = Date()  // 更新时间
    }
    
    /// 检查是否为帖子点赞
    func isPostLike() -> Bool {
        return targetType == "post" && targetPost != nil
    }
    
    /// 检查是否为评论点赞
    func isCommentLike() -> Bool {
        return targetType == "comment" && targetComment != nil
    }
    
    /// 获取目标ID（无论是帖子还是评论）
    func getTargetId() -> UUID? {
        if isPostLike() {
            return targetPost?.id
        } else if isCommentLike() {
            return targetComment?.id
        }
        return nil
    }
    
    /// 检查点赞是否在特定时间内
    func isWithinTimeFrame(hours: Int) -> Bool {
        let timeInterval = TimeInterval(hours * 3600)
        return Date().timeIntervalSince(creationDate) <= timeInterval
    }
    
    /// 获取点赞年龄（小时）
    func getAgeInHours() -> Double {
        return Date().timeIntervalSince(creationDate) / 3600.0
    }
    
    /// 获取用户名（通过关系访问）
    func getUserUsername() -> String {
        return user?.username ?? "未知用户"
    }
    
    /// 获取目标内容预览（通过关系访问）
    func getTargetContentPreview() -> String {
        if let post = targetPost {
            return "帖子: \(String(post.content.prefix(30)))"
        } else if let comment = targetComment {
            return "评论: \(String(comment.content.prefix(30)))"
        }
        return "未知内容"
    }
    
    /// 设置帖子目标（确保互斥性）
    func setPostTarget(_ post: EACommunityPost) {
        self.targetPost = post
        self.targetComment = nil
        self.targetType = "post"
    }
    
    /// 设置评论目标（确保互斥性）
    func setCommentTarget(_ comment: EACommunityComment) {
        self.targetComment = comment
        self.targetPost = nil
        self.targetType = "comment"
    }
} 