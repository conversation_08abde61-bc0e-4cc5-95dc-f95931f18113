import Foundation
import SwiftData

@Model
final class EAAnalytics: @unchecked Sendable {
    var id: UUID = UUID()
    var eventType: String
    var eventData: String // JSON字符串
    var timestamp: Date = Date()
    
    // ✅ 正确：通过EAUserDataProfile建立与用户的关系
    var userDataProfile: EAUserDataProfile?
    
    init(eventType: String, eventData: String = "{}") {
        self.eventType = eventType
        self.eventData = eventData
    }
    
    // MARK: - 便捷访问方法
    
    /// 获取关联的用户（通过数据档案）
    func getUser() -> EAUser? {
        return userDataProfile?.user
    }
    
    /// 解析事件数据为字典
    func getEventDataDictionary() -> [String: Any]? {
        guard let data = eventData.data(using: .utf8) else { return nil }
        return try? JSONSerialization.jsonObject(with: data) as? [String: Any]
    }
} 