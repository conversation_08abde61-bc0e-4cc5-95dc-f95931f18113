import Foundation
import SwiftData

@Model
final class EAUser: @unchecked Sendable {
    var id: UUID = UUID()
    var username: String
    var email: String?
    var creationDate: Date = Date()
    var isPro: Bool = false
    var proExpirationDate: Date?
    
    // 🔑 头像数据持久化字段
    /// 用户头像数据（序列化存储）
    var avatarDataEncoded: Data?
    
    // MARK: - 核心关系（≤5个，遵循开发规范文档）
    
    // 🔗 核心关系1：用户-设置（一对一）
    @Relationship(deleteRule: .cascade, inverse: \EAUserSettings.user)
    var settings: EAUserSettings?
    
    // 🔗 核心关系2：用户-习惯（一对多）
    @Relationship(deleteRule: .cascade, inverse: \EAHabit.user)
    var habits: [EAHabit]
    
    // 🔗 核心关系3：用户-社交档案（一对一）
    @Relationship(deleteRule: .cascade, inverse: \EAUserSocialProfile.user)
    var socialProfile: EAUserSocialProfile?
    
    // 🔗 核心关系4：用户-管理档案（一对一）
    @Relationship(deleteRule: .cascade, inverse: \EAUserModerationProfile.user)
    var moderationProfile: EAUserModerationProfile?
    
    // 🔗 核心关系5：用户-数据档案（一对一）- 替换原有的posts关系
    @Relationship(deleteRule: .cascade, inverse: \EAUserDataProfile.user)
    var dataProfile: EAUserDataProfile?
    
    init(username: String, email: String? = nil) {
        self.username = username
        self.email = email
        
        // ✅ 关键修复：iOS 18.2要求关系集合在init中初始化
        self.habits = []
    }
    
    // MARK: - 头像数据管理
    
    /// 获取头像数据（计算属性）
    var avatarData: EAAvatarData? {
        get {
            guard let encodedData = avatarDataEncoded else { return nil }
            return try? JSONDecoder().decode(EAAvatarData.self, from: encodedData)
        }
        set {
            if let newAvatarData = newValue {
                avatarDataEncoded = try? JSONEncoder().encode(newAvatarData)
            } else {
                avatarDataEncoded = nil
            }
        }
    }
    
    /// 更新头像数据（安全方法）
    @MainActor
    func updateAvatarData(_ avatarData: EAAvatarData?, in context: ModelContext) throws {
        guard self.modelContext == context else {
            throw DataModelError.contextMismatch
        }
        
        // 更新头像数据
        self.avatarData = avatarData
        
        // 保存到数据库
        try context.save()
    }
    
    /// 获取头像显示名称
    func getAvatarDisplayName() -> String {
        return avatarData?.type.displayName ?? "默认头像"
    }
    
    /// 检查是否有自定义头像
    func hasCustomAvatar() -> Bool {
        return avatarData?.type == .custom && avatarData?.customImageData != nil
    }
    
    // MARK: - 核心功能便捷方法
    
    /// 获取用户发布的可见帖子数量（通过社交档案）
    func getVisiblePostsCount() -> Int {
        return socialProfile?.posts.count ?? 0
    }
    
    /// 获取用户关注的人数（通过社交档案）
    func getFollowingCount() -> Int {
        return socialProfile?.followingCount ?? 0
    }
    
    /// 获取关注用户的人数（通过社交档案）
    func getFollowersCount() -> Int {
        return socialProfile?.getFollowersCount() ?? 0
    }
    
    /// 获取用户未处理的举报数量（通过管理档案）
    func getPendingReportsCount() -> Int {
        return moderationProfile?.getActiveReceivedReportsCount() ?? 0
    }
    
    /// 检查是否关注了某个用户（通过社交档案）
    func isFollowing(userId: UUID) -> Bool {
        // 注意：实际应该通过Repository查询，这里返回默认值
        return false
    }
    
    /// 检查是否被某个用户关注（通过社交档案）
    func isFollowedBy(userId: UUID) -> Bool {
        return socialProfile?.isFollowedBy(userId: userId) ?? false
    }
    
    /// 检查用户是否可以发布内容（通过管理档案）
    func canPostContent() -> Bool {
        return moderationProfile?.canPostContent() ?? true
    }
    
    /// 检查用户是否可以评论（通过管理档案）
    func canComment() -> Bool {
        return moderationProfile?.canComment() ?? true
    }
    
    /// 检查用户是否可以点赞（通过管理档案）
    func canLike() -> Bool {
        return moderationProfile?.canLike() ?? true
    }
    
    /// 计算用户的社区活跃度分数
    func getCommunityActivityScore() -> Double {
        let postsScore = Double(socialProfile?.posts.count ?? 0) * 5.0
        let socialScore = socialProfile?.socialActivityScore ?? 0.0
        
        return postsScore + socialScore
    }
    
    // MARK: - 数据档案便捷访问方法
    
    /// 获取活跃支付记录数量（通过数据档案）
    func getActivePaymentsCount() -> Int {
        return dataProfile?.getActivePaymentsCount() ?? 0
    }
    
    /// 获取最近的分析记录（通过数据档案）
    func getRecentAnalytics(days: Int = 7) -> [EAAnalytics] {
        return dataProfile?.getRecentAnalytics(days: days) ?? []
    }
    
    /// 获取活跃路径数量（通过数据档案）
    func getActivePathsCount() -> Int {
        return dataProfile?.getActivePathsCount() ?? 0
    }
    
    /// 获取最近的AI消息（通过数据档案）
    func getRecentAIMessages(limit: Int = 10) -> [EAAIMessage] {
        return dataProfile?.getRecentAIMessages(limit: limit) ?? []
    }
    
    // MARK: - 安全关系操作方法（iOS 18.2兼容性）
    
    /// 安全的设置关联方法
    @MainActor
    func safelyAssignSettings(_ settings: EAUserSettings, in context: ModelContext) throws {
        guard settings.modelContext == context, self.modelContext == context else {
            throw DataModelError.contextMismatch
        }
        self.settings = settings
        try context.save()
    }
    
    /// 安全的习惯添加方法
    @MainActor
    func safelyAddHabit(_ habit: EAHabit, in context: ModelContext) throws {
        guard habit.modelContext == context, self.modelContext == context else {
            throw DataModelError.contextMismatch
        }
        self.habits.append(habit)
        try context.save()
    }
    
    /// 安全的社交档案关联方法
    @MainActor
    func safelyAssignSocialProfile(_ profile: EAUserSocialProfile, in context: ModelContext) throws {
        guard profile.modelContext == context, self.modelContext == context else {
            throw DataModelError.contextMismatch
        }
        self.socialProfile = profile
        try context.save()
    }
    
    /// 安全的管理档案关联方法
    @MainActor
    func safelyAssignModerationProfile(_ profile: EAUserModerationProfile, in context: ModelContext) throws {
        guard profile.modelContext == context, self.modelContext == context else {
            throw DataModelError.contextMismatch
        }
        self.moderationProfile = profile
        try context.save()
    }
    
    /// 安全的数据档案关联方法
    @MainActor
    func safelyAssignDataProfile(_ profile: EAUserDataProfile, in context: ModelContext) throws {
        guard profile.modelContext == context, self.modelContext == context else {
            throw DataModelError.contextMismatch
        }
        self.dataProfile = profile
        try context.save()
    }
    
    /// 创建并关联社交档案
    @MainActor
    func createSocialProfile(in context: ModelContext) throws -> EAUserSocialProfile {
        let profile = EAUserSocialProfile()
        context.insert(profile)
        try safelyAssignSocialProfile(profile, in: context)
        return profile
    }
    
    /// 创建并关联管理档案
    @MainActor
    func createModerationProfile(in context: ModelContext) throws -> EAUserModerationProfile {
        let profile = EAUserModerationProfile()
        context.insert(profile)
        try safelyAssignModerationProfile(profile, in: context)
        return profile
    }
    
    /// 创建并关联数据档案
    @MainActor
    func createDataProfile(in context: ModelContext) throws -> EAUserDataProfile {
        let profile = EAUserDataProfile()
        context.insert(profile)
        try safelyAssignDataProfile(profile, in: context)
        return profile
    }
}

// MARK: - 数据模型错误类型

enum DataModelError: Error, LocalizedError {
    case contextMismatch
    case relationshipValidationFailed
    case dataIntegrityError
    case dataCorruption(String)
    case userNotFound
    case duplicateUser(String)
    case methodDeprecated
    
    // MARK: - 用户身份系统错误类型
    case userIdentityCorrupted(String)
    case profileInitializationFailed(String)
    case integrityCheckFailed(String)
    case asyncInitializationTimeout
    case socialProfileMissing
    case moderationProfileMissing
    case dataProfileMissing
    case criticalDataMissing(String)
    
    // MARK: - 用户身份完整性错误
    case incompleteUserProfile(String)
    case invalidProfileState(String)
    case profileRelationshipBroken(String)
    
    // MARK: - 系统级错误
    case systemResourceUnavailable
    case serviceUnavailable(String)
    case emergencyMode
    case recoveryRequired(String)

    var errorDescription: String? {
        switch self {
        case .contextMismatch:
            return "数据上下文不匹配 - iOS 18.2兼容性要求"
        case .relationshipValidationFailed:
            return "关系验证失败"
        case .dataIntegrityError:
            return "数据完整性错误"
        case .dataCorruption(let message):
            return "数据损坏：\(message)"
        case .userNotFound:
            return "用户未找到"
        case .duplicateUser(let message):
            return message
        case .methodDeprecated:
            return "方法已弃用，请使用Repository模式"
            
        // 用户身份系统错误
        case .userIdentityCorrupted(let message):
            return "用户身份数据损坏：\(message)"
        case .profileInitializationFailed(let message):
            return "用户档案初始化失败：\(message)"
        case .integrityCheckFailed(let message):
            return "完整性检查失败：\(message)"
        case .asyncInitializationTimeout:
            return "异步初始化超时"
        case .socialProfileMissing:
            return "用户社交档案缺失"
        case .moderationProfileMissing:
            return "用户管理档案缺失"
        case .dataProfileMissing:
            return "用户数据档案缺失"
        case .criticalDataMissing(let message):
            return "关键数据缺失：\(message)"
            
        // 用户身份完整性错误
        case .incompleteUserProfile(let message):
            return "用户档案不完整：\(message)"
        case .invalidProfileState(let message):
            return "用户档案状态无效：\(message)"
        case .profileRelationshipBroken(let message):
            return "用户档案关系损坏：\(message)"
            
        // 系统级错误
        case .systemResourceUnavailable:
            return "系统资源不可用"
        case .serviceUnavailable(let message):
            return "服务不可用：\(message)"
        case .emergencyMode:
            return "系统进入紧急模式"
        case .recoveryRequired(let message):
            return "需要数据恢复：\(message)"
        }
    }
    
    // MARK: - 错误级别分类
    
    /// 错误严重级别
    var severity: ErrorSeverity {
        switch self {
        case .contextMismatch, .relationshipValidationFailed, .dataIntegrityError:
            return .critical
        case .dataCorruption, .userIdentityCorrupted, .profileRelationshipBroken:
            return .critical
        case .profileInitializationFailed, .integrityCheckFailed, .criticalDataMissing:
            return .high
        case .socialProfileMissing, .moderationProfileMissing, .dataProfileMissing:
            return .high
        case .incompleteUserProfile, .invalidProfileState:
            return .medium
        case .userNotFound, .duplicateUser, .asyncInitializationTimeout:
            return .medium
        case .systemResourceUnavailable, .serviceUnavailable, .emergencyMode, .recoveryRequired:
            return .critical
        case .methodDeprecated:
            return .low
        }
    }
    
    /// 是否需要立即用户反馈
    var requiresUserFeedback: Bool {
        switch self {
        case .userNotFound, .duplicateUser, .asyncInitializationTimeout:
            return true
        case .systemResourceUnavailable, .serviceUnavailable:
            return true
        case .profileInitializationFailed, .socialProfileMissing:
            return true
        default:
            return false
        }
    }
    
    /// 是否触发降级策略
    var triggersGracefulDegradation: Bool {
        switch self {
        case .socialProfileMissing, .moderationProfileMissing, .dataProfileMissing:
            return true
        case .systemResourceUnavailable, .serviceUnavailable:
            return true
        case .incompleteUserProfile, .invalidProfileState:
            return true
        default:
            return false
        }
    }
    
    /// 是否需要数据恢复
    var requiresDataRecovery: Bool {
        switch self {
        case .dataCorruption, .userIdentityCorrupted, .profileRelationshipBroken:
            return true
        case .criticalDataMissing, .recoveryRequired:
            return true
        default:
            return false
        }
    }
}

// MARK: - 错误严重级别

enum ErrorSeverity: Int, CaseIterable {
    case low = 1
    case medium = 2
    case high = 3
    case critical = 4
    
    var description: String {
        switch self {
        case .low:
            return "低级"
        case .medium:
            return "中级"
        case .high:
            return "高级"
        case .critical:
            return "紧急"
        }
    }
}