import Foundation
import SwiftData

/// 好友请求模型 - 星际邀请系统
/// 记录用户之间的好友请求，支持请求状态管理和过期机制
@Model
final class EAFriendRequest: @unchecked Sendable {
    var id: UUID = UUID()
    var creationDate: Date = Date()
    var responseDate: Date?
    
    /// 好友请求状态
    enum RequestStatus: String, Codable, CaseIterable {
        case pending = "pending"        // 待处理
        case accepted = "accepted"      // 已接受
        case rejected = "rejected"      // 已拒绝
        case expired = "expired"        // 已过期
        case cancelled = "cancelled"    // 已取消
    }
    
    var status: RequestStatus
    var requestMessage: String?         // 请求消息
    var rejectionReason: String?        // 拒绝原因
    var expirationDate: Date = Calendar.current.date(byAdding: .day, value: 30, to: Date()) ?? Date()

    // 🔑 新增：骚扰防护机制字段
    var rejectionCount: Int = 0         // 被拒绝次数（同一发送者对同一接收者）
    var lastRejectionDate: Date?        // 最后拒绝时间
    var cooldownUntil: Date?            // 冷却期结束时间
    
    // 🔑 架构修复：移除临时外键字段，完全依赖SwiftData关系机制
    // 原问题：违反"禁止外键字段"的核心架构规范
    // 解决方案：Repository层必须在同一Context中建立关系，而非依赖外键字段
    
    // 🔑 关键修复：SwiftData关系 - 修复关系对称性问题
    // ✅ 修复：使用普通属性，但必须与EAUserSocialProfile的@Relationship对称
    // 注意：EAUserSocialProfile端定义了inverse，这里必须是普通属性以保持对称性
    var senderProfile: EAUserSocialProfile?     // 发送者档案（普通属性，对应sentFriendRequests的inverse）
    var receiverProfile: EAUserSocialProfile?   // 接收者档案（普通属性，对应receivedFriendRequests的inverse）
    
    init(requestMessage: String? = nil) {
        self.status = .pending  // 在初始化器中设置默认值
        self.requestMessage = requestMessage
        // ✅ 修复：不在init中赋值关系属性，遵循SwiftData最佳实践
        // 关系赋值将在Repository层的"插入→赋值→保存"流程中完成
    }
    
    /// 检查请求是否已过期
    func isExpired() -> Bool {
        return Date() > expirationDate && status == .pending
    }
    
    /// 接受好友请求
    func accept() {
        status = .accepted
        responseDate = Date()
    }
    
    /// 拒绝好友请求
    func reject(reason: String? = nil) {
        status = .rejected
        responseDate = Date()
        rejectionReason = reason

        // 🔑 骚扰防护：更新拒绝统计
        rejectionCount += 1
        lastRejectionDate = Date()

        // 设置冷却期：第一次拒绝24小时，连续拒绝3次后7天
        let cooldownHours: TimeInterval = rejectionCount >= 3 ? 168 : 24  // 7天 或 24小时
        cooldownUntil = Date().addingTimeInterval(cooldownHours * 3600)
    }
    
    /// 取消好友请求
    func cancel() {
        status = .cancelled
        responseDate = Date()
    }
    
    /// 标记为过期
    func markAsExpired() {
        if status == .pending {
            status = .expired
            responseDate = Date()
        }
    }
    
    /// 检查请求是否可以被处理
    func canBeProcessed() -> Bool {
        return status == .pending && !isExpired()
    }

    /// 🔑 新增：检查是否在冷却期内
    func isInCooldown() -> Bool {
        guard let cooldownUntil = cooldownUntil else { return false }
        return Date() < cooldownUntil
    }

    /// 🔑 新增：获取剩余冷却时间（小时）
    func getRemainingCooldownHours() -> Int {
        guard let cooldownUntil = cooldownUntil, isInCooldown() else { return 0 }
        let remainingSeconds = cooldownUntil.timeIntervalSince(Date())
        return max(0, Int(ceil(remainingSeconds / 3600)))
    }
    
    /// 获取请求的显示文本
    func getDisplayText() -> String {
        switch status {
        case .pending:
            return isExpired() ? "请求已过期" : "等待回复"
        case .accepted:
            return "已接受"
        case .rejected:
            return "已拒绝"
        case .expired:
            return "已过期"
        case .cancelled:
            return "已取消"
        }
    }
}
