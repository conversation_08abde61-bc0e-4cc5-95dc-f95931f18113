//
//  EAGracefulDegradationManager.swift
//  Evolve
//
//  Created by AI Assistant on 2025-01-27.
//  降级策略管理器 - 阶段2.3实现
//

import Foundation
import SwiftUI
import Combine

/// 降级策略管理器
/// 负责在用户身份系统出现问题时提供优雅的降级处理
/// 确保用户体验的连续性，遵循开发规范文档的错误处理要求
@MainActor
class EAGracefulDegradationManager: ObservableObject {
    
    // MARK: - 状态属性
    @Published var currentDegradationLevel: DegradationLevel = .normal
    @Published var activeDegradations: Set<DegradationType> = []
    @Published var degradationHistory: [DegradationEvent] = []
    
    // MARK: - 服务可用性状态
    @Published var serviceAvailability = ServiceAvailability()
    
    // MARK: - 依赖注入
    private let userFriendlyErrorService: EAUserFriendlyErrorService
    
    init(userFriendlyErrorService: EAUserFriendlyErrorService) {
        self.userFriendlyErrorService = userFriendlyErrorService
    }
    
    // MARK: - 核心降级决策方法
    
    /// 评估是否需要降级并执行相应策略
    func evaluateAndApplyDegradation(for error: DataModelError, context: ErrorContext = .general) {
        guard error.triggersGracefulDegradation else {
            return
        }
        
        let degradationType = determineDegradationType(for: error, context: context)
        let degradationLevel = calculateDegradationLevel(for: degradationType)
        
        applyDegradation(type: degradationType, level: degradationLevel, triggeredBy: error)
        
        // 通知用户友好错误服务
        userFriendlyErrorService.showError(error, context: context)
    }
    
    /// 主动恢复降级状态
    func attemptRecovery() {
        let recoveryTasks = planRecoveryTasks()
        
        Task {
            for task in recoveryTasks {
                await executeRecoveryTask(task)
            }
            
            await evaluateRecoverySuccess()
        }
    }
    
    /// 检查功能是否可用
    func isFunctionAvailable(_ function: AppFunction) -> Bool {
        switch currentDegradationLevel {
        case .normal:
            return true
        case .limited:
            return function.availableInLimitedMode
        case .essential:
            return function.availableInEssentialMode
        case .emergency:
            return function.availableInEmergencyMode
        }
    }
    
    /// 获取功能降级提示
    func getFunctionUnavailableMessage(_ function: AppFunction) -> String {
        switch function {
        case .community:
            return "社区功能暂时不可用，您仍可以管理个人计划。"
        case .friendship:
            return "好友功能正在修复中，请稍后再试。"
        case .aiChat:
            return "AI对话暂时不可用，基础功能正常使用。"
        case .profileEdit:
            return "资料编辑功能暂时受限，基础信息可以查看。"
        case .dataSync:
            return "数据同步功能暂停，本地数据正常使用。"
        case .notifications:
            return "通知功能暂时受限，重要提醒仍会显示。"
        case .analytics:
            return "数据分析功能暂停，基础统计仍可查看。"
        case .habitCreation:
            return "暂停创建新计划，现有计划可正常使用。"
        case .export:
            return "数据导出功能暂停，请稍后再试。"
        }
    }
    
    // MARK: - 私有方法
    
    /// 确定降级类型
    private func determineDegradationType(for error: DataModelError, context: ErrorContext) -> DegradationType {
        switch error {
        case .socialProfileMissing:
            return .socialFunctions
        case .moderationProfileMissing:
            return .communityFunctions
        case .dataProfileMissing:
            return .dataFunctions
        case .systemResourceUnavailable:
            return .systemLevel
        case .serviceUnavailable:
            return .networkDependent
        case .incompleteUserProfile:
            return .profileDependent
        case .invalidProfileState:
            return .userIdentity
        default:
            return .general
        }
    }
    
    /// 计算降级级别
    private func calculateDegradationLevel(for degradationType: DegradationType) -> DegradationLevel {
        let currentDegradationCount = activeDegradations.count
        
        switch degradationType {
        case .systemLevel:
            return .emergency
        case .userIdentity:
            return .essential
        case .socialFunctions, .communityFunctions:
            return currentDegradationCount > 2 ? .essential : .limited
        case .dataFunctions, .networkDependent:
            return currentDegradationCount > 1 ? .limited : .normal
        case .profileDependent, .general:
            return .limited
        }
    }
    
    /// 应用降级策略
    private func applyDegradation(type: DegradationType, level: DegradationLevel, triggeredBy error: DataModelError) {
        activeDegradations.insert(type)
        
        // 更新总体降级级别（取最严重的）
        if level.rawValue > currentDegradationLevel.rawValue {
            currentDegradationLevel = level
        }
        
        // 更新服务可用性
        updateServiceAvailability(for: type, level: level)
        
        // 记录降级事件
        let event = DegradationEvent(
            id: UUID(),
            type: type,
            level: level,
            triggeredBy: error,
            timestamp: Date(),
            isRecovered: false
        )
        degradationHistory.insert(event, at: 0)
        
        // 保持历史记录最多100条
        if degradationHistory.count > 100 {
            degradationHistory = Array(degradationHistory.prefix(100))
        }
    }
    
    /// 更新服务可用性状态
    private func updateServiceAvailability(for type: DegradationType, level: DegradationLevel) {
        switch type {
        case .socialFunctions:
            serviceAvailability.community = level == .normal
            serviceAvailability.friendship = level == .normal
        case .communityFunctions:
            serviceAvailability.community = false
        case .dataFunctions:
            serviceAvailability.dataSync = level == .normal
            serviceAvailability.analytics = level == .normal
        case .networkDependent:
            serviceAvailability.dataSync = false
            serviceAvailability.aiChat = level != .emergency
        case .profileDependent:
            serviceAvailability.profileEdit = level == .normal
        case .userIdentity:
            serviceAvailability.profileEdit = false
            serviceAvailability.community = false
        case .systemLevel:
            serviceAvailability = ServiceAvailability() // 重置为默认状态（大部分功能不可用）
        case .general:
            break // 不改变具体服务状态
        }
    }
    
    /// 规划恢复任务
    private func planRecoveryTasks() -> [RecoveryTask] {
        var tasks: [RecoveryTask] = []
        
        for degradationType in activeDegradations {
            switch degradationType {
            case .socialFunctions:
                tasks.append(.validateSocialProfile)
                tasks.append(.restoreSocialData)
            case .communityFunctions:
                tasks.append(.validateModerationProfile)
                tasks.append(.restoreCommunityAccess)
            case .dataFunctions:
                tasks.append(.validateDataProfile)
                tasks.append(.restoreDataSync)
            case .networkDependent:
                tasks.append(.testNetworkConnectivity)
                tasks.append(.restoreNetworkServices)
            case .profileDependent:
                tasks.append(.validateUserProfile)
                tasks.append(.restoreProfileFunctions)
            case .userIdentity:
                tasks.append(.validateUserIdentity)
                tasks.append(.restoreUserSession)
            case .systemLevel:
                tasks.append(.checkSystemResources)
                tasks.append(.restoreSystemServices)
            case .general:
                tasks.append(.generalHealthCheck)
            }
        }
        
        return Array(Set(tasks)) // 去重
    }
    
    /// 执行恢复任务
    private func executeRecoveryTask(_ task: RecoveryTask) async {
        switch task {
        case .validateSocialProfile:
            // 验证社交档案完整性
            break
        case .restoreSocialData:
            // 恢复社交数据
            break
        case .validateModerationProfile:
            // 验证管理档案
            break
        case .restoreCommunityAccess:
            // 恢复社区访问
            break
        case .validateDataProfile:
            // 验证数据档案
            break
        case .restoreDataSync:
            // 恢复数据同步
            break
        case .testNetworkConnectivity:
            // 测试网络连接
            break
        case .restoreNetworkServices:
            // 恢复网络服务
            break
        case .validateUserProfile:
            // 验证用户档案
            break
        case .restoreProfileFunctions:
            // 恢复档案功能
            break
        case .validateUserIdentity:
            // 验证用户身份
            break
        case .restoreUserSession:
            // 恢复用户会话
            break
        case .checkSystemResources:
            // 检查系统资源
            break
        case .restoreSystemServices:
            // 恢复系统服务
            break
        case .generalHealthCheck:
            // 通用健康检查
            break
        }
    }
    
    /// 评估恢复成功率
    private func evaluateRecoverySuccess() async {
        // 重新评估当前状态
        let _ = currentDegradationLevel // 保留用于未来的恢复成功率计算
        var recoveredTypes: Set<DegradationType> = []
        
        // 检查每个降级类型是否已恢复
        for degradationType in activeDegradations {
            if await isTypeRecovered(degradationType) {
                recoveredTypes.insert(degradationType)
            }
        }
        
        // 移除已恢复的降级类型
        activeDegradations.subtract(recoveredTypes)
        
        // 重新计算降级级别
        if activeDegradations.isEmpty {
            currentDegradationLevel = .normal
        } else {
            let maxLevel = activeDegradations.compactMap { type in
                calculateDegradationLevel(for: type)
            }.max() ?? .normal
            currentDegradationLevel = maxLevel
        }
        
        // 更新恢复状态
        for index in degradationHistory.indices {
            if recoveredTypes.contains(degradationHistory[index].type) {
                degradationHistory[index].isRecovered = true
                degradationHistory[index].recoveryTime = Date()
            }
        }
        
        // 如果有恢复，更新服务可用性
        if !recoveredTypes.isEmpty {
            updateServiceAvailabilityAfterRecovery()
        }
    }
    
    /// 检查降级类型是否已恢复
    private func isTypeRecovered(_ type: DegradationType) async -> Bool {
        // 这里应该实现具体的恢复检查逻辑
        // 暂时返回true作为示例
        return true
    }
    
    /// 恢复后更新服务可用性
    private func updateServiceAvailabilityAfterRecovery() {
        if currentDegradationLevel == .normal {
            serviceAvailability = ServiceAvailability.normal()
        }
    }
}

// MARK: - 降级相关模型

/// 降级级别
enum DegradationLevel: Int, CaseIterable, Comparable {
    case normal = 0      // 正常运行
    case limited = 1     // 限制功能
    case essential = 2   // 基础功能
    case emergency = 3   // 紧急模式
    
    /// Comparable协议实现 - 通过rawValue比较严重程度
    static func < (lhs: DegradationLevel, rhs: DegradationLevel) -> Bool {
        return lhs.rawValue < rhs.rawValue
    }
    
    var description: String {
        switch self {
        case .normal:
            return "正常"
        case .limited:
            return "限制模式"
        case .essential:
            return "基础模式"
        case .emergency:
            return "紧急模式"
        }
    }
}

/// 降级类型
enum DegradationType: String, CaseIterable {
    case socialFunctions = "social"
    case communityFunctions = "community"
    case dataFunctions = "data"
    case networkDependent = "network"
    case profileDependent = "profile"
    case userIdentity = "identity"
    case systemLevel = "system"
    case general = "general"
}

/// 应用功能枚举
enum AppFunction: String, CaseIterable {
    case community = "community"
    case friendship = "friendship"
    case aiChat = "ai_chat"
    case profileEdit = "profile_edit"
    case dataSync = "data_sync"
    case notifications = "notifications"
    case analytics = "analytics"
    case habitCreation = "habit_creation"
    case export = "export"
    
    var availableInLimitedMode: Bool {
        switch self {
        case .community, .friendship, .aiChat:
            return false
        case .profileEdit, .dataSync, .notifications:
            return true
        case .analytics, .habitCreation, .export:
            return true
        }
    }
    
    var availableInEssentialMode: Bool {
        switch self {
        case .community, .friendship, .aiChat, .analytics, .export:
            return false
        case .profileEdit, .dataSync, .notifications, .habitCreation:
            return true
        }
    }
    
    var availableInEmergencyMode: Bool {
        switch self {
        case .notifications, .habitCreation:
            return true
        default:
            return false
        }
    }
}

/// 降级事件
struct DegradationEvent: Identifiable {
    let id: UUID
    let type: DegradationType
    let level: DegradationLevel
    let triggeredBy: DataModelError
    let timestamp: Date
    var isRecovered: Bool
    var recoveryTime: Date?
    
    var duration: TimeInterval? {
        guard isRecovered, let recoveryTime = recoveryTime else {
            return nil
        }
        return recoveryTime.timeIntervalSince(timestamp)
    }
}

/// 服务可用性状态
struct ServiceAvailability {
    var community: Bool = true
    var friendship: Bool = true
    var aiChat: Bool = true
    var profileEdit: Bool = true
    var dataSync: Bool = true
    var notifications: Bool = true
    var analytics: Bool = true
    var habitCreation: Bool = true
    var export: Bool = true
    
    static func normal() -> ServiceAvailability {
        return ServiceAvailability()
    }
    
    var allAvailable: Bool {
        return community && friendship && aiChat && profileEdit && 
               dataSync && notifications && analytics && habitCreation && export
    }
    
    var availableServicesCount: Int {
        var count = 0
        if community { count += 1 }
        if friendship { count += 1 }
        if aiChat { count += 1 }
        if profileEdit { count += 1 }
        if dataSync { count += 1 }
        if notifications { count += 1 }
        if analytics { count += 1 }
        if habitCreation { count += 1 }
        if export { count += 1 }
        return count
    }
}

/// 恢复任务
enum RecoveryTask: String, CaseIterable {
    case validateSocialProfile = "validate_social"
    case restoreSocialData = "restore_social"
    case validateModerationProfile = "validate_moderation"
    case restoreCommunityAccess = "restore_community"
    case validateDataProfile = "validate_data"
    case restoreDataSync = "restore_sync"
    case testNetworkConnectivity = "test_network"
    case restoreNetworkServices = "restore_network"
    case validateUserProfile = "validate_profile"
    case restoreProfileFunctions = "restore_profile"
    case validateUserIdentity = "validate_identity"
    case restoreUserSession = "restore_session"
    case checkSystemResources = "check_system"
    case restoreSystemServices = "restore_system"
    case generalHealthCheck = "health_check"
} 