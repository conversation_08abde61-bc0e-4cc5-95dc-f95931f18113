import Foundation
import SwiftUI
import SwiftData

// MARK: - AI应用内提醒管理器
@MainActor
class EAAIInAppReminderManager: ObservableObject {
    // 移除单例模式
    // static let shared = EAAIInAppReminderManager()
    
    @Published var pendingReminders: [EAAIInAppReminder] = []
    @Published var todayTriggeredReminders: [EAAIInAppReminder] = []
    
    private let userDefaults = UserDefaults.standard
    private let reminderKey = "ai_in_app_reminders"
    private let triggeredKey = "today_triggered_reminders"
    private var reminderTimer: Timer?
    
    // 改为公开初始化器，支持依赖注入
    init() {
        loadReminders()
        setupNotificationObservers()
        startReminderTimer()
    }
    
    // MARK: - 提醒管理
    
    /// 添加AI应用内提醒
    func addReminder(habitId: UUID, habitName: String, reminderTimes: [String]) {
        // 移除旧的提醒
        removeReminder(habitId: habitId)
        
        // 创建新的提醒
        for timeString in reminderTimes {
            let reminder = EAAIInAppReminder(
                id: UUID(),
                habitId: habitId,
                habitName: habitName,
                timeString: timeString,
                isActive: true
            )
            pendingReminders.append(reminder)
        }
        
        saveReminders()
        // 添加AI应用内提醒完成
    }
    
    /// 移除AI应用内提醒
    func removeReminder(habitId: UUID) {
        pendingReminders.removeAll { $0.habitId == habitId }
        saveReminders()
        // 移除AI应用内提醒完成
    }
    
    /// 获取当前活跃的提醒数量
    func getActiveReminderCount() -> Int {
        return pendingReminders.filter { $0.isActive }.count
    }
    
    /// 检查是否有待触发的提醒
    func checkPendingReminders() -> [EAAIInAppReminder] {
        let now = Date()
        let calendar = Calendar.current
        let currentTime = calendar.dateComponents([.hour, .minute], from: now)
        
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        
        return pendingReminders.filter { reminder in
            guard reminder.isActive else { return false }
            
            // 检查是否已经在今天触发过
            let alreadyTriggered = todayTriggeredReminders.contains { triggered in
                triggered.habitId == reminder.habitId && 
                triggered.timeString == reminder.timeString &&
                calendar.isDateInToday(triggered.triggeredAt)
            }
            
            if alreadyTriggered { return false }
            
            // 解析提醒时间
            let timeComponents = reminder.timeString.split(separator: ":").compactMap { Int($0) }
            guard timeComponents.count == 2 else { return false }
            
            let reminderHour = timeComponents[0]
            let reminderMinute = timeComponents[1]
            
            // 检查是否到了提醒时间（允许2分钟误差）
            if let currentHour = currentTime.hour, let currentMinute = currentTime.minute {
                let currentTotalMinutes = currentHour * 60 + currentMinute
                let reminderTotalMinutes = reminderHour * 60 + reminderMinute
                
                // 在提醒时间的2分钟内都算有效
                return abs(currentTotalMinutes - reminderTotalMinutes) <= 2
            }
            
            return false
        }
    }
    
    /// 触发提醒
    func triggerReminder(_ reminder: EAAIInAppReminder) {
        var triggeredReminder = reminder
        triggeredReminder.triggeredAt = Date()
        
        todayTriggeredReminders.append(triggeredReminder)
        saveTodayTriggeredReminders()
        
        // 发送通知给UI组件
        NotificationCenter.default.post(
            name: .aiInAppReminderTriggered,
            object: nil,
            userInfo: [
                "habitId": reminder.habitId.uuidString,
                "habitName": reminder.habitName,
                "timeString": reminder.timeString
            ]
        )
        
        // 触发AI应用内提醒完成
    }
    
    /// 获取今日应该提醒的习惯
    func getTodayReminderHabits() -> [String] {
        let pendingToday = checkPendingReminders()
        return Array(Set(pendingToday.map { $0.habitName }))
    }
    
    /// 🔑 增强：生成个性化AI提醒消息
    func generateReminderMessage(for habitName: String) -> String {
        // 使用本地化的激励语句
        return EANotificationLocalizer.habitReminderBody(for: habitName)
    }

    /// 🔑 新增：根据时间段生成个性化提醒
    func generateContextualReminderMessage(for habitName: String, at time: Date = Date()) -> String {
        let hour = Calendar.current.component(.hour, from: time)
        let timeContext = getTimeContext(for: hour)

        let contextualMessages = getContextualMessages(for: timeContext, habitName: habitName)
        return contextualMessages.randomElement() ?? generateReminderMessage(for: habitName)
    }

    /// 获取时间上下文
    private func getTimeContext(for hour: Int) -> TimeContext {
        switch hour {
        case 6..<12:
            return .morning
        case 12..<18:
            return .afternoon
        case 18..<22:
            return .evening
        default:
            return .night
        }
    }

    /// 获取上下文相关的提醒消息
    private func getContextualMessages(for context: TimeContext, habitName: String) -> [String] {
        switch context {
        case .morning:
            return [
                "早安！开始今天的「\(habitName)」，为美好的一天加油 🌅",
                "新的一天，新的开始！「\(habitName)」等着你 ☀️",
                "晨光正好，完成「\(habitName)」让今天更精彩 🌤️"
            ]
        case .afternoon:
            return [
                "午后时光，记得完成「\(habitName)」哦 🌞",
                "下午好！「\(habitName)」时间到了 ⏰",
                "午后充电时间，「\(habitName)」让你更有活力 ⚡"
            ]
        case .evening:
            return [
                "傍晚时分，「\(habitName)」为今天画上完美句号 🌅",
                "夕阳西下，完成「\(habitName)」收获满满成就感 🌇",
                "晚间时光，「\(habitName)」让你的一天更充实 🌆"
            ]
        case .night:
            return [
                "夜深了，完成「\(habitName)」再休息吧 🌙",
                "深夜时光，「\(habitName)」是对自己最好的投资 ⭐",
                "夜晚宁静，「\(habitName)」让心灵更平静 🌌"
            ]
        }
    }

    /// 时间上下文枚举
    private enum TimeContext {
        case morning, afternoon, evening, night
    }
    
    // MARK: - 数据持久化
    
    private func saveReminders() {
        do {
            let data = try JSONEncoder().encode(pendingReminders)
            userDefaults.set(data, forKey: reminderKey)
            
            // 更新提醒数量到通知服务
            // TODO: 需要通过依赖注入获取notificationService，暂时注释避免编译错误
            // let notificationService = EANotificationService(sessionManager: sessionManager)
            // notificationService.updateAIInAppReminderCount(getActiveReminderCount())
        } catch {
            // 保存AI应用内提醒失败，静默处理
            // TODO: 实现错误监控和上报机制
        }
    }
    
    private func loadReminders() {
        guard let data = userDefaults.data(forKey: reminderKey) else { return }
        
        do {
            pendingReminders = try JSONDecoder().decode([EAAIInAppReminder].self, from: data)
        } catch {
            // 加载AI应用内提醒失败，使用空数组
            pendingReminders = []
        }
        
        loadTodayTriggeredReminders()
    }
    
    private func saveTodayTriggeredReminders() {
        do {
            let data = try JSONEncoder().encode(todayTriggeredReminders)
            userDefaults.set(data, forKey: triggeredKey)
        } catch {
            // 保存今日触发提醒失败，静默处理
        }
    }
    
    private func loadTodayTriggeredReminders() {
        guard let data = userDefaults.data(forKey: triggeredKey) else { return }
        
        do {
            let allTriggered = try JSONDecoder().decode([EAAIInAppReminder].self, from: data)
            // 只保留今天的触发记录
            let calendar = Calendar.current
            todayTriggeredReminders = allTriggered.filter { reminder in
                calendar.isDateInToday(reminder.triggeredAt)
            }
            
            // 如果有过期的记录，重新保存
            if todayTriggeredReminders.count != allTriggered.count {
                saveTodayTriggeredReminders()
            }
        } catch {
            // 加载今日触发提醒失败，使用空数组
            todayTriggeredReminders = []
        }
    }
    
    // MARK: - 通知观察者
    
    private func setupNotificationObservers() {
        NotificationCenter.default.addObserver(
            forName: .aiInAppReminderScheduled,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            guard let userInfo = notification.userInfo,
                  let habitIdString = userInfo["habitId"] as? String,
                  let habitId = UUID(uuidString: habitIdString),
                  let habitName = userInfo["habitName"] as? String,
                  let reminderTimes = userInfo["reminderTimes"] as? [String] else {
                return
            }
            
            Task { @MainActor in
                self?.addReminder(habitId: habitId, habitName: habitName, reminderTimes: reminderTimes)
            }
        }
        
        NotificationCenter.default.addObserver(
            forName: .aiInAppReminderRemoved,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            guard let userInfo = notification.userInfo,
                  let habitIdString = userInfo["habitId"] as? String,
                  let habitId = UUID(uuidString: habitIdString) else {
                return
            }
            
            Task { @MainActor in
                self?.removeReminder(habitId: habitId)
            }
        }
    }
    
    // MARK: - 定时器
    
    private func startReminderTimer() {
        // 停止现有定时器
        reminderTimer?.invalidate()
        
        // ✅ CPU优化：创建新的定时器，从5分钟延长为15分钟检查一次，进一步减少CPU唤醒
        reminderTimer = Timer.scheduledTimer(withTimeInterval: 900, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.checkAndTriggerReminders()
            }
        }
    }
    
    private func checkAndTriggerReminders() {
        let pendingToday = checkPendingReminders()
        
        for reminder in pendingToday {
            triggerReminder(reminder)
        }
    }
    
    deinit {
        // 清理资源
        reminderTimer?.invalidate()
        reminderTimer = nil
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - AI应用内提醒数据模型
struct EAAIInAppReminder: Codable, Identifiable {
    let id: UUID
    let habitId: UUID
    let habitName: String
    let timeString: String // 格式如 "09:00"
    var isActive: Bool
    var triggeredAt: Date = Date()
    
    init(id: UUID, habitId: UUID, habitName: String, timeString: String, isActive: Bool) {
        self.id = id
        self.habitId = habitId
        self.habitName = habitName
        self.timeString = timeString
        self.isActive = isActive
    }
}

// MARK: - 通知名称扩展
extension Notification.Name {
    static let aiInAppReminderTriggered = Notification.Name("aiInAppReminderTriggered")
} 