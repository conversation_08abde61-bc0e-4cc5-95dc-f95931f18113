//
//  EAPerformanceThresholdManager.swift
//  Evolve
//
//  Created by AI Assistant on 2025-01-06.
//

import Foundation
import SwiftData
import Combine
import os

// MARK: - 性能阈值管理器

/// 性能阈值管理器
/// 用于动态管理Repository性能阈值，实施性能优化策略
@MainActor
class EAPerformanceThresholdManager: ObservableObject {
    
    // MARK: - 日志记录器
    private let logger = OSLog(subsystem: "com.evolve.performance", category: "EAPerformanceThresholdManager")
    
    // MARK: - 性能阈值配置
    
    /// 性能阈值配置结构
    struct PerformanceThreshold {
        let operationType: String
        let warningThreshold: TimeInterval  // 警告阈值
        let criticalThreshold: TimeInterval // 严重阈值
        let optimizationTrigger: Int        // 触发优化的连续超阈值次数
    }
    
    /// 优化策略
    enum OptimizationStrategy {
        case cacheExpansion        // 扩大缓存
        case queryOptimization     // 查询优化
        case batchProcessing       // 批量处理
        case dataPreloading        // 数据预加载
        case relationshipOptimization // 关系优化
    }
    
    // MARK: - 私有属性
    
    /// 性能阈值配置
    private var thresholds: [String: PerformanceThreshold] = [:]
    
    /// 超阈值计数器
    private var thresholdViolationCounts: [String: Int] = [:]
    
    /// 已实施的优化策略
    private var appliedOptimizations: Set<String> = []
    
    /// 性能监控器引用
    private weak var performanceMonitor: EARepositoryPerformanceMonitor?
    
    // MARK: - 公开属性
    
    /// 当前活跃的优化策略数量
    @Published var activeOptimizationCount: Int = 0
    
    /// 性能改善百分比
    @Published var performanceImprovement: Double = 0.0
    
    // MARK: - 初始化
    
    init(performanceMonitor: EARepositoryPerformanceMonitor) {
        self.performanceMonitor = performanceMonitor
        configureDefaultThresholds()
    }
    
    // MARK: - 阈值管理方法
    
    /// 配置默认性能阈值
    private func configureDefaultThresholds() {
        thresholds = [
            "user_fetch": PerformanceThreshold(
                operationType: "user_fetch",
                warningThreshold: 0.3,
                criticalThreshold: 0.8,
                optimizationTrigger: 3
            ),
            "user_integrity_check": PerformanceThreshold(
                operationType: "user_integrity_check",
                warningThreshold: 0.5,
                criticalThreshold: 1.0,
                optimizationTrigger: 2
            ),
            "profile_initialization": PerformanceThreshold(
                operationType: "profile_initialization",
                warningThreshold: 0.8,
                criticalThreshold: 1.5,
                optimizationTrigger: 2
            ),
            "user_social_profile_sync": PerformanceThreshold(
                operationType: "user_social_profile_sync",
                warningThreshold: 0.4,
                criticalThreshold: 1.0,
                optimizationTrigger: 3
            ),
            "user_moderation_check": PerformanceThreshold(
                operationType: "user_moderation_check",
                warningThreshold: 0.2,
                criticalThreshold: 0.6,
                optimizationTrigger: 2
            )
        ]
    }
    
    /// 更新性能阈值
    /// - Parameters:
    ///   - operationType: 操作类型
    ///   - threshold: 新的阈值配置
    func updateThreshold(for operationType: String, threshold: PerformanceThreshold) {
        thresholds[operationType] = threshold
        
        os_log(.info, log: logger, "📊 更新性能阈值: %{public}@ - 警告: %.3fs, 严重: %.3fs", 
               operationType, threshold.warningThreshold, threshold.criticalThreshold)
    }
    
    /// 检查性能并触发优化
    /// - Parameters:
    ///   - operationType: 操作类型
    ///   - duration: 操作耗时
    func checkPerformanceAndOptimize(operationType: String, duration: TimeInterval) {
        guard let threshold = thresholds[operationType] else { return }
        
        // 检查是否超过阈值
        if duration > threshold.criticalThreshold {
            recordThresholdViolation(operationType: operationType, severity: .critical, duration: duration)
        } else if duration > threshold.warningThreshold {
            recordThresholdViolation(operationType: operationType, severity: .warning, duration: duration)
        } else {
            // 性能正常，重置计数器
            thresholdViolationCounts[operationType] = 0
        }
    }
    
    // MARK: - 优化策略实施
    
    /// 记录阈值违规
    private func recordThresholdViolation(operationType: String, severity: ViolationSeverity, duration: TimeInterval) {
        let currentCount = thresholdViolationCounts[operationType, default: 0] + 1
        thresholdViolationCounts[operationType] = currentCount
        
        guard let threshold = thresholds[operationType] else { return }
        
        os_log(.fault, log: logger, "⚠️ 性能阈值违规: %{public}@ - 耗时: %.3fs (%{public}@), 连续次数: %d", 
               operationType, duration, severity.rawValue, currentCount)
        
        // 检查是否需要触发优化
        if currentCount >= threshold.optimizationTrigger {
            triggerOptimization(for: operationType, severity: severity)
        }
    }
    
    /// 触发性能优化
    private func triggerOptimization(for operationType: String, severity: ViolationSeverity) {
        let optimizationKey = "\(operationType)_\(severity.rawValue)"
        
        // 避免重复实施相同的优化
        guard !appliedOptimizations.contains(optimizationKey) else { return }
        
        appliedOptimizations.insert(optimizationKey)
        
        // 根据操作类型选择优化策略
        let strategies = getOptimizationStrategies(for: operationType, severity: severity)
        
        for strategy in strategies {
            applyOptimizationStrategy(strategy, for: operationType)
        }
        
        // 更新活跃优化计数
        activeOptimizationCount = appliedOptimizations.count
        
        os_log(.info, log: logger, "🚀 触发性能优化: %{public}@ - 实施策略: %{public}@", 
               operationType, strategies.map { "\($0)" }.joined(separator: ", "))
    }
    
    /// 获取优化策略
    private func getOptimizationStrategies(for operationType: String, severity: ViolationSeverity) -> [OptimizationStrategy] {
        switch operationType {
        case "user_fetch":
            return severity == .critical ? [.cacheExpansion, .queryOptimization] : [.cacheExpansion]
            
        case "user_integrity_check":
            return [.batchProcessing, .queryOptimization]
            
        case "profile_initialization":
            return [.dataPreloading, .relationshipOptimization]
            
        case "user_social_profile_sync":
            return severity == .critical ? [.batchProcessing, .cacheExpansion] : [.cacheExpansion]
            
        case "user_moderation_check":
            return [.queryOptimization, .batchProcessing]
            
        default:
            return [.queryOptimization]
        }
    }
    
    /// 实施优化策略
    private func applyOptimizationStrategy(_ strategy: OptimizationStrategy, for operationType: String) {
        switch strategy {
        case .cacheExpansion:
            // 扩大缓存大小或延长缓存时间
            applyCacheExpansion(for: operationType)
            
        case .queryOptimization:
            // 查询优化（如添加索引建议、谓词优化）
            applyQueryOptimization(for: operationType)
            
        case .batchProcessing:
            // 启用批量处理
            applyBatchProcessing(for: operationType)
            
        case .dataPreloading:
            // 数据预加载
            applyDataPreloading(for: operationType)
            
        case .relationshipOptimization:
            // 关系优化
            applyRelationshipOptimization(for: operationType)
        }
    }
    
    // MARK: - 具体优化实施方法
    
    private func applyCacheExpansion(for operationType: String) {
        // 这里可以调用相关Repository的缓存配置方法
        os_log(.info, log: logger, "📈 实施缓存扩展优化: %{public}@", operationType)
    }
    
    private func applyQueryOptimization(for operationType: String) {
        os_log(.info, log: logger, "🔍 实施查询优化: %{public}@", operationType)
    }
    
    private func applyBatchProcessing(for operationType: String) {
        os_log(.info, log: logger, "📦 实施批量处理优化: %{public}@", operationType)
    }
    
    private func applyDataPreloading(for operationType: String) {
        os_log(.info, log: logger, "⚡ 实施数据预加载优化: %{public}@", operationType)
    }
    
    private func applyRelationshipOptimization(for operationType: String) {
        os_log(.info, log: logger, "🔗 实施关系优化: %{public}@", operationType)
    }
    
    // MARK: - 性能分析方法
    
    /// 获取性能改善报告
    func getPerformanceImprovementReport() -> String {
        guard let monitor = performanceMonitor else {
            return "⚠️ 性能监控器不可用"
        }
        
        let baseReport = monitor.getPerformanceReport()
        
        return """
        \(baseReport)
        
        🚀 性能优化状态
        ==================
        活跃优化策略: \(activeOptimizationCount)
        性能改善: \(String(format: "%.1f", performanceImprovement))%
        已实施优化: \(appliedOptimizations.count)
        """
    }
    
    /// 重置优化状态
    func resetOptimizations() {
        appliedOptimizations.removeAll()
        thresholdViolationCounts.removeAll()
        activeOptimizationCount = 0
        performanceImprovement = 0.0
        
        os_log(.info, log: logger, "🔄 重置性能优化状态")
    }
}

// MARK: - 辅助类型

/// 违规严重程度
enum ViolationSeverity: String {
    case warning = "警告"
    case critical = "严重"
}

// MARK: - Environment支持（已在EnvironmentValues+Services.swift中定义） 