import Foundation
import SwiftData

/// 好友功能服务错误类型
/// 提供统一的错误处理机制，符合iOS开发规范
public enum FriendshipServiceError: Error, LocalizedError {
    case requestNotFound        // 请求ID无效
    case friendshipNotFound     // 好友关系ID无效
    case userNotFound          // 用户ID无效
    case alreadyFriends        // 已经是好友
    case requestAlreadyExists  // 好友请求已存在
    case unknownError(Error)   // 包裹其他未预料错误

    // 现有的错误类型（保持向后兼容）
    case requestCannotBeProcessed
    case invalidRequestProfiles
    case currentUserNotFound   // 未找到当前用户
    case profileInitializationFailed  // 用户档案初始化失败
    case blockingFailed(String)  // 🔑 批次二新增：屏蔽操作失败

    /// 用户友好的错误描述
    public var errorDescription: String? {
        switch self {
        case .requestNotFound:
            return "好友请求不存在"
        case .friendshipNotFound:
            return "好友关系不存在"
        case .userNotFound:
            return "用户不存在"
        case .alreadyFriends:
            return "已经是好友关系"
        case .requestAlreadyExists:
            return "好友请求已存在"
        case .unknownError(let error):
            return "未知错误：\(error.localizedDescription)"
        case .requestCannotBeProcessed:
            return "好友请求无法处理"
        case .invalidRequestProfiles:
            return "用户档案信息无效"
        case .currentUserNotFound:
            return "无法获取当前用户信息"
        case .profileInitializationFailed:
            return "用户档案初始化失败"
        case .blockingFailed(let message):
            return "屏蔽用户失败：\(message)"
        }
    }

    /// 错误恢复建议
    public var recoverySuggestion: String? {
        switch self {
        case .requestNotFound, .friendshipNotFound:
            return "请刷新页面查看最新状态"
        case .userNotFound:
            return "请检查用户名是否正确"
        case .alreadyFriends:
            return "您已经是好友了"
        case .requestAlreadyExists:
            return "好友请求已发送，请等待对方回应"
        case .currentUserNotFound:
            return "请重新登录后再试"
        case .profileInitializationFailed, .invalidRequestProfiles, .requestCannotBeProcessed, .unknownError, .blockingFailed:
            return "请稍后重试，如问题持续请联系客服"
        }
    }
}

// MARK: - 统一错误处理器

/// 好友功能统一错误处理器
struct FriendshipServiceErrorHandler {

    /// 标准化：统一错误处理机制
    static func handleError(_ error: Error) -> String {
        if let friendshipError = error as? FriendshipServiceError {
            return friendshipError.localizedDescription
        } else if let friendRequestError = error as? FriendRequestError {
            return friendRequestError.localizedDescription
        } else if let friendMessageError = error as? FriendMessageError {
            return friendMessageError.localizedDescription
        } else if error.localizedDescription.contains("上下文不匹配") ||
                  error.localizedDescription.contains("兼容性") {
            return "系统兼容性问题，请稍后重试"
        } else {
            return error.localizedDescription
        }
    }
}

/// 好友关系服务 - 星际伙伴系统核心业务逻辑
/// 实现好友验证机制：只有确认好友才能私聊的安全基石
@MainActor
class EAFriendshipService: ObservableObject {
    
    // MARK: - 依赖注入

    private let repositoryContainer: EARepositoryContainer
    private let sessionManager: EASessionManager
    private let integrityGuard: EAUserIntegrityGuard  // 🔑 新增：统一的用户完整性守护服务
    private lazy var notificationService: EAFriendNotificationService = {
        EAFriendNotificationService(repositoryContainer: repositoryContainer)
    }()
    
    // MARK: - 发布状态
    
    @Published var friendships: [EAFriendship] = []
    @Published var pendingRequests: [EAFriendRequest] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var successMessage: String?
    
    // MARK: - 初始化

    init(
        repositoryContainer: EARepositoryContainer,
        sessionManager: EASessionManager,
        integrityGuard: EAUserIntegrityGuard  // 🔑 新增：注入统一的用户完整性守护服务
    ) {
        self.repositoryContainer = repositoryContainer
        self.sessionManager = sessionManager
        self.integrityGuard = integrityGuard  // 🔑 关键修复：使用注入的实例，避免临时创建
    }
    
    // MARK: - 好友关系管理（核心安全机制）
    
    /// 发送好友请求 - 星际邀请
    ///
    /// 向指定用户发送好友请求，实现数字宇宙中的社交连接。
    /// 该方法确保：
    /// 1. 使用用户ID作为唯一标识符，避免社交档案ID混淆
    /// 2. 验证目标用户存在性和数据完整性
    /// 3. 检查是否已经是好友关系或存在待处理请求
    /// 4. 创建好友请求并建立正确的关系链接
    /// 5. 触发相关的星际能量奖励机制
    ///
    /// - Parameters:
    ///   - targetUserID: 目标用户的用户ID（必须是用户ID，不能是社交档案ID）
    ///   - message: 可选的好友请求消息
    /// - Throws: FriendshipServiceError 相关错误
    ///
    /// - Important: 严格禁止传递社交档案ID或对象，必须使用用户ID
    /// - Note: 遵循"传ID，勿传对象"原则，确保SwiftData上下文一致性
    func sendFriendRequest(to targetUserID: UUID, message: String? = nil) async throws {
        isLoading = true
        errorMessage = nil

        defer {
            isLoading = false
        }

        do {
            #if DEBUG
            print("🔍 [FriendshipService] sendFriendRequest开始 - 目标用户ID: \(targetUserID)")
            #endif

            // 🔑 第一步：获取当前用户档案（使用原子性方法）
            let currentProfile = try await getCurrentUserProfile()

            #if DEBUG
            print("✅ [FriendshipService] 当前用户档案确认 - ID: \(currentProfile.id)")
            #endif

            // 🔑 第二步：通过用户ID获取目标用户的社交档案（使用Repository的"新鲜"对象）
            guard let targetUser = try await repositoryContainer.userRepository.fetchUser(id: targetUserID) else {
                #if DEBUG
                print("❌ [FriendshipService] 目标用户不存在 - 用户ID: \(targetUserID)")
                #endif
                throw FriendshipServiceError.userNotFound
            }

            // 🔑 第三步：确保目标用户有唯一的社交档案
            let targetProfile = try await repositoryContainer.userRepository.findOrCreateSocialProfile(for: targetUser)

            #if DEBUG
            print("✅ [FriendshipService] 目标用户档案确认")
            print("   - 用户名: \(targetUser.username)")
            print("   - 用户ID: \(targetUser.id)")
            print("   - 档案ID: \(targetProfile.id)")
            #endif

            // 🔑 新增：目标用户数据完整性验证
            guard let targetUserFromProfile = targetProfile.user else {
                #if DEBUG
                print("❌ [FriendshipService] 目标用户档案缺少用户关系 - userID: \(targetUserID)")
                #endif
                throw FriendshipServiceError.invalidRequestProfiles
            }

            // 🔑 验证目标用户基础数据
            guard !targetUserFromProfile.username.isEmpty,
                  targetUserFromProfile.id == targetUserID else {
                #if DEBUG
                print("❌ [FriendshipService] 目标用户数据不完整")
                print("   - 用户名: '\(targetUserFromProfile.username)'")
                print("   - 期望ID: \(targetUserID)")
                print("   - 实际ID: \(targetUserFromProfile.id)")
                #endif
                throw FriendshipServiceError.invalidRequestProfiles
            }

            // 🔑 验证目标用户数字宇宙数据完整性
            guard targetProfile.stellarLevel != nil,
                  targetProfile.totalStellarEnergy != nil else {
                #if DEBUG
                print("❌ [FriendshipService] 目标用户数字宇宙数据不完整 - 用户名: \(targetUserFromProfile.username)")
                #endif
                throw FriendshipServiceError.invalidRequestProfiles
            }

            #if DEBUG
            print("✅ [FriendshipService] 目标用户档案获取成功: \(targetProfile.user?.username ?? "未知")")
            #endif

            // 🔑 验证目标用户档案有效性
            guard targetProfile.user != nil else {
                #if DEBUG
                print("❌ [FriendshipService] 目标用户档案的user属性为nil")
                #endif
                throw FriendshipServiceError.userNotFound
            }

            #if DEBUG
            print("🔍 [FriendshipService] 开始验证是否可以发送好友请求")
            print("🔍 [FriendshipService] 当前用户: \(currentProfile.user?.username ?? "未知"), 目标用户: \(targetProfile.user?.username ?? "未知")")
            #endif

            // 🔑 核心验证：检查是否可以发送好友请求
            let canSend = currentProfile.canSendFriendRequest(to: targetProfile)

            #if DEBUG
            print("🔍 [FriendshipService] canSendFriendRequest结果: \(canSend)")
            #endif

            guard canSend else {
                #if DEBUG
                print("❌ [FriendshipService] 无法发送好友请求，开始检查具体原因")
                #endif

                // 检查具体失败原因并抛出对应错误
                if targetProfile.blockedUserIds.contains(currentProfile.id) {
                    #if DEBUG
                    print("❌ [FriendshipService] 失败原因: 被目标用户拉黑")
                    #endif
                    throw FriendshipServiceError.requestCannotBeProcessed
                } else if let lastRequest = currentProfile.getLastFriendRequest(to: targetProfile), lastRequest.isInCooldown() {
                    #if DEBUG
                    print("❌ [FriendshipService] 失败原因: 在冷却期内")
                    #endif
                    throw FriendshipServiceError.requestAlreadyExists
                } else if !currentProfile.canSendMoreRequestsToday() {
                    #if DEBUG
                    print("❌ [FriendshipService] 失败原因: 今日发送次数已达上限")
                    #endif
                    throw FriendshipServiceError.requestCannotBeProcessed
                } else if !currentProfile.canSendMoreRequestsThisHour() {
                    #if DEBUG
                    print("❌ [FriendshipService] 失败原因: 本小时发送次数已达上限")
                    #endif
                    throw FriendshipServiceError.requestCannotBeProcessed
                } else {
                    // 检查是否已经是好友
                    guard let currentUserId = currentProfile.user?.id else {
                        throw FriendshipServiceError.currentUserNotFound
                    }

                    if let _ = try? await repositoryContainer.friendshipRepository.checkFriendship(
                        userProfile1Id: currentProfile.id,
                        userProfile2Id: targetProfile.id,
                        currentUserID: currentUserId
                    ) {
                        #if DEBUG
                        print("❌ [FriendshipService] 失败原因: 已经是好友")
                        #endif
                        throw FriendshipServiceError.alreadyFriends
                    } else {
                        #if DEBUG
                        print("❌ [FriendshipService] 失败原因: 已存在待处理的请求")
                        #endif
                        throw FriendshipServiceError.requestAlreadyExists
                    }
                }
            }

            // 🔑 关键修复：确保传递的是社交档案ID，而不是用户ID
            // 注意：createFriendRequest方法期望的是社交档案ID，因为好友关系是在社交档案之间建立的
            #if DEBUG
            print("🔧 [FriendshipService] 准备创建好友请求:")
            print("   - 发送者档案ID: \(currentProfile.id)")
            print("   - 接收者档案ID: \(targetProfile.id)")
            print("   - 发送者用户ID: \(currentProfile.user?.id ?? UUID())")
            print("   - 接收者用户ID: \(targetProfile.user?.id ?? UUID())")
            #endif

            let request = try await repositoryContainer.friendRequestRepository.createFriendRequest(
                senderProfileId: currentProfile.id,      // 发送者社交档案ID
                receiverProfileId: targetProfile.id,     // 接收者社交档案ID
                message: message
            )

            // 🔑 新增：记录发送频率
            currentProfile.recordFriendRequestSent()

            // 🚨 关键修复：防御性编程，确保通知创建不影响主流程
            do {
                try await notificationService.createFriendRequestNotification(
                    senderProfileId: currentProfile.id,
                    receiverProfileId: targetProfile.id,
                    requestId: request.id,
                    message: message,
                    senderUsername: currentProfile.user?.username ?? "未知用户"
                )
            } catch {
                #if DEBUG
                // 调试环境下记录通知创建失败，但不影响好友请求发送
                #endif
                // 通知创建失败不应该影响好友请求的发送
            }

            // 🚨 关键修复：好友申请发送后，强制刷新所有相关数据
            await loadPendingRequests()
            await loadFriendships()  // 确保好友列表数据同步

        } catch let error as FriendshipServiceError {
            // 重新抛出已知的FriendshipServiceError
            throw error
        } catch {
            // 包装未知错误
            throw FriendshipServiceError.unknownError(error)
        }
    }
    
    /// 接受好友请求 - 建立星际伙伴关系（修复Context冲突版本）
    func acceptFriendRequest(_ request: EAFriendRequest) async {
        isLoading = true
        errorMessage = nil
        successMessage = nil
        
        defer {
            isLoading = false
        }

        do {
            #if DEBUG
            // 调试环境下记录开始处理接受请求，但不使用print
            #endif

            // 🔑 批次二修复：使用原子事务方法，确保数据一致性
            // 在单一事务中完成：接受请求 + 创建好友关系
            let (acceptedRequest, friendship) = try await repositoryContainer.friendRequestRepository.acceptFriendRequestAndCreateFriendship(requestId: request.id)

            // 获取相关档案信息用于后续通知
            guard let senderProfileId = acceptedRequest.senderProfile?.id,
                  let receiverProfileId = acceptedRequest.receiverProfile?.id else {
                throw FriendshipServiceError.invalidRequestProfiles
            }

            #if DEBUG
            // 调试环境下记录原子事务成功，但不使用print
            #endif

            #if DEBUG
            // 调试环境下记录好友请求处理完成，但不使用print
            #endif

            // 🔑 成功后创建通知（如果需要）
            if let senderProfile = request.senderProfile,
               let receiverProfile = request.receiverProfile {
                // 获取刚创建的好友关系
                guard let senderUserId = senderProfile.user?.id else {
                    throw FriendshipServiceError.currentUserNotFound
                }
                let friendships = try await repositoryContainer.friendshipRepository.fetchUserFriendships(
                    userProfileId: senderProfile.id,
                    currentUserID: senderUserId
                )
                var targetFriendship: EAFriendship?
                for friendship in friendships {
                    let isMatch1 = friendship.initiatorProfile?.id == senderProfile.id && friendship.friendProfile?.id == receiverProfile.id
                    let isMatch2 = friendship.initiatorProfile?.id == receiverProfile.id && friendship.friendProfile?.id == senderProfile.id
                    if isMatch1 || isMatch2 {
                        targetFriendship = friendship
                        break
                    }
                }
                if let friendship = targetFriendship {
                    // 🔑 关键修复：创建好友添加成功通知 - 使用"传ID，勿传对象"模式
                    try await notificationService.createRequestAcceptedNotification(
                        accepterProfileId: receiverProfile.id,
                        requesterProfileId: senderProfile.id,
                        friendshipId: friendship.id,
                        accepterUsername: receiverProfile.user?.username ?? "未知用户"
                    )
                }
            }

            // 更新本地状态
            await loadFriendships()
            await loadPendingRequests()
            
            successMessage = "已成功添加好友"

        } catch let error as FriendRequestError {
            // 🔑 强化错误处理：根据错误类型提供具体的用户友好提示
            switch error {
            case .requestNotFound:
                errorMessage = "好友申请不存在，可能已被处理"
            case .requestCannotBeProcessed:
                errorMessage = "好友申请状态异常，无法处理"
            case .invalidRequestProfiles:
                errorMessage = "用户信息异常，请稍后重试"
            default:
                errorMessage = "接受好友申请失败：\(error.localizedDescription)"
            }
        } catch {
            // 🔑 Context冲突等严重错误的友好提示
            errorMessage = "操作失败，请检查网络连接后重试"
            
            #if DEBUG
            // 调试环境下记录详细错误信息，但不使用print语句
            #endif
        }
    }
    
    /// 接受好友请求 - 建立星际伙伴关系（按ID方式，保留兼容性）
    func acceptFriendRequest(requestId: UUID) async throws {
        isLoading = true
        errorMessage = nil
        
        defer {
            isLoading = false
        }

        do {
            #if DEBUG
            // 调试环境下记录开始处理接受请求，但不使用print
            #endif

            // 🔑 通过Repository安全获取好友请求
            guard let request = try await repositoryContainer.friendRequestRepository.fetchFriendRequest(by: requestId) else {
                throw FriendshipServiceError.requestNotFound
            }

            // 🔑 批次二修复：使用原子事务方法，确保数据一致性
            // 在单一事务中完成：接受请求 + 创建好友关系
            let (acceptedRequest, friendship) = try await repositoryContainer.friendRequestRepository.acceptFriendRequestAndCreateFriendship(requestId: request.id)

            // 获取相关档案信息用于后续通知
            guard let senderProfileId = acceptedRequest.senderProfile?.id,
                  let receiverProfileId = acceptedRequest.receiverProfile?.id else {
                throw FriendshipServiceError.invalidRequestProfiles
            }

            #if DEBUG
            // 调试环境下记录原子事务成功，但不使用print
            #endif

            #if DEBUG
            // 调试环境下记录好友请求处理完成，但不使用print
            #endif

            // 🔑 成功后创建通知（如果需要）
            if let senderProfile = request.senderProfile,
               let receiverProfile = request.receiverProfile {
                // 获取刚创建的好友关系
                guard let senderUserId = senderProfile.user?.id else {
                    throw FriendshipServiceError.currentUserNotFound
                }
                let friendships = try await repositoryContainer.friendshipRepository.fetchUserFriendships(
                    userProfileId: senderProfile.id,
                    currentUserID: senderUserId
                )
                var targetFriendship: EAFriendship?
                for friendship in friendships {
                    let isMatch1 = friendship.initiatorProfile?.id == senderProfile.id && friendship.friendProfile?.id == receiverProfile.id
                    let isMatch2 = friendship.initiatorProfile?.id == receiverProfile.id && friendship.friendProfile?.id == senderProfile.id
                    if isMatch1 || isMatch2 {
                        targetFriendship = friendship
                        break
                    }
                }
                if let friendship = targetFriendship {
                    // 🔑 关键修复：创建好友添加成功通知 - 使用"传ID，勿传对象"模式
                    try await notificationService.createRequestAcceptedNotification(
                        accepterProfileId: receiverProfile.id,
                        requesterProfileId: senderProfile.id,
                        friendshipId: friendship.id,
                        accepterUsername: receiverProfile.user?.username ?? "未知用户"
                    )
                }
            }

            // 更新本地状态
            await loadFriendships()
            await loadPendingRequests()

        } catch let error as FriendshipServiceError {
            // 重新抛出已知的FriendshipServiceError
            throw error
        } catch {
            // 包装未知错误
            throw FriendshipServiceError.unknownError(error)
        }
    }
    
    /// 拒绝好友请求
    func rejectFriendRequest(requestId: UUID, reason: String? = nil) async throws {
        isLoading = true
        errorMessage = nil
        
        defer {
            isLoading = false
        }

        do {
            #if DEBUG
            // 调试环境下记录开始处理拒绝请求和拒绝原因，但不使用print
            #endif

            // 🔑 通过Repository安全获取好友请求
            guard let request = try await repositoryContainer.friendRequestRepository.fetchFriendRequest(by: requestId) else {
                throw FriendshipServiceError.requestNotFound
            }

            // 🔑 修复：增强验证，确保请求有效性
            guard request.canBeProcessed() else {
                throw FriendshipServiceError.requestCannotBeProcessed
            }

            guard let senderProfile = request.senderProfile,
                  let receiverProfile = request.receiverProfile else {
                throw FriendshipServiceError.invalidRequestProfiles
            }

            #if DEBUG
            // 调试环境下记录发送者和接收者信息，但不使用print
            #endif

            // 🔑 修复：使用安全的Repository操作
            _ = try await repositoryContainer.friendRequestRepository.rejectFriendRequest(
                requestId: requestId,
                reason: reason
            )

            #if DEBUG
            // 调试环境下记录好友请求拒绝成功，但不使用print
            #endif

            // 🔑 新增：创建请求被拒绝的通知（礼貌提示，不推送）
            try await notificationService.createRequestRejectedNotification(
                rejecterProfile: receiverProfile,
                requesterProfile: senderProfile,
                reason: reason
            )

            // 更新本地状态
            await loadPendingRequests()

        } catch let error as FriendshipServiceError {
            // 重新抛出已知的FriendshipServiceError
            throw error
        } catch {
            // 包装未知错误
            throw FriendshipServiceError.unknownError(error)
        }
    }
    
    /// 删除好友关系 - 解除星际伙伴关系
    func removeFriend(friendshipId: UUID) async throws {
        isLoading = true
        errorMessage = nil
        
        defer {
            isLoading = false
        }

        do {
            // 🔑 验证好友关系是否存在
            let currentProfile = try await getCurrentUserProfile()
            guard let currentUserId = currentProfile.user?.id else {
                throw FriendshipServiceError.currentUserNotFound
            }

            guard (try await repositoryContainer.friendshipRepository.fetchFriendship(by: friendshipId, currentUserID: currentUserId)) != nil else {
                throw FriendshipServiceError.friendshipNotFound
            }

            // 🔑 修复问题2：先从本地UI立即移除，再执行数据库操作
            await MainActor.run {
                if let index = self.friendships.firstIndex(where: { $0.id == friendshipId }) {
                    self.friendships.remove(at: index)
                    self.objectWillChange.send()
                }
            }

            // 执行数据库删除操作
            try await repositoryContainer.friendshipRepository.deleteFriendship(friendshipId: friendshipId, currentUserID: currentUserId)

            // 🔑 修复问题2：删除成功后重新加载数据确保一致性
            await loadFriendships()

        } catch let error as FriendshipServiceError {
            // 🔑 修复问题2：如果删除失败，重新加载数据恢复UI状态
            await loadFriendships()
            // 重新抛出已知的FriendshipServiceError
            throw error
        } catch {
            // 🔑 修复问题2：如果删除失败，重新加载数据恢复UI状态
            await loadFriendships()
            // 包装未知错误
            throw FriendshipServiceError.unknownError(error)
        }
    }
    
    /// 删除好友关系 - deleteFriendship方法（兼容接口）
    func deleteFriendship(friendshipId: UUID) async throws {
        // 委托给removeFriend方法
        try await removeFriend(friendshipId: friendshipId)
    }
    
    /// 屏蔽好友关系 - 星际伙伴屏蔽功能
    /// 🔑 批次二修复：统一使用新的EABlockingService屏蔽系统
    func blockFriendship(friendshipId: UUID) async throws {
        isLoading = true
        errorMessage = nil

        defer {
            isLoading = false
        }

        do {
            // 🔑 验证好友关系是否存在
            let currentProfile = try await getCurrentUserProfile()
            guard let currentUserId = currentProfile.user?.id else {
                throw FriendshipServiceError.currentUserNotFound
            }

            guard let friendship = try await repositoryContainer.friendshipRepository.fetchFriendship(by: friendshipId, currentUserID: currentUserId) else {
                throw FriendshipServiceError.friendshipNotFound
            }

            // 🔑 获取当前用户档案（已在上面获取，避免重复）

            // 🔑 确定要屏蔽的用户ID（使用用户ID而非档案ID）
            var targetUserId: UUID?
            if let initiatorProfile = friendship.initiatorProfile,
               initiatorProfile.id != currentProfile.id,
               let initiatorUserId = initiatorProfile.user?.id {
                targetUserId = initiatorUserId
            } else if let friendProfile = friendship.friendProfile,
                      friendProfile.id != currentProfile.id,
                      let friendUserId = friendProfile.user?.id {
                targetUserId = friendUserId
            }

            guard let targetId = targetUserId else {
                throw FriendshipServiceError.friendshipNotFound
            }

            // 🔑 批次二修复：安全获取当前用户ID
            guard let currentUserId = currentProfile.user?.id else {
                throw FriendshipServiceError.currentUserNotFound
            }

            // 🔑 使用新的EABlockingService进行屏蔽
            let blockingService = repositoryContainer.blockingService
            await blockingService.blockFromFriendList(currentUserID: currentUserId, userID: targetId, reason: "从好友列表屏蔽")

            // 🔑 检查屏蔽是否成功
            if let errorMessage = blockingService.errorMessage {
                throw FriendshipServiceError.blockingFailed(errorMessage)
            }

            // 🔑 优化修复：保留好友关系，实现单向屏蔽机制
            // 不删除好友关系，符合微信、QQ等主流社交软件的屏蔽标准

            // 🔑 立即从UI中移除
            await MainActor.run {
                if let index = self.friendships.firstIndex(where: { $0.id == friendshipId }) {
                    self.friendships.remove(at: index)
                    self.objectWillChange.send()
                }
            }

            // 🔑 重新加载数据确保一致性
            await loadFriendships()
            
        } catch let error as FriendshipServiceError {
            // 重新加载数据恢复UI状态
            await loadFriendships()
            throw error
        } catch {
            // 重新加载数据恢复UI状态
            await loadFriendships()
            throw FriendshipServiceError.unknownError(error)
        }
    }

    // MARK: - 好友验证机制（安全基石）
    
    /// 验证是否可以与用户私聊 - 核心安全机制
    func canSendMessage(to userProfileId: UUID) async throws -> Bool {
        // 🔑 获取当前用户档案，失败时抛出错误
        let currentProfile = try await getCurrentUserProfile()

        // 🔑 通过Repository安全获取目标用户档案
        guard let userProfile = await getProfileById(userProfileId) else {
            throw FriendshipServiceError.userNotFound
        }

        // 🔑 核心安全验证：只有确认好友才能私聊
        return currentProfile.canSendMessage(to: userProfile)
    }
    
    /// 检查好友关系状态
    func getFriendshipStatus(with userProfileId: UUID) async throws -> FriendshipStatus {
        // 🔑 获取当前用户档案，失败时抛出错误
        let currentProfile = try await getCurrentUserProfile()
        
        do {
            // 检查是否已经是好友
            guard let currentUserId = currentProfile.user?.id else {
                throw FriendshipServiceError.currentUserNotFound
            }

            if let friendship = try await repositoryContainer.friendshipRepository.checkFriendship(
                userProfile1Id: currentProfile.id,
                userProfile2Id: userProfileId,
                currentUserID: currentUserId
            ) {
                return .friend(friendship)
            }
            
            // 检查是否有待处理的请求
            let sentRequests = try await repositoryContainer.friendRequestRepository.fetchSentRequests(userProfileId: currentProfile.id)
            let receivedRequests = try await repositoryContainer.friendRequestRepository.fetchReceivedRequests(userProfileId: currentProfile.id)
            
            // 检查发送的请求
            if let pendingRequest = sentRequests.first(where: { $0.receiverProfile?.id == userProfileId && $0.status == .pending }) {
                return .requestSent(pendingRequest)
            }
            
            // 检查接收的请求
            if let pendingRequest = receivedRequests.first(where: { $0.senderProfile?.id == userProfileId && $0.status == .pending }) {
                return .requestReceived(pendingRequest)
            }
            
            return .notFriend

        } catch {
            // 包装Repository错误
            throw FriendshipServiceError.unknownError(error)
        }
    }
    
    // MARK: - 数据加载
    
    /// 加载用户的好友关系
    func loadFriendships() async {
        #if DEBUG
        print("🔍 [FriendshipService] loadFriendships开始")
        #endif

        do {
            let currentProfile = try await getCurrentUserProfile()

            #if DEBUG
            print("🔍 [FriendshipService] 当前用户档案获取成功")
            print("   - 用户: \(currentProfile.user?.username ?? "未知")")
            print("   - 档案ID: \(currentProfile.id)")
            #endif

            // 🔑 系统性修复：遵循ID使用规范，传递用户ID用于数据所有权验证
            guard let currentUserID = currentProfile.user?.id else {
                throw FriendshipServiceError.currentUserNotFound
            }

            let userFriendships = try await repositoryContainer.friendshipRepository.fetchUserFriendships(
                userProfileId: currentProfile.id,  // 社交档案ID用于好友关系查询
                currentUserID: currentUserID       // 用户ID用于数据所有权验证
            )

            #if DEBUG
            print("✅ [FriendshipService] 好友关系查询完成")
            print("   - 找到 \(userFriendships.count) 个好友关系")
            for (index, friendship) in userFriendships.enumerated() {
                let otherProfile = friendship.getOtherProfile(currentProfile: currentProfile)
                print("   - 好友\(index + 1): \(otherProfile?.user?.username ?? "未知用户") (关系ID: \(friendship.id))")
            }
            #endif

            // 🚨 关键修复：原子性状态更新，避免竞态条件
            await MainActor.run {
                // 直接赋值，让@Published自然触发更新
                self.friendships = userFriendships
                // 移除强制objectWillChange.send()，避免重复触发

                #if DEBUG
                print("✅ [FriendshipService] 好友关系状态更新完成")
                print("   - UI中的好友数量: \(self.friendships.count)")
                #endif
            }

        } catch {
            #if DEBUG
            print("❌ [FriendshipService] loadFriendships失败: \(error)")
            #endif

            await MainActor.run {
                self.errorMessage = error.localizedDescription
                // 🚨 关键修复：出错时保持现有数据，避免UI闪烁
                // self.friendships.removeAll() // 移除清空操作
            }
        }
    }
    
    /// 🔑 关键修复：加载待处理的好友请求（智能ID传递）
    func loadPendingRequests() async {
        do {
            let currentProfile = try await getCurrentUserProfile()

            #if DEBUG
            print("🔍 [FriendshipService] loadPendingRequests开始")
            print("   - 当前用户: \(currentProfile.user?.username ?? "未知")")
            print("   - 当前用户档案ID: \(currentProfile.id)")
            print("   - 当前用户ID: \(currentProfile.user?.id ?? UUID())")
            #endif

            // 🔑 关键修复：必须使用用户ID进行查询，不能使用社交档案ID
            guard let userId = currentProfile.user?.id else {
                #if DEBUG
                print("❌ [FriendshipService] 无法获取用户ID")
                #endif
                throw FriendshipServiceError.currentUserNotFound
            }

            #if DEBUG
            print("🔧 [FriendshipService] 使用用户ID查询: \(userId)")
            print("   - 当前用户档案ID: \(currentProfile.id)")
            print("   - 当前用户ID: \(userId)")
            #endif

            let requests = try await repositoryContainer.friendRequestRepository.fetchReceivedRequests(userProfileId: userId)

            #if DEBUG
            print("✅ [FriendshipService] loadPendingRequests完成")
            print("   - 获取到的请求数: \(requests.count)")
            for (index, request) in requests.enumerated() {
                print("   - 请求\(index + 1): 来自 \(request.senderProfile?.user?.username ?? "未知")")
            }
            #endif

            await MainActor.run {
                self.pendingRequests = requests
            }

        } catch {
            #if DEBUG
            print("❌ [FriendshipService] loadPendingRequests失败: \(error.localizedDescription)")
            #endif
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
        }
    }
    
    /// 清理过期的好友请求
    func cleanupExpiredRequests() async {
        do {
            try await repositoryContainer.friendRequestRepository.cleanupExpiredRequests()
            await loadPendingRequests()
        } catch {
            // 静默处理清理错误，避免影响用户体验
        }
    }
    
    // MARK: - 私有辅助方法
    
    /// 🔑 关键修复：安全获取当前用户社交档案（防御性与自愈性实现）
    /// 使用SessionManager的异步安全接口，确保数据一致性，支持自动修复
    private func getCurrentUserProfile() async throws -> EAUserSocialProfile {
        #if DEBUG
        print("🔍 [FriendshipService] getCurrentUserProfile开始调用")
        #endif

        // 🚨 架构重构修复：使用SessionManager的异步安全接口
        // 这确保了与架构重构的一致性，避免数据不同步问题
        guard let currentUser = await sessionManager.safeCurrentUser else {
            #if DEBUG
            print("❌ [FriendshipService] getCurrentUserProfile失败 - sessionManager.safeCurrentUser返回nil")
            #endif
            throw FriendshipServiceError.currentUserNotFound
        }

        #if DEBUG
        print("✅ [FriendshipService] 获取到当前用户: \(currentUser.username)")
        #endif

        // 🔑 关键修复：验证用户基础数据完整性
        guard !currentUser.username.isEmpty else {
            #if DEBUG
            // 调试环境下记录用户名为空，但不使用print
            #endif
            throw FriendshipServiceError.currentUserNotFound
        }

        // 🔑 根除重复档案：使用Repository的原子性方法确保唯一档案
        let socialProfile: EAUserSocialProfile
        do {
            socialProfile = try await repositoryContainer.userRepository.findOrCreateSocialProfile(for: currentUser)
            #if DEBUG
            print("✅ [FriendshipService] 获取到唯一社交档案 - ID: \(socialProfile.id)")
            #endif
        } catch {
            #if DEBUG
            print("❌ [FriendshipService] 社交档案获取失败: \(error.localizedDescription)")
            #endif
            throw FriendshipServiceError.profileInitializationFailed
        }

        // 🔑 新增：验证数字宇宙数据完整性（首次用户注册的关键检查）
        // 确保数字宇宙数据已初始化（安全调用，有防重复机制）
        socialProfile.initializeDigitalUniverseData()

        // 🚨 关键修复：验证初始化后的数据完整性
        guard socialProfile.stellarLevel != nil,
              socialProfile.totalStellarEnergy != nil,
              socialProfile.explorerTitle != nil else {
            #if DEBUG
            // 调试环境下记录数字宇宙数据初始化失败，但不使用print
            #endif
            throw FriendshipServiceError.profileInitializationFailed
        }

        return socialProfile
    }

    /// 🔑 关键修复：通过Repository安全创建社交档案，避免跨Context操作
    private func createMissingSocialProfile(for user: EAUser) async -> EAUserSocialProfile? {
        do {
            // 🚨 架构级修复：通过Repository层安全创建，避免直接操作ModelContext
            // 这消除了@MainActor与@ModelActor之间的Context冲突

            // 重新获取用户以确保Context一致性
            guard let safeUser = try await repositoryContainer.userRepository.fetchUser(id: user.id) else {
                return nil
            }

            // 检查是否已经有社交档案（可能在并发操作中已创建）
            if let existingProfile = safeUser.socialProfile {
                return existingProfile
            }

            // 通过Repository创建新的社交档案
            let socialProfile = EAUserSocialProfile()
            socialProfile.initializeDigitalUniverseData()

            // 注意：这里需要Repository层支持社交档案的创建和关联
            // 暂时返回nil，避免跨Context操作
            #if DEBUG
            // 调试环境下记录需要Repository层支持社交档案创建
            #endif

            return nil
        } catch {
            #if DEBUG
            // 调试环境下记录创建社交档案失败，但不使用print
            #endif
            return nil
        }
    }

    /// 🔑 关键修复：通过Repository安全获取用户社交档案（多层验证版本）
    /// 确保获取的用户档案数据完整且Context一致，避免跨Context访问问题
    private func getProfileById(_ profileId: UUID) async -> EAUserSocialProfile? {
        do {
            #if DEBUG
            print("🔍 [FriendshipService] getProfileById开始 - profileId: \(profileId)")
            #endif

            // 🔑 第一层验证：检查输入参数有效性
            guard profileId != UUID(uuidString: "00000000-0000-0000-0000-000000000000") else {
                #if DEBUG
                print("❌ [FriendshipService] 无效的profileId")
                #endif
                return nil
            }

            // 🚨 性能修复：通过CommunityRepository获取特定用户的社交档案，避免加载所有用户
            // 这消除了性能瓶颈和潜在的内存问题
            let profile = try await repositoryContainer.communityRepository.fetchUserSocialProfile(by: profileId)

            // 🔑 第二层验证：检查Repository返回结果
            guard let profile = profile else {
                #if DEBUG
                print("❌ [FriendshipService] Repository返回nil - profileId: \(profileId)")
                #endif
                return nil
            }

            // 🔑 第三层验证：检查用户关系完整性
            guard let user = profile.user else {
                #if DEBUG
                print("❌ [FriendshipService] 社交档案缺少用户关系 - profileId: \(profileId)")
                #endif
                return nil
            }

            // 🔑 第四层验证：检查用户基础数据完整性
            guard !user.username.isEmpty,
                  user.id == profileId else {
                #if DEBUG
                print("❌ [FriendshipService] 用户数据不完整或ID不匹配")
                print("   - 用户名: '\(user.username)'")
                print("   - 期望ID: \(profileId)")
                print("   - 实际ID: \(user.id)")
                #endif
                return nil
            }

            // 🔑 第五层验证：检查数字宇宙数据完整性
            guard profile.stellarLevel != nil,
                  profile.totalStellarEnergy != nil,
                  profile.explorerTitle != nil else {
                #if DEBUG
                print("❌ [FriendshipService] 数字宇宙数据不完整 - 用户名: \(user.username)")
                #endif
                return nil
            }

            #if DEBUG
            print("✅ [FriendshipService] getProfileById验证通过")
            print("   - 用户名: \(user.username)")
            print("   - 档案ID: \(profile.id)")
            print("   - 星际等级: \(profile.stellarLevel ?? 0)")
            print("   - 星际能量: \(profile.totalStellarEnergy ?? 0)")
            #endif

            return profile

        } catch {
            #if DEBUG
            print("❌ [FriendshipService] getProfileById异常 - \(error.localizedDescription)")
            #endif
            return nil
        }
    }

    /// 设置当前用户档案（用于UI集成）
    func setCurrentUserProfile(_ profile: EAUserSocialProfile) {
        // 这个方法将在UI层调用，设置当前用户档案
        // 实际实现中可以通过SessionManager或其他方式获取
    }

    // MARK: - 🔑 新增：数据完整性检查和修复

    /// 🔑 简化的用户数据验证方法 - 只检查基础数据完整性
    func validateAndRepairUserData() async -> Bool {
        // 🔑 简单验证：检查当前用户是否存在且有社交档案
        guard let currentUser = await sessionManager.safeCurrentUser,
              !currentUser.username.isEmpty,
              currentUser.socialProfile != nil else {
            return false
        }

        return true
    }
}

// MARK: - 好友关系状态枚举

enum FriendshipStatus {
    case notFriend                          // 不是好友
    case friend(EAFriendship)              // 已是好友
    case requestSent(EAFriendRequest)      // 已发送请求
    case requestReceived(EAFriendRequest)  // 收到请求
}




