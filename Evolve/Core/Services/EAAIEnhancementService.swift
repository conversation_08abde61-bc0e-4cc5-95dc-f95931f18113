//
//  EAAIEnhancementService.swift
//  Evolve
//
//  Created by AI Assistant on 2025-06-06.
//  Phase 2 Day 4: AI增强功能开发 - AI增强功能集成服务
//

import Foundation
import SwiftUI

/// AI增强功能集成服务
/// 统一管理分享时机检测、个性化推荐和缓存机制，作为AI功能的统一入口
/// 遵循开发规范文档的"Repository模式强制执行规范"和"AI成本控制开发规范"
@MainActor
class EAAIEnhancementService: ObservableObject {
    
    // MARK: - 发布属性
    
    @Published var isAIEnabled: Bool = true
    @Published var isProcessing: Bool = false
    @Published var lastUpdateTime: Date?
    @Published var enhancementStatistics = EAAIEnhancementStatistics()
    
    // MARK: - 依赖注入
    
    private let repositoryContainer: EARepositoryContainer
    private let aiDataBridge: EACommunityAIDataBridge
    private let sharingMomentDetectionService: EASharingMomentDetectionService
    private let recommendationEngine: EAPersonalizedRecommendationEngine
    private let cacheManager: EAAICacheManager
    
    // MARK: - 配置
    
    private let enhancementConfig = EAAIEnhancementConfig()
    
    // MARK: - 初始化
    
    init(repositoryContainer: EARepositoryContainer, aiDataBridge: EACommunityAIDataBridge, cacheManager: EAAICacheManager) {
        self.repositoryContainer = repositoryContainer
        self.aiDataBridge = aiDataBridge
        self.cacheManager = cacheManager
        self.sharingMomentDetectionService = EASharingMomentDetectionService(
            repositoryContainer: repositoryContainer,
            aiDataBridge: aiDataBridge
        )
        self.recommendationEngine = EAPersonalizedRecommendationEngine(
            repositoryContainer: repositoryContainer,
            aiDataBridge: aiDataBridge
        )
    }
    
    // MARK: - 统一AI增强接口
    
    /// 获取用户的AI增强建议套件
    /// - Parameter userId: 用户ID
    /// - Returns: 包含分享建议、推荐内容、行为洞察的完整套件
    func getAIEnhancementSuite(for userId: UUID) async -> EAAIEnhancementSuite? {
        guard isAIEnabled else {
            enhancementStatistics.aiDisabledCount += 1
            return nil
        }
        
        isProcessing = true
        defer { isProcessing = false }
        
        // 并行获取各项AI增强功能
        async let sharingMoments = getSharingMoments(for: userId)
        async let recommendations = getPersonalizedRecommendations(for: userId)
        async let behaviorInsights = getBehaviorInsights(for: userId)
        
        let suite = EAAIEnhancementSuite(
            userId: userId,
            sharingMoments: await sharingMoments,
            recommendations: await recommendations,
            behaviorInsights: await behaviorInsights,
            generatedAt: Date()
        )
        
        enhancementStatistics.successfulEnhancements += 1
        lastUpdateTime = Date()
        
        return suite
    }
    
    /// 获取智能分享时机建议
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - habitId: 可选的特定习惯ID
    /// - Returns: 分享时机建议数组
    func getSharingMoments(for userId: UUID, habitId: UUID? = nil) async -> [EASharingMoment] {
        // 检查缓存
        if let cachedMoments = cacheManager.getCachedSharingMoments(userId: userId) {
            enhancementStatistics.cacheHits += 1
            return cachedMoments
        }
        
        var allSharingMoments: [EASharingMoment] = []
        
        if let habitId = habitId {
            // 检测特定习惯的分享时机
            if let moment = await sharingMomentDetectionService.detectSharingMoment(userId: userId, habitId: habitId) {
                allSharingMoments.append(moment)
            }
        } else {
            // 检测所有活跃习惯的分享时机
            let user = await repositoryContainer.userRepository.fetchUser(by: userId)
            let activeHabits = user?.habits.filter { $0.isActive } ?? []
            
            for habit in activeHabits {
                if let moment = await sharingMomentDetectionService.detectSharingMoment(userId: userId, habitId: habit.id) {
                    allSharingMoments.append(moment)
                }
            }
        }
        
        // 按置信度排序，只返回高质量的建议
        let qualifiedMoments = allSharingMoments
            .filter { $0.confidenceScore >= enhancementConfig.minimumConfidenceThreshold }
            .sorted { $0.confidenceScore > $1.confidenceScore }
            .prefix(enhancementConfig.maxSharingMomentsPerUser)
        
        let finalMoments = Array(qualifiedMoments)
        
        // 缓存结果
        cacheManager.cacheSharingMoments(finalMoments, for: userId)
        enhancementStatistics.cacheMisses += 1
        
        return finalMoments
    }
    
    /// 获取个性化推荐内容
    /// - Parameter userId: 用户ID
    /// - Returns: 推荐结果
    func getPersonalizedRecommendations(for userId: UUID) async -> EARecommendationResult? {
        // 检查缓存
        if let cachedRecommendations = cacheManager.getCachedRecommendations(userId: userId) {
            enhancementStatistics.cacheHits += 1
            return cachedRecommendations
        }
        
        // 生成新的推荐
        await recommendationEngine.generatePersonalizedRecommendations(for: userId)
        
        let recommendations = EARecommendationResult(
            posts: recommendationEngine.recommendedPosts,
            users: recommendationEngine.recommendedUsers,
            generatedAt: Date()
        )
        
        // 缓存结果
        cacheManager.cacheRecommendations(recommendations, for: userId)
        enhancementStatistics.cacheMisses += 1
        
        return recommendations
    }
    
    /// 获取行为洞察
    /// - Parameter userId: 用户ID
    /// - Returns: 行为分析结果
    func getBehaviorInsights(for userId: UUID) async -> EAAIBehaviorAnalysis? {
        // 检查缓存
        if let cachedAnalysis = cacheManager.getCachedBehaviorAnalysis(userId: userId) {
            enhancementStatistics.cacheHits += 1
            return cachedAnalysis
        }
        
        do {
            // 通过AI数据桥接获取用户数据
            let userSummary = try await aiDataBridge.getUserSocialSummary(userId: userId)
            let user = await repositoryContainer.userRepository.fetchUser(by: userId)
            
            // 生成行为分析（简化版本，实际会有更复杂的AI分析）
            let analysis = generateBehaviorAnalysis(for: user, summary: userSummary)
            
            // 缓存结果
            cacheManager.cacheBehaviorAnalysis(analysis, for: userId)
            enhancementStatistics.cacheMisses += 1
            
            return analysis
            
        } catch {
            return nil
        }
    }
    
    // MARK: - AI功能控制
    
    /// 启用AI增强功能
    func enableAIEnhancement() {
        isAIEnabled = true
        enhancementStatistics.aiEnabledCount += 1
    }
    
    /// 禁用AI增强功能
    func disableAIEnhancement() {
        isAIEnabled = false
        enhancementStatistics.aiDisabledCount += 1
    }
    
    /// 刷新用户的AI增强数据
    /// - Parameter userId: 用户ID
    func refreshEnhancementData(for userId: UUID) async {
        // 清理相关缓存
        cacheManager.clearUserProfileCache(for: userId)
        
        // 刷新推荐内容
        await recommendationEngine.refreshRecommendations(for: userId)
        
        enhancementStatistics.manualRefreshCount += 1
    }
    
    /// 获取AI增强功能统计信息
    func getEnhancementStatistics() -> EAAIEnhancementStatistics {
        var stats = enhancementStatistics
        stats.cacheStatistics = cacheManager.getCacheUsageStatistics()
        return stats
    }
    
    // MARK: - 私有辅助方法
    
    /// 生成简化的行为分析
    private func generateBehaviorAnalysis(for user: EAUser?, summary: EAAISocialSummary) -> EAAIBehaviorAnalysis {
        // 基于用户数据生成简化的行为分析
        let habits = user?.habits ?? []
        
        // 生成完成模式描述
        let completionPatterns = habits.map { habit in
            let completionCount = habit.completions.count
            return "\(habit.name): \(completionCount)次完成"
        }
        
        // 生成时间偏好描述
        let timePreferences = ["早晨习惯", "下午运动", "晚间学习"]
        
        // 生成动机因素建议
        let motivationTriggers = generateMotivationFactors(for: summary.stellarLevel)
        
        // 识别挑战领域
        let challengeAreas = identifyChallengeAreas(for: habits)
        
        // 生成成功因素
        let successFactors = generateSuccessFactors(for: summary.stellarLevel)
        
        return EAAIBehaviorAnalysis(
            userId: summary.userId,
            completionPatterns: completionPatterns,
            timePreferences: timePreferences,
            motivationTriggers: motivationTriggers,
            challengeAreas: challengeAreas,
            successFactors: successFactors,
            analysisTimestamp: Date()
        )
    }
    
    /// 生成动机因素
    private func generateMotivationFactors(for stellarLevel: Int) -> [String] {
        switch stellarLevel {
        case 1...2:
            return ["小步快跑", "庆祝小胜利", "建立简单习惯"]
        case 3...5:
            return ["连击奖励", "社区分享", "进阶挑战"]
        case 6...8:
            return ["长期目标", "习惯叠加", "经验分享"]
        default:
            return ["引领他人", "创新挑战", "习惯大师"]
        }
    }
    
    /// 识别挑战领域
    private func identifyChallengeAreas(for habits: [EAHabit]) -> [String] {
        var challenges: [String] = []
        
        for habit in habits {
            let completionCount = habit.completions.count
            let targetCount = habit.targetFrequency * 7 // 假设一周目标
            let completionRate = Double(completionCount) / max(1.0, Double(targetCount))
            
            if completionRate < 0.5 {
                challenges.append(habit.name)
            }
        }
        
        return challenges.isEmpty ? ["保持现状"] : challenges
    }
    
    /// 生成成功因素
    private func generateSuccessFactors(for stellarLevel: Int) -> [String] {
        switch stellarLevel {
        case 1...2:
            return ["坚持记录", "小目标设定", "及时奖励"]
        case 3...5:
            return ["规律作息", "习惯叠加", "社区互动"]
        case 6...8:
            return ["长期规划", "自我监控", "影响他人"]
        default:
            return ["系统思维", "持续创新", "领导示范"]
        }
    }
}

// MARK: - AI增强功能数据模型

/// AI增强功能套件
struct EAAIEnhancementSuite {
    let userId: UUID
    let sharingMoments: [EASharingMoment]
    let recommendations: EARecommendationResult?
    let behaviorInsights: EAAIBehaviorAnalysis?
    let generatedAt: Date
}

/// AI增强功能配置
struct EAAIEnhancementConfig {
    let minimumConfidenceThreshold: Double = 0.7
    let maxSharingMomentsPerUser: Int = 3
    let maxRecommendationsPerUser: Int = 20
    let cacheValidityHours: Int = 24
    let enableBehaviorAnalysis: Bool = true
    let enablePersonalizedContent: Bool = true
    let enableSharingMoments: Bool = true
}

// EAAIEnhancementStatistics 已在 EAAIDataModels.swift 中定义，此处移除重复定义