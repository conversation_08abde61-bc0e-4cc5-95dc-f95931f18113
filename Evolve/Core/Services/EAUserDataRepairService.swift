//
//  EAUserDataRepairService.swift
//  Evolve
//
//  Created by AI Assistant on 2025-01-26.
//  用户数据自动修复服务 - 阶段3.2实现
//

import Foundation
import SwiftData
import SwiftUI

/// 用户数据自动修复服务
/// 负责检测和修复用户数据问题，确保数据完整性和一致性
/// 遵循开发规范文档的Repository模式强制执行规范
@MainActor
class EAUserDataRepairService: ObservableObject {
    
    // MARK: - 依赖注入
    
    private let repositoryContainer: EARepositoryContainer
    private let validationService: EAUserValidationService
    private let integrityGuard: EAUserIntegrityGuard
    
    // MARK: - 修复状态
    
    @Published var isRepairing = false
    @Published var repairProgress: Double = 0.0
    @Published var lastRepairReport: RepairReport?
    
    // MARK: - 性能监控
    
    private static let maxRepairDuration: TimeInterval = 1.0 // 1秒限制
    private static let maxBatchRepairDuration: TimeInterval = 3.0 // 批量修复3秒限制
    
    // MARK: - 初始化
    
    init(repositoryContainer: EARepositoryContainer, 
         validationService: EAUserValidationService,
         integrityGuard: EAUserIntegrityGuard) {
        self.repositoryContainer = repositoryContainer
        self.validationService = validationService
        self.integrityGuard = integrityGuard
    }
    
    // MARK: - 修复报告
    
    /// 修复报告
    struct RepairReport {
        let userId: UUID
        let repairStartTime: Date
        let repairEndTime: Date
        let repairDuration: TimeInterval
        let issuesFound: [String]
        let issuesRepaired: [String]
        let failedRepairs: [String]
        let finalStatus: EAUserValidationService.UserIntegrityStatus
        let success: Bool
        
        var summary: String {
            if success {
                return "修复成功：解决了\(issuesRepaired.count)个问题"
            } else {
                return "修复部分成功：解决了\(issuesRepaired.count)个问题，\(failedRepairs.count)个问题待处理"
            }
        }
    }
    
    // MARK: - 阶段3.2：智能自动修复
    
    /// 🔑 阶段3.2：执行用户数据智能修复
    /// 检测并自动修复用户数据问题，提供详细的修复报告
    /// - Parameter user: 待修复的用户
    /// - Returns: 修复报告
    func performIntelligentRepair(for user: EAUser) async -> RepairReport {
        let startTime = Date()
        let startTimeInterval = CFAbsoluteTimeGetCurrent()
        
        defer {
            let duration = CFAbsoluteTimeGetCurrent() - startTimeInterval
            #if DEBUG
            if duration > Self.maxRepairDuration {
                print("⚠️ 用户数据修复超时: \(duration * 1000)ms")
            } else {
                print("✅ 用户数据修复完成: \(duration * 1000)ms")
            }
            #endif
        }
        
        await MainActor.run {
            self.isRepairing = true
            self.repairProgress = 0.0
        }
        
        var issuesRepaired: [String] = []
        var failedRepairs: [String] = []
        
        // 🔑 第一步：深度安全检查（10%进度）
        await updateProgress(0.1)
        let integrityReport = await validationService.performIntegrityCheck(for: user)
        
        // 🔑 第二步：执行自动修复（80%进度）
        await updateProgress(0.2)
        
        for (index, action) in integrityReport.repairActions.enumerated() {
            let progress = 0.2 + (0.8 * Double(index + 1) / Double(integrityReport.repairActions.count))
            await updateProgress(progress)
            
            do {
                try await executeRepairAction(action, for: user)
                issuesRepaired.append(getActionDescription(action))
                
                #if DEBUG
                print("✅ 修复操作成功: \(getActionDescription(action))")
                #endif
            } catch {
                failedRepairs.append("修复\(getActionDescription(action))失败: \(error.localizedDescription)")
                
                #if DEBUG
                print("❌ 修复操作失败: \(getActionDescription(action)) - \(error)")
                #endif
            }
        }
        
        // 🔑 第三步：验证修复结果（10%进度）
        await updateProgress(0.9)
        let finalReport = await validationService.performIntegrityCheck(for: user)
        
        await updateProgress(1.0)
        
        let endTime = Date()
        let report = RepairReport(
            userId: user.id,
            repairStartTime: startTime,
            repairEndTime: endTime,
            repairDuration: endTime.timeIntervalSince(startTime),
            issuesFound: integrityReport.issues,
            issuesRepaired: issuesRepaired,
            failedRepairs: failedRepairs,
            finalStatus: finalReport.status,
            success: failedRepairs.isEmpty && finalReport.status == .valid
        )
        
        await MainActor.run {
            self.lastRepairReport = report
            self.isRepairing = false
            self.repairProgress = 0.0
        }
        
        return report
    }
    
    /// 🔑 阶段3.2：批量用户修复
    /// 对多个用户执行批量修复，适用于系统升级后的数据修复
    /// - Parameter users: 待修复的用户列表
    /// - Returns: 批量修复报告
    func performBatchRepair(for users: [EAUser]) async -> [RepairReport] {
        let startTime = CFAbsoluteTimeGetCurrent()
        defer {
            let duration = CFAbsoluteTimeGetCurrent() - startTime
            #if DEBUG
            if duration > Self.maxBatchRepairDuration {
                print("⚠️ 批量用户修复超时: \(duration)秒")
            } else {
                print("✅ 批量用户修复完成: \(duration)秒")
            }
            #endif
        }
        
        await MainActor.run {
            self.isRepairing = true
            self.repairProgress = 0.0
        }
        
        var reports: [RepairReport] = []
        
        for (index, user) in users.enumerated() {
            let progress = Double(index) / Double(users.count)
            await updateProgress(progress)
            
            let report = await performIntelligentRepair(for: user)
            reports.append(report)
            
            // 批量修复间隔，避免系统过载
            if index < users.count - 1 {
                try? await Task.sleep(nanoseconds: 100_000_000) // 100ms间隔
            }
        }
        
        await MainActor.run {
            self.isRepairing = false
            self.repairProgress = 0.0
        }
        
        return reports
    }
    
    // MARK: - 私有修复方法
    
    /// 执行具体的修复操作
    private func executeRepairAction(_ action: EAUserValidationService.RepairAction, for user: EAUser) async throws {
        switch action {
        case .createMissingProfile:
            try await createMissingSocialProfile(for: user)
        case .reinitializeDigitalUniverseData:
            try await reinitializeDigitalUniverseData(for: user)
        case .restoreDefaultSettings:
            try await restoreDefaultSettings(for: user)
        case .rebuildUserRelationships:
            try await rebuildUserRelationships(for: user)
        }
    }
    
    /// 创建缺失的社交档案
    private func createMissingSocialProfile(for user: EAUser) async throws {
        #if DEBUG
        print("🔧 开始创建缺失的社交档案: \(user.username)")
        #endif
        
        // 使用用户完整性守护服务进行安全创建
        _ = await integrityGuard.safeSocialProfile(for: user)
    }
    
    /// 重新初始化数字宇宙数据
    private func reinitializeDigitalUniverseData(for user: EAUser) async throws {
        #if DEBUG
        print("🔧 开始重新初始化数字宇宙数据: \(user.username)")
        #endif
        
        guard let profile = user.socialProfile else {
            throw RepairError.missingProfile
        }
        
        // 重新初始化数字宇宙数据
        profile.stellarLevel = 1
        profile.totalStellarEnergy = 0
        profile.explorerTitle = "新手探索者"
        profile.universeRegion = "起源星域"
        profile.lastActiveDate = Date()
        
        // 通过Repository保存更改
        try await repositoryContainer.userRepository.saveUser(user)
    }
    
    /// 恢复默认设置
    private func restoreDefaultSettings(for user: EAUser) async throws {
        #if DEBUG
        print("🔧 开始恢复默认设置: \(user.username)")
        #endif
        
        if user.settings == nil {
            // 通过Repository创建默认设置
            // 目前Repository没有这个方法，暂时抛出错误
            throw RepairError.featureNotImplemented("默认设置恢复功能需要在Repository层实现")
        }
    }
    
    /// 重建用户关系数据
    private func rebuildUserRelationships(for user: EAUser) async throws {
        #if DEBUG
        print("🔧 开始重建用户关系数据: \(user.username)")
        #endif
        
        // 检查并重建缺失的档案
        if user.dataProfile == nil || user.moderationProfile == nil {
            // 通过Repository重建档案
            // 目前Repository没有这个方法，暂时抛出错误
            throw RepairError.featureNotImplemented("用户档案重建功能需要在Repository层实现")
        }
    }
    
    /// 获取修复操作的描述
    private func getActionDescription(_ action: EAUserValidationService.RepairAction) -> String {
        switch action {
        case .createMissingProfile:
            return "创建社交档案"
        case .reinitializeDigitalUniverseData:
            return "重新初始化数字宇宙数据"
        case .restoreDefaultSettings:
            return "恢复默认设置"
        case .rebuildUserRelationships:
            return "重建用户关系数据"
        }
    }
    
    /// 更新修复进度
    private func updateProgress(_ progress: Double) async {
        await MainActor.run {
            self.repairProgress = progress
        }
    }
}

// MARK: - 修复错误类型

enum RepairError: Error, LocalizedError {
    case missingProfile
    case featureNotImplemented(String)
    case repairFailed(String)
    case dataCorruption(String)
    
    var errorDescription: String? {
        switch self {
        case .missingProfile:
            return "用户档案缺失"
        case .featureNotImplemented(let message):
            return "功能未实现: \(message)"
        case .repairFailed(let message):
            return "修复失败: \(message)"
        case .dataCorruption(let message):
            return "数据损坏: \(message)"
        }
    }
}

// MARK: - Environment支持

/// Environment扩展，支持用户数据修复服务依赖注入
extension EnvironmentValues {
    @Entry var userDataRepairService: EAUserDataRepairService? = nil
} 