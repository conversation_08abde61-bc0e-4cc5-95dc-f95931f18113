//
//  EAUserFriendlyErrorService.swift
//  Evolve
//
//  Created by AI Assistant on 2025-01-27.
//  用户友好错误处理服务 - 阶段2.2实现
//

import Foundation
import SwiftUI
import Combine

/// 用户友好错误处理服务
/// 负责将技术性错误转换为用户可理解的提示信息
/// 遵循开发规范文档的错误处理完整性要求
@MainActor
class EAUserFriendlyErrorService: ObservableObject {
    
    // MARK: - 状态属性
    @Published var currentError: UserFriendlyError?
    @Published var isShowingError = false
    @Published var errorHistory: [UserFriendlyError] = []
    
    // MARK: - 错误统计
    @Published var errorStats = ErrorStatistics()
    
    // MARK: - 核心错误转换方法
    
    /// 将DataModelError转换为用户友好的错误信息
    func processError(_ error: DataModelError, context: ErrorContext = .general) -> UserFriendlyError {
        let friendlyError = UserFriendlyError(
            id: UUID(),
            originalError: error,
            context: context,
            timestamp: Date(),
            userMessage: getUserMessage(for: error, context: context),
            actionSuggestions: getActionSuggestions(for: error, context: context),
            severity: error.severity,
            canRetry: canRetry(error: error),
            requiresUserAction: error.requiresUserFeedback
        )
        
        // 记录错误统计
        recordError(friendlyError)
        
        return friendlyError
    }
    
    /// 显示错误给用户
    func showError(_ error: DataModelError, context: ErrorContext = .general) {
        let friendlyError = processError(error, context: context)
        currentError = friendlyError
        isShowingError = true
        
        // 添加到历史记录
        errorHistory.insert(friendlyError, at: 0)
        
        // 保持历史记录最多50条
        if errorHistory.count > 50 {
            errorHistory = Array(errorHistory.prefix(50))
        }
    }
    
    /// 清除当前错误
    func clearCurrentError() {
        currentError = nil
        isShowingError = false
    }
    
    /// 重试操作
    func retryLastOperation() {
        guard let currentError = currentError, currentError.canRetry else {
            return
        }
        
        // 这里可以添加重试逻辑
        clearCurrentError()
    }
    
    // MARK: - 私有方法
    
    /// 获取用户友好的错误信息
    private func getUserMessage(for error: DataModelError, context: ErrorContext) -> String {
        switch error {
        // 用户身份系统相关错误
        case .userIdentityCorrupted:
            return "您的账户信息出现了问题，我们正在为您修复。"
            
        case .profileInitializationFailed:
            switch context {
            case .registration:
                return "账户创建遇到问题，请稍后重试。"
            case .login:
                return "登录时遇到问题，请重新登录。"
            default:
                return "账户设置遇到问题，请稍后重试。"
            }
            
        case .socialProfileMissing:
            return "您的社交资料正在准备中，请稍等片刻。"
            
        case .asyncInitializationTimeout:
            return "账户准备需要一点时间，请稍后再试。"
            
        case .integrityCheckFailed:
            return "正在检查您的账户安全，请稍候。"
            
        // 数据相关错误
        case .dataCorruption:
            return "数据出现问题，我们正在为您恢复。"
            
        case .userNotFound:
            switch context {
            case .login:
                return "用户名或密码错误，请重新输入。"
            case .friendship:
                return "找不到这位用户，请检查用户名。"
            default:
                return "无法找到用户信息，请重新登录。"
            }
            
        case .duplicateUser(let message):
            return message.isEmpty ? "此账户已存在，请尝试登录。" : message
            
        // 系统级错误
        case .systemResourceUnavailable:
            return "系统暂时繁忙，请稍后再试。"
            
        case .serviceUnavailable:
            return "服务暂时不可用，我们正在修复中。"
            
        case .emergencyMode:
            return "系统正在维护中，部分功能暂时不可用。"
            
        // 档案完整性错误
        case .incompleteUserProfile:
            return "您的资料不完整，请完善个人信息。"
            
        case .invalidProfileState:
            return "账户状态异常，正在为您修复。"
            
        case .profileRelationshipBroken:
            return "账户关联出现问题，正在自动修复。"
            
        // 其他错误
        case .contextMismatch, .relationshipValidationFailed, .dataIntegrityError:
            return "遇到技术问题，我们正在处理中。"
            
        case .methodDeprecated:
            return "功能正在升级，请稍后再试。"
            
        case .criticalDataMissing:
            return "关键信息缺失，正在为您恢复。"
            
        case .moderationProfileMissing:
            return "账户权限设置中，请稍候。"
            
        case .dataProfileMissing:
            return "数据档案准备中，请稍等。"
            
        case .recoveryRequired:
            return "正在恢复您的数据，请耐心等待。"
        }
    }
    
    /// 获取操作建议
    private func getActionSuggestions(for error: DataModelError, context: ErrorContext) -> [String] {
        switch error {
        case .userNotFound:
            switch context {
            case .login:
                return ["检查用户名和密码", "尝试忘记密码功能", "联系客服"]
            case .friendship:
                return ["检查用户名拼写", "刷新页面", "稍后再试"]
            default:
                return ["重新登录", "清除应用缓存", "联系客服"]
            }
            
        case .duplicateUser:
            return ["尝试登录现有账户", "使用其他邮箱注册", "联系客服找回账户"]
            
        case .asyncInitializationTimeout:
            return ["稍后再试", "检查网络连接", "重启应用"]
            
        case .socialProfileMissing, .moderationProfileMissing, .dataProfileMissing:
            return ["等待自动恢复", "重新登录", "联系客服"]
            
        case .systemResourceUnavailable, .serviceUnavailable:
            return ["稍后再试", "检查网络连接", "查看系统状态"]
            
        case .incompleteUserProfile:
            return ["完善个人资料", "添加头像", "更新设置"]
            
        case .profileInitializationFailed:
            return ["重新登录", "清除应用数据", "联系客服"]
            
        case .emergencyMode:
            return ["等待维护完成", "查看公告", "稍后再试"]
            
        default:
            return ["重试操作", "重启应用", "联系客服"]
        }
    }
    
    /// 判断错误是否可以重试
    private func canRetry(error: DataModelError) -> Bool {
        switch error {
        case .userNotFound, .duplicateUser:
            return false
        case .systemResourceUnavailable, .serviceUnavailable:
            return true
        case .asyncInitializationTimeout:
            return true
        case .profileInitializationFailed:
            return true
        case .emergencyMode:
            return false
        default:
            return true
        }
    }
    
    /// 记录错误统计
    private func recordError(_ error: UserFriendlyError) {
        errorStats.totalErrors += 1
        
        switch error.severity {
        case .critical:
            errorStats.criticalErrors += 1
        case .high:
            errorStats.highSeverityErrors += 1
        case .medium:
            errorStats.mediumSeverityErrors += 1
        case .low:
            errorStats.lowSeverityErrors += 1
        }
        
        // 记录错误类型分布
        let errorType = String(describing: error.originalError)
        errorStats.errorTypeDistribution[errorType, default: 0] += 1
        
        // 记录上下文分布
        errorStats.contextDistribution[error.context, default: 0] += 1
    }
}

// MARK: - 用户友好错误模型

struct UserFriendlyError: Identifiable {
    let id: UUID
    let originalError: DataModelError
    let context: ErrorContext
    let timestamp: Date
    let userMessage: String
    let actionSuggestions: [String]
    let severity: ErrorSeverity
    let canRetry: Bool
    let requiresUserAction: Bool
}

// MARK: - 错误上下文

enum ErrorContext: String, CaseIterable {
    case general = "general"
    case registration = "registration"
    case login = "login"
    case friendship = "friendship"
    case community = "community"
    case profile = "profile"
    case settings = "settings"
    case habits = "habits"
    case ai = "ai"
}

// MARK: - 错误统计

struct ErrorStatistics {
    var totalErrors: Int = 0
    var criticalErrors: Int = 0
    var highSeverityErrors: Int = 0
    var mediumSeverityErrors: Int = 0
    var lowSeverityErrors: Int = 0
    var errorTypeDistribution: [String: Int] = [:]
    var contextDistribution: [ErrorContext: Int] = [:]
    
    var criticalErrorRate: Double {
        guard totalErrors > 0 else { return 0 }
        return Double(criticalErrors) / Double(totalErrors)
    }
    
    var mostCommonErrorType: String? {
        return errorTypeDistribution.max(by: { $0.value < $1.value })?.key
    }
    
    var mostProblematicContext: ErrorContext? {
        return contextDistribution.max(by: { $0.value < $1.value })?.key
    }
} 