import Foundation
import UIKit
import AVFoundation

/// 文件存储服务 - 多媒体文件本地存储管理
/// 遵循项目规范，使用依赖注入而非单例模式
@MainActor
class EAFileStorageService: ObservableObject {
    
    // MARK: - 存储路径配置
    
    private let documentsDirectory: URL
    private let audioDirectory: URL
    private let imageDirectory: URL
    private let videoDirectory: URL
    private let thumbnailDirectory: URL
    
    // MARK: - 文件格式配置
    
    struct FileConfig {
        static let maxImageSize: CGSize = CGSize(width: 1920, height: 1920)
        static let imageCompressionQuality: CGFloat = 0.8
        static let maxVideoFileSize: Int64 = 50 * 1024 * 1024 // 50MB
        static let maxAudioDuration: TimeInterval = 60 // 60秒
        static let videoCompressionPreset = AVAssetExportPresetMediumQuality
    }
    
    // MARK: - 初始化
    
    init() throws {
        documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        audioDirectory = documentsDirectory.appendingPathComponent("Audio")
        imageDirectory = documentsDirectory.appendingPathComponent("Images")
        videoDirectory = documentsDirectory.appendingPathComponent("Videos")
        thumbnailDirectory = documentsDirectory.appendingPathComponent("Thumbnails")
        
        try createDirectoriesIfNeeded()
    }
    
    /// 创建必要的目录
    private func createDirectoriesIfNeeded() throws {
        let directories = [audioDirectory, imageDirectory, videoDirectory, thumbnailDirectory]
        
        for directory in directories {
            if !FileManager.default.fileExists(atPath: directory.path) {
                try FileManager.default.createDirectory(
                    at: directory,
                    withIntermediateDirectories: true,
                    attributes: nil
                )
            }
        }
    }
    
    // MARK: - 图片处理
    
    /// 保存图片并返回本地路径
    func saveImage(_ image: UIImage, messageId: UUID) async throws -> (url: String, fileSize: Int64) {
        let fileName = "\(messageId.uuidString)_\(Date().timeIntervalSince1970).jpg"
        let fileURL = imageDirectory.appendingPathComponent(fileName)
        
        // 压缩图片
        let compressedImage = await compressImage(image)
        
        guard let imageData = compressedImage.jpegData(compressionQuality: FileConfig.imageCompressionQuality) else {
            throw FileStorageError.imageCompressionFailed
        }
        
        try imageData.write(to: fileURL)
        
        let fileSize = Int64(imageData.count)
        return (url: fileURL.path, fileSize: fileSize)
    }
    
    /// 压缩图片到指定尺寸
    private func compressImage(_ image: UIImage) async -> UIImage {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                let targetSize = FileConfig.maxImageSize
                let currentSize = image.size
                
                // 计算压缩比例
                let widthRatio = targetSize.width / currentSize.width
                let heightRatio = targetSize.height / currentSize.height
                let ratio = min(widthRatio, heightRatio, 1.0)
                
                let newSize = CGSize(
                    width: currentSize.width * ratio,
                    height: currentSize.height * ratio
                )
                
                // 创建压缩图片
                UIGraphicsBeginImageContextWithOptions(newSize, false, 1.0)
                image.draw(in: CGRect(origin: .zero, size: newSize))
                let compressedImage = UIGraphicsGetImageFromCurrentImageContext() ?? image
                UIGraphicsEndImageContext()
                
                DispatchQueue.main.async {
                    continuation.resume(returning: compressedImage)
                }
            }
        }
    }
    
    // MARK: - 视频处理
    
    /// 保存视频并生成缩略图
    func saveVideo(from url: URL, messageId: UUID) async throws -> (videoURL: String, thumbnailURL: String?, duration: TimeInterval, fileSize: Int64) {
        let fileName = "\(messageId.uuidString)_\(Date().timeIntervalSince1970).mp4"
        let videoFileURL = videoDirectory.appendingPathComponent(fileName)
        
        // 检查文件大小
        let attributes = try FileManager.default.attributesOfItem(atPath: url.path)
        let originalFileSize = attributes[.size] as? Int64 ?? 0
        
        var finalVideoURL: URL
        var finalFileSize: Int64
        
        if originalFileSize > FileConfig.maxVideoFileSize {
            // 需要压缩视频
            let compressedResult = try await compressVideo(inputURL: url, outputURL: videoFileURL)
            finalVideoURL = compressedResult.url
            finalFileSize = compressedResult.fileSize
        } else {
            // 直接复制文件
            try FileManager.default.copyItem(at: url, to: videoFileURL)
            finalVideoURL = videoFileURL
            finalFileSize = originalFileSize
        }
        
        // 获取视频时长
        let asset = AVAsset(url: finalVideoURL)
        let duration = try await asset.load(.duration).seconds
        
        // 生成缩略图
        let thumbnailURL = try await generateVideoThumbnail(from: finalVideoURL, messageId: messageId)
        
        return (
            videoURL: finalVideoURL.path,
            thumbnailURL: thumbnailURL,
            duration: duration,
            fileSize: finalFileSize
        )
    }
    
    /// 压缩视频
    /// ✅ Swift 6并发安全修复：重构异步导出逻辑，避免@Sendable闭包捕获非Sendable类型
    private func compressVideo(inputURL: URL, outputURL: URL) async throws -> (url: URL, fileSize: Int64) {
        guard let exportSession = AVAssetExportSession(
            asset: AVAsset(url: inputURL),
            presetName: FileConfig.videoCompressionPreset
        ) else {
            throw FileStorageError.videoCompressionFailed
        }

        exportSession.outputURL = outputURL
        exportSession.outputFileType = .mp4
        exportSession.shouldOptimizeForNetworkUse = true

        // ✅ 关键修复：使用Task和轮询机制替代@Sendable闭包，确保并发安全
        return try await withCheckedThrowingContinuation { continuation in
            // 在主线程启动导出任务
            exportSession.exportAsynchronously {
                // 导出完成后的处理逻辑
            }

            // 使用Task轮询导出状态，避免@Sendable闭包问题
            Task {
                while exportSession.status == .waiting || exportSession.status == .exporting {
                    try await Task.sleep(nanoseconds: 100_000_000) // 0.1秒轮询间隔
                }

                // 检查最终状态
                switch exportSession.status {
                case .completed:
                    do {
                        let attributes = try FileManager.default.attributesOfItem(atPath: outputURL.path)
                        let fileSize = attributes[.size] as? Int64 ?? 0
                        continuation.resume(returning: (url: outputURL, fileSize: fileSize))
                    } catch {
                        continuation.resume(throwing: error)
                    }
                case .failed:
                    continuation.resume(throwing: exportSession.error ?? FileStorageError.videoCompressionFailed)
                case .cancelled:
                    continuation.resume(throwing: FileStorageError.operationCancelled)
                default:
                    continuation.resume(throwing: FileStorageError.unknownError)
                }
            }
        }
    }
    
    /// 生成视频缩略图
    private func generateVideoThumbnail(from videoURL: URL, messageId: UUID) async throws -> String {
        let asset = AVAsset(url: videoURL)
        let imageGenerator = AVAssetImageGenerator(asset: asset)
        imageGenerator.appliesPreferredTrackTransform = true
        
        let time = CMTime(seconds: 1.0, preferredTimescale: 600)
        
        return try await withCheckedThrowingContinuation { continuation in
            Task {
                do {
                    let cgImage = try imageGenerator.copyCGImage(at: time, actualTime: nil)
                    let thumbnailImage = UIImage(cgImage: cgImage)
                    let fileName = "\(messageId.uuidString)_thumbnail.jpg"
                    let thumbnailURL = self.thumbnailDirectory.appendingPathComponent(fileName)
                    
                    guard let imageData = thumbnailImage.jpegData(compressionQuality: 0.7) else {
                        continuation.resume(throwing: FileStorageError.thumbnailGenerationFailed)
                        return
                    }
                    
                    try imageData.write(to: thumbnailURL)
                    continuation.resume(returning: thumbnailURL.path)
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    // MARK: - 音频处理
    
    /// 保存音频文件
    func saveAudio(from url: URL, messageId: UUID) async throws -> (audioURL: String, duration: TimeInterval, fileSize: Int64) {
        let fileName = "\(messageId.uuidString)_\(Date().timeIntervalSince1970).m4a"
        let audioFileURL = audioDirectory.appendingPathComponent(fileName)
        
        // 复制音频文件
        try FileManager.default.copyItem(at: url, to: audioFileURL)
        
        // 获取文件大小
        let attributes = try FileManager.default.attributesOfItem(atPath: audioFileURL.path)
        let fileSize = attributes[.size] as? Int64 ?? 0
        
        // 获取音频时长
        let asset = AVAsset(url: audioFileURL)
        let duration = try await asset.load(.duration).seconds
        
        return (audioURL: audioFileURL.path, duration: duration, fileSize: fileSize)
    }
    
    // MARK: - 文件管理
    
    /// 删除文件
    func deleteFile(at path: String) {
        let url = URL(fileURLWithPath: path)
        try? FileManager.default.removeItem(at: url)
    }
    
    /// 获取文件大小
    func getFileSize(at path: String) -> Int64? {
        let url = URL(fileURLWithPath: path)
        let attributes = try? FileManager.default.attributesOfItem(atPath: url.path)
        return attributes?[.size] as? Int64
    }
    
    /// 清理过期文件（7天前的文件）
    func cleanupOldFiles() async {
        let directories = [audioDirectory, imageDirectory, videoDirectory, thumbnailDirectory]
        let cutoffDate = Date().addingTimeInterval(-7 * 24 * 60 * 60) // 7天前
        
        for directory in directories {
            await cleanupDirectory(directory, cutoffDate: cutoffDate)
        }
    }
    
    /// 清理指定目录的过期文件
    private func cleanupDirectory(_ directory: URL, cutoffDate: Date) async {
        do {
            let fileURLs = try FileManager.default.contentsOfDirectory(
                at: directory,
                includingPropertiesForKeys: [URLResourceKey.creationDateKey],
                options: .skipsHiddenFiles
            )
            
            for fileURL in fileURLs {
                let attributes = try FileManager.default.attributesOfItem(atPath: fileURL.path)
                if let creationDate = attributes[FileAttributeKey.creationDate] as? Date,
                   creationDate < cutoffDate {
                    try FileManager.default.removeItem(at: fileURL)
                }
            }
        } catch {
            #if DEBUG
            // 调试环境下记录清理失败，但不使用print
            #endif
        }
    }
}

// MARK: - 错误定义

enum FileStorageError: LocalizedError {
    case imageCompressionFailed
    case videoCompressionFailed
    case thumbnailGenerationFailed
    case operationCancelled
    case fileTooLarge
    case unsupportedFormat
    case unknownError
    
    var errorDescription: String? {
        switch self {
        case .imageCompressionFailed:
            return "图片压缩失败"
        case .videoCompressionFailed:
            return "视频压缩失败"
        case .thumbnailGenerationFailed:
            return "缩略图生成失败"
        case .operationCancelled:
            return "操作已取消"
        case .fileTooLarge:
            return "文件过大"
        case .unsupportedFormat:
            return "不支持的文件格式"
        case .unknownError:
            return "未知错误"
        }
    }
} 