import Foundation
import AVFoundation
import UIKit

/// 多媒体服务 - 录音、拍照、录像功能统一管理
/// 遵循项目规范，使用依赖注入而非单例模式
@MainActor
class EAMediaService: NSObject, ObservableObject {
    
    // MARK: - 依赖注入
    
    private let permissionManager: EAPermissionManager
    private let fileStorageService: EAFileStorageService
    
    // MARK: - 录音相关
    
    @Published var isRecording = false
    @Published var recordingTime: TimeInterval = 0
    @Published var audioLevels: [Float] = []
    
    private var audioRecorder: AVAudioRecorder?
    private var recordingTimer: Timer?
    private var levelTimer: Timer?
    private var recordingStartTime: Date?
    
    // MARK: - 后台队列
    private let timerQueue = DispatchQueue(label: "com.evolve.media.timer", qos: .utility)
    
    // MARK: - 初始化
    
    init(permissionManager: EAPermissionManager, fileStorageService: EAFileStorageService) {
        self.permissionManager = permissionManager
        self.fileStorageService = fileStorageService
        super.init()
        
        setupAudioSession()
    }

    
    /// 配置音频会话
    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playAndRecord, mode: .default, options: [.allowBluetooth])
            try audioSession.setActive(true)
        } catch {
            // 音频会话配置失败，但继续执行
        }
    }
    
    // MARK: - 录音功能
    
    /// 开始录音
    func startRecording() async -> Bool {
        // 检查权限
        guard await permissionManager.checkMicrophonePermission() else {
            return false
        }
        
        // 停止之前的录音
        let _ = stopRecording()
        
        // 创建录音文件URL
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let audioFilename = documentsPath.appendingPathComponent("temp_recording_\(Date().timeIntervalSince1970).m4a")
        
        // 录音设置
        let settings: [String: Any] = [
            AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
            AVSampleRateKey: 44100,
            AVNumberOfChannelsKey: 2,
            AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue
        ]
        
        do {
            audioRecorder = try AVAudioRecorder(url: audioFilename, settings: settings)
            audioRecorder?.delegate = self
            audioRecorder?.isMeteringEnabled = true
            audioRecorder?.prepareToRecord()
            
            let success = audioRecorder?.record() ?? false
            if success {
                isRecording = true
                recordingTime = 0
                recordingStartTime = Date()
                audioLevels = []
                
                // 开始计时器 - 在后台队列执行
                startTimers()
                
                // 触觉反馈
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()
            }
            
            return success
        } catch {
            return false
        }
    }
    
    /// 停止录音并返回文件URL
    func stopRecording() -> URL? {
        guard isRecording, let recorder = audioRecorder else {
            return nil
        }
        
        stopTimers()
        recorder.stop()
        isRecording = false
        
        let recordingURL = recorder.url
        audioRecorder = nil
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
        
        return recordingURL
    }
    
    /// 取消录音
    func cancelRecording() {
        guard isRecording, let recorder = audioRecorder else {
            return
        }
        
        stopTimers()
        recorder.stop()
        isRecording = false
        
        // 删除临时文件
        try? FileManager.default.removeItem(at: recorder.url)
        audioRecorder = nil
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
        impactFeedback.impactOccurred()
    }
    
    /// 开始计时器 - ✅ CPU优化：大幅减少Timer频率
    private func startTimers() {
        // ✅ 关键修复：将Timer间隔从0.1秒优化为1.0秒，减少90%的CPU唤醒
        // 录音时长计时器 - 从每秒10次减少到每秒1次
        recordingTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            
            // ✅ 修复：使用Task.detached避免Sendable closure错误
            Task.detached { [weak self] in
                guard let self = self else { return }
                let startTime = await MainActor.run { self.recordingStartTime }
                guard let startTime = startTime else { return }
                
                let currentTime = Date().timeIntervalSince(startTime)
                
                await MainActor.run {
                    self.recordingTime = currentTime
                    
                    // 检查最大录音时长
                    if self.recordingTime >= EAFileStorageService.FileConfig.maxAudioDuration {
                        let _ = self.stopRecording()
                    }
                }
            }
        }
        
        // ✅ 关键修复：音量监测Timer也优化为1.0秒间隔
        // 音量监测计时器 - 从每秒10次减少到每秒1次，仍能提供良好的视觉反馈
        levelTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            
            // ✅ 修复：使用Task.detached避免Sendable closure错误
            Task.detached { [weak self] in
                guard let self = self else { return }
                let recorder = await MainActor.run { self.audioRecorder }
                guard let recorder = recorder else { return }
                
                recorder.updateMeters()
                let averagePower = recorder.averagePower(forChannel: 0)
                let normalizedPower = self.normalizeSoundLevel(level: averagePower)
                
                await MainActor.run {
                    self.audioLevels.append(normalizedPower)
                    // ✅ 优化：减少样本数量，降低内存使用
                    if self.audioLevels.count > 20 {
                        self.audioLevels.removeFirst()
                    }
                }
            }
        }
    }
    
    /// 停止计时器
    private func stopTimers() {
        recordingTimer?.invalidate()
        levelTimer?.invalidate()
        recordingTimer = nil
        levelTimer = nil
        recordingStartTime = nil
    }
    
    /// 标准化音量等级
    /// ✅ 修复：标记为nonisolated，允许在并发上下文中调用
    private nonisolated func normalizeSoundLevel(level: Float) -> Float {
        let minDb: Float = -60
        let maxDb: Float = 0
        
        let clampedLevel = max(minDb, min(maxDb, level))
        return (clampedLevel - minDb) / (maxDb - minDb)
    }
    
    // MARK: - 图片处理
    
    /// 处理选中的图片
    func processImage(_ image: UIImage, messageId: UUID) async throws -> ProcessedMediaResult {
        let result = try await fileStorageService.saveImage(image, messageId: messageId)
        
        return ProcessedMediaResult(
            originalURL: URL(fileURLWithPath: result.url),
            thumbnailURL: URL(fileURLWithPath: result.url), // 图片本身就是缩略图
            fileSize: result.fileSize,
            mediaType: .image
        )
    }
    
    /// 处理多张图片
    func processImages(_ images: [UIImage], messageId: UUID) async throws -> [ProcessedMediaResult] {
        var results: [ProcessedMediaResult] = []
        
        for (_, image) in images.enumerated() {
            let imageMessageId = UUID() // 每张图片使用独立的消息ID
            let result = try await processImage(image, messageId: imageMessageId)
            results.append(result)
        }
        
        return results
    }
    
    // MARK: - 视频处理
    
    /// 处理选中的视频
    func processVideo(from url: URL, messageId: UUID) async throws -> ProcessedMediaResult {
        let result = try await fileStorageService.saveVideo(from: url, messageId: messageId)
        
        return ProcessedMediaResult(
            originalURL: URL(fileURLWithPath: result.videoURL),
            thumbnailURL: result.thumbnailURL != nil ? URL(fileURLWithPath: result.thumbnailURL!) : nil,
            fileSize: result.fileSize,
            mediaType: .video
        )
    }
    
    // MARK: - 音频处理
    
    /// 处理录制的音频
    func processAudio(from url: URL, messageId: UUID) async throws -> ProcessedMediaResult {
        let result = try await fileStorageService.saveAudio(from: url, messageId: messageId)
        
        // 删除临时文件
        try? FileManager.default.removeItem(at: url)
        
        return ProcessedMediaResult(
            originalURL: URL(fileURLWithPath: result.audioURL),
            thumbnailURL: nil, // 音频没有缩略图
            fileSize: result.fileSize,
            mediaType: .audio
        )
    }
    
    // MARK: - 音频播放
    
    private var audioPlayer: AVAudioPlayer?
    @Published var isPlaying = false
    @Published var currentPlayingURL: String?
    
    /// 播放音频
    func playAudio(from path: String) async {
        // 停止当前播放
        stopAudio()
        
        let url = URL(fileURLWithPath: path)
        
        do {
            audioPlayer = try AVAudioPlayer(contentsOf: url)
            audioPlayer?.delegate = self
            audioPlayer?.prepareToPlay()
            
            let success = audioPlayer?.play() ?? false
            if success {
                isPlaying = true
                currentPlayingURL = path
                
                // 触觉反馈
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()
            }
        } catch {
            #if DEBUG
            // 调试环境下记录音频播放失败，但不使用print
            #endif
        }
    }
    
    /// 停止音频播放
    func stopAudio() {
        audioPlayer?.stop()
        audioPlayer = nil
        isPlaying = false
        currentPlayingURL = nil
    }
}

// MARK: - AVAudioRecorderDelegate

extension EAMediaService: AVAudioRecorderDelegate {
    nonisolated func audioRecorderDidFinishRecording(_ recorder: AVAudioRecorder, successfully flag: Bool) {
        Task { @MainActor in
            if !flag {
                // 录音失败处理
                self.cancelRecording()
            }
        }
    }
    
    nonisolated func audioRecorderEncodeErrorDidOccur(_ recorder: AVAudioRecorder, error: Error?) {
        Task { @MainActor in
            // 录音编码错误处理
            self.cancelRecording()
        }
    }
}

// MARK: - AVAudioPlayerDelegate

extension EAMediaService: @preconcurrency AVAudioPlayerDelegate {
    func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        isPlaying = false
        currentPlayingURL = nil
        audioPlayer = nil
    }
    
    func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        isPlaying = false
        currentPlayingURL = nil
        audioPlayer = nil
    }
}

// MARK: - 数据模型

/// 处理后的媒体结果
struct ProcessedMediaResult {
    let originalURL: URL
    let thumbnailURL: URL?
    let fileSize: Int64
    let mediaType: MediaType
    
    enum MediaType {
        case image
        case video
        case audio
    }
} 