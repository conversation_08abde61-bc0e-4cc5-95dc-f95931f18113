import Foundation

/// AI服务类
/// 负责与AI大模型API的通信，处理对话请求和响应
@MainActor
class EAAIService: ObservableObject {
    
    // MARK: - 数据模型
    
    /// AI消息模型
    struct EAAIMessage: Codable {
        let role: String // "user" 或 "assistant"
        let content: String
        let timestamp: Date
        
        init(role: String, content: String, timestamp: Date = Date()) {
            self.role = role
            self.content = content
            self.timestamp = timestamp
        }
    }
    
    /// AI响应模型
    struct EAAIResponse: Codable {
        let message: String
        let suggestions: [String]?
        let confidence: Double
        let responseTime: TimeInterval
        
        init(message: String, suggestions: [String]? = nil, confidence: Double = 1.0, responseTime: TimeInterval = 0) {
            self.message = message
            self.suggestions = suggestions
            self.confidence = confidence
            self.responseTime = responseTime
        }
    }
    
    /// AI错误类型
    enum EAAIError: Error, LocalizedError {
        case networkError(String)
        case apiError(String)
        case invalidResponse
        case rateLimitExceeded
        case authenticationFailed
        case serviceUnavailable
        
        var errorDescription: String? {
            switch self {
            case .networkError(let message):
                return "网络错误: \(message)"
            case .apiError(let message):
                return "API错误: \(message)"
            case .invalidResponse:
                return "响应格式无效"
            case .rateLimitExceeded:
                return "请求频率过高，请稍后再试"
            case .authenticationFailed:
                return "认证失败"
            case .serviceUnavailable:
                return "AI服务暂时不可用"
            }
        }
    }
    
    // MARK: - 属性
    
    private let networkService: EANetworkService
    private let apiKey: String
    private let baseURL: String
    
    // 会话上下文管理
    private var conversationHistory: [EAAIMessage] = []
    private let maxHistoryLength = 20 // 最大保留对话数量
    
    // MARK: - 初始化

    init(networkService: EANetworkService = EANetworkService()) {
        self.networkService = networkService
        // TODO: 从安全配置中获取API密钥和基础URL
        self.apiKey = "your_api_key_here"
        self.baseURL = "https://api.example.com/v1"
    }
    
    // MARK: - 公共方法
    
    /// 发送消息给AI并获取回复
    /// - Parameters:
    ///   - message: 用户消息
    ///   - context: 额外上下文信息（如用户习惯数据）
    /// - Returns: AI响应
    func sendMessage(_ message: String, context: [String: Any]? = nil) async throws -> EAAIResponse {
        let startTime = Date()
        
        // 添加用户消息到历史记录
        let userMessage = EAAIMessage(role: "user", content: message)
        addToHistory(userMessage)
        
        // 构建请求
        let request = buildChatRequest(message: message, context: context)
        
        do {
            // 发送请求（目前使用模拟响应）
            let response = try await sendChatRequest(request)
            
            // 添加AI回复到历史记录
            let aiMessage = EAAIMessage(role: "assistant", content: response.message)
            addToHistory(aiMessage)
            
            // 计算响应时间
            let responseTime = Date().timeIntervalSince(startTime)
            
            return EAAIResponse(
                message: response.message,
                suggestions: response.suggestions,
                confidence: response.confidence,
                responseTime: responseTime
            )
            
        } catch {
            throw EAAIError.apiError(error.localizedDescription)
        }
    }
    
    /// 生成习惯建议
    /// - Parameter goal: 用户目标描述
    /// - Returns: 习惯建议列表
    func generateHabitSuggestions(for goal: String) async throws -> [String] {
        let prompt = """
        用户想要实现以下目标：\(goal)
        
        请为用户推荐3-5个具体的、可执行的习惯建议。每个建议应该：
        1. 具体明确，容易执行
        2. 与目标直接相关
        3. 适合初学者开始
        
        请只返回习惯建议列表，每行一个建议。
        """
        
        let response = try await sendMessage(prompt)
        
        // 解析建议列表
        let suggestions = response.message
            .components(separatedBy: .newlines)
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }
        
        return suggestions
    }
    
    /// 生成习惯完成反馈
    /// - Parameters:
    ///   - habitName: 习惯名称
    ///   - isCompleted: 是否完成
    ///   - streak: 连续天数
    /// - Returns: 个性化反馈消息
    func generateCompletionFeedback(habitName: String, isCompleted: Bool, streak: Int) async throws -> String {
        let prompt = """
        用户的习惯「\(habitName)」今天\(isCompleted ? "已完成" : "未完成")，
        当前连续完成天数：\(streak)天。
        
        请生成一条简短的、鼓励性的反馈消息（不超过50字）。
        """
        
        let response = try await sendMessage(prompt)
        return response.message
    }
    
    /// 清除对话历史
    func clearConversationHistory() {
        conversationHistory.removeAll()
    }
    
    /// 获取对话历史
    func getConversationHistory() -> [EAAIMessage] {
        return conversationHistory
    }
    
    // MARK: - 私有方法
    
    /// 添加消息到历史记录
    private func addToHistory(_ message: EAAIMessage) {
        conversationHistory.append(message)
        
        // 保持历史记录在限制范围内
        if conversationHistory.count > maxHistoryLength {
            conversationHistory.removeFirst(conversationHistory.count - maxHistoryLength)
        }
    }
    
    /// 构建聊天请求
    private func buildChatRequest(message: String, context: [String: Any]?) -> [String: Any] {
        var messages: [[String: Any]] = []
        
        // 添加系统提示
        messages.append([
            "role": "system",
            "content": buildSystemPrompt(context: context)
        ])
        
        // 添加历史对话
        for historyMessage in conversationHistory.suffix(10) { // 只取最近10条
            messages.append([
                "role": historyMessage.role,
                "content": historyMessage.content
            ])
        }
        
        return [
            "model": "gpt-3.5-turbo", // 根据实际使用的模型调整
            "messages": messages,
            "max_tokens": 500,
            "temperature": 0.7
        ]
    }
    
    /// 构建系统提示
    private func buildSystemPrompt(context: [String: Any]?) -> String {
        var prompt = """
        你是Aura，一个专业的AI习惯养成教练。你的特点：
        
        1. 温暖、耐心、专业
        2. 善于倾听和理解用户需求
        3. 提供实用的、个性化的建议
        4. 用简洁、友好的语言交流
        5. 关注用户的情感状态和动机
        
        请用中文回复，保持回复简洁明了（通常不超过100字）。
        """
        
        // 添加上下文信息
        if let context = context {
            if let habitCount = context["habitCount"] as? Int {
                prompt += "\n\n用户当前有 \(habitCount) 个活跃习惯。"
            }
            
            if let completionRate = context["completionRate"] as? Double {
                prompt += "\n用户最近的完成率是 \(Int(completionRate * 100))%。"
            }
        }
        
        return prompt
    }
    
    /// 发送聊天请求
    private func sendChatRequest(_ request: [String: Any]) async throws -> EAAIResponse {
        // TODO: 实现真实的API调用
        // 目前返回模拟响应
        return try await generateMockResponse(for: request)
    }
    
    /// 生成模拟响应（用于开发阶段）
    private func generateMockResponse(for request: [String: Any]) async throws -> EAAIResponse {
        // 模拟网络延迟
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
        
        guard let messages = request["messages"] as? [[String: Any]],
              let lastMessage = messages.last,
              let content = lastMessage["content"] as? String else {
            throw EAAIError.invalidResponse
        }
        
        // 根据用户输入生成相应的模拟回复
        let response = generateContextualResponse(for: content)
        
        return EAAIResponse(
            message: response.message,
            suggestions: response.suggestions,
            confidence: 0.85
        )
    }
    
    /// 生成上下文相关的回复
    private func generateContextualResponse(for input: String) -> (message: String, suggestions: [String]?) {
        let lowercaseInput = input.lowercased()
        
        if lowercaseInput.contains("习惯") && lowercaseInput.contains("创建") {
            return (
                message: "很好！创建新习惯是一个积极的开始。告诉我你想培养什么样的习惯，我会帮你制定一个可行的计划。",
                suggestions: ["我想早起", "我想运动", "我想阅读", "我想冥想"]
            )
        } else if lowercaseInput.contains("早起") {
            return (
                message: "早起是一个很棒的习惯！建议从每天提前15分钟开始，逐步调整。你现在通常几点起床呢？",
                suggestions: ["6点起床", "7点起床", "需要建议", "了解更多技巧"]
            )
        } else if lowercaseInput.contains("运动") {
            return (
                message: "运动对身心健康都很有益！你比较喜欢什么类型的运动？我可以帮你制定一个循序渐进的计划。",
                suggestions: ["跑步", "瑜伽", "力量训练", "散步"]
            )
        } else if lowercaseInput.contains("进度") || lowercaseInput.contains("完成") {
            return (
                message: "你的习惯养成进展很不错！坚持下去，每一天的努力都在积累成长的力量。有什么困难需要我帮助解决吗？",
                suggestions: ["查看详细数据", "调整计划", "需要鼓励", "分享经验"]
            )
        } else if lowercaseInput.contains("困难") || lowercaseInput.contains("坚持") {
            return (
                message: "遇到困难是很正常的，这说明你正在挑战自己！让我们一起分析一下具体的困难，找到解决方案。",
                suggestions: ["时间不够", "缺乏动力", "容易忘记", "其他原因"]
            )
        } else {
            return (
                message: "我理解你的想法。作为你的AI教练，我会陪伴你一起成长。有什么具体的问题或目标想要讨论吗？",
                suggestions: ["创建新习惯", "查看进度", "需要建议", "随便聊聊"]
            )
        }
    }
}

// MARK: - 扩展：快速回复建议

extension EAAIService {
    /// 获取默认的快速回复选项
    static func getDefaultQuickReplies() -> [String] {
        return [
            "我想创建新习惯",
            "查看我的进度",
            "需要一些鼓励",
            "遇到了困难",
            "分享我的成功",
            "了解更多技巧"
        ]
    }
    
    /// 根据上下文生成动态快速回复
    func generateQuickReplies(for context: String) -> [String] {
        let lowercaseContext = context.lowercased()
        
        if lowercaseContext.contains("习惯") {
            return ["告诉我更多", "开始创建", "需要建议", "查看示例"]
        } else if lowercaseContext.contains("困难") {
            return ["具体说说", "需要帮助", "调整计划", "寻求鼓励"]
        } else if lowercaseContext.contains("成功") || lowercaseContext.contains("完成") {
            return ["太棒了！", "继续保持", "分享经验", "设定新目标"]
        } else {
            return Self.getDefaultQuickReplies()
        }
    }
}