import Foundation
import UIKit
import AVFoundation

/// 聊天媒体文件管理器
/// 负责聊天中图片/视频文件的统一存储和路径管理，解决黑屏问题
@MainActor
final class EAChatMediaManager: ObservableObject, @unchecked Sendable {
    
    // MARK: - 目录结构
    /// 聊天媒体文件根目录：Documents/ChatMedia/
    private static let mediaRootDirectory = "ChatMedia"
    
    /// 图片子目录
    private static let imagesSubDirectory = "Images"
    
    /// 视频子目录
    private static let videosSubDirectory = "Videos"
    
    /// 缩略图子目录
    private static let thumbnailsSubDirectory = "Thumbnails"
    
    // MARK: - 初始化
    init() {
        createDirectoriesIfNeeded()
    }
    
    // MARK: - 公共方法
    
    /// 保存图片文件
    /// - Parameters:
    ///   - imageData: 图片数据
    ///   - friendshipId: 好友关系ID，用于创建子目录
    /// - Returns: 相对路径字符串，失败返回nil
    func saveImage(data imageData: Data, for friendshipId: UUID) -> String? {
        let fileName = generateFileName(extension: "jpg")
        let relativePath = "\(Self.mediaRootDirectory)/\(friendshipId.uuidString)/\(Self.imagesSubDirectory)/\(fileName)"
        
        guard let fullURL = getDocumentsURL()?.appendingPathComponent(relativePath) else {
            return nil
        }
        
        // 创建好友专属目录
        let friendDirectory = fullURL.deletingLastPathComponent()
        try? FileManager.default.createDirectory(at: friendDirectory, withIntermediateDirectories: true)
        
        do {
            try imageData.write(to: fullURL)
            
            // ✅ 调试日志：验证保存路径
            #if DEBUG
            print("✅ [MediaManager-Save] 文件已保存至绝对路径: \(fullURL.path)")
            print("✅ [MediaManager-Save] 生成的相对路径为: \(relativePath)")
            print("✅ [MediaManager-Save] FriendshipID: \(friendshipId.uuidString)")
            #endif
            
            return relativePath
        } catch {
            #if DEBUG
            print("❌ EAChatMediaManager: 保存图片失败 - \(error.localizedDescription)")
            #endif
            return nil
        }
    }
    
    /// 保存视频文件
    /// - Parameters:
    ///   - videoURL: 临时视频文件URL
    ///   - friendshipId: 好友关系ID
    /// - Returns: 相对路径字符串，失败返回nil
    func saveVideo(from videoURL: URL, for friendshipId: UUID) -> String? {
        let fileName = generateFileName(extension: "mp4")
        let relativePath = "\(Self.mediaRootDirectory)/\(friendshipId.uuidString)/\(Self.videosSubDirectory)/\(fileName)"
        
        guard let fullURL = getDocumentsURL()?.appendingPathComponent(relativePath) else {
            return nil
        }
        
        // 创建好友专属目录
        let friendDirectory = fullURL.deletingLastPathComponent()
        try? FileManager.default.createDirectory(at: friendDirectory, withIntermediateDirectories: true)
        
        do {
            try FileManager.default.copyItem(at: videoURL, to: fullURL)
            return relativePath
        } catch {
            return nil
        }
    }
    
    /// 生成视频缩略图并保存
    /// - Parameters:
    ///   - videoPath: 视频文件相对路径
    ///   - friendshipId: 好友关系ID
    /// - Returns: 缩略图相对路径，失败返回nil
    func generateAndSaveThumbnail(for videoPath: String, friendshipId: UUID) -> String? {
        guard let videoURL = getMediaURL(for: videoPath) else { return nil }
        
        let asset = AVURLAsset(url: videoURL)
        let imageGenerator = AVAssetImageGenerator(asset: asset)
        imageGenerator.appliesPreferredTrackTransform = true
        
        do {
            let cgImage = try imageGenerator.copyCGImage(at: .zero, actualTime: nil)
            let thumbnail = UIImage(cgImage: cgImage)
            
            guard let thumbnailData = thumbnail.jpegData(compressionQuality: 0.7) else { return nil }
            
            let fileName = generateFileName(extension: "jpg")
            let relativePath = "\(Self.mediaRootDirectory)/\(friendshipId.uuidString)/\(Self.thumbnailsSubDirectory)/\(fileName)"
            
            guard let fullURL = getDocumentsURL()?.appendingPathComponent(relativePath) else {
                return nil
            }
            
            // 创建缩略图目录
            let thumbnailDirectory = fullURL.deletingLastPathComponent()
            try? FileManager.default.createDirectory(at: thumbnailDirectory, withIntermediateDirectories: true)
            
            try thumbnailData.write(to: fullURL)
            return relativePath
            
        } catch {
            #if DEBUG
            print("❌ EAChatMediaManager: 生成缩略图失败 - \(error.localizedDescription)")
            #endif
            return nil
        }
    }
    
    /// 根据相对路径获取完整的本地文件URL
    /// - Parameter relativePath: 相对路径字符串
    /// - Returns: 完整的本地文件URL，如果文件不存在返回nil
    func getMediaURL(for relativePath: String?) -> URL? {
        guard let relativePath = relativePath,
              !relativePath.isEmpty,
              let documentsURL = getDocumentsURL() else {
            #if DEBUG
            print("🔍 [MediaManager] getMediaURL失败 - 无效参数: relativePath=\(relativePath ?? "nil")")
            #endif
            return nil
        }

        // ✅ 调试日志：验证路径拼接过程
        #if DEBUG
        print("🔍 [MediaManager] 开始处理路径: \(relativePath)")
        print("🔍 [MediaManager] Documents目录: \(documentsURL.path)")
        #endif

        let fullURL = documentsURL.appendingPathComponent(relativePath)

        #if DEBUG
        let fileExists = FileManager.default.fileExists(atPath: fullURL.path)
        print("🔍 [MediaManager] 完整路径: \(fullURL.path)")
        print("🔍 [MediaManager] 文件是否存在: \(fileExists)")
        #endif

        // 验证文件是否存在
        guard FileManager.default.fileExists(atPath: fullURL.path) else {
            #if DEBUG
            print("❌ [MediaManager] 文件不存在: \(fullURL.path)")
            // 尝试列出目录内容以帮助调试
            let parentDir = fullURL.deletingLastPathComponent()
            if FileManager.default.fileExists(atPath: parentDir.path) {
                do {
                    let contents = try FileManager.default.contentsOfDirectory(atPath: parentDir.path)
                    print("🔍 [MediaManager] 父目录内容: \(contents)")
                } catch {
                    print("❌ [MediaManager] 无法读取父目录: \(error)")
                }
            } else {
                print("❌ [MediaManager] 父目录也不存在: \(parentDir.path)")
            }
            #endif
            return nil
        }

        #if DEBUG
        print("✅ [MediaManager] 文件验证成功: \(fullURL.path)")
        #endif
        return fullURL
    }
    
    /// 清理指定好友的所有媒体文件
    /// - Parameter friendshipId: 好友关系ID
    func cleanupMediaFiles(for friendshipId: UUID) {
        guard let documentsURL = getDocumentsURL() else { return }
        
        let friendDirectory = documentsURL
            .appendingPathComponent(Self.mediaRootDirectory)
            .appendingPathComponent(friendshipId.uuidString)
        
        do {
            try FileManager.default.removeItem(at: friendDirectory)
            #if DEBUG
            print("✅ EAChatMediaManager: 已清理好友 \(friendshipId.uuidString) 的媒体文件")
            #endif
        } catch {
            #if DEBUG
            print("❌ EAChatMediaManager: 清理媒体文件失败 - \(error.localizedDescription)")
            #endif
        }
    }
    
    /// 获取媒体文件大小
    /// - Parameter relativePath: 相对路径
    /// - Returns: 文件大小（字节），失败返回nil
    func getFileSize(for relativePath: String?) -> Int64? {
        guard let url = getMediaURL(for: relativePath) else { return nil }
        
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: url.path)
            return attributes[.size] as? Int64
        } catch {
            #if DEBUG
            print("❌ EAChatMediaManager: 获取文件大小失败 - \(error.localizedDescription)")
            #endif
            return nil
        }
    }
    
    // MARK: - 私有方法
    
    /// 获取Documents目录URL
    private func getDocumentsURL() -> URL? {
        return FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first
    }
    
    /// 创建必要的目录结构
    private func createDirectoriesIfNeeded() {
        guard let documentsURL = getDocumentsURL() else { return }
        
        let mediaRootURL = documentsURL.appendingPathComponent(Self.mediaRootDirectory)
        
        do {
            try FileManager.default.createDirectory(at: mediaRootURL, withIntermediateDirectories: true)
        } catch {
            // 目录创建失败，静默处理
        }
    }
    
    /// 生成唯一文件名
    /// - Parameter extension: 文件扩展名
    /// - Returns: 包含时间戳和UUID的唯一文件名
    private func generateFileName(extension: String) -> String {
        let timestamp = Int64(Date().timeIntervalSince1970 * 1000) // 毫秒时间戳
        let uuid = UUID().uuidString.prefix(8) // UUID前8位
        return "chat_\(timestamp)_\(uuid).\(`extension`)"
    }
    
    // 🗑️ 已删除兼容性方法 - 现在从源头解决路径问题
}

// MARK: - 扩展：便捷方法
extension EAChatMediaManager {
    
    /// 检查文件是否为图片类型
    static func isImageFile(_ path: String?) -> Bool {
        guard let path = path else { return false }
        let imageExtensions = ["jpg", "jpeg", "png", "gif", "heic", "webp"]
        return imageExtensions.contains(where: { path.lowercased().hasSuffix($0) })
    }
    
    /// 检查文件是否为视频类型
    static func isVideoFile(_ path: String?) -> Bool {
        guard let path = path else { return false }
        let videoExtensions = ["mp4", "mov", "avi", "m4v", "3gp"]
        return videoExtensions.contains(where: { path.lowercased().hasSuffix($0) })
    }
} 