//
//  EASessionManagerProtocol.swift
//  Evolve
//
//  Created by AI Assistant on 2025-06-05.
//

import Foundation
import SwiftData

/// 会话管理协议
/// 定义会话管理的核心接口，支持依赖注入和单元测试
/// 遵循开发规范文档的"依赖注入优于单例"原则
@MainActor
protocol EASessionManagerProtocol: ObservableObject {
    
    // MARK: - 状态属性

    /// 是否已登录
    var isLoggedIn: Bool { get }

    /// 安全的当前用户（异步接口）
    var safeCurrentUser: EAUser? { get async }

    /// Pro用户状态（保持兼容性）
    var isPro: Bool { get }

    /// 访问令牌
    var accessToken: String? { get }
    
    /// Repository容器
    var repositoryContainer: EARepositoryContainer? { get }
    
    // MARK: - 核心方法
    
    /// 设置Repository容器
    /// - Parameter container: Repository容器
    func setRepositoryContainer(_ container: EARepositoryContainer)
    
    /// 设置ModelContext（用于Apple标准配置）
    /// - Parameter context: SwiftData模型上下文
    func setModelContext(_ context: ModelContext)
    
    /// 保存用户会话
    /// - Parameters:
    ///   - authData: 认证数据
    ///   - user: 用户对象
    func saveSession(authData: EAAuthData, user: EAUser) async throws
    
    /// 恢复用户会话
    func restoreSession()
    
    /// 清除用户会话
    func clearSession()
    
    /// 登录用户
    /// - Parameters:
    ///   - username: 用户名
    ///   - email: 邮箱（可选）
    func login(username: String, email: String?) async throws
    
    /// 登出用户
    func logout()
    
    /// 获取当前用户
    /// - Returns: 当前用户对象
    func getCurrentUser() async throws -> EAUser?
    
    /// 更新用户信息
    /// - Parameter user: 用户对象
    func updateUser(_ user: EAUser) async throws
    
    // MARK: - 便捷属性
}

// MARK: - 默认实现扩展

extension EASessionManagerProtocol {

    /// 默认的Pro用户检查实现（异步版本）
    func checkProStatus() async -> Bool {
        guard let user = await safeCurrentUser else { return false }

        if !user.isPro { return false }

        // 检查Pro订阅是否过期
        if let expirationDate = user.proExpirationDate {
            return expirationDate > Date()
        }

        return true
    }
}

// MARK: - 测试用Mock实现

#if DEBUG
/// 测试用的Mock会话管理器
/// 用于单元测试和预览
@MainActor
class EAMockSessionManager: EASessionManagerProtocol {

    @Published var isLoggedIn: Bool = false
    @Published var accessToken: String? = nil

    var repositoryContainer: EARepositoryContainer? = nil

    // 🔑 重构：使用私有缓存替代公开属性
    private var userCache: EAUser? = nil

    /// Pro用户状态（Mock实现）
    var isPro: Bool {
        guard let user = userCache else { return false }

        if !user.isPro { return false }

        // 检查Pro订阅是否过期
        if let expirationDate = user.proExpirationDate {
            return expirationDate > Date()
        }

        return true
    }
    
    func setRepositoryContainer(_ container: EARepositoryContainer) {
        self.repositoryContainer = container
    }
    
    func setModelContext(_ context: ModelContext) {
        // Mock实现，不需要实际操作
    }
    
    func saveSession(authData: EAAuthData, user: EAUser) async throws {
        self.isLoggedIn = true
        self.userCache = user
        self.accessToken = authData.token.accessToken
    }

    func restoreSession() {
        // Mock实现，可以模拟恢复会话
    }

    func clearSession() {
        self.isLoggedIn = false
        self.userCache = nil
        self.accessToken = nil
    }
    
    func login(username: String, email: String? = nil) async throws {
        // 创建Mock用户
        let mockUser = EAUser(username: username, email: email)
        let mockAuthData = EAAuthData(
            token: EAToken(accessToken: "mock_token", refreshToken: "mock_refresh"),
            user: EAAuthUser(id: mockUser.id.uuidString, username: username, email: email)
        )
        
        try await saveSession(authData: mockAuthData, user: mockUser)
    }
    
    func logout() {
        clearSession()
    }
    
    func getCurrentUser() async throws -> EAUser? {
        return userCache
    }

    func updateUser(_ user: EAUser) async throws {
        self.userCache = user
    }

    /// 安全的当前用户（异步接口实现）
    var safeCurrentUser: EAUser? {
        get async {
            return userCache
        }
    }

    /// Pro状态检查（Mock实现）
    func checkProStatus() async -> Bool {
        guard let user = userCache else { return false }

        if !user.isPro { return false }

        // 检查Pro订阅是否过期
        if let expirationDate = user.proExpirationDate {
            return expirationDate > Date()
        }

        return true
    }
}
#endif 