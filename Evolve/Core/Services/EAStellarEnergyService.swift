import Foundation
import SwiftUI
import os.log

// MARK: - 星际能量计算引擎（Phase 3 Day 6新增）

/// 星际能量服务：负责能量计算、等级升级、成就徽章管理
@MainActor
class EAStellarEnergyService: ObservableObject {
    
    // MARK: - 日志记录
    
    private let logger = Logger(subsystem: "com.evolve.stellarenergy", category: "EAStellarEnergyService")
    
    // MARK: - 发布属性
    
    @Published var isCalculating = false
    @Published var energyStatistics = EAEnergyStatistics()
    @Published var recentLevelUpEvent: EALevelUpEvent?
    @Published var recentBadgeEarned: EABadgeEarned?
    
    // MARK: - 依赖注入
    
    private let repositoryContainer: EARepositoryContainer
    private let cacheManager: EAAICacheManager
    
    /// 获取repositoryContainer的访问器（供外部组件使用）
    var repositoryContainerReference: EARepositoryContainer {
        return repositoryContainer
    }
    
    // MARK: - 配置
    
    private let energyConfig = EAStellarEnergyConfig()
    
    // MARK: - 防刷机制配置
    
    /// 防刷机制配置
    private struct AntiSpamConfig {
        // 时间间隔限制（秒）
        static let minIntervalBetweenLikes: TimeInterval = 2.0      // 点赞间隔2秒
        static let minIntervalBetweenComments: TimeInterval = 10.0   // 评论间隔10秒
        static let minIntervalBetweenPosts: TimeInterval = 60.0     // 发帖间隔1分钟
        
        // 每日上限
        static let maxDailyLikeEnergy: Int = 100        // 每日点赞能量上限
        static let maxDailyCommentEnergy: Int = 150     // 每日评论能量上限
        static let maxDailyPostEnergy: Int = 200        // 每日发帖能量上限
        static let maxDailyTotalEnergy: Int = 1000      // 每日总能量上限
        
        // 异常行为检测
        static let maxActionsPerMinute: Int = 10       // 每分钟最大操作数
        static let suspiciousActionThreshold: Int = 50  // 可疑行为阈值
    }
    
    /// 用户操作记录（内存缓存）
    private var userActionHistory: [UUID: [UserActionRecord]] = [:]
    
    /// 用户每日能量统计（内存缓存）
    private var dailyEnergyStats: [UUID: DailyEnergyStats] = [:]
    
    /// 用户操作记录结构
    private struct UserActionRecord {
        let actionType: StellarEnergyActionType
        let timestamp: Date
        let energyGained: Int
    }
    
    /// 每日能量统计结构
    private struct DailyEnergyStats {
        let date: Date
        var likeEnergy: Int = 0
        var commentEnergy: Int = 0
        var postEnergy: Int = 0
        var totalEnergy: Int = 0
        var actionCount: Int = 0
        
        mutating func addEnergy(_ amount: Int, for actionType: StellarEnergyActionType) {
            switch actionType {
            case .giveLike, .receivePostLike:
                likeEnergy += amount
            case .giveComment, .receiveComment:
                commentEnergy += amount
            case .createPost, .createHabitPost:
                postEnergy += amount
            default:
                break
            }
            totalEnergy += amount
            actionCount += 1
        }
    }
    
    // MARK: - 初始化
    
    init(repositoryContainer: EARepositoryContainer, cacheManager: EAAICacheManager) {
        self.repositoryContainer = repositoryContainer
        self.cacheManager = cacheManager
    }
    
    /// ✅ 新增：便利初始化方法（自动创建缓存管理器）
    convenience init(repositoryContainer: EARepositoryContainer) {
        let cacheManager = EAAICacheManager()
        self.init(repositoryContainer: repositoryContainer, cacheManager: cacheManager)
    }
    
    // MARK: - 星际能量计算引擎
    
    /// ✅ 新方法：计算习惯完成的星际能量奖励（使用完整对象）
    /// - Parameters:
    ///   - habit: 习惯对象
    ///   - user: 用户对象
    ///   - isConsecutive: 是否为连续完成
    /// - Returns: 获得的星际能量值
    func calculateHabitCompletionEnergy(for habit: EAHabit, user: EAUser, isConsecutive: Bool = false) async -> Int {
        guard !isCalculating else { return 0 }
        
        isCalculating = true
        defer { isCalculating = false }
        
        // 基础能量奖励
        var baseEnergy = energyConfig.baseHabitCompletionEnergy

        // 根据习惯难度调整
        let difficultyMultiplier = calculateDifficultyMultiplier(for: habit)
        baseEnergy = Int(Double(baseEnergy) * difficultyMultiplier)

        // 连续完成奖励
        if isConsecutive {
            let streak = await calculateCurrentStreak(for: habit, user: user)
            let streakBonus = calculateStreakBonus(streak: streak)
            baseEnergy += streakBonus

            // 记录连击成就
            await checkStreakBadges(for: user, habit: habit, streak: streak)
        }

        // 用户等级影响
        let userLevel = user.socialProfile?.stellarLevel ?? 1
        let levelBonus = calculateLevelBonus(userLevel: userLevel)
        baseEnergy += levelBonus

        // 更新用户星际能量
        await updateUserStellarEnergy(for: user, energyGained: baseEnergy)

        // 检查等级升级
        await checkLevelUpgrade(for: user)

        // 更新统计
        energyStatistics.totalEnergyEarned += baseEnergy
        energyStatistics.habitsCompleted += 1

        return baseEnergy
    }
    
    /// @deprecated 使用接收完整对象的方法替代
    /// 计算习惯完成的星际能量奖励
    /// - Parameters:
    ///   - habitId: 习惯ID
    ///   - userId: 用户ID
    ///   - isConsecutive: 是否为连续完成
    /// - Returns: 获得的星际能量值
    @available(*, deprecated, message: "Use calculateHabitCompletionEnergy(for:user:isConsecutive:) instead")
    func calculateHabitCompletionEnergy(habitId: UUID, userId: UUID, isConsecutive: Bool = false) async -> Int {
        guard !isCalculating else { return 0 }
        
        isCalculating = true
        defer { isCalculating = false }
        
        // 获取习惯信息
        guard let user = await repositoryContainer.userRepository.fetchUser(by: userId) else {
            // ✅ 新增：用户不存在的错误处理
            logger.error("⚠️ 星际能量计算：用户不存在 - \(userId)")
            return 0
        }

        let userHabits = await repositoryContainer.habitRepository.fetchUserHabits(userId: userId)
        guard let targetHabit = userHabits.first(where: { $0.id == habitId }) else {
            // ✅ 新增：习惯不存在的错误处理
            logger.error("⚠️ 星际能量计算：习惯不存在 - \(habitId)")
            return energyConfig.baseHabitCompletionEnergy // 降级策略：返回基础能量
        }

        // 基础能量奖励
        var baseEnergy = energyConfig.baseHabitCompletionEnergy

        // 根据习惯难度调整
        let difficultyMultiplier = calculateDifficultyMultiplier(for: targetHabit)
        baseEnergy = Int(Double(baseEnergy) * difficultyMultiplier)

        // 连续完成奖励
        if isConsecutive {
            let streak = await calculateCurrentStreak(habitId: habitId, userId: userId)
            let streakBonus = calculateStreakBonus(streak: streak)
            baseEnergy += streakBonus

            // 记录连击成就
            await checkStreakBadges(userId: userId, habitId: habitId, streak: streak)
        }

        // 用户等级影响
        let userLevel = user.socialProfile?.stellarLevel ?? 1
        let levelBonus = calculateLevelBonus(userLevel: userLevel)
        baseEnergy += levelBonus

        // 更新用户星际能量
        await updateUserStellarEnergy(userId: userId, energyGained: baseEnergy)

        // 检查等级升级
        await checkLevelUpgrade(userId: userId)

        // 更新统计
        energyStatistics.totalEnergyEarned += baseEnergy
        energyStatistics.habitsCompleted += 1

        return baseEnergy
    }
    
    /// 计算社区广播的星际能量奖励
    /// - Parameters:
    ///   - postId: 帖子ID
    ///   - userId: 用户ID
    ///   - shareType: 广播类型
    /// - Returns: 获得的星际能量值
    func calculateSharingEnergy(postId: UUID, userId: UUID, shareType: EASharingType) async -> Int {
        // ✅ 修复：先获取用户对象，避免deprecated方法调用
        guard let user = await repositoryContainer.userRepository.fetchUser(by: userId) else {
            return 0
        }
        
        let baseEnergy: Int
        
        switch shareType {
        case .habitMilestone:
            baseEnergy = energyConfig.milestoneShareEnergy
        case .reflection:
            baseEnergy = energyConfig.reflectionShareEnergy
        case .achievement:
            baseEnergy = energyConfig.achievementShareEnergy
        case .motivation:
            baseEnergy = energyConfig.motivationShareEnergy
        case .habitCompletion:
            baseEnergy = energyConfig.habitCompletionShareEnergy
        }
        
        // ✅ 修复：使用新版本方法，避免deprecated警告
        await updateUserStellarEnergy(for: user, energyGained: baseEnergy)
        
        // 检查广播成就
        await checkSharingBadges(userId: user.id, shareType: shareType)
        
        energyStatistics.totalEnergyEarned += baseEnergy
        energyStatistics.postsShared += 1
        
        return baseEnergy
    }
    
    /// 计算社区互动的星际能量奖励
    /// - Parameters:
    ///   - interactionType: 互动类型
    ///   - userId: 用户ID
    /// - Returns: 获得的星际能量值
    func calculateInteractionEnergy(interactionType: EAInteractionType, userId: UUID) async -> Int {
        // ✅ 修复：先获取用户对象，避免deprecated方法调用
        guard let user = await repositoryContainer.userRepository.fetchUser(by: userId) else {
            return 0
        }
        
        let baseEnergy: Int
        
        switch interactionType {
        case .like:
            baseEnergy = energyConfig.likeGivenEnergy
        case .comment:
            baseEnergy = energyConfig.commentGivenEnergy
        case .share:
            baseEnergy = energyConfig.shareEnergy
        case .follow:
            baseEnergy = energyConfig.followGivenEnergy
        }
        
        // ✅ 修复：使用新版本方法，避免deprecated警告
        await updateUserStellarEnergy(for: user, energyGained: baseEnergy)
        
        // 检查互动成就
        await checkInteractionBadges(userId: user.id, interactionType: interactionType)
        
        energyStatistics.totalEnergyEarned += baseEnergy
        energyStatistics.interactionsCount += 1
        
        return baseEnergy
    }
    
    // MARK: - 用户等级自动升级逻辑
    
    /// ✅ 新方法：检查并执行用户等级升级（使用完整对象）
    /// - Parameter user: 用户对象
    private func checkLevelUpgrade(for user: EAUser) async {
        guard let socialProfile = user.socialProfile else { return }
        
        let currentLevel = socialProfile.stellarLevel ?? 1
        let totalEnergy = socialProfile.totalStellarEnergy ?? 0
        
        let newLevel = calculateStellarLevel(totalEnergy: totalEnergy)
        
        if newLevel > currentLevel {
            // 执行等级升级
            await upgradeUserLevel(for: user, newLevel: newLevel, oldLevel: currentLevel)
        }
    }
    
    /// @deprecated 使用接收完整对象的方法替代
    /// 检查并执行用户等级升级
    /// - Parameter userId: 用户ID
    @available(*, deprecated, message: "Use checkLevelUpgrade(for:) instead")
    private func checkLevelUpgrade(userId: UUID) async {
        let user = await repositoryContainer.userRepository.fetchUser(by: userId)
        guard let socialProfile = user?.socialProfile else { return }
        
        let currentLevel = socialProfile.stellarLevel ?? 1
        let totalEnergy = socialProfile.totalStellarEnergy ?? 0
        
        let newLevel = calculateStellarLevel(totalEnergy: totalEnergy)
        
        if newLevel > currentLevel {
            // 执行等级升级
            await upgradeUserLevel(userId: userId, newLevel: newLevel, oldLevel: currentLevel)
        }
    }
    
    /// 计算星际等级
    /// - Parameter totalEnergy: 总星际能量
    /// - Returns: 对应的星际等级
    private func calculateStellarLevel(totalEnergy: Int) -> Int {
        switch totalEnergy {
        case 0..<1000:
            return min(3, max(1, totalEnergy / 333 + 1)) // 等级1-3：新手探索者
        case 1000..<5000:
            return min(6, 4 + (totalEnergy - 1000) / 1333) // 等级4-6：星际旅者
        case 5000...:
            return min(10, 7 + (totalEnergy - 5000) / 2500) // 等级7-10：宇宙领航员
        default:
            return 1
        }
    }
    
    /// ✅ 新方法：执行用户等级升级（使用完整对象）
    /// - Parameters:
    ///   - user: 用户对象
    ///   - newLevel: 新等级
    ///   - oldLevel: 旧等级
    private func upgradeUserLevel(for user: EAUser, newLevel: Int, oldLevel: Int) async {
        guard let socialProfile = user.socialProfile else { return }
        
        // 更新等级和称号
        socialProfile.stellarLevel = newLevel
        socialProfile.explorerTitle = getStellarTitle(level: newLevel)
        
        // 生成升级事件
        let levelUpEvent = EALevelUpEvent(
            userId: user.id,
            oldLevel: oldLevel,
            newLevel: newLevel,
            newTitle: getStellarTitle(level: newLevel),
            timestamp: Date()
        )
        
        recentLevelUpEvent = levelUpEvent
        
        // 等级升级奖励
        let upgradeBonus = energyConfig.levelUpBonus
        await updateUserStellarEnergy(for: user, energyGained: upgradeBonus)
        
        // 检查等级成就徽章
        await checkLevelBadges(for: user, level: newLevel)
        
        energyStatistics.levelsGained += 1
    }
    
    /// @deprecated 使用接收完整对象的方法替代
    /// 执行用户等级升级
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - newLevel: 新等级
    ///   - oldLevel: 旧等级
    @available(*, deprecated, message: "Use upgradeUserLevel(for:newLevel:oldLevel:) instead")
    private func upgradeUserLevel(userId: UUID, newLevel: Int, oldLevel: Int) async {
        let user = await repositoryContainer.userRepository.fetchUser(by: userId)
        guard let socialProfile = user?.socialProfile else { return }
        
        // 更新等级和称号
        socialProfile.stellarLevel = newLevel
        socialProfile.explorerTitle = getStellarTitle(level: newLevel)
        
        // 生成升级事件
        let levelUpEvent = EALevelUpEvent(
            userId: userId,
            oldLevel: oldLevel,
            newLevel: newLevel,
            newTitle: getStellarTitle(level: newLevel),
            timestamp: Date()
        )
        
        recentLevelUpEvent = levelUpEvent
        
        // 等级升级奖励
        let upgradeBonus = energyConfig.levelUpBonus
        await updateUserStellarEnergy(userId: userId, energyGained: upgradeBonus)
        
        // 检查等级成就徽章
        await checkLevelBadges(userId: userId, level: newLevel)
        
        energyStatistics.levelsGained += 1
    }
    
    /// 获取星际等级称号
    /// - Parameter level: 等级
    /// - Returns: 对应的称号
    private func getStellarTitle(level: Int) -> String {
        switch level {
        case 1...3:
            return "新手探索者"
        case 4...6:
            return "星际旅者"
        case 7...10:
            return "宇宙领航员"
        default:
            return "传奇探索者"
        }
    }
    
    // MARK: - 成就徽章系统
    
    /// ✅ 新方法：检查习惯连击成就徽章（使用完整对象）
    /// - Parameters:
    ///   - user: 用户对象
    ///   - habit: 习惯对象
    ///   - streak: 连击天数
    private func checkStreakBadges(for user: EAUser, habit: EAHabit, streak: Int) async {
        let badgeType: EABadgeType?
        
        switch streak {
        case 7:
            badgeType = .weekStreak
        case 30:
            badgeType = .monthStreak
        case 100:
            badgeType = .hundredDayStreak
        case 365:
            badgeType = .yearStreak
        default:
            badgeType = nil
        }
        
        if let badgeType = badgeType {
            await awardBadge(for: user, badgeType: badgeType, context: "习惯连击\(streak)天")
        }
    }
    
    /// @deprecated 使用接收完整对象的方法替代
    /// 检查习惯连击成就徽章
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - habitId: 习惯ID
    ///   - streak: 连击天数
    @available(*, deprecated, message: "Use checkStreakBadges(for:habit:streak:) instead")
    private func checkStreakBadges(userId: UUID, habitId: UUID, streak: Int) async {
        let badgeType: EABadgeType?
        
        switch streak {
        case 7:
            badgeType = .weekStreak
        case 30:
            badgeType = .monthStreak
        case 100:
            badgeType = .hundredDayStreak
        case 365:
            badgeType = .yearStreak
        default:
            badgeType = nil
        }
        
        if let badgeType = badgeType {
            await awardBadge(userId: userId, badgeType: badgeType, context: "习惯连击\(streak)天")
        }
    }
    
    /// 检查广播成就徽章
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - shareType: 广播类型
    private func checkSharingBadges(userId: UUID, shareType: EASharingType) async {
        let totalShares = energyStatistics.postsShared
        
        let badgeType: EABadgeType?
        switch totalShares {
        case 10:
            badgeType = .sharingNovice
        case 50:
            badgeType = .sharingExpert
        case 100:
            badgeType = .sharingMaster
        default:
            badgeType = nil
        }
        
        if let badgeType = badgeType,
           let user = await repositoryContainer.userRepository.fetchUser(by: userId) {
            await awardBadge(for: user, badgeType: badgeType, context: "广播达人")
        }
    }
    
    /// 检查互动成就徽章
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - interactionType: 互动类型
    private func checkInteractionBadges(userId: UUID, interactionType: EAInteractionType) async {
        let totalInteractions = energyStatistics.interactionsCount
        
        let badgeType: EABadgeType?
        switch totalInteractions {
        case 50:
            badgeType = .socialNovice
        case 200:
            badgeType = .socialExpert
        case 500:
            badgeType = .socialMaster
        default:
            badgeType = nil
        }
        
        if let badgeType = badgeType,
           let user = await repositoryContainer.userRepository.fetchUser(by: userId) {
            await awardBadge(for: user, badgeType: badgeType, context: "社交达人")
        }
    }
    
    /// ✅ 新方法：检查等级成就徽章（使用完整对象）
    /// - Parameters:
    ///   - user: 用户对象
    ///   - level: 等级
    private func checkLevelBadges(for user: EAUser, level: Int) async {
        let badgeType: EABadgeType?
        
        switch level {
        case 3:
            badgeType = .noviceExplorer
        case 6:
            badgeType = .stellarTraveler
        case 10:
            badgeType = .cosmicNavigator
        default:
            badgeType = nil
        }
        
        if let badgeType = badgeType {
            await awardBadge(for: user, badgeType: badgeType, context: "等级成就")
        }
    }
    
    /// @deprecated 使用接收完整对象的方法替代
    /// 检查等级成就徽章
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - level: 等级
    @available(*, deprecated, message: "Use checkLevelBadges(for:level:) instead")
    private func checkLevelBadges(userId: UUID, level: Int) async {
        let badgeType: EABadgeType?
        
        switch level {
        case 3:
            badgeType = .noviceExplorer
        case 6:
            badgeType = .stellarTraveler
        case 10:
            badgeType = .cosmicNavigator
        default:
            badgeType = nil
        }
        
        if let badgeType = badgeType {
            await awardBadge(userId: userId, badgeType: badgeType, context: "等级成就")
        }
    }
    
    /// ✅ 新方法：颁发成就徽章（使用完整对象）
    /// - Parameters:
    ///   - user: 用户对象
    ///   - badgeType: 徽章类型
    ///   - context: 获得上下文
    private func awardBadge(for user: EAUser, badgeType: EABadgeType, context: String) async {
        let badgeEarned = EABadgeEarned(
            userId: user.id,
            badgeType: badgeType,
            title: badgeType.title,
            description: badgeType.description,
            earnedAt: Date(),
            context: context
        )
        
        recentBadgeEarned = badgeEarned
        energyStatistics.badgesEarned += 1
        
        // 徽章奖励能量
        let bonusEnergy = energyConfig.badgeBonus
        await updateUserStellarEnergy(for: user, energyGained: bonusEnergy)
    }
    
    /// @deprecated 使用接收完整对象的方法替代
    /// 颁发成就徽章
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - badgeType: 徽章类型
    ///   - context: 获得上下文
    @available(*, deprecated, message: "Use awardBadge(for:badgeType:context:) instead")
    private func awardBadge(userId: UUID, badgeType: EABadgeType, context: String) async {
        let badgeEarned = EABadgeEarned(
            userId: userId,
            badgeType: badgeType,
            title: badgeType.title,
            description: badgeType.description,
            earnedAt: Date(),
            context: context
        )
        
        recentBadgeEarned = badgeEarned
        energyStatistics.badgesEarned += 1
        
        // 徽章奖励能量
        let bonusEnergy = energyConfig.badgeBonus
        await updateUserStellarEnergy(userId: userId, energyGained: bonusEnergy)
    }
    
    // MARK: - 辅助计算方法
    
    /// 计算习惯难度倍数
    /// - Parameter habit: 习惯
    /// - Returns: 难度倍数
    private func calculateDifficultyMultiplier(for habit: EAHabit) -> Double {
        // 根据习惯频率计算难度
        switch habit.targetFrequency {
        case 1...3:
            return 1.0 // 简单习惯
        case 4...6:
            return 1.2 // 中等习惯
        case 7...:
            return 1.5 // 困难习惯
        default:
            return 1.0
        }
    }
    
    /// 计算连击奖励
    /// - Parameter streak: 连击天数
    /// - Returns: 奖励能量
    private func calculateStreakBonus(streak: Int) -> Int {
        switch streak {
        case 3...6:
            return 5
        case 7...13:
            return 10
        case 14...29:
            return 20
        case 30...:
            return 50
        default:
            return 0
        }
    }
    
    /// 计算等级奖励
    /// - Parameter userLevel: 用户等级
    /// - Returns: 等级奖励能量
    private func calculateLevelBonus(userLevel: Int) -> Int {
        return userLevel * 2 // 每级增加2点额外能量
    }
    
    /// ✅ 新方法：计算当前连击天数（使用完整对象）
    /// - Parameters:
    ///   - habit: 习惯对象
    ///   - user: 用户对象
    /// - Returns: 连击天数
    private func calculateCurrentStreak(for habit: EAHabit, user: EAUser) async -> Int {
        let recentCompletions = habit.completions
        
        // 简化连击计算：连续完成的天数
        let calendar = Calendar.current
        let today = Date()
        var streak = 0
        
        for day in 0..<365 {
            let checkDate = calendar.date(byAdding: .day, value: -day, to: today)!
            let dayStart = calendar.startOfDay(for: checkDate)
            let dayEnd = calendar.date(byAdding: .day, value: 1, to: dayStart)!
            
            let hasCompletion = recentCompletions.contains { completion in
                completion.date >= dayStart && completion.date < dayEnd
            }
            
            if hasCompletion {
                streak += 1
            } else {
                break
            }
        }
        
        return streak
    }
    
    /// @deprecated 使用接收完整对象的方法替代
    /// 计算当前连击天数
    /// - Parameters:
    ///   - habitId: 习惯ID
    ///   - userId: 用户ID
    /// - Returns: 连击天数
    @available(*, deprecated, message: "Use calculateCurrentStreak(for:user:) instead")
    private func calculateCurrentStreak(habitId: UUID, userId: UUID) async -> Int {
        let recentCompletions = await repositoryContainer.habitRepository.fetchRecentCompletions(userId: userId, days: 1)
        let habitCompletions = recentCompletions.filter { $0.habit?.id == habitId }
        
        // 简化连击计算：连续完成的天数
        let calendar = Calendar.current
        let today = Date()
        var streak = 0
        
        for day in 0..<365 {
            let checkDate = calendar.date(byAdding: .day, value: -day, to: today)!
            let dayStart = calendar.startOfDay(for: checkDate)
            let dayEnd = calendar.date(byAdding: .day, value: 1, to: dayStart)!
            
            let hasCompletion = habitCompletions.contains { completion in
                completion.date >= dayStart && completion.date < dayEnd
            }
            
            if hasCompletion {
                streak += 1
            } else {
                break
            }
        }
        
        return streak
    }
    
    // MARK: - ✅ 新增：数据验证机制
    
    /// 验证用户数据完整性
    /// - Parameter userId: 用户ID
    /// - Returns: 数据是否完整
    private func validateUserData(userId: UUID) async -> Bool {
        guard let user = await repositoryContainer.userRepository.fetchUser(by: userId) else {
            return false
        }
        
        // 检查用户社交档案是否存在
        guard user.socialProfile != nil else {
            logger.error("⚠️ 数据验证：用户社交档案缺失 - \(userId)")
            return false
        }
        
        return true
    }
    
    /// 验证习惯数据完整性
    /// - Parameters:
    ///   - habitId: 习惯ID
    ///   - userId: 用户ID
    /// - Returns: 数据是否完整
    private func validateHabitData(habitId: UUID, userId: UUID) async -> Bool {
        let userHabits = await repositoryContainer.habitRepository.fetchUserHabits(userId: userId)
        
        guard let habit = userHabits.first(where: { $0.id == habitId }) else {
            logger.error("⚠️ 数据验证：习惯不存在 - \(habitId)")
            return false
        }
        
        // 检查习惯是否属于该用户
        guard habit.user?.id == userId else {
            logger.error("⚠️ 数据验证：习惯不属于该用户 - \(habitId) vs \(userId)")
            return false
        }
        
        return true
    }
    
    /// ✅ 新方法：安全的星际能量更新（使用完整对象）
    /// - Parameters:
    ///   - user: 用户对象
    ///   - energyGained: 获得的能量
    private func updateUserStellarEnergy(for user: EAUser, energyGained: Int) async {
        guard energyGained > 0 else {
            logger.error("⚠️ 星际能量更新：无效的能量值 - \(energyGained)")
            return
        }
        
        guard let socialProfile = user.socialProfile else {
            logger.error("⚠️ 星际能量更新：用户社交档案不存在")
            return
        }
        
        do {
            // 安全更新能量值
            let currentEnergy = socialProfile.totalStellarEnergy ?? 0
            let newTotalEnergy = currentEnergy + energyGained
            
            // ✅ 新增：能量值合理性检查
            guard newTotalEnergy <= 1000000 else { // 设置合理上限
                logger.error("⚠️ 星际能量更新：能量值超出合理范围 - \(newTotalEnergy)")
                return
            }
            
            socialProfile.totalStellarEnergy = newTotalEnergy
            socialProfile.lastEnergyUpdateDate = Date()
            
            // 通过Repository保存
            _ = try await repositoryContainer.communityRepository.updateUserSocialProfile(socialProfile)
            
        } catch {
            logger.error("⚠️ 星际能量更新异常：\(error.localizedDescription)")
        }
    }
    
    /// @deprecated 使用接收完整对象的方法替代
    /// 安全的星际能量更新
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - energyGained: 获得的能量
    @available(*, deprecated, message: "Use updateUserStellarEnergy(for:energyGained:) instead")
    private func updateUserStellarEnergy(userId: UUID, energyGained: Int) async {
        // ✅ 新增：数据验证
        guard await validateUserData(userId: userId) else {
            logger.error("⚠️ 星际能量更新失败：用户数据验证失败 - \(userId)")
            return
        }
        
        guard energyGained > 0 else {
            logger.error("⚠️ 星际能量更新：无效的能量值 - \(energyGained)")
            return
        }
        
        do {
            guard let user = await repositoryContainer.userRepository.fetchUser(by: userId),
                  let socialProfile = user.socialProfile else {
                logger.error("⚠️ 星际能量更新：用户或社交档案不存在")
                return
            }
            
            // 安全更新能量值
            let currentEnergy = socialProfile.totalStellarEnergy ?? 0
            let newTotalEnergy = currentEnergy + energyGained
            
            // ✅ 新增：能量值合理性检查
            guard newTotalEnergy <= 1000000 else { // 设置合理上限
                logger.error("⚠️ 星际能量更新：能量值超出合理范围 - \(newTotalEnergy)")
                return
            }
            
            socialProfile.totalStellarEnergy = newTotalEnergy
            socialProfile.lastEnergyUpdateDate = Date()
            
            // 通过Repository保存
            _ = try await repositoryContainer.communityRepository.updateUserSocialProfile(socialProfile)
            
            // 检查等级升级
            await checkLevelUpgrade(userId: user.id)
            
        } catch {
            logger.error("⚠️ 星际能量更新异常：\(error.localizedDescription)")
        }
    }
    
    // MARK: - 公开接口
    
    /// 奖励星际能量给用户
    /// - Parameters:
    ///   - user: 目标用户
    ///   - amount: 奖励数量
    ///   - source: 能量来源
    ///   - description: 奖励描述
    func awardStellarEnergy(to user: EAUser, amount: Int, source: String, description: String) async {
        guard let socialProfile = user.socialProfile else { return }
        
        let currentEnergy = socialProfile.totalStellarEnergy ?? 0
        socialProfile.totalStellarEnergy = currentEnergy + amount
        
        // 更新统计
        energyStatistics.totalEnergyEarned += amount
        
        // 检查等级升级
        await checkLevelUpgrade(for: user)
        
        // 记录能量获得事件（可以在这里添加日志或通知）
        // 生产环境中应使用正式的日志系统或通知机制
    }
    
    /// ✅ 新方法：获取用户星际能量概览（使用完整对象）
    /// - Parameter user: 用户对象
    /// - Returns: 能量概览
    func getUserEnergyOverview(for user: EAUser) async -> EAUserEnergyOverview? {
        guard let socialProfile = user.socialProfile else { return nil }
        
        let currentLevel = socialProfile.stellarLevel ?? 1
        let totalEnergy = socialProfile.totalStellarEnergy ?? 0
        let currentTitle = getStellarTitle(level: currentLevel)
        
        // 计算到下一级的进度
        let nextLevelEnergy = getNextLevelRequirement(currentLevel: currentLevel)
        let currentLevelEnergy = getCurrentLevelRequirement(currentLevel: currentLevel)
        let progressToNext = Double(totalEnergy - currentLevelEnergy) / Double(nextLevelEnergy - currentLevelEnergy)
        
        return EAUserEnergyOverview(
            userId: user.id,
            stellarLevel: currentLevel,
            explorerTitle: currentTitle,
            totalStellarEnergy: totalEnergy,
            progressToNextLevel: max(0, min(1, progressToNext)),
            nextLevelRequirement: nextLevelEnergy
        )
    }
    
    /// @deprecated 使用接收完整对象的方法替代
    /// 获取用户星际能量概览
    /// - Parameter userId: 用户ID
    /// - Returns: 能量概览
    @available(*, deprecated, message: "Use getUserEnergyOverview(for:) instead")
    func getUserEnergyOverview(userId: UUID) async -> EAUserEnergyOverview? {
        let user = await repositoryContainer.userRepository.fetchUser(by: userId)
        guard let socialProfile = user?.socialProfile else { return nil }
        
        let currentLevel = socialProfile.stellarLevel ?? 1
        let totalEnergy = socialProfile.totalStellarEnergy ?? 0
        let currentTitle = getStellarTitle(level: currentLevel)
        
        // 计算到下一级的进度
        let nextLevelEnergy = getNextLevelRequirement(currentLevel: currentLevel)
        let currentLevelEnergy = getCurrentLevelRequirement(currentLevel: currentLevel)
        let progressToNext = Double(totalEnergy - currentLevelEnergy) / Double(nextLevelEnergy - currentLevelEnergy)
        
        return EAUserEnergyOverview(
            userId: userId,
            stellarLevel: currentLevel,
            explorerTitle: currentTitle,
            totalStellarEnergy: totalEnergy,
            progressToNextLevel: max(0, min(1, progressToNext)),
            nextLevelRequirement: nextLevelEnergy
        )
    }
    
    /// 获取当前等级要求
    /// - Parameter currentLevel: 当前等级
    /// - Returns: 当前等级所需能量
    private func getCurrentLevelRequirement(currentLevel: Int) -> Int {
        switch currentLevel {
        case 1...3:
            return (currentLevel - 1) * 333
        case 4...6:
            return 1000 + (currentLevel - 4) * 1333
        case 7...10:
            return 5000 + (currentLevel - 7) * 2500
        default:
            return 0
        }
    }
    
    /// 获取下一等级要求
    /// - Parameter currentLevel: 当前等级
    /// - Returns: 下一等级所需能量
    private func getNextLevelRequirement(currentLevel: Int) -> Int {
        let nextLevel = min(10, currentLevel + 1)
        return getCurrentLevelRequirement(currentLevel: nextLevel)
    }
    
    /// 重置能量统计
    func resetStatistics() {
        energyStatistics = EAEnergyStatistics()
    }
    
    // MARK: - ✅ 新增：创建习惯计划的星际能量奖励
    
    /// 计算创建习惯计划的星际能量奖励
    /// - Parameters:
    ///   - habitId: 习惯ID
    ///   - userId: 用户ID
    ///   - difficulty: 习惯难度
    ///   - frequency: 习惯频率
    /// - Returns: 获得的星际能量值
    func calculateHabitCreationEnergy(habitId: UUID, userId: UUID, difficulty: String = "medium", frequency: Int = 3) async -> Int {
        guard !isCalculating else { return 0 }
        
        isCalculating = true
        defer { isCalculating = false }
        
        // 基础创建奖励
        var baseEnergy = energyConfig.habitCreationEnergy
        
        // 根据难度调整
        let difficultyMultiplier = energyConfig.difficultyMultiplier[difficulty] ?? 1.0
        baseEnergy = Int(Double(baseEnergy) * difficultyMultiplier)
        
        // 根据频率调整
        let frequencyMultiplier = energyConfig.frequencyMultiplier[frequency] ?? 1.0
        baseEnergy = Int(Double(baseEnergy) * frequencyMultiplier)
        
        // 用户等级影响
        guard let user = await repositoryContainer.userRepository.fetchUser(by: userId) else {
            return 0
        }
        let userLevel = user.socialProfile?.stellarLevel ?? 1
        let levelBonus = calculateLevelBonus(userLevel: userLevel)
        baseEnergy += levelBonus
        
        // ✅ 修复：使用新版本方法，避免deprecated警告
        await updateUserStellarEnergy(for: user, energyGained: baseEnergy)
        
        // 检查等级升级
        await checkLevelUpgrade(for: user)
        
        // 更新统计
        energyStatistics.totalEnergyEarned += baseEnergy
        
        return baseEnergy
    }
    
    // MARK: - ✅ 优化：习惯完成能量计算（增强版）
    
    /// 计算习惯完成的星际能量奖励（增强版）
    /// - Parameters:
    ///   - habitId: 习惯ID
    ///   - userId: 用户ID
    ///   - isConsecutive: 是否为连续完成
    ///   - isFirstCompletion: 是否为首次完成
    /// - Returns: 获得的星际能量值
    func calculateEnhancedHabitCompletionEnergy(habitId: UUID, userId: UUID, isConsecutive: Bool = false, isFirstCompletion: Bool = false) async -> Int {
        guard !isCalculating else { return 0 }
        
        isCalculating = true
        defer { isCalculating = false }
        
        // 获取习惯信息
        let user = await repositoryContainer.userRepository.fetchUser(by: userId)
        let userHabits = await repositoryContainer.habitRepository.fetchUserHabits(userId: userId)
        let targetHabit = userHabits.first { $0.id == habitId }
        
        guard let habit = targetHabit else { return 0 }
        
        // 基础能量奖励
        var baseEnergy = energyConfig.baseHabitCompletionEnergy
        
        // 首次完成奖励
        if isFirstCompletion {
            baseEnergy += energyConfig.habitFirstCompletionBonus
        }
        
        // 根据习惯难度调整
        let difficultyMultiplier = calculateDifficultyMultiplier(for: habit)
        baseEnergy = Int(Double(baseEnergy) * difficultyMultiplier)
        
        // 用户等级影响
        guard let currentUser = user else { return 0 }
        
        // 连续完成奖励
        if isConsecutive, let targetHabit = try? await repositoryContainer.habitRepository.fetchHabit(id: habitId) {
            let streak = await calculateCurrentStreak(for: targetHabit, user: currentUser)
            let streakBonus = energyConfig.habitStreakBonus * min(streak, 30) // 最多30天连击奖励
            baseEnergy += streakBonus

            // 记录连击成就
            await checkStreakBadges(for: currentUser, habit: targetHabit, streak: streak)
        }
        let userLevel = currentUser.socialProfile?.stellarLevel ?? 1
        let levelBonus = calculateLevelBonus(userLevel: userLevel)
        baseEnergy += levelBonus
        
        // ✅ 修复：使用新版本方法，避免deprecated警告
        await updateUserStellarEnergy(for: currentUser, energyGained: baseEnergy)
        
        // 检查等级升级
        await checkLevelUpgrade(for: currentUser)
        
        // 更新统计
        energyStatistics.totalEnergyEarned += baseEnergy
        energyStatistics.habitsCompleted += 1
        
        return baseEnergy
    }
    
    // MARK: - ✅ 新增：收到互动的星际能量奖励
    
    /// 计算收到互动的星际能量奖励
    /// - Parameters:
    ///   - interactionType: 收到的互动类型
    ///   - userId: 用户ID
    ///   - fromUserId: 互动来源用户ID
    /// - Returns: 获得的星际能量值
    func calculateReceivedInteractionEnergy(interactionType: EAReceivedInteractionType, userId: UUID, fromUserId: UUID) async -> Int {
        // ✅ 修复：先获取用户对象，避免deprecated方法调用
        guard let user = await repositoryContainer.userRepository.fetchUser(by: userId) else {
            return 0
        }
        
        let baseEnergy: Int
        
        switch interactionType {
        case .receivedLike:
            baseEnergy = energyConfig.likeReceivedEnergy
        case .receivedComment:
            baseEnergy = energyConfig.commentReceivedEnergy
        case .receivedFollow:
            baseEnergy = energyConfig.followReceivedEnergy
        }
        
        // ✅ 修复：使用新版本方法，避免deprecated警告
        await updateUserStellarEnergy(for: user, energyGained: baseEnergy)
        
        // 检查互动成就
        await checkReceivedInteractionBadges(userId: user.id, interactionType: interactionType)
        
        energyStatistics.totalEnergyEarned += baseEnergy
        energyStatistics.interactionsCount += 1
        
        return baseEnergy
    }
    
    // MARK: - ✅ 新增：完整行为链奖励
    
    /// 计算完整行为链的星际能量奖励
    /// 当用户完成"创建习惯→完成习惯→分享广播"的完整流程时给予额外奖励
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - habitId: 习惯ID
    ///   - postId: 分享帖子ID
    /// - Returns: 获得的星际能量值
    func calculateBehaviorChainBonus(userId: UUID, habitId: UUID, postId: UUID) async -> Int {
        // ✅ 修复：先获取用户对象，避免deprecated方法调用
        guard let user = await repositoryContainer.userRepository.fetchUser(by: userId) else {
            return 0
        }
        
        // 验证行为链完整性
        let isValidChain = await validateBehaviorChain(userId: userId, habitId: habitId, postId: postId)
        
        guard isValidChain else { return 0 }
        
        let bonusEnergy = energyConfig.completeBehaviorChainBonus
        
        // ✅ 修复：使用新版本方法，避免deprecated警告
        await updateUserStellarEnergy(for: user, energyGained: bonusEnergy)
        
        // 记录行为链成就
        await checkBehaviorChainBadges(userId: user.id)
        
        energyStatistics.totalEnergyEarned += bonusEnergy
        
        return bonusEnergy
    }
    
    // MARK: - ✅ 新增：每日/每周活跃奖励
    
    /// 计算每日活跃奖励
    /// - Parameter userId: 用户ID
    /// - Returns: 获得的星际能量值
    func calculateDailyActiveBonus(userId: UUID) async -> Int {
        // ✅ 修复：先获取用户对象，避免deprecated方法调用
        guard let user = await repositoryContainer.userRepository.fetchUser(by: userId) else {
            return 0
        }
        
        // 检查今日是否已经获得过活跃奖励
        let hasReceivedToday = await checkDailyActiveBonusReceived(userId: userId)
        
        guard !hasReceivedToday else { return 0 }
        
        let bonusEnergy = energyConfig.dailyActiveBonus
        
        // ✅ 修复：使用新版本方法，避免deprecated警告
        await updateUserStellarEnergy(for: user, energyGained: bonusEnergy)
        
        // 记录今日已获得活跃奖励
        await markDailyActiveBonusReceived(userId: userId)
        
        energyStatistics.totalEnergyEarned += bonusEnergy
        
        return bonusEnergy
    }
    
    /// 计算每周活跃奖励
    /// - Parameter userId: 用户ID
    /// - Returns: 获得的星际能量值
    func calculateWeeklyActiveBonus(userId: UUID) async -> Int {
        // ✅ 修复：先获取用户对象，避免deprecated方法调用
        guard let user = await repositoryContainer.userRepository.fetchUser(by: userId) else {
            return 0
        }
        
        // 检查本周是否已经获得过活跃奖励
        let hasReceivedThisWeek = await checkWeeklyActiveBonusReceived(userId: userId)
        
        guard !hasReceivedThisWeek else { return 0 }
        
        // 检查本周活跃天数
        let activeDaysThisWeek = await calculateActiveDaysThisWeek(userId: userId)
        
        guard activeDaysThisWeek >= 5 else { return 0 } // 至少活跃5天才能获得周奖励
        
        let bonusEnergy = energyConfig.weeklyActiveBonus
        
        // ✅ 修复：使用新版本方法，避免deprecated警告
        await updateUserStellarEnergy(for: user, energyGained: bonusEnergy)
        
        // 记录本周已获得活跃奖励
        await markWeeklyActiveBonusReceived(userId: userId)
        
        energyStatistics.totalEnergyEarned += bonusEnergy
        
        return bonusEnergy
    }
    
    // MARK: - ✅ 新增：辅助方法实现
    
    /// 验证行为链完整性
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - habitId: 习惯ID
    ///   - postId: 帖子ID
    /// - Returns: 是否为有效的行为链
    private func validateBehaviorChain(userId: UUID, habitId: UUID, postId: UUID) async -> Bool {
        // 检查习惯是否存在且属于该用户
        let userHabits = await repositoryContainer.habitRepository.fetchUserHabits(userId: userId)
        guard userHabits.contains(where: { $0.id == habitId }) else { return false }
        
        // 检查最近是否完成了该习惯（24小时内）
        let recentCompletions = await repositoryContainer.habitRepository.fetchRecentCompletions(userId: userId, days: 1)
        let hasRecentCompletion = recentCompletions.contains { completion in
            completion.habit?.id == habitId &&
            Calendar.current.isDateInToday(completion.date)
        }
        
        guard hasRecentCompletion else { return false }
        
        // 检查帖子是否存在且与习惯相关
        // 这里可以根据实际需求添加更多验证逻辑
        
        return true
    }
    
    /// 检查今日是否已获得活跃奖励
    /// - Parameter userId: 用户ID
    /// - Returns: 是否已获得
    private func checkDailyActiveBonusReceived(userId: UUID) async -> Bool {
        // 这里可以通过用户档案或缓存来检查
        // 简化实现：通过用户社交档案的lastEnergyUpdateDate判断
        let user = await repositoryContainer.userRepository.fetchUser(by: userId)
        guard let socialProfile = user?.socialProfile,
              let lastUpdate = socialProfile.lastEnergyUpdateDate else { return false }
        
        return Calendar.current.isDate(lastUpdate, inSameDayAs: Date())
    }
    
    /// 标记今日已获得活跃奖励
    /// - Parameter userId: 用户ID
    private func markDailyActiveBonusReceived(userId: UUID) async {
        let user = await repositoryContainer.userRepository.fetchUser(by: userId)
        user?.socialProfile?.lastEnergyUpdateDate = Date()
    }
    
    /// 检查本周是否已获得活跃奖励
    /// - Parameter userId: 用户ID
    /// - Returns: 是否已获得
    private func checkWeeklyActiveBonusReceived(userId: UUID) async -> Bool {
        // 简化实现：检查用户档案中的周奖励记录
        // 实际项目中可以使用更精确的记录机制
        return false // 暂时返回false，允许获得周奖励
    }
    
    /// 标记本周已获得活跃奖励
    /// - Parameter userId: 用户ID
    private func markWeeklyActiveBonusReceived(userId: UUID) async {
        // 记录本周已获得奖励的逻辑
        // 可以在用户档案中添加相应字段
    }
    
    /// 计算本周活跃天数
    /// - Parameter userId: 用户ID
    /// - Returns: 活跃天数
    private func calculateActiveDaysThisWeek(userId: UUID) async -> Int {
        // 获取本周的习惯完成记录
        let recentCompletions = await repositoryContainer.habitRepository.fetchRecentCompletions(userId: userId, days: 7)
        
        let calendar = Calendar.current
        let now = Date()
        let weekStart = calendar.dateInterval(of: .weekOfYear, for: now)?.start ?? now
        
        // 统计本周有完成记录的天数
        let activeDays = Set(recentCompletions.compactMap { completion -> Date? in
            guard completion.date >= weekStart else { return nil }
            return calendar.startOfDay(for: completion.date)
        })
        
        return activeDays.count
    }
    
    /// 检查收到互动的成就
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - interactionType: 互动类型
    private func checkReceivedInteractionBadges(userId: UUID, interactionType: EAReceivedInteractionType) async {
        // 根据收到的互动类型检查相应成就
        // 这里可以添加具体的成就检查逻辑
    }
    
    /// 检查行为链成就
    /// - Parameter userId: 用户ID
    private func checkBehaviorChainBadges(userId: UUID) async {
        // 检查完整行为链相关的成就
        // 例如：连续完成行为链的次数等
    }
    
    // MARK: - 防刷机制核心方法
    
    /// 检查用户操作是否违反防刷规则
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - actionType: 操作类型
    ///   - energyAmount: 预期获得的能量值
    /// - Returns: 是否允许操作和实际能量值
    private func checkAntiSpamRules(userId: UUID, actionType: StellarEnergyActionType, energyAmount: Int) -> (allowed: Bool, actualEnergy: Int) {
        let now = Date()
        
        // 1. 检查时间间隔限制
        if !checkTimeInterval(userId: userId, actionType: actionType, currentTime: now) {
            logger.warning("用户 \(userId) 操作过于频繁，类型: \(actionType.rawValue)")
            return (false, 0)
        }
        
        // 2. 检查每日能量上限
        let actualEnergy = checkDailyEnergyLimit(userId: userId, actionType: actionType, requestedEnergy: energyAmount)
        if actualEnergy == 0 {
            logger.warning("用户 \(userId) 已达到每日能量上限，类型: \(actionType.rawValue)")
            return (false, 0)
        }
        
        // 3. 检查异常行为模式
        if !checkSuspiciousBehavior(userId: userId, actionType: actionType, currentTime: now) {
            logger.warning("检测到用户 \(userId) 可疑行为，类型: \(actionType.rawValue)")
            return (false, 0)
        }
        
        return (true, actualEnergy)
    }
    
    /// 检查操作时间间隔
    private func checkTimeInterval(userId: UUID, actionType: StellarEnergyActionType, currentTime: Date) -> Bool {
        guard let userHistory = userActionHistory[userId] else { return true }
        
        let minInterval: TimeInterval
        switch actionType {
        case .giveLike, .receivePostLike:
            minInterval = AntiSpamConfig.minIntervalBetweenLikes
        case .giveComment, .receiveComment:
            minInterval = AntiSpamConfig.minIntervalBetweenComments
        case .createPost, .createHabitPost:
            minInterval = AntiSpamConfig.minIntervalBetweenPosts
        default:
            return true // 其他操作不限制间隔
        }
        
        // 查找最近的同类型操作
        let recentActions = userHistory.filter { $0.actionType == actionType }
        guard let lastAction = recentActions.last else { return true }
        
        return currentTime.timeIntervalSince(lastAction.timestamp) >= minInterval
    }
    
    /// 检查每日能量上限
    private func checkDailyEnergyLimit(userId: UUID, actionType: StellarEnergyActionType, requestedEnergy: Int) -> Int {
        let today = Calendar.current.startOfDay(for: Date())
        
        // 获取或创建今日统计
        var todayStats = dailyEnergyStats[userId]
        if todayStats == nil || !Calendar.current.isDate(todayStats!.date, inSameDayAs: today) {
            todayStats = DailyEnergyStats(date: today)
            dailyEnergyStats[userId] = todayStats!
        }
        
        guard let stats = todayStats else { return requestedEnergy }
        
        // 检查总能量上限
        if stats.totalEnergy >= AntiSpamConfig.maxDailyTotalEnergy {
            return 0
        }
        
        // 检查分类能量上限
        let categoryLimit: Int
        let currentCategoryEnergy: Int
        
        switch actionType {
        case .giveLike, .receivePostLike:
            categoryLimit = AntiSpamConfig.maxDailyLikeEnergy
            currentCategoryEnergy = stats.likeEnergy
        case .giveComment, .receiveComment:
            categoryLimit = AntiSpamConfig.maxDailyCommentEnergy
            currentCategoryEnergy = stats.commentEnergy
        case .createPost, .createHabitPost:
            categoryLimit = AntiSpamConfig.maxDailyPostEnergy
            currentCategoryEnergy = stats.postEnergy
        default:
            return requestedEnergy // 其他操作不限制
        }
        
        // 计算实际可获得的能量
        let remainingCategoryEnergy = max(0, categoryLimit - currentCategoryEnergy)
        let remainingTotalEnergy = max(0, AntiSpamConfig.maxDailyTotalEnergy - stats.totalEnergy)
        
        return min(requestedEnergy, min(remainingCategoryEnergy, remainingTotalEnergy))
    }
    
    /// 检查可疑行为模式
    private func checkSuspiciousBehavior(userId: UUID, actionType: StellarEnergyActionType, currentTime: Date) -> Bool {
        guard let userHistory = userActionHistory[userId] else { return true }
        
        // 检查每分钟操作频率
        let oneMinuteAgo = currentTime.addingTimeInterval(-60)
        let recentActions = userHistory.filter { $0.timestamp > oneMinuteAgo }
        
        if recentActions.count >= AntiSpamConfig.maxActionsPerMinute {
            return false
        }
        
        // 检查总体可疑行为阈值
        let oneDayAgo = currentTime.addingTimeInterval(-86400)
        let dayActions = userHistory.filter { $0.timestamp > oneDayAgo }
        
        if dayActions.count >= AntiSpamConfig.suspiciousActionThreshold {
            return false
        }
        
        return true
    }
    
    /// 记录用户操作
    private func recordUserAction(userId: UUID, actionType: StellarEnergyActionType, energyGained: Int) {
        let record = UserActionRecord(
            actionType: actionType,
            timestamp: Date(),
            energyGained: energyGained
        )
        
        // 添加到操作历史
        if userActionHistory[userId] == nil {
            userActionHistory[userId] = []
        }
        userActionHistory[userId]?.append(record)
        
        // 清理过期记录（保留24小时）
        let oneDayAgo = Date().addingTimeInterval(-86400)
        userActionHistory[userId] = userActionHistory[userId]?.filter { $0.timestamp > oneDayAgo }
        
        // 更新每日统计
        updateDailyStats(userId: userId, actionType: actionType, energyGained: energyGained)
    }
    
    /// 更新每日能量统计
    private func updateDailyStats(userId: UUID, actionType: StellarEnergyActionType, energyGained: Int) {
        let today = Calendar.current.startOfDay(for: Date())
        
        // 获取或创建今日统计
        var todayStats = dailyEnergyStats[userId]
        if todayStats == nil || !Calendar.current.isDate(todayStats!.date, inSameDayAs: today) {
            todayStats = DailyEnergyStats(date: today)
        }
        
        guard var stats = todayStats else { return }
        
        // 更新统计
        stats.addEnergy(energyGained, for: actionType)
        dailyEnergyStats[userId] = stats
    }
    
    // MARK: - 公共能量奖励方法
    
    /// 处理用户行为并更新星际能量（包含防刷机制）
    /// - Parameters:
    ///   - actionType: 行为类型
    ///   - userId: 用户ID
    /// - Returns: (能量增益, 新等级, 是否等级提升)
    func processUserActionForStellarEnergy(
        actionType: StellarEnergyActionType,
        userId: UUID
    ) async throws -> (energyGain: Int, newLevel: Int, leveledUp: Bool) {
        
        // 获取基础能量值
        let baseEnergy = actionType.baseEnergyValue
        
        // ✅ 防刷机制检查
        let antiSpamResult = checkAntiSpamRules(
            userId: userId,
            actionType: actionType,
            energyAmount: baseEnergy
        )
        
        guard antiSpamResult.allowed else {
            logger.warning("用户行为被防刷机制阻止，用户: \(userId)，行为: \(actionType.rawValue)")
            return (energyGain: 0, newLevel: 1, leveledUp: false)
        }
        
        let actualEnergy = antiSpamResult.actualEnergy
        
        // 获取当前用户等级
        let oldLevel = await getCurrentUserLevel(userId: userId)
        
        // 更新用户星际能量
        if let user = await repositoryContainer.userRepository.fetchUser(by: userId) {
            await updateUserStellarEnergy(for: user, energyGained: actualEnergy)

            // 检查等级升级
            await checkLevelUpgrade(for: user)
        }

        // ✅ 记录用户操作
        recordUserAction(userId: userId, actionType: actionType, energyGained: actualEnergy)
        
        // 获取新等级
        let newLevel = await getCurrentUserLevel(userId: userId)
        let leveledUp = newLevel > oldLevel
        
        // 更新统计
        energyStatistics.totalEnergyEarned += actualEnergy
        
        logger.info("成功处理用户行为: \(actionType.rawValue)，获得能量: \(actualEnergy)，等级: \(oldLevel) → \(newLevel)")
        
        return (energyGain: actualEnergy, newLevel: newLevel, leveledUp: leveledUp)
    }
    
    /// 获取当前用户等级
    private func getCurrentUserLevel(userId: UUID) async -> Int {
        // ✅ 修复：移除不必要的do-catch，因为此方法不包含throwing操作
        guard let user = await repositoryContainer.userRepository.fetchUser(by: userId),
              let socialProfile = user.socialProfile else {
            logger.warning("获取用户等级失败: 用户或社交档案不存在")
            return 1
        }
        return socialProfile.stellarLevel ?? 1
    }
    
    /// 奖励创建习惯的星际能量
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - habitName: 习惯名称
    ///   - difficulty: 习惯难度
    func awardHabitCreationEnergy(userId: UUID, habitName: String, difficulty: String) async {
        // ✅ 修复：移除不必要的do-catch，因为此方法不包含throwing操作
        // 基础创建习惯能量
        var baseEnergy = energyConfig.habitCreationEnergy

        // 根据难度调整能量奖励
        let difficultyBonus = calculateDifficultyBonus(difficulty: difficulty)
        baseEnergy += difficultyBonus

        // ✅ 新增：防刷机制检查
        let antiSpamResult = checkAntiSpamRules(
            userId: userId,
            actionType: .createHabit,
            energyAmount: baseEnergy
        )

        guard antiSpamResult.allowed else {
            logger.warning("创建习惯能量奖励被防刷机制阻止，用户: \(userId)")
            return
        }

        let actualEnergy = antiSpamResult.actualEnergy

        // 更新用户星际能量
        if let user = await repositoryContainer.userRepository.fetchUser(by: userId) {
            await updateUserStellarEnergy(for: user, energyGained: actualEnergy)

            // 检查等级升级
            await checkLevelUpgrade(for: user)
        }

        // ✅ 新增：记录用户操作
        recordUserAction(userId: userId, actionType: .createHabit, energyGained: actualEnergy)

        // 更新统计
        energyStatistics.totalEnergyEarned += actualEnergy

        logger.info("成功奖励创建习惯能量: \(actualEnergy)星际能量，习惯: \(habitName)，难度: \(difficulty)")
    }
    
    /// 计算难度奖励
    /// - Parameter difficulty: 习惯难度
    /// - Returns: 难度奖励能量
    private func calculateDifficultyBonus(difficulty: String) -> Int {
        switch difficulty.lowercased() {
        case "简单", "easy":
            return 0
        case "中等", "medium":
            return 5
        case "困难", "hard":
            return 10
        case "专家", "expert":
            return 15
        default:
            return 0
        }
    }
    
    // MARK: - 能量减少方法（确保与增加逻辑完全一致）
    
    /// 减少习惯完成的星际能量（取消完成时调用）
    /// - Parameters:
    ///   - habitId: 习惯ID
    ///   - userId: 用户ID
    ///   - wasConsecutive: 之前是否为连续完成
    /// - Returns: 减少的星际能量值
    func deductHabitCompletionEnergy(habitId: UUID, userId: UUID, wasConsecutive: Bool = false) async -> Int {
        guard !isCalculating else { return 0 }
        
        isCalculating = true
        defer { isCalculating = false }
        
        // ✅ 修复：移除不必要的do-catch，因为此方法不包含throwing操作
        // 获取习惯信息
        guard let user = await repositoryContainer.userRepository.fetchUser(by: userId) else {
            logger.error("⚠️ 星际能量减少：用户不存在 - \(userId)")
            return 0
        }

        let userHabits = await repositoryContainer.habitRepository.fetchUserHabits(userId: userId)
        guard let targetHabit = userHabits.first(where: { $0.id == habitId }) else {
            logger.error("⚠️ 星际能量减少：习惯不存在 - \(habitId)")
            return energyConfig.baseHabitCompletionEnergy // 降级策略：返回基础能量
        }

        // 基础能量减少（与增加逻辑完全一致）
        var baseEnergy = energyConfig.baseHabitCompletionEnergy

        // 根据习惯难度调整
        let difficultyMultiplier = calculateDifficultyMultiplier(for: targetHabit)
        baseEnergy = Int(Double(baseEnergy) * difficultyMultiplier)

        // 连续完成奖励减少
        if wasConsecutive, let user = await repositoryContainer.userRepository.fetchUser(by: userId) {
            let streak = await calculateCurrentStreak(for: targetHabit, user: user)
            let streakBonus = calculateStreakBonus(streak: streak)
            baseEnergy += streakBonus
        }

        // 用户等级影响
        let userLevel = user.socialProfile?.stellarLevel ?? 1
        let levelBonus = calculateLevelBonus(userLevel: userLevel)
        baseEnergy += levelBonus

        // 减少用户星际能量
        await deductUserStellarEnergy(userId: userId, energyDeducted: baseEnergy)

        // 更新统计
        energyStatistics.totalEnergyEarned = max(0, energyStatistics.totalEnergyEarned - baseEnergy)
        energyStatistics.habitsCompleted = max(0, energyStatistics.habitsCompleted - 1)

        logger.info("成功减少习惯完成能量: \(baseEnergy)星际能量，习惯: \(targetHabit.name)")

        return baseEnergy
    }
    
    /// 减少社区互动的星际能量（取消互动时调用）
    /// - Parameters:
    ///   - interactionType: 互动类型
    ///   - userId: 用户ID
    /// - Returns: 减少的星际能量值
    func deductInteractionEnergy(interactionType: EAInteractionType, userId: UUID) async -> Int {
        let baseEnergy: Int
        
        switch interactionType {
        case .like:
            baseEnergy = energyConfig.likeGivenEnergy
        case .comment:
            baseEnergy = energyConfig.commentGivenEnergy
        case .share:
            baseEnergy = energyConfig.shareEnergy
        case .follow:
            baseEnergy = energyConfig.followGivenEnergy
        }
        
        // 减少用户星际能量
        await deductUserStellarEnergy(userId: userId, energyDeducted: baseEnergy)
        
        // 更新统计
        energyStatistics.totalEnergyEarned = max(0, energyStatistics.totalEnergyEarned - baseEnergy)
        energyStatistics.interactionsCount = max(0, energyStatistics.interactionsCount - 1)
        
        logger.info("成功减少互动能量: \(baseEnergy)星际能量，类型: \(String(describing: interactionType))")
        
        return baseEnergy
    }
    
    /// 减少用户星际能量的核心方法
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - energyDeducted: 减少的能量值
    private func deductUserStellarEnergy(userId: UUID, energyDeducted: Int) async {
        // ✅ 修复：移除不必要的do-catch，因为此方法不包含throwing操作
        // 获取用户数据
        guard let user = await repositoryContainer.userRepository.fetchUser(by: userId) else {
            logger.error("⚠️ 减少星际能量：用户不存在 - \(userId)")
            return
        }

        // 获取或创建社交档案
        let socialProfile = user.socialProfile ?? {
            let profile = EAUserSocialProfile()
            user.socialProfile = profile
            return profile
        }()

        // 减少总星际能量（确保不会变成负数）
        let currentEnergy = socialProfile.totalStellarEnergy ?? 0
        let newTotalEnergy = max(0, currentEnergy - energyDeducted)
        socialProfile.totalStellarEnergy = newTotalEnergy

        // 记录能量变化历史（负值表示减少）
        socialProfile.recordDailyEnergyChange(-energyDeducted, reason: "取消操作")

        // 更新最后能量更新时间
        socialProfile.lastEnergyUpdateDate = Date()

        logger.info("用户 \(userId) 星际能量减少 \(energyDeducted)，当前总能量: \(newTotalEnergy)")
    }
}

// MARK: - 数据模型

/// 星际能量配置（优化版 - 完整行为链奖励）
struct EAStellarEnergyConfig {
    // MARK: - 习惯管理奖励
    let habitCreationEnergy = 20            // ✅ 新增：创建习惯计划奖励
    let baseHabitCompletionEnergy = 15      // ✅ 优化：提升基础习惯完成能量（原10→15）
    let habitFirstCompletionBonus = 10      // ✅ 新增：首次完成奖励
    let habitStreakBonus = 5                // ✅ 新增：连击基础奖励（每天+5）
    
    // MARK: - 社区广播奖励
    let milestoneShareEnergy = 40           // ✅ 优化：里程碑广播能量（原50→40）
    let reflectionShareEnergy = 25          // ✅ 优化：反思广播能量（原30→25）
    let achievementShareEnergy = 35         // ✅ 优化：成就广播能量（原40→35）
    let motivationShareEnergy = 20          // ✅ 优化：激励广播能量（原25→20）
    let habitCompletionShareEnergy = 30     // ✅ 新增：习惯完成广播奖励
    
    // MARK: - 社区互动奖励
    let likeGivenEnergy = 1                 // ✅ 优化：给出点赞能量（原2→1）
    let likeReceivedEnergy = 3              // ✅ 新增：收到点赞能量
    let commentGivenEnergy = 3              // ✅ 优化：发表评论能量（原5→3）
    let commentReceivedEnergy = 5           // ✅ 新增：收到评论能量
    let shareEnergy = 8                     // 转发能量（保持不变）
    let followGivenEnergy = 5               // ✅ 优化：关注他人能量（原10→5）
    let followReceivedEnergy = 15           // ✅ 新增：被关注能量
    
    // MARK: - 特殊奖励
    let levelUpBonus = 100                  // 等级升级奖励（保持不变）
    let badgeBonus = 30                     // 徽章奖励能量（保持不变）
    let dailyActiveBonus = 10               // ✅ 新增：每日活跃奖励
    let weeklyActiveBonus = 50              // ✅ 新增：每周活跃奖励
    let completeBehaviorChainBonus = 25     // ✅ 新增：完整行为链奖励（创建→完成→分享）
    
    // MARK: - 权重系数
    let difficultyMultiplier: [String: Double] = [
        "easy": 1.0,
        "medium": 1.2,
        "hard": 1.5,
        "expert": 2.0
    ]
    
    let frequencyMultiplier: [Int: Double] = [
        1: 1.0,      // 每周1次
        2: 1.1,      // 每周2次
        3: 1.2,      // 每周3次
        7: 1.5       // 每天
    ]
}

/// 收到的互动类型
enum EAReceivedInteractionType {
    case receivedLike      // 收到点赞
    case receivedComment   // 收到评论
    case receivedFollow    // 收到关注
}

/// 广播类型
enum EASharingType {
    case habitMilestone    // 习惯里程碑
    case reflection        // 反思广播
    case achievement       // 成就广播
    case motivation        // 激励广播
    case habitCompletion   // ✅ 新增：习惯完成广播
}

/// 互动类型
enum EAInteractionType {
    case like              // 点赞
    case comment           // 评论
    case share             // 转发
    case follow            // 关注
}

/// 徽章类型
enum EABadgeType: String, CaseIterable {
    case weekStreak = "week_streak"
    case monthStreak = "month_streak"
    case hundredDayStreak = "hundred_day_streak"
    case yearStreak = "year_streak"
    case sharingNovice = "sharing_novice"
    case sharingExpert = "sharing_expert"
    case sharingMaster = "sharing_master"
    case socialNovice = "social_novice"
    case socialExpert = "social_expert"
    case socialMaster = "social_master"
    case noviceExplorer = "novice_explorer"
    case stellarTraveler = "stellar_traveler"
    case cosmicNavigator = "cosmic_navigator"
    
    var title: String {
        switch self {
        case .weekStreak: return "七日连击"
        case .monthStreak: return "月度坚持"
        case .hundredDayStreak: return "百日成就"
        case .yearStreak: return "年度传奇"
        case .sharingNovice: return "广播新手"
        case .sharingExpert: return "广播专家"
        case .sharingMaster: return "广播大师"
        case .socialNovice: return "社交新星"
        case .socialExpert: return "社交达人"
        case .socialMaster: return "社交领袖"
        case .noviceExplorer: return "新手探索者"
        case .stellarTraveler: return "星际旅者"
        case .cosmicNavigator: return "宇宙领航员"
        }
    }
    
    var description: String {
        switch self {
        case .weekStreak: return "连续7天完成习惯"
        case .monthStreak: return "连续30天完成习惯"
        case .hundredDayStreak: return "连续100天完成习惯"
        case .yearStreak: return "连续365天完成习惯"
        case .sharingNovice: return "广播10次内容"
        case .sharingExpert: return "广播50次内容"
        case .sharingMaster: return "广播100次内容"
        case .socialNovice: return "社区互动50次"
        case .socialExpert: return "社区互动200次"
        case .socialMaster: return "社区互动500次"
        case .noviceExplorer: return "达到星际等级3级"
        case .stellarTraveler: return "达到星际等级6级"
        case .cosmicNavigator: return "达到星际等级10级"
        }
    }
}

/// 能量统计
struct EAEnergyStatistics {
    var totalEnergyEarned = 0      // 总获得能量
    var habitsCompleted = 0        // 完成习惯数
    var postsShared = 0           // 广播帖子数
    var interactionsCount = 0     // 互动次数
    var levelsGained = 0          // 升级次数
    var badgesEarned = 0          // 获得徽章数
}

/// 等级升级事件
struct EALevelUpEvent {
    let userId: UUID
    let oldLevel: Int
    let newLevel: Int
    let newTitle: String
    let timestamp: Date
}

/// 徽章获得事件
struct EABadgeEarned {
    let userId: UUID
    let badgeType: EABadgeType
    let title: String
    let description: String
    let earnedAt: Date
    let context: String
}

/// 用户能量概览
struct EAUserEnergyOverview {
    let userId: UUID
    let stellarLevel: Int
    let explorerTitle: String
    let totalStellarEnergy: Int
    let progressToNextLevel: Double
    let nextLevelRequirement: Int
} 