import Foundation
import SwiftData
import SwiftUI
import os.log

// MARK: - 星际能量行为类型（优化版）

/// 星际能量行为类型枚举（优化版 - 完整行为链支持）
/// 定义用户在社区中可以获得星际能量奖励的行为类型
enum StellarEnergyActionType: String, CaseIterable {
    // MARK: - 习惯管理行为
    case createHabit = "create_habit"                   // ✅ 新增：创建习惯计划
    case completeHabit = "complete_habit"               // ✅ 优化：完成习惯
    case firstHabitCompletion = "first_habit_completion" // ✅ 新增：首次完成习惯
    
    // MARK: - 社区发布行为
    case createPost = "create_post"                     // 发布帖子
    case createHabitPost = "create_habit_post"          // ✅ 新增：发布习惯相关帖子
    
    // MARK: - 社区互动行为（给出）
    case giveLike = "give_like"                         // ✅ 优化：给出点赞
    case giveComment = "give_comment"                   // ✅ 优化：发表评论
    case giveFollow = "give_follow"                     // ✅ 优化：关注用户
    
    // MARK: - 社区互动行为（收到）
    case receivePostLike = "receive_post_like"          // 帖子被点赞
    case receiveCommentLike = "receive_comment_like"    // 评论被点赞
    case receiveComment = "receive_comment"             // ✅ 新增：收到评论
    case receiveFollow = "receive_follow"               // ✅ 优化：被用户关注
    
    // MARK: - 特殊奖励
    case dailyActive = "daily_active"                   // 每日活跃
    case weeklyActive = "weekly_active"                 // ✅ 新增：每周活跃
    case behaviorChain = "behavior_chain"               // ✅ 新增：完整行为链
    
    /// 行为描述
    var description: String {
        switch self {
        case .createHabit:
            return "创建习惯计划"
        case .completeHabit:
            return "完成习惯"
        case .firstHabitCompletion:
            return "首次完成习惯"
        case .createPost:
            return "发布帖子"
        case .createHabitPost:
            return "发布习惯相关帖子"
        case .giveLike:
            return "给出点赞"
        case .giveComment:
            return "发表评论"
        case .giveFollow:
            return "关注用户"
        case .receivePostLike:
            return "帖子被点赞"
        case .receiveCommentLike:
            return "评论被点赞"
        case .receiveComment:
            return "收到评论"
        case .receiveFollow:
            return "被用户关注"
        case .dailyActive:
            return "每日活跃"
        case .weeklyActive:
            return "每周活跃"
        case .behaviorChain:
            return "完整行为链"
        }
    }
    
    /// ✅ 新增：获取基础能量值
    var baseEnergyValue: Int {
        switch self {
        case .createHabit:
            return 20
        case .completeHabit:
            return 15
        case .firstHabitCompletion:
            return 25 // 15 + 10 首次奖励
        case .createPost:
            return 25
        case .createHabitPost:
            return 30
        case .giveLike:
            return 1
        case .giveComment:
            return 3
        case .giveFollow:
            return 5
        case .receivePostLike:
            return 3
        case .receiveCommentLike:
            return 2
        case .receiveComment:
            return 5
        case .receiveFollow:
            return 15
        case .dailyActive:
            return 10
        case .weeklyActive:
            return 50
        case .behaviorChain:
            return 25
        }
    }
}

/// 社区核心业务服务（性能优化版）
/// 负责处理社区相关的所有业务逻辑，包括帖子CRUD、点赞评论、搜索等功能
/// 严格遵循项目Repository架构规范，通过Repository层访问数据
/// ✅ Phase 2优化：增强性能、缓存策略和错误处理
@MainActor
@Observable
final class EACommunityService {
    
    // MARK: - 属性
    
    /// 社区数据仓库
    private let repository: EACommunityRepositoryProtocol
    
    /// ✅ 修复：添加sessionManager依赖注入
    private let sessionManager: EASessionManager
    
    /// ✅ 新增：星际能量服务（包含防刷机制）
    private var stellarEnergyService: EAStellarEnergyService?
    
    /// ✅ 优化：调整分页配置，提升加载性能
    private let defaultPageSize: Int = 15 // 减少默认页面大小，提升加载速度
    
    /// 错误状态
    var lastError: CommunityServiceError?
    
    /// ✅ 新增：性能监控
    private var operationCount: Int = 0
    private var lastOperationTime: Date = Date()
    
    /// ✅ 新增：日志记录器
    private let logger = Logger(subsystem: "com.evolve.community", category: "EACommunityService")
    
    // MARK: - Performance Optimized Data Loading（新增性能优化版本）
    
    /// 🔑 性能优化：智能分页加载，包含缓存策略
    func loadPostsWithCache(category: String? = nil, limit: Int = 20, offset: Int = 0) async throws -> [EACommunityPost] {
        
        // 🔑 缓存键生成
        let cacheKey = "posts_\(category ?? "all")_\(limit)_\(offset)"
        
        // 🔑 检查缓存（5分钟有效期）
        if let cachedPosts = postCache[cacheKey],
           let cacheTime = cacheTimestamps[cacheKey],
           Date().timeIntervalSince(cacheTime) < 300 { // 5分钟缓存
            return cachedPosts
        }
        
        // 🔑 从Repository获取数据（使用优化后的分页查询）
        let posts: [EACommunityPost]
        if let category = category {
            posts = try await repository.fetchPostsByCategory(
                category: category,
                limit: limit,
                offset: offset
            )
        } else {
            posts = try await repository.fetchPosts(
                limit: limit,
                offset: offset
            )
        }
        
        // 🔑 更新缓存
        postCache[cacheKey] = posts
        cacheTimestamps[cacheKey] = Date()
        
        // 🔑 缓存清理（保持最多50个缓存项）
        if postCache.count > 50 {
            cleanupOldCache()
        }
        
        return posts
    }
    
    /// 🔑 智能缓存清理
    private func cleanupOldCache() {
        let now = Date()
        let expiredKeys = cacheTimestamps.compactMap { key, timestamp in
            now.timeIntervalSince(timestamp) > 300 ? key : nil
        }
        
        for key in expiredKeys {
            postCache.removeValue(forKey: key)
            cacheTimestamps.removeValue(forKey: key)
        }
        
        // 如果还是太多，删除最老的缓存
        if postCache.count > 50 {
            let sortedKeys = cacheTimestamps.sorted { $0.value < $1.value }.map { $0.key }
            for key in sortedKeys.prefix(postCache.count - 40) {
                postCache.removeValue(forKey: key)
                cacheTimestamps.removeValue(forKey: key)
            }
        }
    }
    
    /// 🔑 缓存失效（在创建、更新、删除帖子时调用）
    private func invalidatePostCache() {
        postCache.removeAll()
        cacheTimestamps.removeAll()
    }
    
    // MARK: - Cache Properties（添加缓存属性）
    
    private var postCache: [String: [EACommunityPost]] = [:]
    private var cacheTimestamps: [String: Date] = [:]
    
    // MARK: - 初始化
    
    /// ✅ 修复：添加sessionManager依赖注入
    /// - Parameters:
    ///   - repository: 社区数据仓库
    ///   - sessionManager: 会话管理器
    init(repository: EACommunityRepositoryProtocol, sessionManager: EASessionManager) {
        self.repository = repository
        self.sessionManager = sessionManager
    }
    
    /// ✅ 新增：设置星际能量服务
    /// - Parameter stellarEnergyService: 星际能量服务实例
    func setStellarEnergyService(_ stellarEnergyService: EAStellarEnergyService) {
        self.stellarEnergyService = stellarEnergyService
    }
    
    /// ✅ 修复：更新便利构造器，移除sessionManager参数传递给Repository
    /// - Parameters:
    ///   - modelContext: SwiftData模型上下文
    ///   - sessionManager: 会话管理器
    convenience init(modelContext: ModelContext, sessionManager: EASessionManager) {
        // ✅ 修复：Repository不再需要sessionManager，保持纯净的数据访问层
        let repository = EARepositoryFactory.createCommunityRepository(modelContext: modelContext)
        self.init(repository: repository, sessionManager: sessionManager)
    }
    
    // MARK: - 帖子管理（性能优化版）
    
    /// 创建新帖子
    /// ✅ 优化：增强参数验证和性能监控
    /// - Parameters:
    ///   - content: 帖子内容
    ///   - habitName: 相关习惯名称（可选）
    ///   - imageURLs: 图片URL数组（可选）
    /// - Returns: 创建的帖子
    func createPost(
        content: String,
        habitName: String? = nil,
        imageURLs: [String] = []
    ) async throws -> EACommunityPost {
        // ✅ 优化：增强输入验证
        guard !content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw CommunityServiceError.invalidInput("帖子内容不能为空")
        }
        
        guard content.count <= 1000 else {
            throw CommunityServiceError.invalidInput("帖子内容不能超过1000字符")
        }
        
        operationCount += 1
        lastOperationTime = Date()
        
        do {
            // 创建帖子对象
            let post = EACommunityPost(
                title: "分享我的习惯进展", // 默认标题，可以后续优化为基于内容自动生成
                content: content,
                habitName: habitName,
                category: habitName != nil ? "achievement" : "general",
                energyLevel: 5
            )
            
            // 设置图片URL
            post.imageURLs = imageURLs
            
            // 🔑 重构：增强用户验证（使用ID检查）
            guard let currentUserId = sessionManager.currentUserID else {
                throw CommunityServiceError.userNotFound
            }
            
            // 通过Repository创建帖子
            let createdPost = try await repository.createPost(post, authorId: currentUserId)
            
            // ✅ 新增：发布帖子后奖励星际能量
            await awardPostCreationEnergy(userId: currentUserId, postType: post.category)
            
            return createdPost
        } catch {
            let serviceError: CommunityServiceError
            if let repoError = error as? EACommunityRepositoryError {
                serviceError = .postCreationFailed(repoError.localizedDescription)
            } else if let existingServiceError = error as? CommunityServiceError {
                serviceError = existingServiceError
            } else {
                serviceError = .postCreationFailed(error.localizedDescription)
            }
            lastError = serviceError
            throw serviceError
        }
    }
    
    /// 获取帖子列表（分页）
    /// ✅ 优化：增强缓存策略和性能监控
    /// - Parameters:
    ///   - page: 页码（从0开始）
    ///   - pageSize: 每页数量
    /// - Returns: 帖子列表
    func fetchPosts(page: Int = 0, pageSize: Int? = nil) async throws -> [EACommunityPost] {
        // ✅ 优化：参数验证
        guard page >= 0 else {
            throw CommunityServiceError.invalidInput("页码不能为负数")
        }
        
        operationCount += 1
        lastOperationTime = Date()
        
        do {
            let size = min(pageSize ?? defaultPageSize, 50) // 限制最大页面大小
            let offset = page * size
            
            // 通过Repository获取帖子
            let posts = try await repository.fetchPosts(limit: size, offset: offset)
            
            // ✅ 优化：在Repository层已经过滤，这里不需要重复过滤
            return posts
        } catch {
            let serviceError: CommunityServiceError
            if let repoError = error as? EACommunityRepositoryError {
                serviceError = .fetchFailed(repoError.localizedDescription)
            } else {
                serviceError = .fetchFailed(error.localizedDescription)
            }
            lastError = serviceError
            throw serviceError
        }
    }
    
    /// ✅ 新增：获取用户帖子（优化版）
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - limit: 限制数量
    ///   - includeHidden: 是否包含隐藏帖子
    /// - Returns: 用户帖子列表
    func fetchUserPosts(userId: UUID, limit: Int = 20, includeHidden: Bool = false) async throws -> [EACommunityPost] {
        operationCount += 1
        lastOperationTime = Date()
        
        do {
            // 直接使用优化的Repository方法
            return try await repository.fetchUserPosts(userId: userId, limit: limit, includeHidden: includeHidden)
        } catch {
            let serviceError: CommunityServiceError
            if let repoError = error as? EACommunityRepositoryError {
                serviceError = .fetchFailed(repoError.localizedDescription)
            } else {
                serviceError = .fetchFailed(error.localizedDescription)
            }
            lastError = serviceError
            throw serviceError
        }
    }
    
    /// 搜索帖子
    /// ✅ 优化：增强搜索性能和结果质量
    /// - Parameter query: 搜索关键词
    /// - Returns: 匹配的帖子列表
    func searchPosts(query: String) async throws -> [EACommunityPost] {
        // ✅ 优化：增强搜索参数验证
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedQuery.isEmpty else {
            return []
        }
        
        guard trimmedQuery.count >= 2 else {
            throw CommunityServiceError.invalidInput("搜索关键词至少需要2个字符")
        }
        
        operationCount += 1
        lastOperationTime = Date()
        
        do {
            // 通过Repository搜索帖子
            let posts = try await repository.searchPosts(query: trimmedQuery, limit: 30) // 减少搜索结果数量
            
            // ✅ 优化：在Repository层已经处理，这里不需要重复过滤
            return posts
        } catch {
            let serviceError: CommunityServiceError
            if let repoError = error as? EACommunityRepositoryError {
                serviceError = .searchFailed(repoError.localizedDescription)
            } else {
                serviceError = .searchFailed(error.localizedDescription)
            }
            lastError = serviceError
            throw serviceError
        }
    }
    
    /// 删除帖子
    /// ✅ 优化：增强权限验证和错误处理
    /// - Parameter post: 要删除的帖子
    func deletePost(_ post: EACommunityPost) async throws {
        operationCount += 1
        lastOperationTime = Date()
        
        do {
            // 🔑 重构：使用依赖注入的sessionManager替代单例（使用ID检查）
            guard let currentUserId = sessionManager.currentUserID,
                  post.getAuthor()?.id == currentUserId else {
                throw CommunityServiceError.insufficientPermissions
            }
            
            // 软删除：设置为不可见
            post.isVisible = false
            post.lastEditDate = Date()
            
            // 通过Repository更新帖子
            _ = try await repository.updatePost(post)
        } catch let error as CommunityServiceError {
            lastError = error
            throw error
        } catch {
            let serviceError: CommunityServiceError
            if let repoError = error as? EACommunityRepositoryError {
                serviceError = .deletionFailed(repoError.localizedDescription)
            } else {
                serviceError = .deletionFailed(error.localizedDescription)
            }
            lastError = serviceError
            throw serviceError
        }
    }
    
    // MARK: - 点赞管理
    
    /// 🔑 新增：点赞帖子（单独操作）
    /// - Parameter postId: 帖子ID
    /// - Throws: 点赞操作相关错误
    func likePost(_ postId: UUID) async throws {
        do {
            // 🔑 重构：使用依赖注入的sessionManager替代单例（使用ID检查）
            guard let currentUserId = sessionManager.currentUserID else {
                throw CommunityServiceError.userNotFound
            }
            
            // 🔑 修复：检查当前点赞状态
            let likes = try await repository.fetchLikes(for: postId)
            let isAlreadyLiked = likes.contains { like in
                guard let likeUserId = like.user?.id else { return false }
                return likeUserId == currentUserId && like.isActive
            }
            
            // 如果已经点赞，直接返回
            if isAlreadyLiked {
                return
            }
            
            // 🔑 修复：使用toggleLike方法执行点赞操作
            let _ = try await repository.toggleLike(postId: postId, userId: currentUserId)
            
            // 用户点赞时奖励能量
            await awardInteractionEnergy(userId: currentUserId, actionType: .giveLike)
            
        } catch let error as CommunityServiceError {
            lastError = error
            throw error
        } catch {
            let serviceError: CommunityServiceError
            if let repoError = error as? EACommunityRepositoryError {
                serviceError = .likeFailed(repoError.localizedDescription)
            } else {
                serviceError = .likeFailed(error.localizedDescription)
            }
            lastError = serviceError
            throw serviceError
        }
    }
    
    /// 🔑 新增：取消点赞帖子（单独操作）
    /// - Parameter postId: 帖子ID
    /// - Throws: 取消点赞操作相关错误
    func unlikePost(_ postId: UUID) async throws {
        do {
            // 🔑 重构：使用依赖注入的sessionManager替代单例（使用ID检查）
            guard let currentUserId = sessionManager.currentUserID else {
                throw CommunityServiceError.userNotFound
            }
            
            // 🔑 修复：检查当前点赞状态
            let likes = try await repository.fetchLikes(for: postId)
            let isLiked = likes.contains { like in
                guard let likeUserId = like.user?.id else { return false }
                return likeUserId == currentUserId && like.isActive
            }
            
            // 如果没有点赞，直接返回
            if !isLiked {
                return
            }
            
            // 🔑 修复：使用toggleLike方法执行取消点赞操作
            let _ = try await repository.toggleLike(postId: postId, userId: currentUserId)
            
            // 取消点赞时减少能量
            await deductInteractionEnergy(userId: currentUserId, actionType: .giveLike)
            
        } catch let error as CommunityServiceError {
            lastError = error
            throw error
        } catch {
            let serviceError: CommunityServiceError
            if let repoError = error as? EACommunityRepositoryError {
                serviceError = .likeFailed(repoError.localizedDescription)
            } else {
                serviceError = .likeFailed(error.localizedDescription)
            }
            lastError = serviceError
            throw serviceError
        }
    }
    
    /// 切换帖子点赞状态
    /// - Parameter post: 目标帖子
    /// - Returns: 当前点赞状态
    func togglePostLike(_ post: EACommunityPost) async throws -> Bool {
        do {
            // 🔑 重构：使用依赖注入的sessionManager替代单例（使用ID检查）
            guard let currentUserId = sessionManager.currentUserID else {
                throw CommunityServiceError.userNotFound
            }
            
            // 🔑 修复：先获取当前点赞状态
            let wasLiked = try await checkPostLikeStatus(post)
            
            // 通过Repository切换点赞状态（返回新的点赞数量）
            let _ = try await repository.toggleLike(postId: post.id, userId: currentUserId)
            
            // ✅ 修复：点赞/取消点赞的能量逻辑
            if !wasLiked {
                // 用户点赞时奖励能量
                await awardInteractionEnergy(userId: currentUserId, actionType: .giveLike)
            } else {
                // ✅ 新增：用户取消点赞时减少能量
                await deductInteractionEnergy(userId: currentUserId, actionType: .giveLike)
            }
            
            // 🔑 修复：返回切换后的状态（与之前相反）
            return !wasLiked
        } catch let error as CommunityServiceError {
            lastError = error
            throw error
        } catch {
            let serviceError: CommunityServiceError
            if let repoError = error as? EACommunityRepositoryError {
                serviceError = .likeFailed(repoError.localizedDescription)
            } else {
                serviceError = .likeFailed(error.localizedDescription)
            }
            lastError = serviceError
            throw serviceError
        }
    }
    
    /// 检查用户是否点赞了指定帖子
    /// - Parameter post: 目标帖子
    /// - Returns: 是否已点赞
    func checkPostLikeStatus(_ post: EACommunityPost) async throws -> Bool {
        do {
            // 🔑 重构：使用依赖注入的sessionManager替代单例（使用ID检查）
            guard let currentUserId = sessionManager.currentUserID else {
                return false
            }
            
            // 只检查isActive为true的点赞记录
            let likes = try await repository.fetchLikes(for: post.id)
            return likes.contains { like in
                guard let likeUserId = like.user?.id else { return false }
                return likeUserId == currentUserId && like.isActive
            }
        } catch {
            return false // 查询失败时默认为未点赞
        }
    }
    
    // MARK: - 评论管理
    
    /// 为帖子添加评论
    /// - Parameters:
    ///   - post: 目标帖子
    ///   - content: 评论内容
    ///   - replyToUsername: 回复的用户名（可选）
    /// - Returns: 创建的评论
    func addComment(
        to post: EACommunityPost,
        content: String,
        replyToUsername: String? = nil
    ) async throws -> EACommunityComment {
        do {
            // 🔑 重构：获取当前用户ID（使用ID检查）
            guard let currentUserId = sessionManager.currentUserID else {
                throw CommunityServiceError.userNotFound
            }
            
            // 创建评论对象
            let comment = EACommunityComment(
                content: content,
                replyToUsername: replyToUsername
            )
            
            // 通过Repository创建评论
            let createdComment = try await repository.createComment(comment, for: post.id, authorId: currentUserId)
            
            // ✅ 新增：发表评论后奖励星际能量
            await awardInteractionEnergy(userId: currentUserId, actionType: .giveComment)
            
            return createdComment
        } catch let error as CommunityServiceError {
            lastError = error
            throw error
        } catch {
            let serviceError: CommunityServiceError
            if let repoError = error as? EACommunityRepositoryError {
                serviceError = .commentCreationFailed(repoError.localizedDescription)
            } else {
                serviceError = .commentCreationFailed(error.localizedDescription)
            }
            lastError = serviceError
            throw serviceError
        }
    }
    
    /// 获取帖子的评论列表
    /// - Parameter post: 目标帖子
    /// - Returns: 评论列表
    func fetchComments(for post: EACommunityPost) async throws -> [EACommunityComment] {
        do {
            // 通过Repository获取评论
            let comments = try await repository.fetchComments(for: post.id)
            
            // 过滤可见评论
            return comments.filter { $0.isVisible }
        } catch {
            let serviceError: CommunityServiceError
            if let repoError = error as? EACommunityRepositoryError {
                serviceError = .fetchFailed(repoError.localizedDescription)
            } else {
                serviceError = .fetchFailed(error.localizedDescription)
            }
            lastError = serviceError
            throw serviceError
        }
    }
    
    // MARK: - 星际能量计算业务逻辑
    
    /// 计算用户星际能量增益（优化版）
    /// 根据用户行为类型计算相应的星际能量奖励
    /// - Parameters:
    ///   - actionType: 行为类型（发帖、点赞、评论等）
    ///   - userId: 用户ID
    /// - Returns: 星际能量增益值
    func calculateStellarEnergyGain(for actionType: StellarEnergyActionType, userId: UUID) async throws -> Int {
        operationCount += 1
        lastOperationTime = Date()
        
        do {
            // 获取用户当前数据
            guard let user = try await repository.fetchUser(by: userId) else {
                throw CommunityServiceError.userNotFound
            }
            
            // 获取用户社交档案
            guard let socialProfile = user.socialProfile else {
                throw CommunityServiceError.dataCorruption
            }
            
            // ✅ 优化：使用新的基础能量值
            let baseEnergy = actionType.baseEnergyValue
            
            // 根据用户等级计算倍数奖励
            let levelMultiplier = calculateLevelMultiplier(for: socialProfile.stellarLevel ?? 1)
            
            // 连续活跃奖励
            let streakBonus = calculateStreakBonus(for: socialProfile)
            
            // ✅ 新增：特殊行为额外奖励
            let specialBonus = calculateSpecialBonus(for: actionType, socialProfile: socialProfile)
            
            // 最终能量值
            let finalEnergy = Int(Double(baseEnergy) * levelMultiplier) + streakBonus + specialBonus
            
            return max(finalEnergy, 1) // 确保至少获得1点能量
            
        } catch let error as CommunityServiceError {
            lastError = error
            throw error
        } catch {
            let serviceError = CommunityServiceError.fetchFailed("星际能量计算失败: \(error.localizedDescription)")
            lastError = serviceError
            throw serviceError
        }
    }
    
    /// 更新用户星际等级
    /// 根据用户当前总星际能量更新等级
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - energyGain: 新增的星际能量
    /// - Returns: 更新后的用户等级
    func updateUserStellarLevel(userId: UUID, energyGain: Int) async throws -> Int {
        operationCount += 1
        lastOperationTime = Date()
        
        do {
            // 获取用户数据
            guard let user = try await repository.fetchUser(by: userId) else {
                throw CommunityServiceError.userNotFound
            }
            
            // 获取用户社交档案
            guard let socialProfile = user.socialProfile else {
                throw CommunityServiceError.dataCorruption
            }
            
            // 更新总星际能量
            let currentEnergy = socialProfile.totalStellarEnergy ?? 0
            let newTotalEnergy = currentEnergy + energyGain
            socialProfile.totalStellarEnergy = newTotalEnergy
            
            // 计算新等级
            let newLevel = calculateStellarLevel(from: newTotalEnergy)
            let oldLevel = socialProfile.stellarLevel ?? 1
            
            // 更新等级
            socialProfile.stellarLevel = newLevel
            socialProfile.lastEnergyUpdateDate = Date()
            
            // 如果等级提升，记录里程碑
            if newLevel > oldLevel {
                socialProfile.levelUpDate = Date()
                // 可以在这里触发等级提升通知或奖励
            }
            
            // 通过Repository保存更新
            _ = try await repository.updateUserSocialProfile(socialProfile)
            
            return newLevel
            
        } catch let error as CommunityServiceError {
            lastError = error
            throw error
        } catch {
            let serviceError = CommunityServiceError.fetchFailed("星际等级更新失败: \(error.localizedDescription)")
            lastError = serviceError
            throw serviceError
        }
    }
    
    /// 处理用户行为并更新星际能量
    /// 综合处理用户行为，计算并更新星际能量和等级
    /// - Parameters:
    ///   - actionType: 行为类型
    ///   - userId: 用户ID
    /// - Returns: (能量增益, 新等级, 是否等级提升)
    func processUserActionForStellarEnergy(
        actionType: StellarEnergyActionType,
        userId: UUID
    ) async throws -> (energyGain: Int, newLevel: Int, leveledUp: Bool) {
        
        do {
            // 获取当前等级
            guard let user = try await repository.fetchUser(by: userId),
                  let socialProfile = user.socialProfile else {
                throw CommunityServiceError.userNotFound
            }
            
            let oldLevel = socialProfile.stellarLevel ?? 1
            
            // 计算能量增益
            let energyGain = try await calculateStellarEnergyGain(for: actionType, userId: userId)
            
            // 更新等级
            let newLevel = try await updateUserStellarLevel(userId: userId, energyGain: energyGain)
            
            // 检查是否等级提升
            let leveledUp = newLevel > oldLevel
            
            return (energyGain: energyGain, newLevel: newLevel, leveledUp: leveledUp)
            
        } catch {
            throw error
        }
    }
    
    // MARK: - 私有辅助方法
    
    /// 计算等级倍数
    private func calculateLevelMultiplier(for level: Int) -> Double {
        switch level {
        case 1...3:
            return 1.0 // 新手探索者
        case 4...6:
            return 1.2 // 星际旅者
        case 7...10:
            return 1.5 // 宇宙领航员
        default:
            return 2.0 // 传奇探索者
        }
    }
    
    /// 计算连续活跃奖励
    private func calculateStreakBonus(for socialProfile: EAUserSocialProfile) -> Int {
        // 简化的连续活跃计算，可以根据需要扩展
        let daysSinceLastUpdate = Calendar.current.dateComponents([.day], 
            from: socialProfile.lastEnergyUpdateDate ?? Date().addingTimeInterval(-86400), 
            to: Date()).day ?? 0
        
        if daysSinceLastUpdate <= 1 {
            return 10 // 连续活跃奖励
        }
        return 0
    }
    
    /// 根据总星际能量计算等级
    private func calculateStellarLevel(from totalEnergy: Int) -> Int {
        switch totalEnergy {
        case 0...999:
            return min(3, max(1, totalEnergy / 333 + 1)) // 新手探索者 (1-3级)
        case 1000...4999:
            return min(6, 4 + (totalEnergy - 1000) / 1000) // 星际旅者 (4-6级)
        case 5000...14999:
            return min(10, 7 + (totalEnergy - 5000) / 2500) // 宇宙领航员 (7-10级)
        default:
            return min(15, 11 + (totalEnergy - 15000) / 5000) // 传奇探索者 (11-15级)
        }
    }
    
    /// 计算特殊行为额外奖励
    private func calculateSpecialBonus(for actionType: StellarEnergyActionType, socialProfile: EAUserSocialProfile) -> Int {
        // 实现特殊行为额外奖励的计算逻辑
        // 这里可以根据实际需求进行实现
        return 0 // 默认返回0，可以根据实际需求修改
    }
    
    // MARK: - 星际能量奖励方法
    
    /// 奖励发布帖子的星际能量
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - postType: 帖子类型
    private func awardPostCreationEnergy(userId: UUID, postType: String) async {
        do {
            let actionType: StellarEnergyActionType = postType == "achievement" ? .createHabitPost : .createPost
            
            // ✅ 新增：通过星际能量服务进行防刷检查和能量奖励
            guard let stellarEnergyService = self.stellarEnergyService else {
                logger.warning("星际能量服务未初始化，跳过发布帖子能量奖励")
                return
            }
            
            // 使用星际能量服务的防刷机制
            let result = try await stellarEnergyService.processUserActionForStellarEnergy(
                actionType: actionType,
                userId: userId
            )
            
            // 记录日志
            logger.info("成功奖励发布帖子能量: \(result.energyGain)星际能量，用户等级: \(result.newLevel)")
            
            if result.leveledUp {
                logger.info("用户等级提升至: \(result.newLevel)")
            }
            
        } catch {
            logger.error("奖励发布帖子能量失败: \(error.localizedDescription)")
        }
    }
    
    /// 奖励社区互动的星际能量
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - actionType: 互动类型
    private func awardInteractionEnergy(userId: UUID, actionType: StellarEnergyActionType) async {
        do {
            // ✅ 新增：通过星际能量服务进行防刷检查和能量奖励
            guard let stellarEnergyService = self.stellarEnergyService else {
                logger.warning("星际能量服务未初始化，跳过社区互动能量奖励")
                return
            }
            
            // 使用星际能量服务的防刷机制
            let result = try await stellarEnergyService.processUserActionForStellarEnergy(
                actionType: actionType,
                userId: userId
            )
            
            // 记录日志
            logger.info("成功奖励社区互动能量: \(result.energyGain)星际能量，行为类型: \(actionType.rawValue)")
            
            if result.leveledUp {
                logger.info("用户等级提升至: \(result.newLevel)")
            }
            
        } catch {
            logger.error("奖励社区互动能量失败: \(error.localizedDescription)")
        }
    }
    
    /// ✅ 新增：减少社区互动的星际能量（取消互动时调用）
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - actionType: 互动类型
    private func deductInteractionEnergy(userId: UUID, actionType: StellarEnergyActionType) async {
        guard let stellarEnergyService = self.stellarEnergyService else {
            logger.warning("星际能量服务未初始化，跳过社区互动能量减少")
            return
        }

        // 将StellarEnergyActionType转换为EAInteractionType
        let interactionType: EAInteractionType
        switch actionType {
        case .giveLike:
            interactionType = .like
        case .giveComment:
            interactionType = .comment
        default:
            logger.warning("不支持的互动类型减少能量: \(actionType.rawValue)")
            return
        }

        // 使用星际能量服务减少能量
        let energyDeducted = await stellarEnergyService.deductInteractionEnergy(
            interactionType: interactionType,
            userId: userId
        )

        // 记录日志
        logger.info("成功减少社区互动能量: \(energyDeducted)星际能量，行为类型: \(actionType.rawValue)")
    }
    
    // MARK: - 性能监控方法
    
    /// ✅ 新增：获取服务性能统计
    func getPerformanceStatistics() -> (operationCount: Int, lastOperationTime: Date) {
        return (operationCount: operationCount, lastOperationTime: lastOperationTime)
    }
    
    /// ✅ 新增：重置性能统计
    func resetPerformanceStatistics() {
        operationCount = 0
        lastOperationTime = Date()
    }
}

// MARK: - 错误定义

/// 社区服务错误类型
enum CommunityServiceError: LocalizedError {
    case userNotLoggedIn
    case userNotFound
    case insufficientPermissions
    case postNotFound
    case postCreationFailed(String)
    case deletionFailed(String)
    case fetchFailed(String)
    case searchFailed(String)
    case likeFailed(String)
    case commentCreationFailed(String)
    case networkError
    case dataCorruption
    case invalidInput(String)
    
    var errorDescription: String? {
        switch self {
        case .userNotLoggedIn:
            return "用户未登录"
        case .userNotFound:
            return "用户不存在"
        case .insufficientPermissions:
            return "权限不足"
        case .postNotFound:
            return "帖子不存在"
        case .postCreationFailed(let details):
            return "创建帖子失败：\(details)"
        case .deletionFailed(let details):
            return "删除失败：\(details)"
        case .fetchFailed(let details):
            return "加载数据失败：\(details)"
        case .searchFailed(let details):
            return "搜索失败：\(details)"
        case .likeFailed(let details):
            return "点赞操作失败：\(details)"
        case .commentCreationFailed(let details):
            return "创建评论失败：\(details)"
        case .networkError:
            return "网络连接错误"
        case .dataCorruption:
            return "数据异常"
        case .invalidInput(let details):
            return "输入无效：\(details)"
        }
    }
} 