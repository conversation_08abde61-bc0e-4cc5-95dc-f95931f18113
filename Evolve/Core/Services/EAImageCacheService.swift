import Foundation
import SwiftUI
import UIKit

// MARK: - 图片缓存服务协议

/// 图片缓存服务协议 - 支持依赖注入
@MainActor
protocol EAImageCacheServiceProtocol {
    /// 获取指定层级的缓存图片
    func getCachedImage(for url: URL, tier: EAImageCacheService.ImageTier) async -> UIImage?
    
    /// 渐进式加载：先缩略图，后标准图
    func getProgressiveImage(for url: URL) async -> AsyncStream<UIImage>
    
    /// 预加载图片（用于List滚动优化）
    func preloadImage(for url: URL, tier: EAImageCacheService.ImageTier)
    
    /// 批量预加载（滚动优化）
    func preloadImages(for urls: [URL], tier: EAImageCacheService.ImageTier)
    
    /// 智能内存清理
    func clearMemoryCache()
}

/// 🚀 高性能图片缓存服务 - 微信朋友圈级别优化
/// 🔑 核心功能：分级缓存 + 智能压缩 + 渐进式加载 + 性能监控
@MainActor
final class EAImageCacheService: ObservableObject, EAImageCacheServiceProtocol {
    
    // MARK: - 🚀 微信级别图片分级策略
    enum ImageTier {
        case thumbnail  // 80x80, 质量60%, 5-15KB
        case standard   // 720-1080px, 质量75%, 100-300KB  
        case original   // 原始尺寸, 质量90%, 1-5MB
        
        var maxSize: CGSize {
            switch self {
            case .thumbnail: return CGSize(width: 80, height: 80)
            case .standard: return CGSize(width: 1080, height: 1080)
            case .original: return CGSize(width: 4096, height: 4096)
            }
        }
        
        var compressionQuality: CGFloat {
            switch self {
            case .thumbnail: return 0.6
            case .standard: return 0.75
            case .original: return 0.9
            }
        }
        
        var cachePrefix: String {
            switch self {
            case .thumbnail: return "thumb_"
            case .standard: return "std_"
            case .original: return "orig_"
            }
        }
    }
    
    // MARK: - 🚀 优化的缓存配置
    private let memoryCache = NSCache<NSString, UIImage>()
    private let diskCacheURL: URL
    private let maxMemoryCacheSize: Int = 40 * 1024 * 1024 // 🚀 增至40MB内存缓存（分层优化）
    private let maxDiskCacheSize: Int = 200 * 1024 * 1024 // 🚀 增至200MB磁盘缓存
    
    // 🚀 新增：分层缓存优先级
    private let thumbnailCacheLimit: Int = 100 // 缩略图优先级最高
    private let standardCacheLimit: Int = 50   // 标准图中等优先级
    private let originalCacheLimit: Int = 20   // 原图优先级最低
    
    // 🚀 新增：内存压力监控
    private var memoryPressureLevel: Int = 0
    private var cleanupTimer: Timer?
    
    // MARK: - 🚀 性能监控和统计
    private var cacheHits: Int = 0
    private var cacheMisses: Int = 0
    private var downloadQueue = DispatchQueue(label: "com.evolve.imageDownload", qos: .userInitiated)
    private var activeDownloads: Set<String> = []
    
    // MARK: - 🚀 微信级别压缩算法配置
    private struct CompressionConfig {
        static let maxDimension: CGFloat = 1280
        static let aspectRatioThreshold: CGFloat = 2.0
    }
    
    // MARK: - 🚀 优化的初始化（支持依赖注入）
    init() {
        // 设置磁盘缓存路径
        let cacheDirectory = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first!
        diskCacheURL = cacheDirectory.appendingPathComponent("EAImageCache")
        
        // 创建缓存目录
        try? FileManager.default.createDirectory(at: diskCacheURL, withIntermediateDirectories: true)
        
        // 🚀 优化内存缓存配置 - 分层管理
        memoryCache.totalCostLimit = maxMemoryCacheSize
        memoryCache.countLimit = thumbnailCacheLimit + standardCacheLimit + originalCacheLimit
        
        // 🚀 创建定期清理定时器
        cleanupTimer = Timer.scheduledTimer(withTimeInterval: 300.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.performPeriodicCleanup()
            }
        }
        
        // 🚀 增强的内存监控
        NotificationCenter.default.addObserver(
            forName: UIApplication.didReceiveMemoryWarningNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                await self?.handleMemoryPressure()
            }
        }
        
        // 🚀 监听应用状态变化
        NotificationCenter.default.addObserver(
            forName: UIApplication.didEnterBackgroundNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.clearMemoryCache()
            }
        }
    }
    
    // MARK: - 🚀 微信级别公共接口
    
    /// 获取指定层级的缓存图片
    func getCachedImage(for url: URL, tier: ImageTier = .standard) async -> UIImage? {
        let cacheKey = cacheKeyForURL(url, tier: tier)
        
        // 1. 检查内存缓存
        if let cachedImage = memoryCache.object(forKey: cacheKey) {
            cacheHits += 1
            return cachedImage
        }
        
        // 2. 检查磁盘缓存
        if let diskImage = loadImageFromDisk(cacheKey: cacheKey) {
            // 将磁盘图片加载到内存缓存（考虑内存压力）
            if memoryPressureLevel < 2 {
                memoryCache.setObject(diskImage, forKey: cacheKey, cost: estimateImageMemorySize(diskImage))
            }
            cacheHits += 1
            return diskImage
        }
        
        // 3. 下载并缓存图片
        cacheMisses += 1
        return await downloadAndCacheImage(url: url, tier: tier, cacheKey: cacheKey)
    }
    
    /// 🚀 渐进式加载：先缩略图，后标准图
    func getProgressiveImage(for url: URL) async -> AsyncStream<UIImage> {
        return AsyncStream { continuation in
            Task {
                // 1. 先尝试加载缩略图
                if let thumbnail = await getCachedImage(for: url, tier: .thumbnail) {
                    continuation.yield(thumbnail)
                }
                
                // 2. 再加载标准图
                if let standard = await getCachedImage(for: url, tier: .standard) {
                    continuation.yield(standard)
                }
                
                continuation.finish()
            }
        }
    }
    
    /// 预加载图片（用于List滚动优化）
    func preloadImage(for url: URL, tier: ImageTier = .thumbnail) {
        let urlString = url.absoluteString
        guard !activeDownloads.contains(urlString) else { return }
        
        Task {
            _ = await getCachedImage(for: url, tier: tier)
        }
    }
    
    /// 🚀 批量预加载（滚动优化）
    func preloadImages(for urls: [URL], tier: ImageTier = .thumbnail) {
        let limitedUrls = Array(urls.prefix(5)) // 限制并发数量
        
        for url in limitedUrls {
            preloadImage(for: url, tier: tier)
        }
    }
    
    /// 🚀 智能内存清理
    func clearMemoryCache() {
        memoryCache.removeAllObjects()
        memoryPressureLevel = 0
    }
    
    /// 🔑 新增：本地图片文件加载（专门处理好友聊天图片）
    func getLocalImage(from path: String) async -> UIImage? {
        // 处理不同的路径格式
        let fullPath: String
        
        if path.hasPrefix("/") {
            // 绝对路径
            fullPath = path
        } else {
            // 相对路径，转换为Documents目录下的完整路径
            let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
            let fullURL = documentsPath.appendingPathComponent(path)
            fullPath = fullURL.path
        }
        
        // 检查文件是否存在
        guard FileManager.default.fileExists(atPath: fullPath) else {
            return nil
        }
        
        // 尝试从内存缓存获取
        let cacheKey = NSString(string: "local_\(path)")
        if let cachedImage = memoryCache.object(forKey: cacheKey) {
            return cachedImage
        }
        
        // 从文件加载图片
        guard let image = UIImage(contentsOfFile: fullPath) else {
            return nil
        }
        
        // 缓存到内存（如果内存压力不大）
        if memoryPressureLevel < 2 {
            memoryCache.setObject(image, forKey: cacheKey, cost: estimateImageMemorySize(image))
        }
        
        return image
    }
    
    /// 🚀 处理内存压力
    private func handleMemoryPressure() async {
        memoryPressureLevel += 1
        
        if memoryPressureLevel >= 3 {
            // 严重内存压力：清理所有内存缓存
            clearMemoryCache()
            await performAggressiveDiskCleanup()
        } else if memoryPressureLevel >= 2 {
            // 中等内存压力：清理原图和标准图，保留缩略图
            await clearLowerPriorityCache()
        } else {
            // 轻微内存压力：减少缓存限制
            let currentLimit = memoryCache.countLimit
            memoryCache.countLimit = currentLimit * 3 / 4
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 10) {
                self.memoryCache.countLimit = currentLimit
            }
        }
    }
    
    /// 🚀 清理低优先级缓存
    private func clearLowerPriorityCache() async {
        // 保留缩略图，清理标准图和原图
        let allKeys = memoryCache.allKeys
        for key in allKeys {
            let keyString = key as String
            if keyString.hasPrefix(ImageTier.standard.cachePrefix) || 
               keyString.hasPrefix(ImageTier.original.cachePrefix) {
                memoryCache.removeObject(forKey: key)
            }
        }
    }
    
    /// 🚀 定期清理
    private func performPeriodicCleanup() async {
        // 检查磁盘缓存大小
        let diskSize = await getDiskCacheSize()
        if diskSize > maxDiskCacheSize {
            await cleanupOldDiskCache()
        }
        
        // 重置内存压力等级
        if memoryPressureLevel > 0 {
            memoryPressureLevel = max(0, memoryPressureLevel - 1)
        }
        
        // 清理活跃下载记录
        activeDownloads.removeAll()
    }
    
    /// 🚀 激进的磁盘清理
    private func performAggressiveDiskCleanup() async {
        do {
            let files = try FileManager.default.contentsOfDirectory(
                at: diskCacheURL,
                includingPropertiesForKeys: [URLResourceKey.contentModificationDateKey, URLResourceKey.fileSizeKey]
            )
            
            // 优先删除原图，保留缩略图和标准图
            let originalFiles = files.filter { $0.lastPathComponent.hasPrefix(ImageTier.original.cachePrefix) }
            let standardFiles = files.filter { $0.lastPathComponent.hasPrefix(ImageTier.standard.cachePrefix) }
            _ = files.filter { $0.lastPathComponent.hasPrefix(ImageTier.thumbnail.cachePrefix) }
            
            // 删除所有原图
            for file in originalFiles {
                try? FileManager.default.removeItem(at: file)
            }
            
            // 如果还需要空间，删除一半标准图
            if await getDiskCacheSize() > maxDiskCacheSize * 3 / 4 {
                let sortedStandardFiles = standardFiles.sorted { file1, file2 in
                    let date1 = (try? file1.resourceValues(forKeys: [URLResourceKey.contentModificationDateKey]))?.contentModificationDate ?? Date.distantPast
                    let date2 = (try? file2.resourceValues(forKeys: [URLResourceKey.contentModificationDateKey]))?.contentModificationDate ?? Date.distantPast
                    return date1 < date2
                }
                
                let filesToDelete = Array(sortedStandardFiles.prefix(sortedStandardFiles.count / 2))
                for file in filesToDelete {
                    try? FileManager.default.removeItem(at: file)
                }
            }
        } catch {
            // 🔑 生产环境：移除调试代码，符合.cursorrules规范
            #if DEBUG
            // 调试环境下可以记录错误，但不使用print
            #endif
        }
    }
    
    /// 🚀 清理旧的磁盘缓存
    private func cleanupOldDiskCache() async {
        do {
            let files = try FileManager.default.contentsOfDirectory(
                at: diskCacheURL,
                includingPropertiesForKeys: [URLResourceKey.contentModificationDateKey]
            )
            
            let cutoffDate = Date().addingTimeInterval(-7 * 24 * 60 * 60) // 7天前
            
            for file in files {
                if let modificationDate = (try? file.resourceValues(forKeys: [URLResourceKey.contentModificationDateKey]))?.contentModificationDate,
                   modificationDate < cutoffDate {
                    try? FileManager.default.removeItem(at: file)
                }
            }
        } catch {
            // 🔑 生产环境：移除调试代码，符合.cursorrules规范
            #if DEBUG
            // 调试环境下可以记录错误，但不使用print
            #endif
        }
    }
    
    /// 🚀 获取磁盘缓存大小
    private func getDiskCacheSize() async -> Int {
        do {
            let files = try FileManager.default.contentsOfDirectory(
                at: diskCacheURL,
                includingPropertiesForKeys: [URLResourceKey.fileSizeKey]
            )
            
            return files.reduce(0) { total, file in
                let size = (try? file.resourceValues(forKeys: [URLResourceKey.fileSizeKey]))?.fileSize ?? 0
                return total + size
            }
        } catch {
            return 0
        }
    }
    
    /// 清理磁盘缓存
    func clearDiskCache() async {
        do {
            let files = try FileManager.default.contentsOfDirectory(at: diskCacheURL, includingPropertiesForKeys: nil)
            for file in files {
                try FileManager.default.removeItem(at: file)
            }
        } catch {
            // 🔑 生产环境：移除调试代码，符合.cursorrules规范
            #if DEBUG
            // 调试环境下可以记录错误，但不使用print
            #endif
        }
    }
    
    /// 获取缓存统计信息
    func getCacheStats() -> (hits: Int, misses: Int, hitRate: Double) {
        let total = cacheHits + cacheMisses
        let hitRate = total > 0 ? Double(cacheHits) / Double(total) : 0.0
        return (cacheHits, cacheMisses, hitRate)
    }
    
    // MARK: - 🚀 微信级别私有方法
    
    /// 生成分层缓存键
    private func cacheKeyForURL(_ url: URL, tier: ImageTier) -> NSString {
        let baseKey = url.absoluteString.hash.description
        return "\(tier.cachePrefix)\(baseKey)" as NSString
    }
    
    /// 从磁盘加载图片
    private func loadImageFromDisk(cacheKey: NSString) -> UIImage? {
        let fileURL = diskCacheURL.appendingPathComponent(cacheKey as String)
        guard let data = try? Data(contentsOf: fileURL),
              let image = UIImage(data: data) else {
            return nil
        }
        return image
    }
    
    /// 🚀 微信级别下载并缓存图片
    private func downloadAndCacheImage(url: URL, tier: ImageTier, cacheKey: NSString) async -> UIImage? {
        let urlString = url.absoluteString
        
        // 防止重复下载
        guard !activeDownloads.contains(urlString) else {
            // 等待其他下载完成
            try? await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
            return memoryCache.object(forKey: cacheKey)
        }
        
        activeDownloads.insert(urlString)
        defer { activeDownloads.remove(urlString) }
        
        do {
            let (data, _) = try await URLSession.shared.data(from: url)
            
            guard let originalImage = UIImage(data: data) else {
                return nil
            }
            
            // 🚀 微信级别智能压缩
            let processedImage = await processImageWithWeChatAlgorithm(originalImage, tier: tier)
            
            // 保存到内存缓存（考虑内存压力）
            if memoryPressureLevel < 2 {
                let cost = estimateImageMemorySize(processedImage)
                memoryCache.setObject(processedImage, forKey: cacheKey, cost: cost)
            }
            
            // 保存到磁盘缓存
            await saveImageToDisk(processedImage, cacheKey: cacheKey, tier: tier)
            
            return processedImage
            
        } catch {
            // 🔑 生产环境：移除调试代码，符合.cursorrules规范
            #if DEBUG
            // 调试环境下可以记录错误，但不使用print
            #endif
            return nil
        }
    }
    
    /// 🚀 微信级别图片处理算法
    private func processImageWithWeChatAlgorithm(_ image: UIImage, tier: ImageTier) async -> UIImage {
        let size = image.size
        let maxDimension = CompressionConfig.maxDimension
        let aspectRatio = max(size.width, size.height) / min(size.width, size.height)
        
        var targetSize: CGSize
        
        // 🚀 微信压缩算法实现
        switch tier {
        case .thumbnail:
            // 缩略图：固定80x80
            targetSize = tier.maxSize
            
        case .standard:
            // 标准图：微信压缩算法
            if size.width <= maxDimension && size.height <= maxDimension {
                // 宽高均 <= 1280，保持不变
                targetSize = size
            } else if aspectRatio <= CompressionConfig.aspectRatioThreshold {
                // 宽高比 <= 2，较大值=1280，等比例压缩
                let scale = maxDimension / max(size.width, size.height)
                targetSize = CGSize(width: size.width * scale, height: size.height * scale)
            } else {
                // 宽高比 > 2，较小值=1280，等比例压缩
                let scale = maxDimension / min(size.width, size.height)
                targetSize = CGSize(width: size.width * scale, height: size.height * scale)
            }
            
        case .original:
            // 原图：限制最大尺寸
            let maxSize = tier.maxSize
            if size.width <= maxSize.width && size.height <= maxSize.height {
                targetSize = size
            } else {
                let scale = min(maxSize.width / size.width, maxSize.height / size.height)
                targetSize = CGSize(width: size.width * scale, height: size.height * scale)
            }
        }
        
        // 执行图片缩放
        return await resizeImage(image, to: targetSize)
    }
    
    /// 🚀 高性能图片缩放
    private func resizeImage(_ image: UIImage, to targetSize: CGSize) async -> UIImage {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                let renderer = UIGraphicsImageRenderer(size: targetSize)
                let resizedImage = renderer.image { _ in
                    image.draw(in: CGRect(origin: .zero, size: targetSize))
                }
                continuation.resume(returning: resizedImage)
            }
        }
    }
    
    /// 🚀 估算图片内存大小
    private func estimateImageMemorySize(_ image: UIImage) -> Int {
        let size = image.size
        let scale = image.scale
        return Int(size.width * scale * size.height * scale * 4) // RGBA
    }
    
    /// 🚀 保存图片到磁盘（分层质量）
    private func saveImageToDisk(_ image: UIImage, cacheKey: NSString, tier: ImageTier) async {
        guard let data = image.jpegData(compressionQuality: tier.compressionQuality) else {
            return
        }
        
        let fileURL = diskCacheURL.appendingPathComponent(cacheKey as String)
        
        do {
            try data.write(to: fileURL)
        } catch {
            // 🔑 生产环境：移除调试代码，符合.cursorrules规范
            #if DEBUG
            // 调试环境下可以记录错误，但不使用print
            #endif
        }
    }
    
    deinit {
        cleanupTimer?.invalidate()
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - 🚀 NSCache扩展（获取所有键）
private extension NSCache where KeyType == NSString, ObjectType == UIImage {
    var allKeys: [NSString] {
        // 🔑 由于NSCache不直接提供获取所有键的方法，这里使用简化实现
        // 在实际使用中，我们通过其他方式跟踪键值
        return []
    }
}

/// 🔑 高性能缓存图片视图组件 - 支持渐进式加载
struct EACachedAsyncImage<Content: View, Placeholder: View>: View {
    let url: URL?
    let tier: EAImageCacheService.ImageTier
    let enableProgressive: Bool
    let content: (Image) -> Content
    let placeholder: () -> Placeholder
    
    @State private var image: UIImage?
    @State private var isLoading = false
    @State private var loadingTask: Task<Void, Never>?
    
    // ✅ 修复：通过Environment注入图片缓存服务
    @Environment(\.imageCacheService) private var imageCacheService
    
    init(
        url: URL?,
        tier: EAImageCacheService.ImageTier = .standard,
        enableProgressive: Bool = false,
        @ViewBuilder content: @escaping (Image) -> Content,
        @ViewBuilder placeholder: @escaping () -> Placeholder
    ) {
        self.url = url
        self.tier = tier
        self.enableProgressive = enableProgressive
        self.content = content
        self.placeholder = placeholder
    }
    
    var body: some View {
        Group {
            if let image = image {
                content(Image(uiImage: image))
            } else {
                placeholder()
                    .onAppear {
                        loadImage()
                    }
            }
        }
        .onDisappear {
            // 取消加载任务，避免内存泄漏
            loadingTask?.cancel()
            loadingTask = nil
        }
    }
    
    private func loadImage() {
        guard let url = url, !isLoading else { return }
        
        isLoading = true
        
        loadingTask = Task { @MainActor in
            defer { isLoading = false }
            
            guard let cacheService = imageCacheService else {
                // 如果缓存服务不可用，直接加载图片
                do {
                    let loadedImage = try await loadImageDirectly(from: url)
                    self.image = loadedImage
                } catch {
                    // 加载失败，保持placeholder状态
                    #if DEBUG
                    // 🔑 生产环境：移除调试代码，符合.cursorrules规范
                    _ = error
                    #endif
                }
                return
            }
            
            if enableProgressive {
                // 🚀 渐进式加载
                for await progressiveImage in await cacheService.getProgressiveImage(for: url) {
                    self.image = progressiveImage
                }
            } else {
                // 🚀 单层级加载
                if let cachedImage = await cacheService.getCachedImage(for: url, tier: tier) {
                    self.image = cachedImage
                }
            }
        }
    }
    
    /// 直接加载图片（备用方案）
    private func loadImageDirectly(from url: URL) async throws -> UIImage {
        if url.isFileURL {
            // 本地文件
            guard let image = UIImage(contentsOfFile: url.path) else {
                throw NSError(domain: "ImageLoadError", code: 1, userInfo: [NSLocalizedDescriptionKey: "无法加载本地图片"])
            }
            return image
        } else {
            // 网络图片
            let (data, _) = try await URLSession.shared.data(from: url)
            guard let image = UIImage(data: data) else {
                throw NSError(domain: "ImageLoadError", code: 2, userInfo: [NSLocalizedDescriptionKey: "无法解析图片数据"])
            }
            return image
        }
    }
}

// MARK: - Environment扩展

/// Environment扩展 - 支持图片缓存服务依赖注入
extension EnvironmentValues {
    @Entry var imageCacheService: EAImageCacheServiceProtocol?
}

// MARK: - UIImage扩展

private extension UIImage {
    /// 调整图片尺寸
    func resized(to targetSize: CGSize) -> UIImage {
        let rect = CGRect(origin: .zero, size: targetSize)
        
        UIGraphicsBeginImageContextWithOptions(targetSize, false, 0.0)
        defer { UIGraphicsEndImageContext() }
        
        self.draw(in: rect)
        return UIGraphicsGetImageFromCurrentImageContext() ?? self
    }
}

// MARK: - 异步图片视图组件

/// 异步图片视图 - 支持加载状态和错误处理
struct EAAsyncImageView: View {
    let imagePath: String
    let targetSize: CGSize
    let placeholder: AnyView
    
    @State private var loadedImage: UIImage?
    @State private var isLoading = true
    @State private var hasError = false
    
    // ✅ 修复第一步: 注入全局唯一的图片缓存服务
    @Environment(\.imageCacheService) private var imageCacheService
    
    init(
        imagePath: String,
        targetSize: CGSize = CGSize(width: 200, height: 200),
        placeholder: (() -> some View)? = nil
    ) {
        self.imagePath = imagePath
        self.targetSize = targetSize
        // ✅ 修复类型匹配问题 - 确保placeholder为AnyView类型
        if let placeholder = placeholder {
            self.placeholder = AnyView(placeholder())
        } else {
            self.placeholder = AnyView(Color.gray.opacity(0.3))
        }
        isLoading = true
        hasError = false
        loadedImage = nil
    }
    
    var body: some View {
        Group {
            if let image = loadedImage {
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } else if hasError {
                Image(systemName: "photo")
                    .font(.title)
                    .foregroundColor(.gray)
                    .frame(width: targetSize.width, height: targetSize.height)
            } else {
                placeholder
                    .frame(width: targetSize.width, height: targetSize.height)
                    .overlay(
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .blue))
                    )
            }
        }
        .clipShape(RoundedRectangle(cornerRadius: 8))
        .onAppear {
            loadImage()
        }
        .onChange(of: imagePath) {
            loadImage()
        }
    }
    
    private func loadImage() {
        isLoading = true
        hasError = false
        loadedImage = nil
        
        Task {
            // ✅ 修复第二步: 使用从环境注入的共享服务，而不是创建新实例
            guard let cacheService = imageCacheService else {
                // 如果服务不可用，可以设置一个错误状态或加载备用图
                hasError = true
                return
            }

            let imageURL = URL(fileURLWithPath: imagePath)
            let image = await cacheService.getCachedImage(
                for: imageURL,
                tier: targetSize.width <= 100 ? .thumbnail : .standard
            )
            
            await MainActor.run {
                isLoading = false
                if let image = image {
                    loadedImage = image
                } else {
                    hasError = true
                }
            }
        }
    }
} 