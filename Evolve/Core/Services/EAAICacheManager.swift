//
//  EAAICacheManager.swift
//  Evolve
//
//  Created by AI Assistant on 2025-06-06.
//  Phase 2 Day 4: AI增强功能开发 - AI缓存机制
//

import Foundation
import SwiftUI

/// AI缓存管理器
/// 实现多层级缓存策略，确保AI功能的性能和成本控制
/// 遵循开发规范文档的"AI成本控制开发规范"中的缓存机制要求
@MainActor
class EAAICacheManager: ObservableObject {
    
    // MARK: - 缓存存储
    
    /// 用户画像缓存（7天有效期）
    private var userProfileCache: [UUID: (profile: EAAIUserProfile, timestamp: Date)] = [:]
    
    /// AI洞察缓存（24小时有效期）
    private var aiInsightCache: [String: (insight: EAAIInsight, timestamp: Date)] = [:]
    
    /// 行为分析缓存（3天有效期）
    private var behaviorAnalysisCache: [UUID: (analysis: EAAIBehaviorAnalysis, timestamp: Date)] = [:]
    
    /// 相似场景缓存（1天有效期）
    private var similarScenarioCache: [String: (response: EAAISimilarScenarioResponse, timestamp: Date)] = [:]
    
    /// 分享时机检测缓存（6小时有效期）
    private var sharingMomentCache: [UUID: (moments: [EASharingMoment], timestamp: Date)] = [:]
    
    /// 推荐内容缓存（7天有效期）
    private var recommendationCache: [UUID: (recommendations: EARecommendationResult, timestamp: Date)] = [:]
    
    /// 宇宙向导对话缓存（1天有效期）
    private var guideConversationCache: [UUID: (conversation: [EAGuideMessage], timestamp: Date)] = [:]
    
    // MARK: - 缓存有效期配置
    
    private let userProfileCacheValidDuration: TimeInterval = 7 * 24 * 3600      // 7天
    private let aiInsightCacheValidDuration: TimeInterval = 24 * 3600            // 24小时
    private let behaviorAnalysisCacheValidDuration: TimeInterval = 3 * 24 * 3600 // 3天
    private let similarScenarioCacheValidDuration: TimeInterval = 24 * 3600      // 1天
    private let sharingMomentCacheValidDuration: TimeInterval = 6 * 3600         // 6小时
    private let recommendationCacheValidDuration: TimeInterval = 7 * 24 * 3600   // 7天
    private let guideConversationCacheValidDuration: TimeInterval = 24 * 3600    // 1天
    
    // MARK: - 缓存统计

    @Published var cacheStatistics = EAAICacheStatistics()
    
    // ✅ CPU优化：添加定时器属性管理
    private var cleanupTimer: Timer?
    
    // MARK: - 初始化
    
    init() {
        // 启动缓存清理定时器
        startCacheCleanupTimer()
    }
    
    deinit {
        // 清理定时器避免内存泄漏
        cleanupTimer?.invalidate()
    }
    
    // MARK: - 用户画像缓存
    
    /// 获取缓存的用户画像
    func getCachedUserProfile(userId: UUID) -> EAAIUserProfile? {
        guard let cached = userProfileCache[userId],
              Date().timeIntervalSince(cached.timestamp) < userProfileCacheValidDuration else {
            userProfileCache.removeValue(forKey: userId)
            return nil
        }
        
        cacheStatistics.userProfileHits += 1
        return cached.profile
    }
    
    /// 缓存用户画像
    func cacheUserProfile(_ profile: EAAIUserProfile, for userId: UUID) {
        userProfileCache[userId] = (profile, Date())
        cacheStatistics.userProfileSets += 1
    }
    
    /// 清理用户画像缓存
    func clearUserProfileCache(for userId: UUID? = nil) {
        if let userId = userId {
            userProfileCache.removeValue(forKey: userId)
        } else {
            userProfileCache.removeAll()
        }
    }
    
    // MARK: - AI洞察缓存
    
    /// 获取缓存的AI洞察
    func getCachedAIInsight(key: String) -> EAAIInsight? {
        guard let cached = aiInsightCache[key],
              Date().timeIntervalSince(cached.timestamp) < aiInsightCacheValidDuration else {
            aiInsightCache.removeValue(forKey: key)
            return nil
        }
        
        cacheStatistics.aiInsightHits += 1
        return cached.insight
    }
    
    /// 缓存AI洞察
    func cacheAIInsight(_ insight: EAAIInsight, key: String) {
        aiInsightCache[key] = (insight, Date())
        cacheStatistics.aiInsightSets += 1
    }
    
    /// 生成AI洞察缓存键
    func generateAIInsightKey(userId: UUID, insightType: String, context: String? = nil) -> String {
        if let context = context {
            return "\(userId.uuidString)_\(insightType)_\(context.hashValue)"
        } else {
            return "\(userId.uuidString)_\(insightType)"
        }
    }
    
    // MARK: - 行为分析缓存
    
    /// 获取缓存的行为分析
    func getCachedBehaviorAnalysis(userId: UUID) -> EAAIBehaviorAnalysis? {
        guard let cached = behaviorAnalysisCache[userId],
              Date().timeIntervalSince(cached.timestamp) < behaviorAnalysisCacheValidDuration else {
            behaviorAnalysisCache.removeValue(forKey: userId)
            return nil
        }
        
        cacheStatistics.behaviorAnalysisHits += 1
        return cached.analysis
    }
    
    /// 缓存行为分析
    func cacheBehaviorAnalysis(_ analysis: EAAIBehaviorAnalysis, for userId: UUID) {
        behaviorAnalysisCache[userId] = (analysis, Date())
        cacheStatistics.behaviorAnalysisSets += 1
    }
    
    // MARK: - 相似场景缓存
    
    /// 获取缓存的相似场景回复
    func getCachedSimilarScenario(scenario: String) -> EAAISimilarScenarioResponse? {
        let key = scenario.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard let cached = similarScenarioCache[key],
              Date().timeIntervalSince(cached.timestamp) < similarScenarioCacheValidDuration else {
            similarScenarioCache.removeValue(forKey: key)
            return nil
        }
        
        cacheStatistics.similarScenarioHits += 1
        return cached.response
    }
    
    /// 缓存相似场景回复
    func cacheSimilarScenario(_ response: EAAISimilarScenarioResponse, for scenario: String) {
        let key = scenario.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)
        similarScenarioCache[key] = (response, Date())
        cacheStatistics.similarScenarioSets += 1
    }
    
    // MARK: - 分享时机检测缓存
    
    /// 获取缓存的分享时机检测结果
    func getCachedSharingMoments(userId: UUID) -> [EASharingMoment]? {
        guard let cached = sharingMomentCache[userId],
              Date().timeIntervalSince(cached.timestamp) < sharingMomentCacheValidDuration else {
            sharingMomentCache.removeValue(forKey: userId)
            return nil
        }
        
        cacheStatistics.sharingMomentHits += 1
        return cached.moments
    }
    
    /// 缓存分享时机检测结果
    func cacheSharingMoments(_ moments: [EASharingMoment], for userId: UUID) {
        sharingMomentCache[userId] = (moments, Date())
        cacheStatistics.sharingMomentSets += 1
    }
    
    // MARK: - 推荐内容缓存
    
    /// 获取缓存的推荐内容
    func getCachedRecommendations(userId: UUID) -> EARecommendationResult? {
        guard let cached = recommendationCache[userId],
              Date().timeIntervalSince(cached.timestamp) < recommendationCacheValidDuration else {
            recommendationCache.removeValue(forKey: userId)
            return nil
        }
        
        cacheStatistics.recommendationHits += 1
        return cached.recommendations
    }
    
    /// 缓存推荐内容
    func cacheRecommendations(_ recommendations: EARecommendationResult, for userId: UUID) {
        recommendationCache[userId] = (recommendations, Date())
        cacheStatistics.recommendationSets += 1
    }
    
    // MARK: - 宇宙向导对话缓存
    
    /// 获取缓存的宇宙向导对话
    func getCachedGuideConversation(sessionId: UUID) -> [EAGuideMessage]? {
        guard let cached = guideConversationCache[sessionId],
              Date().timeIntervalSince(cached.timestamp) < guideConversationCacheValidDuration else {
            guideConversationCache.removeValue(forKey: sessionId)
            return nil
        }
        
        cacheStatistics.guideConversationHits += 1
        return cached.conversation
    }
    
    /// 缓存宇宙向导对话
    func cacheGuideConversation(_ conversation: [EAGuideMessage], sessionId: UUID) {
        guideConversationCache[sessionId] = (conversation, Date())
        cacheStatistics.guideConversationSets += 1
    }
    
    /// 清理宇宙向导对话缓存
    func clearGuideConversationCache(sessionId: UUID? = nil) {
        if let sessionId = sessionId {
            guideConversationCache.removeValue(forKey: sessionId)
        } else {
            guideConversationCache.removeAll()
        }
    }
    
    // MARK: - 缓存管理
    
    /// 清理所有过期缓存
    func cleanExpiredCache() {
        let now = Date()
        
        // 清理用户画像缓存
        userProfileCache = userProfileCache.filter { _, value in
            now.timeIntervalSince(value.timestamp) < userProfileCacheValidDuration
        }
        
        // 清理AI洞察缓存
        aiInsightCache = aiInsightCache.filter { _, value in
            now.timeIntervalSince(value.timestamp) < aiInsightCacheValidDuration
        }
        
        // 清理行为分析缓存
        behaviorAnalysisCache = behaviorAnalysisCache.filter { _, value in
            now.timeIntervalSince(value.timestamp) < behaviorAnalysisCacheValidDuration
        }
        
        // 清理相似场景缓存
        similarScenarioCache = similarScenarioCache.filter { _, value in
            now.timeIntervalSince(value.timestamp) < similarScenarioCacheValidDuration
        }
        
        // 清理分享时机缓存
        sharingMomentCache = sharingMomentCache.filter { _, value in
            now.timeIntervalSince(value.timestamp) < sharingMomentCacheValidDuration
        }
        
        // 清理推荐内容缓存
        recommendationCache = recommendationCache.filter { _, value in
            now.timeIntervalSince(value.timestamp) < recommendationCacheValidDuration
        }
        
        // 清理宇宙向导对话缓存
        guideConversationCache = guideConversationCache.filter { _, value in
            now.timeIntervalSince(value.timestamp) < guideConversationCacheValidDuration
        }
        
        cacheStatistics.lastCleanupTime = now
    }
    
    /// 清理所有缓存
    func clearAllCache() {
        userProfileCache.removeAll()
        aiInsightCache.removeAll()
        behaviorAnalysisCache.removeAll()
        similarScenarioCache.removeAll()
        sharingMomentCache.removeAll()
        recommendationCache.removeAll()
        guideConversationCache.removeAll()
        
        cacheStatistics = EAAICacheStatistics()
    }
    
    /// 获取缓存使用情况统计
    func getCacheUsageStatistics() -> EAAICacheStatistics {
        var stats = cacheStatistics
        stats.userProfileCacheSize = userProfileCache.count
        stats.aiInsightCacheSize = aiInsightCache.count
        stats.behaviorAnalysisCacheSize = behaviorAnalysisCache.count
        stats.similarScenarioCacheSize = similarScenarioCache.count
        stats.sharingMomentCacheSize = sharingMomentCache.count
        stats.recommendationCacheSize = recommendationCache.count
        stats.guideConversationCacheSize = guideConversationCache.count
        return stats
    }
    
    // MARK: - 私有方法
    
    /// 启动缓存清理定时器
    private func startCacheCleanupTimer() {
        // 停止现有定时器
        cleanupTimer?.invalidate()
        
        // ✅ CPU优化：从1小时延长为4小时清理一次，减少CPU唤醒次数
        cleanupTimer = Timer.scheduledTimer(withTimeInterval: 14400, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.performScheduledCleanup()
            }
        }
        
        #if DEBUG
        print("✅ [AICacheManager] 缓存清理定时器已启动，优化后间隔: 4小时")
        #endif
    }
    
    /// ✅ CPU优化：定期清理方法，性能优化版本
    private func performScheduledCleanup() async {
        // 执行清理操作
        cleanExpiredCache()
        
        #if DEBUG
        let stats = getCacheUsageStatistics()
        print("✅ [AICacheManager] 定期清理完成，当前缓存大小: \(stats.userProfileCacheSize + stats.aiInsightCacheSize)项")
        #endif
    }
}

// MARK: - AI缓存数据模型
// 注意：EAAIInsight, EAAISimilarScenarioResponse, EAAICacheStatistics 已在 EAAIDataModels.swift 中定义
// 此处移除重复定义，使用统一的数据模型
// 此处移除重复定义，使用统一的数据模型