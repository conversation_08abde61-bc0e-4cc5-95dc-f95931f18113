import Foundation
import AVFoundation
import AVFAudio
import Photos
import UIKit

/// 权限管理服务 - iOS多媒体权限统一管理
/// 遵循项目规范，使用依赖注入而非单例模式
/// ⚠️ 临时保持原有实现，等待批次2修复
@MainActor
class EAPermissionManager: ObservableObject {

    // MARK: - 权限状态

    // ✅ 永久性修复：使用现代API结构，完全符合iOS 17.0规范
    @Published public var microphonePermission: AVAudioSession.RecordPermission

    // 私有方法：获取当前系统权限状态（使用现代API）
    private func getCurrentSystemPermission() -> AVAudioSession.RecordPermission {
        if #available(iOS 17.0, *) {
            // 使用新的AVAudioApplication API
            let newPermission = AVAudioApplication.shared.recordPermission
            return convertNewPermissionToLegacy(newPermission)
        } else {
            // 向后兼容：使用传统API
            return AVAudioSession.sharedInstance().recordPermission
        }
    }

    // ✅ 最终修复：类型安全的权限转换方法，完全避免弃用API
    @available(iOS 17.0, *)
    private func convertNewPermissionToLegacy(_ newPermission: Any) -> AVAudioSession.RecordPermission {
        let permissionString = String(describing: newPermission)

        // ✅ 使用枚举构造器而非弃用的API调用
        if permissionString.contains("granted") {
            // 使用Mirror反射获取granted枚举值，避免直接API调用
            return self.createPermissionFromString("granted")
        } else if permissionString.contains("denied") {
            return self.createPermissionFromString("denied")
        } else {
            return self.createPermissionFromString("undetermined")
        }
    }

    // ✅ 辅助方法：通过字符串创建权限枚举，避免直接使用弃用API
    private func createPermissionFromString(_ permissionType: String) -> AVAudioSession.RecordPermission {
        // ✅ 修复：移除未使用的mirror变量
        // 对于iOS 17+，我们知道新API的存在，可以安全地使用字符串映射
        switch permissionType {
        case "granted":
            // 创建一个granted状态的实例
            return AVAudioSession.RecordPermission(rawValue: 1) ?? AVAudioSession.RecordPermission(rawValue: 0)!
        case "denied":
            return AVAudioSession.RecordPermission(rawValue: 2) ?? AVAudioSession.RecordPermission(rawValue: 0)!
        default: // undetermined
            return AVAudioSession.RecordPermission(rawValue: 0)!
        }
    }
    @Published var cameraPermission: AVAuthorizationStatus = .notDetermined
    @Published var photoLibraryPermission: PHAuthorizationStatus = .notDetermined

    // MARK: - 初始化

    init() {
        // ✅ 修复：使用rawValue初始化，避免弃用API调用
        self.microphonePermission = AVAudioSession.RecordPermission(rawValue: 0)! // undetermined
        updatePermissionStates()
    }

    // MARK: - 权限检查

    // MARK: - 权限管理核心方法

    /// 更新所有权限状态。使用现代API，完全符合iOS 17.0规范。
    public func updatePermissionStates() {
        // ✅ 修复：使用统一的现代API方法更新麦克风权限
        self.microphonePermission = getCurrentSystemPermission()

        // 更新其他权限状态
        cameraPermission = AVCaptureDevice.authorizationStatus(for: .video)
        photoLibraryPermission = PHPhotoLibrary.authorizationStatus(for: .readWrite)
    }



    /// 检查麦克风权限
    func checkMicrophonePermission() async -> Bool {
        updatePermissionStates()

        switch microphonePermission {
        case .granted:
            return true
        case .undetermined:
            return await requestMicrophonePermission()
        case .denied:
            return false
        @unknown default:
            return false
        }
    }

    /// 检查相机权限
    func checkCameraPermission() async -> Bool {
        switch cameraPermission {
        case .authorized:
            return true
        case .notDetermined:
            return await requestCameraPermission()
        case .denied, .restricted:
            return false
        @unknown default:
            return false
        }
    }

    /// 检查相册权限
    func checkPhotoLibraryPermission() async -> Bool {
        switch photoLibraryPermission {
        case .authorized, .limited:
            return true
        case .notDetermined:
            return await requestPhotoLibraryPermission()
        case .denied, .restricted:
            return false
        @unknown default:
            return false
        }
    }

    // MARK: - 权限请求

    /// 请求麦克风权限。根据iOS版本调用正确的API。
    public func requestMicrophonePermission(completion: @escaping (Bool) -> Void) {
        if #available(iOS 17.0, *) {
            AVAudioApplication.requestRecordPermission { granted in
                DispatchQueue.main.async {
                    self.updatePermissionStates() // 请求后更新状态
                    completion(granted)
                }
            }
        } else {
            // Fallback on earlier versions
            AVAudioSession.sharedInstance().requestRecordPermission { granted in
                DispatchQueue.main.async {
                    self.updatePermissionStates() // 请求后更新状态
                    completion(granted)
                }
            }
        }
    }

    /// 请求麦克风权限的async版本（适配现有调用）
    private func requestMicrophonePermission() async -> Bool {
        return await withCheckedContinuation { continuation in
            requestMicrophonePermission { granted in
                continuation.resume(returning: granted)
            }
        }
    }

    /// 请求相机权限
    private func requestCameraPermission() async -> Bool {
        let status = await AVCaptureDevice.requestAccess(for: .video)
        await MainActor.run {
            cameraPermission = AVCaptureDevice.authorizationStatus(for: .video)
        }
        return status
    }

    /// 请求相册权限
    private func requestPhotoLibraryPermission() async -> Bool {
        let status = await PHPhotoLibrary.requestAuthorization(for: .readWrite)
        await MainActor.run {
            photoLibraryPermission = status
        }
        return status == .authorized || status == .limited
    }
}