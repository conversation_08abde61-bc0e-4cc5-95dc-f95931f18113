import Foundation
import Security
import SwiftUI
import SwiftData

/// 🔑 新增：会话数据结构体，确保原子性操作
private struct SessionData {
    let wasLoggedIn: Bool
    let userIdString: String?
    let token: String?
    let userId: UUID?

    var isValid: Bool {
        return wasLoggedIn && userIdString != nil && token != nil && userId != nil
    }
}

// MARK: - Keychain错误枚举

enum EAKeychainError: Error {
    case itemNotFound
    case duplicateItem
    case invalidItemFormat
    case unexpectedStatus(OSStatus)
}

// MARK: - Keychain服务

class EAKeychainService {
    
    // MARK: - 常量
    
    private let service = "com.evolve.app"
    
    // MARK: - 保存数据到Keychain
    
    func save(key: String, data: Data) throws {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecValueData as String: data
        ]
        
        // 删除已存在的项目
        SecItemDelete(query as CFDictionary)
        
        // 添加新项目
        let status = SecItemAdd(query as CFDictionary, nil)
        
        guard status == errSecSuccess else {
            throw EAKeychainError.unexpectedStatus(status)
        }
    }
    
    // MARK: - 从Keychain读取数据
    
    func load(key: String) throws -> Data {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        guard status == errSecSuccess else {
            if status == errSecItemNotFound {
                throw EAKeychainError.itemNotFound
            }
            throw EAKeychainError.unexpectedStatus(status)
        }
        
        guard let data = result as? Data else {
            throw EAKeychainError.invalidItemFormat
        }
        
        return data
    }
    
    // MARK: - 从Keychain删除数据
    
    func delete(key: String) throws {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        
        guard status == errSecSuccess || status == errSecItemNotFound else {
            throw EAKeychainError.unexpectedStatus(status)
        }
    }
    
    // MARK: - 便捷方法：保存字符串
    
    func save(key: String, string: String) throws {
        guard let data = string.data(using: .utf8) else {
            throw EAKeychainError.invalidItemFormat
        }
        try save(key: key, data: data)
    }
    
    // MARK: - 便捷方法：读取字符串
    
    func loadString(key: String) throws -> String {
        let data = try load(key: key)
        guard let string = String(data: data, encoding: .utf8) else {
            throw EAKeychainError.invalidItemFormat
        }
        return string
    }
    
    // MARK: - 便捷方法：保存Codable对象
    
    func save<T: Codable>(key: String, object: T) throws {
        let encoder = JSONEncoder()
        let data = try encoder.encode(object)
        try save(key: key, data: data)
    }
    
    // MARK: - 便捷方法：读取Codable对象
    
    func load<T: Codable>(key: String, type: T.Type) throws -> T {
        let data = try load(key: key)
        let decoder = JSONDecoder()
        return try decoder.decode(type, from: data)
    }
}

// MARK: - 会话管理器（适配新Repository层）

@MainActor
class EASessionManager: EASessionManagerProtocol, ObservableObject {
    
    // MARK: - 移除单例模式
    // static let shared = EASessionManager()
    
    // MARK: - Published属性（适配新模型）

    @Published var isLoggedIn = false
    @Published var accessToken: String? = nil
    @Published var refreshToken: String? = nil

    // MARK: - 核心状态重构：从对象持有改为ID持有

    /// 当前用户ID（核心状态，避免游离对象问题）
    @Published private(set) var currentUserID: UUID? = nil
    
    // MARK: - 阶段5.2：用户身份监控
    @Published var identityMonitor: EAUserIdentityMonitor?
    
    // 🔑 新增：会话恢复完成状态 - 解决启动时序竞态问题
    @Published var isSessionRestoreCompleted: Bool = false
    
    // MARK: - 🔑 原子化状态更新架构
    
    /// 🔑 会话状态数据结构 - 确保原子化更新
    private struct SessionState {
        let isLoggedIn: Bool
        let currentUserID: UUID?
        let accessToken: String?
        let refreshToken: String?
        
        /// 创建已登录状态
        static func loggedIn(userID: UUID, accessToken: String, refreshToken: String? = nil) -> SessionState {
            return SessionState(
                isLoggedIn: true,
                currentUserID: userID,
                accessToken: accessToken,
                refreshToken: refreshToken
            )
        }
        
        /// 创建未登录状态
        static func loggedOut() -> SessionState {
            return SessionState(
                isLoggedIn: false,
                currentUserID: nil,
                accessToken: nil,
                refreshToken: nil
            )
        }
    }
    
    /// 🔑 原子化更新会话状态 - 解决微观竞态条件
    @MainActor
    private func atomicUpdateSessionState(_ newState: SessionState) {
        // 🔑 关键：在一个@MainActor.run块中同时更新所有状态
        // 确保UI只在所有状态都准备好后才响应
        
        // 临时禁用自动发布，防止中间状态触发UI更新
        let willChange = objectWillChange
        
        // 批量更新所有状态
        self.currentUserID = newState.currentUserID
        self.accessToken = newState.accessToken
        self.refreshToken = newState.refreshToken
        
        // 🔑 关键：isLoggedIn必须最后设置，且只在所有其他状态都准备好后
        self.isLoggedIn = newState.isLoggedIn
        
        // 手动触发UI更新，确保所有状态同步
        willChange.send()
        
        #if DEBUG
        print("🔄 [SessionManager] 原子化状态更新完成 - isLoggedIn: \(newState.isLoggedIn), userID: \(newState.currentUserID?.uuidString ?? "nil")")
        #endif
    }
    
    /// 🔑 原子化登录状态设置
    @MainActor
    private func atomicSetLoggedIn(user: EAUser, accessToken: String, refreshToken: String? = nil) {
        let newState = SessionState.loggedIn(
            userID: user.id,
            accessToken: accessToken,
            refreshToken: refreshToken
        )
        atomicUpdateSessionState(newState)
    }
    
    /// 🔑 原子化登出状态设置
    @MainActor
    private func atomicSetLoggedOut() {
        let newState = SessionState.loggedOut()
        atomicUpdateSessionState(newState)
    }
    
    // MARK: - 私有属性

    // MARK: - 🔑 新架构：直接管理数据栈生命周期
    
    /// 当前数据库管理器（每次登录时创建新实例）
    private var databaseManager: EADatabaseManager?
    
    /// 当前仓库容器（随数据库管理器创建）
    private var _repositoryContainer: EARepositoryContainer?
    
    // MARK: - 私有属性
    
    private let keychain = EAKeychainService()
    private let userDefaults = UserDefaults.standard
    // Keychain键名
    private let accessTokenKey = "access_token"
    private let refreshTokenKey = "refresh_token"
    private let userDataKey = "user_data"
    
    // UserDefaults键名
    private let isLoggedInKey = "is_logged_in"
    private let lastLoginDateKey = "last_login_date"
    
    // MARK: - 公共访问器
    
    /// 获取Repository容器
    var repositoryContainer: EARepositoryContainer? {
        return _repositoryContainer
    }
    
    // MARK: - 初始化

    /// 🔑 新架构：简化的初始化方法
    public init() {
        #if DEBUG
        print("🔧 [SessionManager] SessionManager 初始化，等待登录时创建数据栈")
        #endif
        // 不再在初始化时依赖任何外部服务
        // 数据栈将在登录时按需创建
    }

    /// 公共初始化方法（用于Preview和测试）
    public convenience init(repositoryContainer: EARepositoryContainer) {
        self.init()
        self.setRepositoryContainer(repositoryContainer)
    }
    
    // MARK: - Repository注入
    
    func setRepositoryContainer(_ container: EARepositoryContainer) {
        // 🔑 修复：允许在账号切换时重新设置Repository容器
        if _repositoryContainer != nil {
            #if DEBUG
            print("⚠️ [SessionManager] Repository容器已存在，将更新为新容器（账号切换场景）")
            #endif
        }

        self._repositoryContainer = container

        #if DEBUG
        print("✅ [SessionManager] RepositoryContainer Injected into SessionManager.")
        #endif

        // 🔑 阶段5.2：初始化用户身份监控器
        initializeIdentityMonitor(container: container)

        // 🔑 修复：使用新的会话恢复方法
        Task { @MainActor in
            await restoreSessionOnAppLaunch()
        }
    }
    

    // MARK: - 新异步接口实现

    /// 安全的当前用户（异步接口）
    /// 通过Repository安全获取用户对象，避免游离对象问题
    /// 🔑 新增：包含关系属性验证，确保返回完整的用户数据
    var safeCurrentUser: EAUser? {
        get async {
            #if DEBUG
            print("🔍 [SessionManager] safeCurrentUser调用 - currentUserID: \(currentUserID?.uuidString ?? "nil"), repositoryContainer: \(_repositoryContainer != nil ? "存在" : "nil")")
            #endif

            guard let userID = self.currentUserID else {
                #if DEBUG
                print("❌ [SessionManager] safeCurrentUser失败 - currentUserID为nil")
                #endif
                return nil
            }

            // 🔑 关键修复：如果Repository容器为nil，尝试恢复
            var repositoryContainer = _repositoryContainer
            if repositoryContainer == nil {
                #if DEBUG
                print("⚠️ [SessionManager] Repository容器为nil，尝试恢复...")
                #endif

                // 尝试从DatabaseManager重新获取容器
                if let recoveredContainer = databaseManager?.getRepositoryContainer() {
                    await MainActor.run {
                        _repositoryContainer = recoveredContainer
                    }
                    repositoryContainer = recoveredContainer
                    #if DEBUG
                    print("✅ [SessionManager] Repository容器已恢复")
                    #endif
                } else {
                    #if DEBUG
                    print("❌ [SessionManager] safeCurrentUser失败 - 无法恢复Repository容器")
                    #endif
                    return nil
                }
            }

            guard let finalContainer = repositoryContainer else {
                #if DEBUG
                print("❌ [SessionManager] safeCurrentUser失败 - repositoryContainer为nil")
                #endif
                return nil
            }

            // 通过Repository安全获取用户对象
            guard let user = await finalContainer.userRepository.fetchUser(by: userID) else {
                #if DEBUG
                print("❌ [SessionManager] safeCurrentUser失败 - 无法从Repository获取用户，userID: \(userID.uuidString)")
                #endif
                return nil
            }

            #if DEBUG
            print("✅ [SessionManager] safeCurrentUser成功 - 用户: \(user.username), socialProfile: \(user.socialProfile != nil ? "存在" : "nil")")
            #endif

            // 🔑 新增：验证关键关系属性是否已加载
            // 这对于账号切换场景和新注册用户特别重要
            await validateUserRelationships(user: user, repositoryContainer: finalContainer)

            return user
        }
    }

    /// Pro状态检查（异步实现）
    func checkProStatus() async -> Bool {
        guard let user = await safeCurrentUser else { return false }

        if !user.isPro { return false }

        // 检查Pro订阅是否过期
        if let expirationDate = user.proExpirationDate {
            return expirationDate > Date()
        }

        return true
    }

    /// Pro用户状态（同步实现，用于兼容性）
    var isPro: Bool {
        // 注意：这是一个同步属性，仅用于向后兼容
        // 新代码应该使用 checkProStatus() 异步方法
        return false // 默认返回false，避免同步访问用户数据
    }

    /// 设置ModelContext（用于新的Apple标准配置）
    func setModelContext(_ context: ModelContext) {
        // 将ModelContext传递给相关服务
        #if DEBUG
        print("📱 [SessionManager] ModelContext已设置")
        #endif
    }

    /// 🔑 新增：设置DatabaseManager引用（用于Repository容器恢复）
    func setDatabaseManager(_ manager: EADatabaseManager) {
        self.databaseManager = manager
        #if DEBUG
        print("📱 [SessionManager] DatabaseManager已设置")
        #endif
    }
    
    // MARK: - 会话管理方法（适配新Repository）

    /// 统一的用户会话更新方法（简化版）
    @MainActor
    func updateUserSession(with userID: UUID) async throws {
        #if DEBUG
        print("🔧 [SessionManager] updateUserSession called with userID: \(userID.uuidString)")
        #endif

        guard let repositoryContainer = _repositoryContainer else {
            throw SessionError.repositoryNotAvailable
        }

        // 从数据库获取完整的用户对象
        guard let user = try await repositoryContainer.userRepository.fetchUser(id: userID) else {
            #if DEBUG
            print("❌ [SessionManager] updateUserSession失败 - 无法获取用户: \(userID.uuidString)")
            #endif
            throw DataModelError.userNotFound
        }

        // 生成访问令牌
        let accessToken = "session_token_\(userID.uuidString)_\(Date().timeIntervalSince1970)"

        // 原子化更新会话状态
        atomicSetLoggedIn(user: user, accessToken: accessToken)

        // 保存到持久化存储
        try await saveSessionToPersistentStorage(user: user, accessToken: accessToken)

        #if DEBUG
        print("✅ [SessionManager] updateUserSession成功 - 用户: \(user.username), ID: \(userID.uuidString)")
        #endif
    }

    /// 保存会话到持久化存储（简化版）
    @MainActor
    private func saveSessionToPersistentStorage(user: EAUser, accessToken: String, refreshToken: String? = nil) async throws {
        // 保存到UserDefaults
        UserDefaults.standard.set(true, forKey: "isLoggedIn")
        UserDefaults.standard.set(user.id.uuidString, forKey: "currentUserId")
        UserDefaults.standard.set(accessToken, forKey: "authToken")
        UserDefaults.standard.set(user.username, forKey: "currentUsername")
        if let refreshToken = refreshToken {
            UserDefaults.standard.set(refreshToken, forKey: "refreshToken")
        }
        UserDefaults.standard.synchronize()

        // 保存到Keychain
        do {
            try keychain.save(key: accessTokenKey, string: accessToken)
            try keychain.save(key: userDataKey, string: user.id.uuidString)
            if let refreshToken = refreshToken {
                try keychain.save(key: refreshTokenKey, string: refreshToken)
            }
        } catch {
            #if DEBUG
            print("⚠️ [SessionManager] Keychain保存失败: \(error)")
            #endif
            throw error
        }

        #if DEBUG
        print("✅ [SessionManager] 会话持久化保存成功 - 用户: \(user.username)")
        #endif
    }

    /// 🔑 关键修复：保存会话状态到SwiftData
    @MainActor
    func saveSession(authData: EAAuthData, user: EAUser) async throws {
        // 🔑 重构：使用新的统一方法
        try await updateUserSession(with: user.id)

        // 🔑 强化修复：确保会话数据保存到UserDefaults
        UserDefaults.standard.set(authData.token.accessToken, forKey: "authToken")
        UserDefaults.standard.set(authData.token.refreshToken, forKey: "refreshToken")
        UserDefaults.standard.set(user.id.uuidString, forKey: "currentUserId")
        UserDefaults.standard.set(true, forKey: "isLoggedIn")
        UserDefaults.standard.set(user.username, forKey: "currentUsername")
        
        // 🔑 强化修复：立即同步到磁盘
        UserDefaults.standard.synchronize()
        
        // 手动触发UI更新
        objectWillChange.send()
        
        // 发送通知
        NotificationCenter.default.post(
            name: NSNotification.Name("EASessionLoginCompleted"),
            object: nil,
            userInfo: ["userId": user.id.uuidString, "username": user.username]
        )
        
        // 🔑 阶段5.2：启动用户身份监控
        startIdentityMonitoring()
        
        // 会话保存完成
    }
    
    /// 恢复用户会话
    func restoreSession() {
        guard let repositoryContainer = repositoryContainer else {
            #if DEBUG
            print("⚠️ [SessionManager] 恢复会话失败：Repository容器不可用")
            #endif
            return
        }
        
        // 检查UserDefaults中的登录状态
        let wasLoggedIn = userDefaults.bool(forKey: isLoggedInKey)
        
        guard wasLoggedIn else {
            return
        }
        
        Task { @MainActor in
            do {
                // 从Keychain恢复Token
                let token = try keychain.loadString(key: accessTokenKey)
                
                // 🔑 关键修复：从Keychain恢复保存的用户ID
                let userIdString = try keychain.loadString(key: userDataKey)
                guard let userId = UUID(uuidString: userIdString) else {
                    throw EAKeychainError.invalidItemFormat
                }
                
                // ✅ 修复：通过Repository获取用户数据 - 使用正确的方法签名
                guard let user = try await repositoryContainer.userRepository.fetchUser(id: userId) else {
                    throw DataModelError.userNotFound
                }

                // 🔑 重构：使用原子化更新，解决微观竞态条件
                atomicSetLoggedIn(user: user, accessToken: token)
                
                // 更新最后登录时间
                userDefaults.set(Date(), forKey: lastLoginDateKey)
                
            } catch EAKeychainError.itemNotFound {
                // Token或用户ID不存在，清除会话
                await MainActor.run {
                    clearSession()
                }
                
            } catch {
                // ✅ 修复：不要因为轻微错误就清除整个会话
                // 不调用clearSession()，保持当前登录状态
            }
        }
    }
    
    /// 清除用户会话
    @MainActor
    func clearSession() {
        // 开始清除会话
        
        do {
            // 清除Keychain数据
            try keychain.delete(key: accessTokenKey)
            try keychain.delete(key: refreshTokenKey)
            try keychain.delete(key: userDataKey)
            
            // Keychain数据已清除
        } catch EAKeychainError.itemNotFound {
            // 项目不存在，正常情况
            #if DEBUG
            print("🔑 [SessionManager] Keychain项目不存在（正常情况）")
            #endif
        } catch {
            #if DEBUG
            print("🚨 [SessionManager] Keychain清除失败")
            #endif
            _ = error
        }
        
        // 🚨 关键修复：彻底清除UserDefaults中的所有用户身份数据
        userDefaults.removeObject(forKey: isLoggedInKey)
        userDefaults.removeObject(forKey: lastLoginDateKey)
        // 🔑 新增：清除用户身份标识，避免用户身份混乱
        userDefaults.removeObject(forKey: "currentUserId")
        userDefaults.removeObject(forKey: "currentUsername")
        userDefaults.removeObject(forKey: "authToken")
        userDefaults.removeObject(forKey: "refreshToken")
        // 🔑 强制同步到磁盘
        userDefaults.synchronize()
        
        #if DEBUG
        print("✅ [SessionManager] UserDefaults已清除")
        #endif
        
        // 🔑 关键修复：使用原子化更新，确保状态一致性
        atomicSetLoggedOut()
        
        // 🔑 关键修复：强制触发UI更新
        self.objectWillChange.send()
        
        #if DEBUG
        print("🔍 [SessionManager] 最终状态 - isLoggedIn: \(self.isLoggedIn), currentUserID: \(self.currentUserID?.uuidString ?? "nil"), accessToken: \(self.accessToken != nil ? "存在" : "nil")")
        #endif
    }
    
    /// 登录用户
    /// 🔑 新架构：登录时创建全新的数据栈
    func login(username: String, email: String? = nil) async throws {
        #if DEBUG
        print("🚀 [SessionManager] 开始登录流程，创建全新数据栈")
        #endif
        
        // 🔑 第一步：创建全新的数据库管理器
        let newDatabaseManager = EADatabaseManager(forTesting: false)
        guard let repositoryContainer = newDatabaseManager.getRepositoryContainer() else {
            #if DEBUG
            print("❌ [SessionManager] 数据库初始化失败")
            #endif
            throw SessionError.repositoryNotAvailable
        }
        
        // 🔑 第二步：查找或创建用户
        var user: EAUser?
        let allUsers = try await repositoryContainer.userRepository.fetchAllUsers()
        user = allUsers.first { $0.username == username }
        
        if user == nil {
            // 创建新用户
            user = try await repositoryContainer.userRepository.createUser(username: username, email: email)
            #if DEBUG
            print("✅ [SessionManager] 创建新用户: \(username)")
            #endif
        } else {
            // 确保现有用户的社交档案完整
            guard let existingUser = user else {
                throw SessionError.userNotFound
            }
            _ = try await repositoryContainer.userRepository.ensureSocialProfile(for: existingUser)
            #if DEBUG
            print("✅ [SessionManager] 现有用户登录: \(username)")
            #endif
        }
        
        guard let currentUser = user else {
            throw SessionError.userNotFound
        }
        
        // 🔑 第三步：严格的用户身份完整性检查
        let integrityGuard = EAUserIntegrityGuard(repositoryContainer: repositoryContainer)
        let safetyResult = integrityGuard.deepSafetyCheck(for: currentUser)

        if !safetyResult {
            #if DEBUG
            print("⚠️ [SessionManager] 用户身份完整性检查失败，尝试自动修复")
            #endif

            // 尝试获取安全的社交档案（包含自动修复）
            guard let _ = await integrityGuard.safeSocialProfile(for: currentUser) else {
                throw SessionError.userDataCorruption
            }
        }
        
        // 🔑 第四步：登录后数据完整性验证
        await validateUserDataIntegrityAfterLogin(user: currentUser, repositoryContainer: repositoryContainer)
        
        // 🔑 第五步：原子化更新会话状态
        let authToken = EAToken(
            accessToken: UUID().uuidString,
            refreshToken: UUID().uuidString
        )
        let authData = EAAuthData(
            token: authToken,
            user: EAAuthUser(
                id: currentUser.id.uuidString,
                username: currentUser.username,
                email: currentUser.email
            )
        )
        
        // 🔑 关键：只有在所有验证都通过后，才更新数据栈引用
        self.databaseManager = newDatabaseManager
        self._repositoryContainer = repositoryContainer
        
        // 保存会话
        try await saveSession(authData: authData, user: currentUser)
        
        #if DEBUG
        print("✅ [SessionManager] 登录成功，数据栈已创建")
        #endif
    }
    
    /// ✅ 最佳实践：简化的安全登出流程（完全重写）
    /// 遵循iOS开发规范，避免复杂的异步时序依赖
    @MainActor
    func logout() {
        #if DEBUG
        print("🚀 [SessionManager] 开始简化的安全登出流程")
        #endif
        
        // 停止用户身份监控
        stopIdentityMonitoring()
        
        // 第一步：立即清理所有会话状态（原子操作）
        self.isLoggedIn = false
        self.currentUserID = nil
        self.accessToken = nil
        self.refreshToken = nil
        
        // 第二步：清理持久化数据
        clearSession()
        
        // 第三步：🔑 关键改进 - 让ARC自动销毁数据栈，不强制清理
        self.databaseManager = nil
        self._repositoryContainer = nil
        
        // 第四步：确保UI更新
        self.objectWillChange.send()
        
        // 第五步：发送登出完成通知
        NotificationCenter.default.post(
            name: Notification.Name("EASessionLogoutCompleted"),
            object: nil
        )
        
        #if DEBUG
        print("✅ [SessionManager] 简化安全登出流程完成")
        #endif
    }
    
    /// 获取当前用户
    func getCurrentUser() async throws -> EAUser? {
        guard let repositoryContainer = _repositoryContainer else {
            throw SessionError.repositoryNotAvailable
        }
        
        return try await repositoryContainer.getCurrentUser()
    }
    
    /// 🔑 重构：更新用户信息
    func updateUser(_ user: EAUser) async throws {
        guard let repositoryContainer = _repositoryContainer else {
            throw SessionError.repositoryNotAvailable
        }

        try await repositoryContainer.userRepository.saveUser(user)
        // 🔑 重构：更新用户ID
        self.currentUserID = user.id
    }
    
    // MARK: - 用户数据刷新方法（修复头像保存问题）
    
    /// 刷新当前用户数据（从数据库重新获取最新数据）
    @MainActor
    func refreshCurrentUser() async {
        guard let currentUserId = self.currentUserID,
              let repositoryContainer = _repositoryContainer else { return }

        do {
            // 从数据库重新获取最新的用户数据
            if let refreshedUser = try await repositoryContainer.userRepository.fetchUser(id: currentUserId) {
                // 🔑 重构：用户数据已通过Repository更新，无需额外操作
                _ = refreshedUser
            }
        } catch {
            #if DEBUG
            print("❌ [SessionManager] 刷新用户数据失败: \(error)")
            #endif
        }
    }
    
    /// 🔑 修复：更新用户头像后刷新用户数据（强化版）
    @MainActor
    func updateUserAvatarAndRefresh(avatarData: EAAvatarData?) async throws {
        guard let currentUserId = self.currentUserID,
              let repositoryContainer = _repositoryContainer else {
            throw NSError(domain: "SessionManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "用户未登录或Repository未初始化"])
        }
        
        // 🔧 临时调试信息：记录保存前状态
        #if DEBUG
        print("🔍 [SessionManager] 准备保存头像 - 用户ID: \(currentUserId.uuidString)")
        #endif
        if let avatar = avatarData {
            #if DEBUG
            print("🔍 [SessionManager] 头像类型: \(avatar.type.rawValue), 显示名: \(avatar.type.displayName)")
            #endif
        } else {
            #if DEBUG
            print("🔍 [SessionManager] 清除头像数据")
            #endif
        }
        
        // 保存头像到数据库
        try await repositoryContainer.userRepository.updateUserAvatar(userId: currentUserId, avatarData: avatarData)
        
        // 🔑 强化修复：重新获取完整的用户数据（包括社交档案关系）
        if let refreshedUser = try await repositoryContainer.userRepository.fetchUser(id: currentUserId) {
            // 🔑 重构：确保社交档案关系也被正确加载
            _ = refreshedUser
            
            // 🔧 临时调试信息：验证刷新后的数据状态
            let hasRawData = refreshedUser.avatarDataEncoded != nil
            let hasAvatarData = refreshedUser.avatarData != nil
            #if DEBUG
            print("🔍 [SessionManager] 数据刷新完成 - 原始数据: \(hasRawData), 头像数据: \(hasAvatarData)")
            #endif
            if let refreshedAvatar = refreshedUser.avatarData {
                #if DEBUG
                print("🔍 [SessionManager] 刷新后头像类型: \(refreshedAvatar.type.rawValue)")
                #endif
            }
            
            // 🔑 强制触发UI更新：发送多种通知确保所有组件响应
            objectWillChange.send()
            
            // 🔑 发送头像更新通知，确保所有UI组件同步更新
            NotificationCenter.default.post(
                name: NSNotification.Name("EAUserAvatarDidUpdate"),
                object: nil,
                userInfo: ["userId": currentUserId.uuidString, "avatarData": avatarData as Any]
            )
            
            // 🔑 新增：发送用户数据更新通知
            NotificationCenter.default.post(
                name: NSNotification.Name("EAUserDataDidUpdate"),
                object: nil,
                userInfo: ["userId": currentUserId.uuidString]
            )
        } else {
            // 备用方案：只刷新当前用户数据
            await refreshCurrentUser()
            
            #if DEBUG
            print("⚠️ [SessionManager] 无法重新获取用户数据，使用备用刷新方案")
            #endif
        }
    }
    
    // MARK: - 会话恢复方法（修复重启后无法登录的问题）
    
    /// ✅ 最佳实践：强化的会话恢复流程（完全重写）
    /// 增加数据一致性检查，提高iOS版本兼容性
    @MainActor
    func restoreSessionOnAppLaunch() async {
        #if DEBUG
        print("🔧 [SessionManager] 开始强化的会话恢复流程")
        #endif
        
        // 第一步：验证数据源一致性
        let sessionData = validateSessionDataConsistency()
        
        guard sessionData.isValid else {
            // 数据不一致，清理并重置为干净状态
            await clearInconsistentSessionData()
            self.isSessionRestoreCompleted = true
            #if DEBUG
            print("ℹ️ [SessionManager] 数据不一致，已清理为干净状态")
            #endif
            return
        }
        
        // 第二步：尝试恢复会话
        do {
            try await updateUserSession(with: sessionData.userId!)
            #if DEBUG
            print("✅ [SessionManager] 会话恢复成功")
            #endif
        } catch {
            // 恢复失败，执行降级策略
            await handleSessionRestoreFailure(error: error)
            #if DEBUG
            print("❌ [SessionManager] 会话恢复失败，已执行降级策略: \(error)")
            #endif
        }
        
        // 第三步：标记恢复完成
        self.isSessionRestoreCompleted = true
    }
    
    /// 数据源一致性验证（新增）
    private func validateSessionDataConsistency() -> (isValid: Bool, userId: UUID?) {
        let userDefaultsLoggedIn = UserDefaults.standard.bool(forKey: "isLoggedIn")
        let userDefaultsUserId = UserDefaults.standard.string(forKey: "currentUserId")
        
        // 基础验证
        guard userDefaultsLoggedIn,
              let userIdString = userDefaultsUserId,
              let userId = UUID(uuidString: userIdString) else {
            return (isValid: false, userId: nil)
        }
        
        // Keychain一致性验证
        do {
            let _ = try keychain.loadString(key: accessTokenKey)
            return (isValid: true, userId: userId)
        } catch {
            // Keychain数据缺失或损坏
            return (isValid: false, userId: nil)
        }
    }
    
    /// 清理不一致的会话数据（新增）
    private func clearInconsistentSessionData() async {
        // 清理所有会话状态
        self.isLoggedIn = false
        self.currentUserID = nil
        self.accessToken = nil
        self.refreshToken = nil
        
        // 清理持久化数据
        clearSession()
        
        // 触发UI更新
        self.objectWillChange.send()
    }
    
    /// 会话恢复失败处理（新增）
    private func handleSessionRestoreFailure(error: Error) async {
        // 分析错误类型并采取相应策略
        if error.localizedDescription.contains("Context") ||
           error.localizedDescription.contains("database") {
            // 🔑 新架构：不再尝试重置数据库，直接进入清理流程
        }
        
        // 无论如何，清理会话状态
        await clearInconsistentSessionData()
    }

    /// 原子性收集会话数据的辅助方法
    private func gatherSessionData() -> SessionData {
        let userIdString = UserDefaults.standard.string(forKey: "currentUserId")
        let token = UserDefaults.standard.string(forKey: "authToken")
        let wasLoggedIn = UserDefaults.standard.bool(forKey: "isLoggedIn")

        return SessionData(
            wasLoggedIn: wasLoggedIn,
            userIdString: userIdString,
            token: token,
            userId: userIdString.flatMap { UUID(uuidString: $0) }
        )
    }

    /// 清除会话数据的辅助方法
    @MainActor
    private func clearSessionData() async {
        // 使用原子化更新，确保状态一致性
        atomicSetLoggedOut()

        // 清除UserDefaults
        UserDefaults.standard.removeObject(forKey: "isLoggedIn")
        UserDefaults.standard.removeObject(forKey: "currentUserId")
        UserDefaults.standard.removeObject(forKey: "authToken")
        UserDefaults.standard.removeObject(forKey: "refreshToken")
        UserDefaults.standard.removeObject(forKey: "currentUsername")
        UserDefaults.standard.synchronize()

        objectWillChange.send()
    }

    /// 🔑 新增：设置当前用户方法（用于注册流程）
    
    /// 设置当前用户并保存会话状态（用于新用户注册）
    @MainActor
    func setCurrentUser(_ user: EAUser) async throws {
        #if DEBUG
        print("🔧 [SessionManager] setCurrentUser called with user: \(user.username), ID: \(user.id.uuidString)")
        #endif

        // 🔑 重构：使用统一的会话更新方法
        try await updateUserSession(with: user.id)

        #if DEBUG
        print("✅ [SessionManager] 新用户会话已建立 - 用户: \(user.username)")
        #endif
    }

    /// �� 新增：验证用户数据完整性
    @MainActor
    private func validateUserDataIntegrity(user: EAUser, repositoryContainer: EARepositoryContainer) async {
        // 检查用户是否有必要的关联数据
        if user.settings == nil {
            do {
                _ = try await repositoryContainer.userSettingsRepository.createDefaultSettings(for: user.id)
                #if DEBUG
                print("🔧 [SessionManager] 为用户创建默认设置")
                #endif
            } catch {
                #if DEBUG
                print("⚠️ [SessionManager] 创建用户设置失败: \(error.localizedDescription)")
                #endif
            }
        }

        // 🔑 关键修复：不在启动时检查社交档案，避免触发复杂操作
        // 社交档案将在需要时通过专门的方法按需创建
        // 这避免了启动时的复杂SwiftData操作，防止Signal 9崩溃
    }

    /// 🔑 新增：登录后的数据完整性验证（专门用于账号切换场景）
    /// 确保新注册用户或切换账号后的用户数据完整性
    @MainActor
    private func validateUserDataIntegrityAfterLogin(user: EAUser, repositoryContainer: EARepositoryContainer) async {
        #if DEBUG
        print("🔍 [SessionManager] 开始登录后数据完整性验证")
        #endif

        // 🔑 关键验证：确保社交档案关系正确建立
        // 这对于新注册用户和账号切换场景至关重要
        if user.socialProfile == nil {
            #if DEBUG
            print("⚠️ [SessionManager] 检测到用户缺少社交档案，开始创建")
            #endif

            do {
                // 使用Repository的安全创建方法
                let _ = try await repositoryContainer.userRepository.ensureSocialProfile(for: user)

                #if DEBUG
                print("✅ [SessionManager] 社交档案创建成功")
                #endif

            } catch {
                #if DEBUG
                print("❌ [SessionManager] 社交档案创建失败: \(error.localizedDescription)")
                #endif
            }
        } else {
            #if DEBUG
            print("✅ [SessionManager] 社交档案验证通过")
            #endif
        }

        // 🔑 验证其他关键关联数据
        if user.dataProfile == nil {
            #if DEBUG
            print("⚠️ [SessionManager] 检测到用户缺少数据档案")
            #endif
        }

        if user.moderationProfile == nil {
            #if DEBUG
            print("⚠️ [SessionManager] 检测到用户缺少管理档案")
            #endif
        }

        #if DEBUG
        print("🔍 [SessionManager] 登录后数据完整性验证完成")
        #endif
    }

    /// 🔑 新增：验证用户关系属性（轻量级验证，不执行修复）
    /// 确保safeCurrentUser返回的用户对象包含必要的关联数据
    private func validateUserRelationships(user: EAUser, repositoryContainer: EARepositoryContainer) async {
        // 🔑 轻量级验证：只检查关键关系是否存在，不执行修复
        // 这避免了在每次获取用户时都执行复杂操作

        // 检查社交档案关系
        if user.socialProfile == nil {
            // 调试环境下记录社交档案关系缺失，但不自动修复
            // 修复将在具体功能需要时通过专门的方法执行
        }

        // 检查数据档案关系
        if user.dataProfile == nil {
            // 调试环境下记录数据档案关系缺失
        }

        // 检查管理档案关系
        if user.moderationProfile == nil {
            // 调试环境下记录管理档案关系缺失
        }

        // 🔑 注意：这里只进行验证，不执行修复
        // 修复操作将在具体功能模块中按需执行，避免性能影响
    }

    /// 🔑 新增：查找备用用户
    private func findFallbackUser(repositoryContainer: EARepositoryContainer) async throws -> EAUser? {
        // 尝试获取最近创建的用户
        return try await repositoryContainer.userRepository.fetchCurrentUser()
    }

    /// 🔑 新增：从备用用户恢复会话
    @MainActor
    private func restoreFromFallbackUser(_ user: EAUser, token: String) async {
        // 🔑 重构：使用原子化更新，解决微观竞态条件
        let refreshToken = UserDefaults.standard.string(forKey: "refreshToken")
        atomicSetLoggedIn(user: user, accessToken: token, refreshToken: refreshToken)

        // 更新UserDefaults
        UserDefaults.standard.set(user.id.uuidString, forKey: "currentUserId")
        UserDefaults.standard.set(user.username, forKey: "currentUsername")
        UserDefaults.standard.synchronize()

        objectWillChange.send()
        
        // 🔑 新增：发送会话恢复完成通知
        NotificationCenter.default.post(
            name: NSNotification.Name("EASessionLoginCompleted"),
            object: nil,
            userInfo: ["userId": user.id.uuidString, "username": user.username]
        )

        #if DEBUG
        print("🔄 [SessionManager] 从备用用户恢复会话: \(user.username)")
        #endif
    }

    /// 🔑 新增：处理会话恢复错误
    @MainActor
    private func handleSessionRestoreError(_ error: Error, userIdString: String, token: String, repositoryContainer: EARepositoryContainer) async {
        #if DEBUG
        print("❌ [SessionManager] 会话恢复失败: \(error.localizedDescription)")
        #endif

        // 根据错误类型决定处理策略
        if error.localizedDescription.contains("Context") || error.localizedDescription.contains("context") {
            // SwiftData Context相关错误，延迟重试
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                Task { @MainActor in
                    await self?.restoreSessionOnAppLaunch()
                }
            }
        } else {
            // 其他错误，尝试备用恢复
            if let fallbackUser = try? await findFallbackUser(repositoryContainer: repositoryContainer) {
                await restoreFromFallbackUser(fallbackUser, token: token)
            } else {
                await clearSessionData()
                // 🔑 新增：发送会话恢复失败通知
                NotificationCenter.default.post(
                    name: NSNotification.Name("EASessionRestoreFailed"),
                    object: nil
                )
            }
        }
    }

    /// 🔑 新增：尝试自动登录最近用户
    @MainActor
    private func attemptAutoLoginRecentUser(repositoryContainer: EARepositoryContainer) async {
        do {
            if let recentUser = try await repositoryContainer.userRepository.fetchCurrentUser() {
                // 创建临时认证数据
                let tempAuthData = EAAuthData(
                    token: EAToken(
                        accessToken: "temp_token_\(UUID().uuidString)",
                        refreshToken: "temp_refresh_\(UUID().uuidString)"
                    ),
                    user: EAAuthUser(
                        id: recentUser.id.uuidString,
                        username: recentUser.username,
                        email: recentUser.email
                    )
                )

                try await saveSession(authData: tempAuthData, user: recentUser)

                #if DEBUG
                print("🔄 [SessionManager] 自动登录最近用户: \(recentUser.username)")
                #endif
            } else {
                // 🔑 修复：没有用户时保持未登录状态，不创建默认用户
                await clearSessionData()
                
                #if DEBUG
                print("ℹ️ [SessionManager] 没有找到已注册用户，保持未登录状态")
                #endif
            }
        } catch {
            await clearSessionData()
            // 🔑 新增：发送会话恢复失败通知
            NotificationCenter.default.post(
                name: NSNotification.Name("EASessionRestoreFailed"),
                object: nil
            )
            #if DEBUG
            print("❌ [SessionManager] 自动登录失败: \(error.localizedDescription)")
            #endif
        }
    }
    
    // MARK: - 阶段5.2：用户身份监控方法
    
    /// 初始化用户身份监控器
    private func initializeIdentityMonitor(container: EARepositoryContainer) {
        self.identityMonitor = EAUserIdentityMonitor(repositoryContainer: container)
        
        #if DEBUG
        print("✅ [SessionManager] 用户身份监控器初始化完成")
        #endif
    }
    
    /// 启动用户身份监控
    func startIdentityMonitoring() {
        guard isLoggedIn, let monitor = identityMonitor else {
            #if DEBUG
            print("⚠️ [SessionManager] 无法启动身份监控：用户未登录或监控器未初始化")
            #endif
            return
        }
        
        monitor.startMonitoring()
        
        #if DEBUG
        print("🔍 [SessionManager] 用户身份监控已启动")
        #endif
    }
    
    /// 停止用户身份监控
    func stopIdentityMonitoring() {
        identityMonitor?.stopMonitoring()
        
        #if DEBUG
        print("⏹️ [SessionManager] 用户身份监控已停止")
        #endif
    }
    
    // MARK: - 阶段5.3：性能监控方法
    
    /// 记录启动性能指标
    private func recordStartupPerformance(duration: TimeInterval) {
        let thresholdWarning: TimeInterval = 2.0 // 2秒警告阈值
        let thresholdCritical: TimeInterval = 5.0 // 5秒严重阈值
        
        #if DEBUG
        if duration > thresholdCritical {
            print("🚨 [SessionManager] 启动性能严重告警: \(duration * 1000)ms")
        } else if duration > thresholdWarning {
            print("⚠️ [SessionManager] 启动性能告警: \(duration * 1000)ms")
        } else {
            print("✅ [SessionManager] 启动性能正常: \(duration * 1000)ms")
        }
        #endif
        
        // 记录性能数据到用户身份监控器
        if let monitor = identityMonitor {
            Task { @MainActor in
                monitor.performanceMetrics.lastStartupDuration = duration
                monitor.performanceMetrics.startupCount += 1
                
                if duration > thresholdWarning {
                    monitor.performanceMetrics.slowStartupCount += 1
                }
            }
        }
        
        // 发送性能监控通知
        NotificationCenter.default.post(
            name: NSNotification.Name("EAStartupPerformanceRecorded"),
            object: nil,
            userInfo: [
                "duration": duration,
                "isSlowStartup": duration > thresholdWarning
            ]
        )
    }
}

// MARK: - 会话错误类型

enum SessionError: Error, LocalizedError {
    case repositoryNotAvailable
    case userNotFound
    case invalidCredentials
    case networkError
    case userDataCorruption
    
    var errorDescription: String? {
        switch self {
        case .repositoryNotAvailable:
            return "数据访问服务不可用"
        case .userNotFound:
            return "用户未找到"
        case .invalidCredentials:
            return "登录凭证无效"
        case .networkError:
            return "网络连接错误"
        case .userDataCorruption:
            return "用户数据损坏，请重新登录"
        }
    }
}



// MARK: - 认证数据结构（保持兼容性）

struct EAAuthData {
    let token: EAToken
    let user: EAAuthUser
}

struct EAToken {
    let accessToken: String
    let refreshToken: String
}

struct EAAuthUser: Codable {
    let id: String
    let username: String
    let email: String?
}

// MARK: - 扩展：便捷方法

extension EASessionManager {
    
    /// Pro会员剩余天数（异步版本）
    func getProRemainingDays() async -> Int? {
        guard let user = await safeCurrentUser,
              user.isPro,
              let expirationDate = user.proExpirationDate else {
            return nil
        }
        
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: Date(), to: expirationDate)
        return max(0, components.day ?? 0)
    }
    
    /// 检查会话是否有效
    /// 🔑 重构：使用新的ID模式检查会话有效性
    func isSessionValid() -> Bool {
        return isLoggedIn && accessToken != nil && currentUserID != nil
    }
    
    /// 获取最后登录时间
    func getLastLoginDate() -> Date? {
        return userDefaults.object(forKey: lastLoginDateKey) as? Date
    }
}

// MARK: - 扩展：调试方法

#if DEBUG
extension EASessionManager {
    
    /// 打印会话信息（仅调试模式）
    /// 🔑 重构：使用新的异步接口作为内部迁移示例
    func printSessionInfo() async {
        let safeUser = await safeCurrentUser
        print("""
        📱 会话信息:
        - 登录状态: \(isLoggedIn)
        - 用户名: \(safeUser?.username ?? "无")
        - 邮箱: \(safeUser?.email ?? "无")
        - Pro状态: \(isPro)
        - Token存在: \(accessToken != nil)
        - 最后登录: \(getLastLoginDate()?.description ?? "无")
        """)
    }
    
    /// 模拟登录（仅调试模式）
    func simulateLogin() async {
        guard let repositoryContainer = _repositoryContainer else {
            #if DEBUG
            print("⚠️ Repository容器未注入，无法模拟登录")
            #endif
            return
        }
        
        do {
            // 创建模拟用户
            let mockUser = try await repositoryContainer.userRepository.createUser(
                username: "调试用户",
                email: "<EMAIL>"
            )
            
            // 创建模拟认证数据
            let mockAuthData = EAAuthData(
                token: EAToken(
                    accessToken: "debug_access_token",
                    refreshToken: "debug_refresh_token"
                ),
                user: EAAuthUser(
                    id: mockUser.id.uuidString,
                    username: mockUser.username,
                    email: mockUser.email
                )
            )
            
            // 保存会话
            try await saveSession(authData: mockAuthData, user: mockUser)
            
            #if DEBUG
            print("✅ 模拟登录完成")
            #endif
            
        } catch {
            // 模拟登录失败，静默处理
        }
    }
}
#endif
