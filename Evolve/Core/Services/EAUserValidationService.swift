import Foundation
import SwiftUI
import RegexBuilder

/// 用户数据验证服务
/// 负责用户名、邮箱等数据的格式验证和业务规则检查
/// 遵循开发规范文档的"数据验证规范"和"Repository模式强制执行规范"
@MainActor
class EAUserValidationService: ObservableObject {
    
    // MARK: - 依赖注入
    
    private let repositoryContainer: EARepositoryContainer
    
    // MARK: - 验证状态
    
    @Published var isValidating = false
    @Published var validationMessage: String?
    
    // MARK: - 初始化
    
    init(repositoryContainer: EARepositoryContainer) {
        self.repositoryContainer = repositoryContainer
    }
    
    // MARK: - 用户名验证
    
    /// 用户名验证结果
    enum UsernameValidationResult {
        case valid
        case tooShort
        case tooLong
        case invalidCharacters
        case alreadyExists
        case networkError(String)
        
        var message: String {
            switch self {
            case .valid:
                return "用户名可用"
            case .tooShort:
                return "用户名至少需要2个字符"
            case .tooLong:
                return "用户名不能超过20个字符"
            case .invalidCharacters:
                return "用户名只能包含中文、英文字母和数字"
            case .alreadyExists:
                return "用户名已被使用，请选择其他用户名"
            case .networkError(let error):
                return "验证失败：\(error)"
            }
        }
        
        var isValid: Bool {
            if case .valid = self {
                return true
            }
            return false
        }
    }
    
    /// 验证用户名格式和可用性
    /// - Parameters:
    ///   - username: 待验证的用户名
    ///   - excludeCurrentUser: 是否排除当前用户（用于编辑时验证）
    /// - Returns: 验证结果
    func validateUsername(_ username: String, excludeCurrentUser: Bool = false) async -> UsernameValidationResult {
        let trimmedUsername = username.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 1. 长度验证
        if trimmedUsername.count < 2 {
            return .tooShort
        }
        
        if trimmedUsername.count > 20 {
            return .tooLong
        }
        
        // 2. 字符类型验证
        if !isValidUsernameCharacters(trimmedUsername) {
            return .invalidCharacters
        }
        
        // 3. 重复性检查
        do {
            let isAvailable = try await checkUsernameAvailability(trimmedUsername, excludeCurrentUser: excludeCurrentUser)
            return isAvailable ? .valid : .alreadyExists
        } catch {
            return .networkError(error.localizedDescription)
        }
    }
    
    /// 实时用户名验证（带防抖）
    /// - Parameters:
    ///   - username: 用户名
    ///   - excludeCurrentUser: 是否排除当前用户
    /// - Returns: 验证结果
    func validateUsernameRealtime(_ username: String, excludeCurrentUser: Bool = false) async -> UsernameValidationResult {
        isValidating = true
        
        // 防抖延迟
        try? await Task.sleep(nanoseconds: 300_000_000) // 300ms
        
        let result = await validateUsername(username, excludeCurrentUser: excludeCurrentUser)
        
        await MainActor.run {
            self.validationMessage = result.message
            self.isValidating = false
        }
        
        return result
    }
    
    // MARK: - 私有验证方法
    
    /// 检查用户名字符是否有效
    /// 允许：中文汉字、英文字母（大小写）、数字
    /// 禁止：特殊字符、空格、表情符号等
    private func isValidUsernameCharacters(_ username: String) -> Bool {
        // 使用正则表达式验证字符类型
        let pattern = "^[\\u4e00-\\u9fa5a-zA-Z0-9]+$"
        let regex = try? NSRegularExpression(pattern: pattern)
        let range = NSRange(location: 0, length: username.utf16.count)
        return regex?.firstMatch(in: username, options: [], range: range) != nil
    }
    
    /// 检查用户名是否可用
    /// - Parameters:
    ///   - username: 用户名
    ///   - excludeCurrentUser: 是否排除当前用户
    /// - Returns: 是否可用
    private func checkUsernameAvailability(_ username: String, excludeCurrentUser: Bool) async throws -> Bool {
        // 通过Repository检查用户名是否已存在
        let existingUser = try await repositoryContainer.userRepository.fetchUserByUsername(username)

        if existingUser != nil {
            // 如果是当前用户编辑自己的用户名，则允许
            if excludeCurrentUser {
                // 获取当前用户进行比较
                do {
                    let currentUser = try await repositoryContainer.userRepository.fetchCurrentUser()
                    if let currentUser = currentUser, let existingUser = existingUser {
                        // 如果是同一个用户，则允许使用该用户名
                        return currentUser.id == existingUser.id
                    }
                } catch {
                    // 如果无法获取当前用户，保守处理
                    return false
                }
            }
            return false // 用户名已存在
        }

        return true // 用户名可用
    }
    
    // MARK: - 邮箱验证
    
    /// 邮箱验证结果
    enum EmailValidationResult {
        case valid
        case invalid
        case alreadyExists
        case networkError(String)
        
        var message: String {
            switch self {
            case .valid:
                return "邮箱格式正确"
            case .invalid:
                return "请输入有效的邮箱地址"
            case .alreadyExists:
                return "邮箱已被注册"
            case .networkError(let error):
                return "验证失败：\(error)"
            }
        }
        
        var isValid: Bool {
            if case .valid = self {
                return true
            }
            return false
        }
    }
    
    /// 验证邮箱格式和可用性
    func validateEmail(_ email: String) async -> EmailValidationResult {
        let trimmedEmail = email.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 格式验证
        if !isValidEmailFormat(trimmedEmail) {
            return .invalid
        }
        
        // 重复性检查
        do {
            let existingUser = try await repositoryContainer.userRepository.fetchUserByEmail(email: trimmedEmail)
            return existingUser == nil ? .valid : .alreadyExists
        } catch {
            return .networkError(error.localizedDescription)
        }
    }
    
    /// 检查邮箱格式是否有效
    private func isValidEmailFormat(_ email: String) -> Bool {
        let emailPattern = "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
        let regex = try? NSRegularExpression(pattern: emailPattern)
        let range = NSRange(location: 0, length: email.utf16.count)
        return regex?.firstMatch(in: email, options: [], range: range) != nil
    }
    
    // MARK: - 阶段3.1：用户身份完整性检查
    
    /// 用户身份完整性状态
    enum UserIntegrityStatus {
        case valid
        case missingProfile
        case corruptedData
        case partialInitialization
        case unknown
    }
    
    /// 用户完整性检查报告
    struct UserIntegrityReport {
        let status: UserIntegrityStatus
        let issues: [String]
        let canAutoRepair: Bool
        let repairActions: [RepairAction]
    }
    
    /// 修复操作类型
    enum RepairAction {
        case createMissingProfile
        case reinitializeDigitalUniverseData
        case restoreDefaultSettings
        case rebuildUserRelationships
    }
    
    /// 🔑 阶段3.1：全面用户身份完整性检查
    /// 检查用户数据的完整性，包括基础字段、关系完整性和社交档案
    /// - Parameter user: 待检查的用户
    /// - Returns: 完整性检查报告
    func performIntegrityCheck(for user: EAUser) async -> UserIntegrityReport {
        let startTime = CFAbsoluteTimeGetCurrent()
        defer {
            let duration = CFAbsoluteTimeGetCurrent() - startTime
            #if DEBUG
            if duration > 0.1 { // 100ms限制
                print("⚠️ 用户完整性检查超时: \(duration * 1000)ms")
            } else {
                print("✅ 用户完整性检查完成: \(duration * 1000)ms")
            }
            #endif
        }
        
        var issues: [String] = []
        var repairActions: [RepairAction] = []
        
        // 🔑 基础字段检查
        if user.username.isEmpty {
            issues.append("用户名为空")
        }
        
        if user.id == UUID() {
            issues.append("用户ID异常")
        }
        
        if user.creationDate > Date() {
            issues.append("用户创建时间异常")
        }
        
        // 🔑 用户设置检查
        if user.settings == nil {
            issues.append("缺少用户设置")
            repairActions.append(.restoreDefaultSettings)
        }
        
        // 🔑 社交档案检查
        if user.socialProfile == nil {
            issues.append("缺少社交档案")
            repairActions.append(.createMissingProfile)
        } else if let profile = user.socialProfile {
            // 社交档案数据完整性检查
            if profile.stellarLevel == nil || profile.stellarLevel! <= 0 {
                issues.append("星际等级异常")
                repairActions.append(.reinitializeDigitalUniverseData)
            }
            
            if profile.totalStellarEnergy == nil {
                issues.append("星际能量数据缺失")
                repairActions.append(.reinitializeDigitalUniverseData)
            }
            
            if profile.explorerTitle == nil || profile.explorerTitle!.isEmpty {
                issues.append("探索者称号缺失")
                repairActions.append(.reinitializeDigitalUniverseData)
            }
            
            if profile.universeRegion == nil || profile.universeRegion!.isEmpty {
                issues.append("宇宙区域数据缺失")
                repairActions.append(.reinitializeDigitalUniverseData)
            }
        }
        
        // 🔑 数据档案检查
        if user.dataProfile == nil {
            issues.append("缺少数据档案")
            repairActions.append(.rebuildUserRelationships)
        }
        
        // 🔑 管理档案检查
        if user.moderationProfile == nil {
            issues.append("缺少管理档案")
            repairActions.append(.rebuildUserRelationships)
        }
        
        // 🔑 关系完整性检查（仅对老用户）
        let daysSinceCreation = Date().timeIntervalSince(user.creationDate) / 86400
        if daysSinceCreation > 1 && user.habits.isEmpty {
            // 创建超过1天的用户应该有一些数据
            issues.append("用户数据异常稀少")
        }
        
        // 🔑 确定状态
        let status: UserIntegrityStatus
        if issues.isEmpty {
            status = .valid
        } else if repairActions.contains(.createMissingProfile) {
            status = .missingProfile
        } else if repairActions.contains(.reinitializeDigitalUniverseData) {
            status = .partialInitialization
        } else {
            status = .corruptedData
        }
        
        return UserIntegrityReport(
            status: status,
            issues: issues,
            canAutoRepair: !repairActions.isEmpty,
            repairActions: repairActions
        )
    }
    
    /// 🔑 阶段3.1：自动修复用户数据
    /// 根据检查报告自动修复用户数据问题
    /// - Parameters:
    ///   - user: 待修复的用户
    ///   - repairActions: 修复操作列表
    func autoRepairUserData(for user: EAUser, repairActions: [RepairAction]) async throws {
        let startTime = CFAbsoluteTimeGetCurrent()
        defer {
            let duration = CFAbsoluteTimeGetCurrent() - startTime
            #if DEBUG
            print("🔧 用户数据自动修复耗时: \(duration * 1000)ms")
            #endif
        }
        
        for action in repairActions {
            switch action {
            case .createMissingProfile:
                try await createMissingSocialProfile(for: user)
            case .reinitializeDigitalUniverseData:
                try await reinitializeUserDigitalData(for: user)
            case .restoreDefaultSettings:
                try await restoreUserDefaultSettings(for: user)
            case .rebuildUserRelationships:
                try await rebuildUserRelationships(for: user)
            }
        }
    }
    
    // MARK: - 私有修复方法
    
    /// 创建缺失的社交档案
    private func createMissingSocialProfile(for user: EAUser) async throws {
        #if DEBUG
        print("🔧 为用户创建缺失的社交档案: \(user.username)")
        #endif
        
        // 暂时标记为需要通过Repository实现
        // 当前架构下，Repository没有直接创建社交档案的方法
        // 这需要在Repository层添加相应的方法
        throw ValidationError.repairNotImplemented("社交档案创建需要通过Repository层实现")
    }
    
    /// 重新初始化用户数字宇宙数据
    private func reinitializeUserDigitalData(for user: EAUser) async throws {
        #if DEBUG
        print("🔧 重新初始化用户数字宇宙数据: \(user.username)")
        #endif

        // ✅ 修复：移除未使用的变量，直接检查socialProfile存在性
        guard user.socialProfile != nil else {
            throw ValidationError.missingProfile
        }

        // 暂时标记为需要通过Repository实现
        // 当前架构下，需要在Repository层添加数字宇宙数据重新初始化的方法
        throw ValidationError.repairNotImplemented("数字宇宙数据重新初始化需要通过Repository层实现")
    }
    
    /// 恢复用户默认设置
    private func restoreUserDefaultSettings(for user: EAUser) async throws {
        #if DEBUG
        print("🔧 恢复用户默认设置: \(user.username)")
        #endif
        
        // 通过Repository创建默认设置
        // 这里应该调用Repository的方法，但当前Repository没有这个方法
        // 暂时直接创建，后续可以优化
        if user.settings == nil {
            // 这里需要通过Repository保存，但当前架构限制
            // 暂时标记为需要优化的地方
            throw ValidationError.repairNotImplemented("用户设置修复功能待实现")
        }
    }
    
    /// 重建用户关系数据
    private func rebuildUserRelationships(for user: EAUser) async throws {
        #if DEBUG
        print("🔧 重建用户关系数据: \(user.username)")
        #endif
        
        // 检查并创建缺失的档案
        if user.dataProfile == nil || user.moderationProfile == nil {
            // 这里需要通过Repository创建档案
            // 暂时标记为需要优化的地方
            throw ValidationError.repairNotImplemented("用户档案重建功能待实现")
        }
    }
}

// MARK: - 验证错误类型

enum ValidationError: Error, LocalizedError {
    case missingProfile
    case repairNotImplemented(String)
    case dataCorruption(String)
    
    var errorDescription: String? {
        switch self {
        case .missingProfile:
            return "用户档案缺失"
        case .repairNotImplemented(let message):
            return "修复功能未实现: \(message)"
        case .dataCorruption(let message):
            return "数据损坏: \(message)"
        }
    }
}

// MARK: - Environment支持

/// Environment扩展，支持用户验证服务依赖注入
extension EnvironmentValues {
    @Entry var userValidationService: EAUserValidationService? = nil
}
