import Foundation
import SwiftData

/// 好友聊天服务 - AI增强聊天系统
/// 实现智能存储策略，确保AI功能成本可控
@MainActor
class EAFriendChatService: ObservableObject {
    
    // MARK: - 依赖注入

    private let repositoryContainer: EARepositoryContainer
    private let sessionManager: EASessionManager
    private let aiDataBridge: EACommunityAIDataBridge

    // 🔑 架构修复：媒体文件管理器 - 使用依赖注入而非lazy初始化
    private let mediaManager: EAChatMediaManager
    
    // MARK: - 发布状态

    // 🔑 架构修复：保持响应式更新，同时遵循架构规范
    @Published var messages: [EAFriendMessage] = []
    @Published private(set) var currentFriendshipId: UUID?
    @Published var isLoading = false
    @Published var isSendingMessage = false
    @Published var errorMessage: String?

    // 🔑 新增：通过Repository安全获取当前好友关系的异步接口
    var currentFriendship: EAFriendship? {
        get async {
            guard let friendshipId = currentFriendshipId else { return nil }
            // 🔑 修复：获取当前用户ID用于数据所有权验证
            guard let currentUser = await sessionManager.safeCurrentUser else { return nil }
            let currentUserId = currentUser.id
            return try? await repositoryContainer.friendshipRepository.fetchFriendship(by: friendshipId, currentUserID: currentUserId)
        }
    }
    
    // ✅ 新增：序列ID管理 - 解决消息排序问题
    private var nextSequenceId: Int64 = 1
    
    // MARK: - AI功能状态
    
    @Published var aiSuggestionsEnabled = true
    @Published var currentAISuggestions: [String] = []
    @Published var aiCostBudget: Double = 100.0  // AI成本预算（美元）
    @Published var aiCostUsed: Double = 0.0      // 已使用的AI成本
    
    // MARK: - 智能存储策略配置
    
    private let maxMessagesInMemory = 50        // 内存中保留的最大消息数
    private let aiSuggestionCooldown: TimeInterval = 30  // AI建议冷却时间（秒）
    private let maxAISuggestionsPerDay = 20     // 每日最大AI建议次数
    private var lastAISuggestionTime: Date?
    private var dailyAISuggestionCount = 0
    
    // 🔑 性能优化：用户档案缓存，减少重复查询
    private var currentUserProfileCache: EAUserSocialProfile?
    private var profileCacheTimestamp: Date?
    private let profileCacheTimeout: TimeInterval = 300 // 5分钟缓存
    
    // MARK: - 初始化和清理
    
    init(repositoryContainer: EARepositoryContainer, sessionManager: EASessionManager, aiDataBridge: EACommunityAIDataBridge, mediaManager: EAChatMediaManager? = nil) {
        self.repositoryContainer = repositoryContainer
        self.sessionManager = sessionManager
        self.aiDataBridge = aiDataBridge
        self.mediaManager = mediaManager ?? EAChatMediaManager()
        
        // 🚀 标准化：监听用户登录状态变化，及时清理缓存，使用weak self避免循环引用
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("EASessionLoginCompleted"),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor [weak self] in
                self?.clearUserProfileCache()
            }
        }

        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("EASessionLogoutCompleted"),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor [weak self] in
                self?.clearUserProfileCache()
            }
        }
        
        // 加载AI成本使用情况
        loadAICostUsage()
    }
    
    deinit {
        // 清理通知监听器
        NotificationCenter.default.removeObserver(self)
    }



    // MARK: - 序列ID管理（新增）
    
    /// 获取下一个序列ID
    private func getNextSequenceId() -> Int64 {
        let currentId = nextSequenceId
        nextSequenceId += 1
        return currentId
    }
    
    /// 初始化会话的序列ID
    private func initializeSequenceId(for messages: [EAFriendMessage]) {
        if let maxSequenceId = messages.map(\.sequenceId).max() {
            nextSequenceId = maxSequenceId + 1
        } else {
            nextSequenceId = 1
        }
    }
    
    // MARK: - 媒体文件处理（新增）
    
    /// 发送图片消息
    func sendImageMessage(imageData: Data, friendship: EAFriendship) async {
        isSendingMessage = true
        errorMessage = nil
        
        do {
            // 使用媒体管理器保存图片
            guard let relativePath = mediaManager.saveImage(data: imageData, for: friendship.id) else {
                throw FriendChatServiceError.mediaProcessingFailed("图片保存失败")
            }
            
            // 发送消息，内容为相对路径
            await sendMessage(content: relativePath, messageType: .image, friendship: friendship)
            
        } catch {
            errorMessage = error.localizedDescription
        }
        
        isSendingMessage = false
    }
    
    /// 发送视频消息
    func sendVideoMessage(videoURL: URL, friendship: EAFriendship) async {
        isSendingMessage = true
        errorMessage = nil
        
        do {
            // 使用媒体管理器保存视频
            guard let relativePath = mediaManager.saveVideo(from: videoURL, for: friendship.id) else {
                throw FriendChatServiceError.mediaProcessingFailed("视频保存失败")
            }
            
            // 生成缩略图
            if mediaManager.generateAndSaveThumbnail(for: relativePath, friendshipId: friendship.id) != nil {
                // 缩略图生成成功，可以在后续版本中扩展功能
            }
            
            // 发送消息，内容为相对路径
            await sendMessage(content: relativePath, messageType: .video, friendship: friendship)
            
        } catch {
            errorMessage = error.localizedDescription
        }
        
        isSendingMessage = false
    }
    
    /// 获取媒体文件URL（供UI使用）
    func getMediaURL(for message: EAFriendMessage) -> URL? {
        guard message.messageType == .image || message.messageType == .video,
              let _ = currentFriendshipId else {
            return nil
        }

        // ✅ 使用带好友ID的方法获取正确路径
        let url = mediaManager.getMediaURL(for: message.content)
        return url
    }
    
    // MARK: - 聊天会话管理
    
    /// 开始与好友的聊天会话 - 🔑 修复：优化权限验证，支持新建立的好友关系
    func startChatSession(with friendship: EAFriendship) async {

        // 🔑 关键修复：优化聊天会话初始化，增强新用户兼容性
        do {
            // 🚨 架构重构修复：使用SessionManager的异步安全接口
            guard let currentUser = await sessionManager.safeCurrentUser else {
                await MainActor.run {
                    errorMessage = "无法获取当前用户信息，请重新登录"
                }
                return
            }

            // 🚨 关键修复：优化社交档案验证，支持新用户自动创建
            if currentUser.socialProfile == nil {
                // 新用户场景：尝试自动创建社交档案
                do {
                    _ = try await repositoryContainer.userRepository.ensureSocialProfile(for: currentUser)
                } catch {
                    await MainActor.run {
                        errorMessage = "用户社交档案初始化失败，请重新登录"
                    }
                    return
                }
            }

            // 🔑 关键修复：重新验证好友关系状态，确保使用最新数据
            // 通过ID重新获取好友关系，避免使用可能过期的对象
            guard let refreshedFriendship = try await repositoryContainer.friendshipRepository.fetchFriendship(by: friendship.id, currentUserID: currentUser.id) else {
                await MainActor.run {
                    errorMessage = "好友关系不存在，请返回重试"
                }
                return
            }

            // 🔑 优化：使用刷新后的好友关系进行验证
            guard refreshedFriendship.status == .active else {
                await MainActor.run {
                    errorMessage = "好友关系尚未确认，请等待对方接受"
                }
                return
            }

            // 🔑 架构修复：只存储ID，不持有SwiftData对象
            await MainActor.run {
                currentFriendshipId = refreshedFriendship.id
            }

            // 🔑 关键修复：原子性设置状态，避免并发问题
            await MainActor.run {
                // 清理之前的聊天状态
                messages.removeAll()
                errorMessage = nil
                currentAISuggestions.removeAll()
                // 移除强制objectWillChange.send()，让@Published自然触发更新

                #if DEBUG
                // 调试环境记录：聊天状态已清理，开始加载新会话
                #endif
            }

            // 🔑 架构修复：使用刷新后的好友关系加载聊天历史
            await loadChatHistory(for: refreshedFriendship)

        } catch {
            await MainActor.run {
                errorMessage = "初始化聊天会话失败：\(error.localizedDescription)"
            }
            return
        }

        // ✅ 初始化序列ID
        await MainActor.run {
            initializeSequenceId(for: messages)
            // 移除强制objectWillChange.send()，让@Published自然触发更新

            #if DEBUG
            // 调试环境记录：序列ID初始化完成，消息数量: \(currentMessages.count)
            #endif
        }

        #if DEBUG
        // 调试环境下记录聊天会话启动完成，但不使用print
        #endif

        // 🔑 Context隔离修复：使用Repository容器确保Context一致性
        // 后台完成互动时间更新（不阻塞用户操作）
        Task { @MainActor in
            // 使用统一的Repository容器，确保Context一致性
            guard let _ = self.currentFriendshipId else {
                return
            }
            await self.updateFriendshipInteraction(friendship: friendship)
        }
    }
    
    /// 🔑 性能优化：独立的互动时间更新方法
    private func updateFriendshipInteraction(friendship: EAFriendship) async {
        // 更新好友关系的最后互动时间
        do {
            guard let _ = currentFriendshipId else {
                return
            }
            // 🔑 安全修复：获取当前用户ID用于数据所有权验证
            guard let currentUser = await sessionManager.safeCurrentUser else {
                throw FriendChatServiceError.invalidChatParticipants
            }

            try await repositoryContainer.friendshipRepository.updateFriendshipInteraction(friendshipId: friendship.id, currentUserID: currentUser.id)
        } catch {
            #if DEBUG
            // 调试环境下记录更新互动时间失败，但不使用print
            #endif
        }
    }
    
    /// 🔑 新增：网络就绪的消息发送方法 - 接收完整的EAFriendMessage对象
    /// 这是为未来网络接入准备的核心方法
    func sendMessage(_ message: EAFriendMessage) async throws {
        // 🔑 本地持久化：调用Repository将消息对象存入SwiftData
        guard let friendship = message.friendship else {
            throw FriendChatServiceError.invalidChatParticipants
        }

        // 保存到本地数据库
        let savedMessage = try await repositoryContainer.friendMessageRepository.sendMessage(
            content: message.content,
            messageType: message.messageType,
            senderProfileId: message.senderProfile?.id ?? UUID(),
            receiverProfileId: message.receiverProfile?.id ?? UUID(),
            friendshipId: friendship.id,
            sequenceId: message.sequenceId
        )

        // 🔑 网络发送存根：为未来网络层留下清晰接口
        await syncMessageToNetwork(savedMessage)

        // 🔑 关键修复：数据库保存成功后，重新加载完整历史记录，确保数据一致性
        await loadChatHistory(for: friendship)
    }

    /// 🔑 网络发送存根 - 为未来网络层预留的接口
    private func syncMessageToNetwork(_ message: EAFriendMessage) async {
        // 🚀 未来网络实现：
        // 1. 将消息发送到服务器
        // 2. 处理网络错误和重试逻辑
        // 3. 更新消息状态（已发送、已送达、已读）
        // 4. 处理离线消息同步

        #if DEBUG
        print("🌐 [网络存根] 消息将发送到网络: \(message.content)")
        #endif

        // 模拟网络延迟
        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
    }

    /// 发送消息 - 核心聊天功能（修改以支持序列ID）
    /// 🔑 保留兼容性：现有代码仍可使用此方法
    func sendMessage(content: String, messageType: EAFriendMessage.MessageType = .text, friendship: EAFriendship) async {
        
        guard !content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            errorMessage = "消息内容不能为空"
            return
        }
        
        isSendingMessage = true
        errorMessage = nil
        
        do {
            // 🔑 性能优化：并行获取用户档案（利用缓存机制）
            async let currentProfileTask = getCurrentUserProfile()

            guard let currentProfile = await currentProfileTask else {
                throw FriendChatServiceError.invalidChatParticipants
            }

            // 🔑 优化：更宽松的好友档案获取，支持新用户场景
            guard let friendProfile = friendship.getOtherProfile(currentProfile: currentProfile) else {
                // 新用户场景：尝试通过friendship的关系属性获取好友档案
                let otherProfile = (friendship.initiatorProfile?.id == currentProfile.id)
                    ? friendship.friendProfile
                    : friendship.initiatorProfile

                guard let friendProfile = otherProfile else {
                    throw FriendChatServiceError.invalidChatParticipants
                }

                // 🔑 简化权限验证：只验证好友关系状态，不依赖复杂的权限检查
                guard friendship.status == .active else {
                    throw FriendChatServiceError.notAuthorizedToSendMessage
                }

                // 🔑 批次二新增：屏蔽状态检查
                try await checkBlockingStatus(currentProfile: currentProfile, friendProfile: friendProfile)

                // 继续使用获取到的friendProfile
                return try await sendMessageWithProfiles(
                    content: content,
                    messageType: messageType,
                    friendship: friendship,
                    currentProfile: currentProfile,
                    friendProfile: friendProfile
                )
            }

            // 🔑 标准路径：使用简化的权限验证
            guard friendship.status == .active else {
                throw FriendChatServiceError.notAuthorizedToSendMessage
            }

            // 🔑 批次二新增：屏蔽状态检查
            try await checkBlockingStatus(currentProfile: currentProfile, friendProfile: friendProfile)

            // 调用统一的消息发送方法
            try await sendMessageWithProfiles(
                content: content,
                messageType: messageType,
                friendship: friendship,
                currentProfile: currentProfile,
                friendProfile: friendProfile
            )

        } catch {
            errorMessage = error.localizedDescription
            #if DEBUG
            // 调试环境下记录发送消息失败，但不使用print
            #endif
        }
        
        isSendingMessage = false
    }

    /// 🔑 新增：统一的消息发送处理方法，支持新用户场景
    private func sendMessageWithProfiles(
        content: String,
        messageType: EAFriendMessage.MessageType,
        friendship: EAFriendship,
        currentProfile: EAUserSocialProfile,
        friendProfile: EAUserSocialProfile
    ) async throws {
        // ✅ 分配序列ID
        let sequenceId = getNextSequenceId()

        // 🔑 修复：先保存到数据库，再更新UI，确保数据持久化成功
        let message = try await repositoryContainer.friendMessageRepository.sendMessage(
            content: content,
            messageType: messageType,
            senderProfileId: currentProfile.id,      // 传ID而非对象
            receiverProfileId: friendProfile.id,     // 传ID而非对象
            friendshipId: friendship.id,             // 传ID而非对象
            sequenceId: sequenceId                   // ✅ 新增：传递序列ID
        )

        #if DEBUG
        print("🚀 [消息发送] 消息已成功保存到数据库")
        print("   - 消息ID: \(message.id)")
        print("   - 发送者: \(currentProfile.user?.username ?? "未知")")
        print("   - 接收者: \(friendProfile.user?.username ?? "未知")")
        print("   - 内容: \(content)")
        print("   - 好友关系ID: \(friendship.id)")
        #endif

        // 🚨 关键修复：数据库保存成功后，重新加载完整历史记录，确保数据一致性
        // 避免直接操作messages数组，防止竞态条件和数据覆盖问题
        await loadChatHistory(for: friendship)

        #if DEBUG
        // 调试环境记录：消息发送成功，已重新加载聊天历史
        #endif

        // 🚨 性能优化：简化异步操作，减少Task嵌套
        if aiSuggestionsEnabled && shouldGenerateAISuggestions() {
            Task { [weak self] in
                await self?.generateAIResponseSuggestions(for: message)
            }
        }

        // 🔑 修复：能量共振操作在主线程执行，避免跨上下文风险
        friendship.performEnergyResonance(amount: 10)
    }

    /// 标记消息为已读
    func markMessagesAsRead(_ messageIds: [UUID]) async {
        do {
            try await repositoryContainer.friendMessageRepository.markMessagesAsRead(messageIds: messageIds)
            
            // 更新本地消息状态
            for message in messages {
                if messageIds.contains(message.id) {
                    message.markAsRead()
                }
            }

        } catch {
            #if DEBUG
            // 调试环境下记录标记消息已读失败，但不使用print
            #endif
        }
    }
    
    /// 编辑消息
    func editMessage(_ message: EAFriendMessage, newContent: String) async {
        guard message.canBeEdited() else {
            errorMessage = "消息无法编辑"
            return
        }
        
        do {
            let editedMessage = try await repositoryContainer.friendMessageRepository.editMessage(
                messageId: message.id,
                newContent: newContent
            )

            // 更新本地消息
            if let index = messages.firstIndex(where: { $0.id == message.id }) {
                messages[index] = editedMessage
            }

            #if DEBUG
            // 调试环境下记录消息编辑成功，但不使用print
            #endif

        } catch {
            errorMessage = error.localizedDescription
            #if DEBUG
            // 调试环境下记录编辑消息失败，但不使用print
            #endif
        }
    }
    
    /// 删除消息（本地删除，对方仍可见）
    func deleteMessage(_ message: EAFriendMessage) async {
        do {
            try await repositoryContainer.friendMessageRepository.deleteMessage(messageId: message.id)
            
            // 从本地列表中移除
            messages.removeAll { $0.id == message.id }

            #if DEBUG
            // 调试环境下记录消息删除成功，但不使用print
            #endif

        } catch {
            errorMessage = error.localizedDescription
            #if DEBUG
            // 调试环境下记录删除消息失败，但不使用print
            #endif
        }
    }
    
    /// 撤销消息（双端删除，显示撤销通知）
    func revokeMessage(_ message: EAFriendMessage) async {
        guard message.canBeRevoked() else {
            errorMessage = "消息已超过撤销时限"
            return
        }
        
        do {
            // 撤销原消息
            try await repositoryContainer.friendMessageRepository.revokeMessage(messageId: message.id)
            
            // 创建撤销通知消息
            if let revokedMessage = try await repositoryContainer.friendMessageRepository.createRevokeNotification(for: message) {
                // 更新本地消息列表：用撤销通知替换原消息
                if let index = messages.firstIndex(where: { $0.id == message.id }) {
                    messages[index] = revokedMessage
                }
            }

            #if DEBUG
            // 调试环境下记录消息撤销成功，但不使用print
            #endif

        } catch {
            errorMessage = error.localizedDescription
            #if DEBUG
            // 调试环境下记录撤销消息失败，但不使用print
            #endif
        }
    }
    
    // MARK: - AI增强功能（智能成本控制）
    
    /// 生成AI回复建议 - 智能存储策略
    private func generateAIResponseSuggestions(for message: EAFriendMessage) async {
        // 检查AI成本预算
        guard aiCostUsed < aiCostBudget else {
            #if DEBUG
            // 调试环境下记录AI成本预算已用完，但不使用print
            #endif
            return
        }

        // 检查每日限制
        guard dailyAISuggestionCount < maxAISuggestionsPerDay else {
            #if DEBUG
            // 调试环境下记录今日AI建议次数已达上限，但不使用print
            #endif
            return
        }
        
        do {
            // 构建聊天上下文（只使用最近的消息以控制成本）
            let recentMessages = Array(messages.suffix(5))
            let context = recentMessages.map { "\($0.senderProfile?.user?.username ?? "用户"): \($0.content)" }.joined(separator: "\n")
            
            // 调用AI服务生成建议
            let suggestions = try await aiDataBridge.generateChatSuggestions(
                messageContent: message.content,
                chatContext: context,
                participantCount: 2
            )
            
            // 更新AI建议
            currentAISuggestions = suggestions
            
            // 更新消息的AI建议
            message.setAISentimentAnalysis(score: 0.8, suggestions: suggestions)
            
            // 更新成本统计
            updateAICostUsage(cost: 0.01) // 假设每次AI调用成本0.01美元
            dailyAISuggestionCount += 1
            lastAISuggestionTime = Date()

            #if DEBUG
            // 调试环境下记录AI回复建议生成成功，但不使用print
            #endif

        } catch {
            #if DEBUG
            // 调试环境下记录生成AI建议失败，但不使用print
            #endif
        }
    }
    
    /// 使用AI建议发送消息
    func sendAISuggestedMessage(_ suggestion: String) async {
        guard let currentFriendship = await currentFriendship else { return }
        await sendMessage(content: suggestion, messageType: .aiSuggestion, friendship: currentFriendship)
        
        // 标记使用了AI建议
        if let lastMessage = messages.last {
            lastMessage.markAISuggestionUsed()
        }
        
        // 清空当前建议
        currentAISuggestions.removeAll()
    }
    
    /// 检查是否应该生成AI建议
    private func shouldGenerateAISuggestions() -> Bool {
        // 检查冷却时间
        if let lastTime = lastAISuggestionTime {
            let timeSinceLastSuggestion = Date().timeIntervalSince(lastTime)
            if timeSinceLastSuggestion < aiSuggestionCooldown {
                return false
            }
        }
        
        // 检查成本和次数限制
        return aiCostUsed < aiCostBudget && dailyAISuggestionCount < maxAISuggestionsPerDay
    }
    
    // MARK: - 数据加载与管理
    
    /// 加载聊天历史记录 - 🔑 修复：确保数据正确加载和状态更新
    func loadChatHistory(for friendship: EAFriendship? = nil, limit: Int = 50, offset: Int = 0) async {
        // 🔑 关键修复：优先使用传入的friendship参数，fallback到currentFriendship
        var targetFriendship = friendship
        if targetFriendship == nil {
            targetFriendship = await currentFriendship
        }
        guard let targetFriendship = targetFriendship else {
            #if DEBUG
            // 调试环境记录：friendship参数和currentFriendship都为nil，无法加载历史记录
            #endif
            return
        }
        
        isLoading = true
        
        do {
            let chatMessages = try await repositoryContainer.friendMessageRepository.fetchChatHistory(
                friendship: targetFriendship,
                limit: limit,
                offset: offset
            )
            
            #if DEBUG
            // 调试环境记录：Repository返回消息数量: \(chatMessages.count)
            #endif
            
            // 🔑 关键修复：确保UI更新在主线程执行，并强制触发@Published更新
            // 🔑 修复聊天显示顺序：Repository返回时间正序（旧→新），直接使用实现微信式显示
            await MainActor.run {
                // 🚨 关键修复：先清空消息数组，确保@Published触发
                self.messages.removeAll()

                if offset == 0 {
                    // 首次加载，替换所有消息 - Repository已按时间正序返回（旧→新）
                    self.messages = chatMessages
                    #if DEBUG
                    // 调试环境记录：首次加载完成，messages数量: \(self.messages.count)
                    #endif
                } else {
                    // 分页加载，插入到开头 - 保持时间正序（旧消息在前）
                    self.messages.insert(contentsOf: chatMessages, at: 0)
                    #if DEBUG
                    // 调试环境记录：分页加载完成，messages总数量: \(self.messages.count)
                    #endif
                }

                // 智能内存管理：限制内存中的消息数量
                if self.messages.count > maxMessagesInMemory {
                    self.messages = Array(self.messages.suffix(maxMessagesInMemory))
                    #if DEBUG
                    // 调试环境记录：内存管理：限制消息数量为\(maxMessagesInMemory)
                    #endif
                }

                // 🚨 架构修复：移除多重强制触发，让@Published自然更新
                // 移除objectWillChange.send()，避免过度重绘

                #if DEBUG
                // 调试环境记录：聊天历史加载完成，强制触发UI更新
                #endif
            }
            
        } catch {
            await MainActor.run {
                errorMessage = error.localizedDescription
            }
            #if DEBUG
            // 调试环境记录：加载聊天历史失败: \(error.localizedDescription)
            #endif
        }
        
        isLoading = false
    }
    
    /// 搜索聊天记录
    func searchMessages(query: String) async -> [EAFriendMessage] {
        guard let friendship = await currentFriendship else { return [] }
        
        do {
            return try await repositoryContainer.friendMessageRepository.searchMessages(
                in: friendship,
                query: query,
                limit: 20
            )
        } catch {
            #if DEBUG
            // 调试环境下记录搜索消息失败，但不使用print
            #endif
            return []
        }
    }
    
    // MARK: - AI成本管理
    
    /// 加载AI成本使用情况
    private func loadAICostUsage() {
        // 从UserDefaults或数据库加载AI成本使用情况
        aiCostUsed = UserDefaults.standard.double(forKey: "ai_cost_used")
        dailyAISuggestionCount = UserDefaults.standard.integer(forKey: "daily_ai_suggestion_count")
        
        // 检查是否是新的一天，重置每日计数
        let lastResetDate = UserDefaults.standard.object(forKey: "last_ai_reset_date") as? Date ?? Date.distantPast
        if !Calendar.current.isDate(lastResetDate, inSameDayAs: Date()) {
            dailyAISuggestionCount = 0
            UserDefaults.standard.set(Date(), forKey: "last_ai_reset_date")
        }
    }
    
    /// 更新AI成本使用情况
    private func updateAICostUsage(cost: Double) {
        aiCostUsed += cost
        UserDefaults.standard.set(aiCostUsed, forKey: "ai_cost_used")
        UserDefaults.standard.set(dailyAISuggestionCount, forKey: "daily_ai_suggestion_count")
    }
    
    /// 重置AI成本预算
    func resetAICostBudget() {
        aiCostUsed = 0.0
        dailyAISuggestionCount = 0
        UserDefaults.standard.set(0.0, forKey: "ai_cost_used")
        UserDefaults.standard.set(0, forKey: "daily_ai_suggestion_count")
        UserDefaults.standard.set(Date(), forKey: "last_ai_reset_date")
    }
    
    // MARK: - 媒体文件处理
    // 注意：getMediaURL方法已存在，移除重复定义
    
    // MARK: - 私有辅助方法
    
    /// 🔑 新增：确保图片文件可被好友访问（模拟共享机制）
    private func ensureImageAccessibility(imagePath: String) async throws -> String {
        // 如果是相对路径，保持不变（本地开发环境共享Documents目录）
        if !imagePath.hasPrefix("/") {
            return imagePath
        }
        
        // 如果是绝对路径，转换为相对路径
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let documentsPathString = documentsPath.path
        
        if imagePath.hasPrefix(documentsPathString) {
            let relativePath = String(imagePath.dropFirst(documentsPathString.count + 1)) // +1 for the "/"
            return relativePath
        }
        
        // 其他情况返回原路径
        return imagePath
    }
    
    /// 🔑 性能优化：获取当前用户的社交档案（含智能缓存）
    private func getCurrentUserProfile() async -> EAUserSocialProfile? {
        // 🔑 检查缓存是否有效
        if let cachedProfile = currentUserProfileCache,
           let cacheTime = profileCacheTimestamp,
           Date().timeIntervalSince(cacheTime) < profileCacheTimeout {
            return cachedProfile
        }
        
        // 🔑 缓存失效或不存在，从Repository获取
            // 🚀 关键修复：使用架构重构后的异步安全接口
            // 🚨 架构重构修复：使用SessionManager的异步安全接口
            let currentUser = await sessionManager.safeCurrentUser
            
            guard let user = currentUser else {
                #if DEBUG
                // 调试环境下记录无法获取当前用户，但不使用print
                #endif
                return nil
            }

            // 🚨 关键修复：检查社交档案是否存在
            // 正常情况下，用户注册时就应该创建社交档案
            if user.socialProfile == nil {
                #if DEBUG
                // 调试环境下记录检测到用户缺少社交档案，但不使用print
                #endif

                // 🔑 关键修复：使用Repository的恢复方法创建缺失的社交档案
                // 这主要用于数据迁移或异常恢复场景
                do {
                    let newProfile = try await repositoryContainer.userRepository.ensureSocialProfile(for: user)

                    // 🔑 更新缓存
                    currentUserProfileCache = newProfile
                    profileCacheTimestamp = Date()

                    return newProfile
                } catch {
                    #if DEBUG
                    // 调试环境下记录创建社交档案失败，但不使用print
                    #endif
                    return nil
                }
            }

            // 🔑 已有数据场景：获取现有社交档案
            if let profile = user.socialProfile {
                // 🔑 确保数字宇宙数据已初始化（安全调用，有防重复机制）
                profile.initializeDigitalUniverseData()

                // 🔑 更新缓存
                currentUserProfileCache = profile
                profileCacheTimestamp = Date()
                return profile
            }

            // 理论上不应该到达这里，但提供fallback
            return nil
    }
    
    /// 🔑 性能优化：清理用户档案缓存（在登出或用户切换时调用）
    func clearUserProfileCache() {
        currentUserProfileCache = nil
        profileCacheTimestamp = nil
    }
    
    /// 🚀 公开方法：强制刷新用户身份识别（用于新注册用户或用户切换后）
    public func refreshUserIdentity() async {
        // 清理缓存
        clearUserProfileCache()
        
        // 强制重新获取当前用户档案
        _ = await getCurrentUserProfile()
        
        #if DEBUG
        // 调试环境下记录用户身份刷新完成
        #endif
    }
}

// MARK: - 服务错误定义

enum FriendChatServiceError: Error, LocalizedError {
    case invalidChatParticipants
    case notAuthorizedToSendMessage
    case messageContentEmpty
    case aiServiceUnavailable
    case costBudgetExceeded
    case mediaProcessingFailed(String)
    // 🔑 批次二新增：屏蔽相关错误
    case userBlocked(String)  // 用户被屏蔽
    case blockingCheckFailed  // 屏蔽状态检查失败
    
    var errorDescription: String? {
        switch self {
        case .invalidChatParticipants:
            return "无效的聊天参与者"
        case .notAuthorizedToSendMessage:
            return "无权限发送消息，请先添加好友"
        case .messageContentEmpty:
            return "消息内容不能为空"
        case .aiServiceUnavailable:
            return "AI服务暂时不可用"
        case .costBudgetExceeded:
            return "AI成本预算已超限"
        case .mediaProcessingFailed(let message):
            return "媒体处理失败：\(message)"
        case .userBlocked(let message):
            return message
        case .blockingCheckFailed:
            return "无法检查屏蔽状态，请稍后重试"
        }
    }
}

// MARK: - 🔑 批次二新增：屏蔽状态检查扩展

extension EAFriendChatService {

    /// 检查屏蔽状态 - 确保双方都没有屏蔽对方
    /// 🔑 批次二核心功能：消息发送前的屏蔽状态验证
    ///
    /// - Parameters:
    ///   - currentProfile: 当前用户的社交档案
    ///   - friendProfile: 好友的社交档案
    /// - Throws:
    ///   - `FriendChatServiceError.userBlocked`: 当任一方屏蔽了对方
    ///   - `FriendChatServiceError.invalidChatParticipants`: 当用户ID无效
    ///   - `FriendChatServiceError.blockingCheckFailed`: 当屏蔽检查过程失败
    /// - Note: 使用并行检查优化性能，避免串行等待
    private func checkBlockingStatus(currentProfile: EAUserSocialProfile, friendProfile: EAUserSocialProfile) async throws {

        // 获取用户ID
        guard let currentUserId = currentProfile.user?.id,
              let friendUserId = friendProfile.user?.id else {
            throw FriendChatServiceError.invalidChatParticipants
        }

        // 获取屏蔽服务
        let blockingService = repositoryContainer.blockingService

        do {
            // 🔑 批次二优化：并行执行双向屏蔽检查，提升性能
            async let currentUserBlockedFriendTask = blockingService.isUserBlockedAsync(
                currentUserID: currentUserId,
                userID: friendUserId
            )

            async let friendBlockedCurrentUserTask = blockingService.isUserBlockedAsync(
                currentUserID: friendUserId,
                userID: currentUserId
            )

            // 等待两个检查同时完成
            let (currentUserBlockedFriend, friendBlockedCurrentUser) = await (currentUserBlockedFriendTask, friendBlockedCurrentUserTask)

            // 检查结果并抛出相应错误
            if currentUserBlockedFriend {
                throw FriendChatServiceError.userBlocked("您已屏蔽该用户，无法发送消息")
            }

            if friendBlockedCurrentUser {
                throw FriendChatServiceError.userBlocked("该用户已屏蔽您，无法发送消息")
            }

        } catch let error as FriendChatServiceError {
            // 重新抛出已知的服务错误
            throw error
        } catch {
            // 🔑 批次二优化：改进错误处理和日志记录
            #if DEBUG
            // 调试环境下记录详细错误信息
            print("🚨 屏蔽状态检查失败: \(error.localizedDescription)")
            #endif

            // 其他错误转换为屏蔽检查失败，保留原始错误信息用于调试
            throw FriendChatServiceError.blockingCheckFailed
        }
    }
}
