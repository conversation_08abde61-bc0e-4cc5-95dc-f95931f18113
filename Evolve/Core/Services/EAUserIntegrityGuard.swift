//
//  EAUserIntegrityGuard.swift
//  Evolve
//
//  Created by AI Assistant on 2025-01-26.
//  用户身份完整性守护服务 - 阶段1实现
//

import Foundation
import SwiftUI
import SwiftData

/// 用户身份完整性状态
enum UserIntegrityStatus {
    case unknown          // 未检查
    case healthy          // 健康状态
    case needsRepair      // 需要修复
    case repairing        // 修复中
    case repairFailed     // 修复失败
    case degraded         // 降级模式
}

/// 用户身份完整性守护服务
/// 负责用户身份数据的完整性检查、自动修复和状态监控
/// 遵循开发规范文档的Repository模式强制执行规范
@MainActor
final class EAUserIntegrityGuard: ObservableObject {
    
    // MARK: - 依赖注入（遵循开发规范）
    
    private let repositoryContainer: EARepositoryContainer
    
    // MARK: - 性能监控器引用（阶段3新增）
    private weak var performanceThresholdManager: EAPerformanceThresholdManager?
    
    // MARK: - 状态管理
    
    @Published var integrityStatus: UserIntegrityStatus = .unknown
    @Published var lastCheckTime: Date?
    @Published var autoRepairCount: Int = 0
    @Published var isPerformingCheck: Bool = false
    
    // MARK: - 性能监控
    
    private var checkStartTime: CFAbsoluteTime = 0
    private let maxCheckDuration: TimeInterval = 0.1 // 100ms限制
    private let maxRepairDuration: TimeInterval = 1.0 // 1秒限制
    
    // MARK: - 初始化
    
    init(repositoryContainer: EARepositoryContainer) {
        self.repositoryContainer = repositoryContainer
    }
    
    /// 配置性能阈值管理器（阶段3新增）
    func configure(performanceThresholdManager: EAPerformanceThresholdManager) {
        self.performanceThresholdManager = performanceThresholdManager
    }
    
    // MARK: - 核心检查方法
    
    /// 深度安全检查（同步，性能优化）
    /// 检查用户身份数据的完整性和一致性
    /// - Parameter user: 待检查的用户
    /// - Returns: 检查结果，true表示安全
    func deepSafetyCheck(for user: EAUser) -> Bool {
        checkStartTime = CFAbsoluteTimeGetCurrent()
        isPerformingCheck = true
        defer { 
            isPerformingCheck = false
            recordCheckDuration()
        }
        
        // 🔑 基础字段完整性检查
        guard !user.username.isEmpty,
              user.id != UUID(),
              user.creationDate <= Date() else {
            integrityStatus = .needsRepair
            return false
        }
        
        // 🔑 数据一致性检查
        if !validateUserDataConsistency(user) {
            integrityStatus = .needsRepair
            return false
        }
        
        // 🔑 关系完整性检查
        if !validateUserRelationships(user) {
            integrityStatus = .needsRepair
            return false
        }
        
        integrityStatus = .healthy
        lastCheckTime = Date()
        return true
    }
    
    /// 社交档案安全访问（按需创建，轻量级）
    /// 确保用户社交档案存在且可用
    /// - Parameter user: 用户实例
    /// - Returns: 安全的社交档案，失败时返回nil
    func safeSocialProfile(for user: EAUser) async -> EAUserSocialProfile? {
        // 🔑 先检查现有社交档案
        if let existingProfile = user.socialProfile {
            // 验证现有档案的完整性
            if validateSocialProfileIntegrity(existingProfile) {
                return existingProfile
            }
            // 档案损坏，需要修复
            return await repairSocialProfile(existingProfile)
        }
        
        // 🔑 创建轻量级社交档案
        return await createLightweightSocialProfile(for: user)
    }
    
    /// 🔑 阶段3.3：确保用户身份完整性（集成检查和修复）
    /// 执行完整的用户身份检查和修复流程
    /// - Parameter user: 待检查和修复的用户
    /// - Returns: 用户身份是否完整且安全
    func ensureUserIntegrity(for user: EAUser) async -> Bool {
        let startTime = CFAbsoluteTimeGetCurrent()
        defer {
            let duration = CFAbsoluteTimeGetCurrent() - startTime
            #if DEBUG
            if duration > maxCheckDuration {
                print("⚠️ 用户身份完整性确保超时: \(duration * 1000)ms")
            } else {
                print("✅ 用户身份完整性确保完成: \(duration * 1000)ms")
            }
            #endif
        }
        
        // 🔑 第一步：执行深度安全检查
        if deepSafetyCheck(for: user) {
            // 用户数据完整，无需修复
            await MainActor.run {
                self.integrityStatus = .healthy
                self.lastCheckTime = Date()
            }
            return true
        }
        
        // 🔑 第二步：尝试自动修复
        let repairSuccess = await repairUserIntegrity(for: user)
        
        if repairSuccess {
            // 🔑 第三步：验证修复结果
            let finalCheck = deepSafetyCheck(for: user)
            await MainActor.run {
                self.integrityStatus = finalCheck ? .healthy : .repairFailed
                self.lastCheckTime = Date()
            }
            return finalCheck
        } else {
            await MainActor.run {
                self.integrityStatus = .repairFailed
                self.lastCheckTime = Date()
            }
            return false
        }
    }
    
    /// 用户身份完整性修复
    /// 自动修复检测到的用户身份问题
    /// - Parameter user: 需要修复的用户
    /// - Returns: 修复是否成功
    func repairUserIntegrity(for user: EAUser) async -> Bool {
        let repairStartTime = CFAbsoluteTimeGetCurrent()
        integrityStatus = .repairing
        
        defer {
            let repairDuration = CFAbsoluteTimeGetCurrent() - repairStartTime
            if repairDuration > maxRepairDuration {
                #if DEBUG
                print("⚠️ 用户身份修复超时: \(repairDuration)秒")
                #endif
            }
        }
        
        // 🔑 修复基础数据
        if !repairBasicUserData(user) {
            integrityStatus = .repairFailed
            return false
        }
        
        // 🔑 修复社交档案
        if user.socialProfile == nil {
            _ = await createLightweightSocialProfile(for: user)
        } else if let profile = user.socialProfile,
                  !validateSocialProfileIntegrity(profile) {
            _ = await repairSocialProfile(profile)
        }
        
        // 🔑 验证修复结果
        if deepSafetyCheck(for: user) {
            integrityStatus = .healthy
            autoRepairCount += 1
            return true
        } else {
            integrityStatus = .repairFailed
            return false
        }
    }
    
    // MARK: - 私有验证方法
    
    /// 验证用户数据一致性
    private func validateUserDataConsistency(_ user: EAUser) -> Bool {
        // 检查用户名格式
        if user.username.count < 2 || user.username.count > 20 {
            return false
        }
        
        // 检查邮箱格式（如果存在）
        if let email = user.email, !email.isEmpty {
            let emailRegex = "^[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
            let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
            if !emailPredicate.evaluate(with: email) {
                return false
            }
        }
        
        return true
    }
    
    /// 验证用户关系完整性
    private func validateUserRelationships(_ user: EAUser) -> Bool {
        // 检查习惯关系是否正常
        // 注意：这里不能直接访问关系数据，避免触发SwiftData查询
        // 只检查关系引用的基本有效性
        return true
    }
    
    /// 验证社交档案完整性
    private func validateSocialProfileIntegrity(_ profile: EAUserSocialProfile) -> Bool {
        // 检查必要字段（使用可选值安全解包）
        guard let stellarLevel = profile.stellarLevel,
              let totalStellarEnergy = profile.totalStellarEnergy,
              stellarLevel >= 1,
              totalStellarEnergy >= 0 else {
            return false
        }
        
        // 检查数据合理性
        if stellarLevel > 100 || totalStellarEnergy > 1000000 {
            return false
        }
        
        return true
    }
    
    /// 修复基础用户数据
    private func repairBasicUserData(_ user: EAUser) -> Bool {
        // 修复用户名
        if user.username.isEmpty {
            user.username = "用户\(String(user.id.uuidString.prefix(8)))"
        }
        
        // 修复创建时间
        if user.creationDate > Date() {
            user.creationDate = Date()
        }
        
        return true
    }
    
    /// 创建轻量级社交档案
    private func createLightweightSocialProfile(for user: EAUser) async -> EAUserSocialProfile? {
        // 🔑 轻量级创建策略：使用默认初始化器
        let profile = EAUserSocialProfile()
        
        // 🔑 设置基础数据
        profile.stellarLevel = 1
        profile.totalStellarEnergy = 0
        profile.explorerTitle = "新手探索者"
        profile.universeRegion = "银河系"
        
        // 🔑 建立关系（iOS 18+安全模式）
        user.socialProfile = profile
        profile.user = user
        
        return profile
    }
    
    /// 修复社交档案
    private func repairSocialProfile(_ profile: EAUserSocialProfile) async -> EAUserSocialProfile? {
        // 修复基础数据
        if let stellarLevel = profile.stellarLevel, stellarLevel < 1 {
            profile.stellarLevel = 1
        } else if profile.stellarLevel == nil {
            profile.stellarLevel = 1
        }
        
        if let totalStellarEnergy = profile.totalStellarEnergy, totalStellarEnergy < 0 {
            profile.totalStellarEnergy = 0
        } else if profile.totalStellarEnergy == nil {
            profile.totalStellarEnergy = 0
        }
        
        if profile.explorerTitle == nil {
            profile.explorerTitle = "新手探索者"
        }
        
        if profile.universeRegion == nil {
            profile.universeRegion = "银河系"
        }
        
        // 修复完成，直接返回
        return profile
    }
    
    /// 记录检查性能
    private func recordCheckDuration() {
        let duration = CFAbsoluteTimeGetCurrent() - checkStartTime
        if duration > maxCheckDuration {
            #if DEBUG
            print("⚠️ 用户身份检查超时: \(duration)秒")
            #endif
        }
    }
    
    // MARK: - 降级策略
    
    /// 启用降级模式
    /// 当无法修复时，提供基础功能保障
    func enableDegradedMode() {
        integrityStatus = .degraded
        #if DEBUG
        print("⚠️ 用户身份守护进入降级模式")
        #endif
    }
    
    /// 检查是否处于降级模式
    var isDegradedMode: Bool {
        return integrityStatus == .degraded
    }

    // MARK: - 聊天数据完整性验证

    /// 🚨 关键新增：验证好友聊天数据完整性
    /// 确保双方用户数据完整，可以安全进行聊天
    /// - Parameters:
    ///   - currentUser: 当前用户
    ///   - friendship: 好友关系对象
    /// - Returns: 验证结果，true表示数据完整可以安全聊天
    func validateChatDataIntegrity(currentUser: EAUser, friendship: EAFriendship) async -> Bool {
        let startTime = CFAbsoluteTimeGetCurrent()
        defer {
            let duration = CFAbsoluteTimeGetCurrent() - startTime
            #if DEBUG
            if duration > maxCheckDuration {
                print("⚠️ 聊天数据完整性验证超时: \(duration * 1000)ms")
            } else {
                print("✅ 聊天数据完整性验证完成: \(duration * 1000)ms")
            }
            #endif
        }

        // 🔑 第一步：验证当前用户完整性
        let currentUserIntegrity = await ensureUserIntegrity(for: currentUser)
        if !currentUserIntegrity {
            return false
        }

        // 🔑 第二步：验证好友用户完整性
        guard let friendUser = friendship.friendProfile?.user else {
            return false
        }

        // 🔑 修复：对于新注册用户，允许渐进式数据初始化
        // 只验证核心用户数据，不强制要求完整的数字宇宙数据
        if friendUser.username.isEmpty {
            return false
        }

        // 🔑 第三步：自动修复好友的数字宇宙数据（如果需要）
        if let friendProfile = friendship.friendProfile {
            await MainActor.run {
                if friendProfile.stellarLevel == nil {
                    friendProfile.stellarLevel = 1
                }
                if friendProfile.totalStellarEnergy == nil {
                    friendProfile.totalStellarEnergy = 0
                }
            }
        }

        // 🔑 第四步：新用户友好处理：尝试确保用户完整性，但不强制失败
        let friendUserIntegrity = await ensureUserIntegrity(for: friendUser)
        if !friendUserIntegrity {
            #if DEBUG
            // 调试环境下记录好友用户完整性验证失败，但继续允许聊天
            print("⚠️ 好友用户完整性验证失败，但继续允许聊天 - 支持新用户使用场景")
            #endif
            // 不阻止聊天，只记录警告 - 支持新用户使用场景
        }

        // 🔑 第五步：验证好友关系完整性
        guard friendship.initiatorProfile != nil && friendship.friendProfile != nil else {
            return false
        }

        return true
    }
}

 