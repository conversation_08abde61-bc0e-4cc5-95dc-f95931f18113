//
//  EAAIDataBridge.swift
//  Evolve
//
//  Created by AI Assistant on 2025-06-06.
//

import Foundation
import SwiftData

/// AI数据桥接服务
/// 负责AI服务与数据层之间的数据格式转换和访问控制
/// 遵循开发规范文档的"AI数据桥接架构规范"
@MainActor
class EAAIDataBridge: ObservableObject {
    
    // MARK: - 依赖注入
    
    private let repositoryContainer: EARepositoryContainer
    
    // MARK: - 缓存管理
    
    private var userHabitSummaryCache: [UUID: (summary: EAAIUserHabitSummary, timestamp: Date)] = [:]
    private var userProfileCache: [UUID: (profile: EAAIUserProfile, timestamp: Date)] = [:]
    
    private let habitSummaryCacheValidDuration: TimeInterval = 3 * 3600 // 3小时
    private let userProfileCacheValidDuration: TimeInterval = 7 * 24 * 3600 // 7天
    
    // MARK: - 初始化
    
    init(repositoryContainer: EARepositoryContainer) {
        self.repositoryContainer = repositoryContainer
    }
    
    // MARK: - AI数据访问接口
    
    /// 获取用户习惯数据摘要（AI格式）
    /// - Parameter userId: 用户ID
    /// - Returns: AI可用的用户习惯数据摘要
    func getUserHabitSummary(userId: UUID) async -> EAAIUserHabitSummary {
        // 检查缓存
        if let cached = userHabitSummaryCache[userId],
           Date().timeIntervalSince(cached.timestamp) < habitSummaryCacheValidDuration {
            return cached.summary
        }
        
        // 通过Repository获取数据
        let habits = await repositoryContainer.habitRepository.fetchUserHabits(userId: userId)
        let recentCompletions = await repositoryContainer.habitRepository.fetchRecentCompletions(userId: userId, days: 30)
        
        // 转换为AI格式
        let summary = EAAIUserHabitSummary(
            userId: userId,
            totalHabits: habits.count,
            activeHabits: habits.filter { $0.isActive }.count,
            habits: habits.map { $0.toAIFormat() },
            recentCompletions: recentCompletions.map { $0.toAIFormat() },
            completionRate: calculateCompletionRate(habits: habits, completions: recentCompletions),
            streakData: calculateStreakData(habits: habits, completions: recentCompletions),
            analysisTimestamp: Date()
        )
        
        // 更新缓存
        userHabitSummaryCache[userId] = (summary, Date())
        
        return summary
    }
    
    /// 获取用户画像数据（AI格式）
    /// - Parameter userId: 用户ID
    /// - Returns: AI可用的用户画像数据
    func getUserProfile(userId: UUID) async -> EAAIUserProfile {
        // 检查缓存
        if let cached = userProfileCache[userId],
           Date().timeIntervalSince(cached.timestamp) < userProfileCacheValidDuration {
            return cached.profile
        }
        
        // 通过Repository获取用户基础数据
        guard let user = await repositoryContainer.userRepository.fetchUser(by: userId) else {
            return EAAIUserProfile.empty(userId: userId)
        }
        
        // 获取用户设置
        let settings = await repositoryContainer.userRepository.fetchUserSettings(userId: userId)
        
        // 获取习惯数据
        let habitSummary = await getUserHabitSummary(userId: userId)
        
        // 构建AI用户画像
        let profile = EAAIUserProfile(
            userId: userId,
            username: user.username,
            creationDate: user.creationDate,
            preferredCoachStyle: settings?.preferredCoachStyle ?? "supportive",
            timeZone: TimeZone.current.identifier,
            habitPreferences: extractHabitPreferences(from: habitSummary),
            behaviorPatterns: analyzeBehaviorPatterns(from: habitSummary),
            analysisTimestamp: Date()
        )
        
        // 更新缓存
        userProfileCache[userId] = (profile, Date())
        
        return profile
    }
    
    /// 获取用户行为分析数据（AI格式）
    /// - Parameter userId: 用户ID
    /// - Returns: AI可用的行为分析数据
    func getUserBehaviorAnalysis(userId: UUID) async -> EAAIBehaviorAnalysis {
        let habitSummary = await getUserHabitSummary(userId: userId)
        let userProfile = await getUserProfile(userId: userId)
        
        return EAAIBehaviorAnalysis(
            userId: userId,
            completionPatterns: analyzeCompletionPatterns(from: habitSummary),
            timePreferences: analyzeTimePreferences(from: habitSummary),
            motivationTriggers: userProfile.behaviorPatterns.motivationTriggers,
            challengeAreas: identifyChallengeAreas(from: habitSummary),
            successFactors: identifySuccessFactors(from: habitSummary),
            analysisTimestamp: Date()
        )
    }
    
    // MARK: - 数据分析方法
    
    /// 计算完成率
    private func calculateCompletionRate(habits: [EAHabit], completions: [EACompletion]) -> Double {
        guard !habits.isEmpty else { return 0.0 }
        
        let totalExpected = habits.count * 30 // 30天期间的预期完成次数
        let actualCompletions = completions.count
        
        return min(1.0, Double(actualCompletions) / Double(totalExpected))
    }
    
    /// 计算连续完成数据
    private func calculateStreakData(habits: [EAHabit], completions: [EACompletion]) -> [String: Int] {
        var streakData: [String: Int] = [:]
        
        for habit in habits {
            let habitCompletions = completions.filter { $0.habit?.id == habit.id }
                .sorted { $0.date > $1.date }
            
            var currentStreak = 0
            var checkDate = Calendar.current.startOfDay(for: Date())
            
            for completion in habitCompletions {
                let completionDate = Calendar.current.startOfDay(for: completion.date)
                if completionDate == checkDate {
                    currentStreak += 1
                    checkDate = Calendar.current.date(byAdding: .day, value: -1, to: checkDate) ?? checkDate
                } else {
                    break
                }
            }
            
            streakData[habit.name] = currentStreak
        }
        
        return streakData
    }
    
    /// 提取习惯偏好
    private func extractHabitPreferences(from summary: EAAIUserHabitSummary) -> EAAIHabitPreferences {
        var preferences: [String] = []
        
        // 分析习惯类型分布
        let habitTypes = summary.habits.map { $0.category }.reduce(into: [:]) { counts, type in
            counts[type, default: 0] += 1
        }
        
        // 找出最常见的习惯类型
        let sortedTypes = habitTypes.sorted { $0.value > $1.value }
        preferences.append(contentsOf: sortedTypes.prefix(3).map { $0.key })
        
        // 计算平均目标频率
        let avgFrequency = summary.habits.isEmpty ? 1.0 : 
            Double(summary.habits.map { $0.targetFrequency }.reduce(0, +)) / Double(summary.habits.count)
        
        // 分析最活跃的工作日
        let weekdayCompletions = summary.recentCompletions.map { 
            Calendar.current.component(.weekday, from: $0.timestamp) 
        }.reduce(into: [Int: Int]()) { counts, weekday in
            counts[weekday, default: 0] += 1
        }
        let mostActiveWeekdays = weekdayCompletions.sorted { $0.value > $1.value }
            .prefix(3).map { $0.key }
        
        return EAAIHabitPreferences(
            preferredCategories: preferences,
            preferredDifficulty: "简单", // 默认值，可以基于数据分析
            preferredTimeSlots: summary.habits.compactMap { $0.timeOfDay },
            averageTargetFrequency: avgFrequency,
            mostActiveWeekdays: Array(mostActiveWeekdays)
        )
    }
    
    /// 分析行为模式
    private func analyzeBehaviorPatterns(from summary: EAAIUserHabitSummary) -> EAAIBehaviorPatterns {
        var patterns: [String] = []
        
        // 分析完成时间模式
        let timePatterns = summary.recentCompletions.map { $0.timeOfDay }.reduce(into: [:]) { counts, time in
            counts[time, default: 0] += 1
        }
        
        if let mostCommonTime = timePatterns.max(by: { $0.value < $1.value })?.key {
            patterns.append("prefers_\(mostCommonTime)")
        }
        
        // 分析完成率模式
        if summary.completionRate > 0.8 {
            patterns.append("high_consistency")
        } else if summary.completionRate > 0.5 {
            patterns.append("moderate_consistency")
        } else {
            patterns.append("needs_support")
        }
        
        // 计算峰值活动小时
        let completionsByHour = summary.recentCompletions.reduce(into: [Int: Int]()) { counts, completion in
            let hour = Calendar.current.component(.hour, from: completion.timestamp)
            counts[hour, default: 0] += 1
        }
        let peakHours = completionsByHour.sorted { $0.value > $1.value }
            .prefix(3).map { $0.key }
        
        // 计算时间段完成率
        var timeOfDayRates = summary.recentCompletions.reduce(into: [String: Double]()) { rates, completion in
            rates[completion.timeOfDay] = (rates[completion.timeOfDay] ?? 0) + 1
        }
        let totalCompletions = Double(summary.recentCompletions.count)
        for (timeOfDay, count) in timeOfDayRates {
            timeOfDayRates[timeOfDay] = count / totalCompletions
        }
        
        return EAAIBehaviorPatterns(
            peakActivityHours: Array(peakHours),
            completionRateByTimeOfDay: timeOfDayRates,
            streakPatterns: summary.streakData,
            energyLevelPatterns: [:], // 可以基于energyLevel数据分析
            consistencyScore: summary.completionRate,
            motivationTriggers: patterns
        )
    }
    
    /// 提取动机因素
    private func extractMotivationFactors(from summary: EAAIUserHabitSummary) -> [String] {
        var factors: [String] = []
        
        // 基于完成率推断动机类型
        if summary.completionRate > 0.7 {
            factors.append("self_motivated")
        }
        
        // 基于习惯类型推断动机
        let healthHabits = summary.habits.filter { $0.category == "health" }.count
        let productivityHabits = summary.habits.filter { $0.category == "productivity" }.count
        
        if healthHabits > productivityHabits {
            factors.append("health_focused")
        } else if productivityHabits > healthHabits {
            factors.append("productivity_focused")
        }
        
        return factors
    }
    
    /// 分析完成模式
    private func analyzeCompletionPatterns(from summary: EAAIUserHabitSummary) -> [String] {
        var patterns: [String] = []
        
        // 分析完成时间分布
        let completionsByHour = summary.recentCompletions.reduce(into: [Int: Int]()) { counts, completion in
            let hour = Calendar.current.component(.hour, from: completion.timestamp)
            counts[hour, default: 0] += 1
        }
        
        if let peakHour = completionsByHour.max(by: { $0.value < $1.value })?.key {
            if peakHour < 12 {
                patterns.append("morning_person")
            } else if peakHour < 18 {
                patterns.append("afternoon_person")
            } else {
                patterns.append("evening_person")
            }
        }
        
        return patterns
    }
    
    /// 分析时间偏好
    private func analyzeTimePreferences(from summary: EAAIUserHabitSummary) -> [String] {
        // 基于完成时间分析用户的时间偏好
        let timePreferences = summary.recentCompletions.map { $0.timeOfDay }
        let uniquePreferences = Array(Set(timePreferences))
        return uniquePreferences
    }
    
    /// 识别挑战领域
    private func identifyChallengeAreas(from summary: EAAIUserHabitSummary) -> [String] {
        var challenges: [String] = []
        
        // 识别完成率低的习惯类型
        let habitsByCategory = Dictionary(grouping: summary.habits) { $0.category }
        
        for (category, habits) in habitsByCategory {
            let categoryCompletions = summary.recentCompletions.filter { completion in
                habits.contains { $0.name == completion.habitName }
            }
            
            let categoryRate = Double(categoryCompletions.count) / Double(habits.count * 30)
            if categoryRate < 0.5 {
                challenges.append("struggling_with_\(category)")
            }
        }
        
        return challenges
    }
    
    /// 识别成功因素
    private func identifySuccessFactors(from summary: EAAIUserHabitSummary) -> [String] {
        var factors: [String] = []
        
        // 识别完成率高的习惯特征
        let successfulHabits = summary.habits.filter { habit in
            let habitCompletions = summary.recentCompletions.filter { $0.habitName == habit.name }
            return Double(habitCompletions.count) / 30.0 > 0.7
        }
        
        if !successfulHabits.isEmpty {
            let commonCategories = successfulHabits.map { $0.category }
            let mostCommonCategory = commonCategories.reduce(into: [:]) { counts, category in
                counts[category, default: 0] += 1
            }.max(by: { $0.value < $1.value })?.key
            
            if let category = mostCommonCategory {
                factors.append("successful_with_\(category)")
            }
        }
        
        return factors
    }
    
    // MARK: - 缓存管理
    
    /// 清除过期缓存
    func clearExpiredCache() {
        let now = Date()
        
        // 清除过期的习惯摘要缓存
        userHabitSummaryCache = userHabitSummaryCache.filter { _, cached in
            now.timeIntervalSince(cached.timestamp) < habitSummaryCacheValidDuration
        }
        
        // 清除过期的用户画像缓存
        userProfileCache = userProfileCache.filter { _, cached in
            now.timeIntervalSince(cached.timestamp) < userProfileCacheValidDuration
        }
    }
    
    /// 清除指定用户的缓存
    func clearUserCache(userId: UUID) {
        userHabitSummaryCache.removeValue(forKey: userId)
        userProfileCache.removeValue(forKey: userId)
    }
    
    /// 清除所有缓存
    func clearAllCache() {
        userHabitSummaryCache.removeAll()
        userProfileCache.removeAll()
    }
} 