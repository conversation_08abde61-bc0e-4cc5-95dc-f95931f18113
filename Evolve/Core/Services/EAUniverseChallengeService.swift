import Foundation
import SwiftUI

// MARK: - 宇宙挑战服务协议
@MainActor
protocol EAUniverseChallengeServiceProtocol: ObservableObject {
    var activeChallenges: [EAUniverseChallenge] { get }
    var userParticipations: [EAUniverseChallengeParticipation] { get }
    var isLoading: Bool { get }
    var errorMessage: String? { get }
    
    func fetchActiveChallenges() async
    func fetchUserParticipations(userId: UUID) async
    func joinChallenge(_ challenge: EAUniverseChallenge, userId: UUID) async throws
    func updateChallengeProgress(challengeId: UUID, userId: UUID, progress: Int) async throws
    func createSampleChallenges() async
    func calculateUserProgress(for challenge: EAUniverseChallenge, userId: UUID) async -> Int
}

// MARK: - 宇宙挑战服务实现
@MainActor
class EAUniverseChallengeService: EAUniverseChallengeServiceProtocol {
    @Published var activeChallenges: [EAUniverseChallenge] = []
    @Published var userParticipations: [EAUniverseChallengeParticipation] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // ✅ 正确：通过Repository容器访问数据
    private let repositoryContainer: EARepositoryContainer
    private let stellarEnergyService: EAStellarEnergyService
    
    init(repositoryContainer: EARepositoryContainer, stellarEnergyService: EAStellarEnergyService) {
        self.repositoryContainer = repositoryContainer
        self.stellarEnergyService = stellarEnergyService
    }
    
    // MARK: - 公共方法
    
    /// 获取活跃挑战列表
    func fetchActiveChallenges() async {
        isLoading = true
        errorMessage = nil
        
        defer { isLoading = false }
        
        do {
            // ✅ 正确：通过Repository获取挑战数据
            activeChallenges = try await repositoryContainer.challengeRepository.fetchActiveChallenges()
            
        } catch {
            errorMessage = "获取挑战列表失败：\(error.localizedDescription)"
            activeChallenges = []
        }
    }
    
    /// 获取用户参与记录
    func fetchUserParticipations(userId: UUID) async {
        isLoading = true
        errorMessage = nil
        
        defer { isLoading = false }
        
        do {
            // ✅ 正确：通过Repository查询用户参与记录（使用UUID）
            userParticipations = try await repositoryContainer.challengeRepository.fetchUserParticipations(for: userId)
            
        } catch {
            errorMessage = "获取参与记录失败：\(error.localizedDescription)"
            userParticipations = []
        }
    }
    
    /// 参与挑战
    func joinChallenge(_ challenge: EAUniverseChallenge, userId: UUID) async throws {
        // ✅ 正确：通过Repository处理挑战参与
        guard let user = await repositoryContainer.userRepository.fetchUser(by: userId) else {
            throw ChallengeError.challengeNotFound
        }
        
        let participation = try await repositoryContainer.challengeRepository.joinChallenge(challenge, user: user)
        
        // 更新本地状态
        userParticipations.append(participation)
        
        // 奖励星际能量
        await stellarEnergyService.awardStellarEnergy(
            to: user,
            amount: 50,
            source: "challenge_join",
            description: "参与挑战：\(challenge.title)"
        )
        
        // 发送通知
        await sendChallengeJoinedNotification(challenge: challenge)
    }
    
    /// 更新挑战进度
    func updateChallengeProgress(challengeId: UUID, userId: UUID, progress: Int) async throws {
        // ✅ 正确：通过Repository处理进度更新
        guard let user = await repositoryContainer.userRepository.fetchUser(by: userId) else {
            throw ChallengeError.challengeNotFound
        }
        
        guard let challenge = try await repositoryContainer.challengeRepository.fetchChallenge(by: challengeId) else {
            throw ChallengeError.challengeNotFound
        }
        
        guard let participation = try await repositoryContainer.challengeRepository.fetchParticipation(challengeId: challengeId, userId: userId) else {
            throw ChallengeError.participationNotFound
        }
        
        // 通过Repository更新进度
        try await repositoryContainer.challengeRepository.updateParticipationProgress(participation, progress: progress, targetValue: challenge.targetValue)
        
        // 检查是否完成挑战
        if participation.isCompleted {
            await handleChallengeCompletion(challenge: challenge, participation: participation, user: user)
        }
    }
    
    /// 计算用户在特定挑战中的进度
    func calculateUserProgress(for challenge: EAUniverseChallenge, userId: UUID) async -> Int {
        // 在实际项目中，这里会根据挑战类型计算实际进度
        // 例如：查询用户在挑战期间的习惯完成记录
        
        switch challenge.challengeType {
        case "habit_completion":
            return await calculateHabitCompletionProgress(challenge: challenge, userId: userId)
        case "streak_goal":
            return await calculateStreakProgress(challenge: challenge, userId: userId)
        case "community_goal":
            return await calculateCommunityProgress(challenge: challenge, userId: userId)
        default:
            return 0
        }
    }
    
    // MARK: - 私有方法
    
    /// 创建示例挑战数据
    func createSampleChallenges() async {
        do {
            let challenges = await createSampleChallengeData()
            // ✅ 正确：通过Repository保存挑战数据
            for challenge in challenges {
                try await repositoryContainer.challengeRepository.createChallenge(challenge)
            }
            activeChallenges = challenges
        } catch {
            errorMessage = "创建示例挑战失败：\(error.localizedDescription)"
        }
    }
    
    /// 创建示例挑战数据（内部方法）
    private func createSampleChallengeData() async -> [EAUniverseChallenge] {
        let calendar = Calendar.current
        let now = Date()
        
        let challenge1 = EAUniverseChallenge(
            title: "银河系早起探索者",
            challengeDescription: "连续7天早起完成晨间习惯，探索银河系的奥秘",
            targetHabitCategory: "morning_routine",
            startDate: now,
            endDate: calendar.date(byAdding: .day, value: 7, to: now) ?? now,
            stellarReward: 500
        )
        challenge1.difficulty = "normal"
        challenge1.universeRegion = "银河系"
        challenge1.challengeBadge = "explorer_badge"
        challenge1.cosmicDifficulty = 2
        challenge1.energyMultiplier = 1.5
        
        let challenge2 = EAUniverseChallenge(
            title: "星际健身征程",
            challengeDescription: "本周完成5次运动习惯，增强星际探索体能",
            targetHabitCategory: "fitness",
            startDate: now,
            endDate: calendar.date(byAdding: .day, value: 7, to: now) ?? now,
            stellarReward: 300
        )
        challenge2.difficulty = "easy"
        challenge2.universeRegion = "猎户座"
        challenge2.challengeBadge = "warrior_badge"
        challenge2.cosmicDifficulty = 1
        challenge2.energyMultiplier = 1.2
        
        let challenge3 = EAUniverseChallenge(
            title: "宇宙冥想大师",
            challengeDescription: "连续14天完成冥想习惯，达到宇宙意识层次",
            targetHabitCategory: "mindfulness",
            startDate: calendar.date(byAdding: .day, value: 1, to: now) ?? now,
            endDate: calendar.date(byAdding: .day, value: 15, to: now) ?? now,
            stellarReward: 800
        )
        challenge3.difficulty = "hard"
        challenge3.universeRegion = "仙女座"
        challenge3.challengeBadge = "master_badge"
        challenge3.cosmicDifficulty = 4
        challenge3.energyMultiplier = 2.0
        
        let challenge4 = EAUniverseChallenge(
            title: "传奇阅读征服者",
            challengeDescription: "30天阅读挑战，征服知识宇宙的边界",
            targetHabitCategory: "reading",
            startDate: calendar.date(byAdding: .day, value: -5, to: now) ?? now,
            endDate: calendar.date(byAdding: .day, value: 25, to: now) ?? now,
            stellarReward: 1200
        )
        challenge4.difficulty = "extreme"
        challenge4.universeRegion = "天鹅座"
        challenge4.challengeBadge = "legend_badge"
        challenge4.cosmicDifficulty = 5
        challenge4.energyMultiplier = 3.0
        
        return [challenge1, challenge2, challenge3, challenge4]
    }
    
    /// 创建示例参与记录（已废弃，现在通过Repository管理）
    private func createSampleParticipations(for userId: UUID) async -> [EAUniverseChallengeParticipation] {
        // ✅ 现在通过Repository获取真实数据
        guard let user = await repositoryContainer.userRepository.fetchUser(by: userId) else {
            return []
        }
        
        do {
            return try await repositoryContainer.challengeRepository.fetchUserParticipations(for: user)
        } catch {
            return []
        }
    }
    
    /// 计算习惯完成进度
    private func calculateHabitCompletionProgress(challenge: EAUniverseChallenge, userId: UUID) async -> Int {
        // 在实际项目中，这里会查询用户在挑战期间的习惯完成记录
        // 目前返回模拟数据
        return Int.random(in: 0...challenge.targetValue)
    }
    
    /// 计算连续完成进度
    private func calculateStreakProgress(challenge: EAUniverseChallenge, userId: UUID) async -> Int {
        // 在实际项目中，这里会计算用户的连续完成天数
        return Int.random(in: 0...challenge.targetValue)
    }
    
    /// 计算社区目标进度
    private func calculateCommunityProgress(challenge: EAUniverseChallenge, userId: UUID) async -> Int {
        // 在实际项目中，这里会计算社区整体进度
        return Int.random(in: 0...challenge.targetValue)
    }
    
    /// 处理挑战完成
    private func handleChallengeCompletion(challenge: EAUniverseChallenge, participation: EAUniverseChallengeParticipation, user: EAUser) async {
        // 计算奖励
        let baseReward = challenge.calculateBaseStellarReward()
        participation.earnedReward = baseReward
        participation.stellarEnergyEarned = baseReward
        
        // ✅ 正确：发放星际能量奖励
        await stellarEnergyService.awardStellarEnergy(
            to: user,
            amount: baseReward,
            source: "challenge_completion",
            description: "完成挑战：\(challenge.title)"
        )
        
        // 检查是否是个人最佳
        let userChallenges = userParticipations.filter { $0.user?.id == user.id }
        let completedChallenges = userChallenges.filter { $0.isCompleted }
        
        if completedChallenges.count == 1 {
            participation.personalBest = true
        }
        
        // 添加里程碑奖励
        participation.addMilestoneReward("挑战完成者")
        
        if participation.personalBest {
            participation.addMilestoneReward("首次完成")
        }
        
        // 发送完成通知
        await sendChallengeCompletedNotification(challenge: challenge, participation: participation)
    }
    
    /// 发送挑战参与通知
    private func sendChallengeJoinedNotification(challenge: EAUniverseChallenge) async {
        // 在实际项目中，这里会发送本地通知
        // 生产环境中应使用正式的通知系统
    }
    
    /// 发送挑战完成通知
    private func sendChallengeCompletedNotification(challenge: EAUniverseChallenge, participation: EAUniverseChallengeParticipation) async {
        // 在实际项目中，这里会发送庆祝通知
        // 生产环境中应使用正式的通知系统
    }
}

// MARK: - 挑战错误类型已在Repository中定义，这里移除重复定义

// MARK: - 挑战过滤器
struct EAChallengeFilter {
    var difficulty: EAChallengeDifficulty?
    var challengeType: EAChallengeType?
    var universeRegion: String?
    var isActive: Bool = true
    var hasAvailableSlots: Bool = true
    
    func matches(_ challenge: EAUniverseChallenge) -> Bool {
        if let difficulty = difficulty, challenge.difficulty != difficulty.rawValue {
            return false
        }
        
        if let challengeType = challengeType, challenge.challengeType != challengeType.rawValue {
            return false
        }
        
        if let universeRegion = universeRegion, challenge.universeRegion != universeRegion {
            return false
        }
        
        if isActive && !challenge.isActive {
            return false
        }
        
        if hasAvailableSlots && !challenge.canParticipate() {
            return false
        }
        
        return true
    }
} 