//
//  EAUserIdentityMonitor.swift
//  Evolve
//
//  Created by AI Assistant on 2025-01-26.
//  用户身份监控服务 - 阶段5.1实现
//

import Foundation
import SwiftUI

/// 用户身份监控服务
/// 负责长期监控用户身份状态，自动检测和修复问题
/// 遵循开发规范文档的Repository模式强制执行规范
@MainActor
class EAUserIdentityMonitor: ObservableObject {
    
    // MARK: - 监控状态
    
    @Published var monitoringStatus: MonitoringStatus = .disabled
    @Published var lastIssueDetected: Date?
    @Published var autoRepairCount: Int = 0
    @Published var performanceMetrics: PerformanceMetrics = PerformanceMetrics()
    
    // MARK: - 依赖注入
    
    private let repositoryContainer: EARepositoryContainer
    private let integrityGuard: EAUserIntegrityGuard
    
    // MARK: - 监控配置（性能优化）
    
    private let monitoringInterval: TimeInterval = 600 // 10分钟检查一次（降低频率）
    private let maxMonitoringDuration: TimeInterval = 0.2 // 200ms监控时间限制
    private var monitoringTimer: Timer?
    
    // MARK: - 初始化
    
    init(repositoryContainer: EARepositoryContainer) {
        self.repositoryContainer = repositoryContainer
        self.integrityGuard = EAUserIntegrityGuard(repositoryContainer: repositoryContainer)
    }
    
    // MARK: - 监控控制
    
    /// 启动轻量级监控
    func startMonitoring() {
        // 避免重复启动
        guard monitoringStatus != .active else { return }
        
        // ✅ CPU优化：延长监控间隔，减少性能影响和CPU唤醒
        // 将监控间隔从之前的频率延长为5分钟
        let optimizedInterval: TimeInterval = 300.0 // 5分钟
        monitoringTimer = Timer.scheduledTimer(withTimeInterval: optimizedInterval, repeats: true) { [weak self] _ in
            Task { [weak self] in
                await self?.performLightweightCheck()
            }
        }
        
        monitoringStatus = .active
        performanceMetrics.monitoringStartTime = Date()
        
        #if DEBUG
        print("✅ [UserIdentityMonitor] 监控已启动，优化后检查间隔: \(optimizedInterval)秒")
        #endif
    }
    
    /// 停止监控
    func stopMonitoring() {
        monitoringTimer?.invalidate()
        monitoringTimer = nil
        monitoringStatus = .disabled
        
        #if DEBUG
        print("🛑 [UserIdentityMonitor] 监控已停止")
        #endif
    }
    
    /// 暂停监控
    func pauseMonitoring() {
        monitoringTimer?.invalidate()
        monitoringTimer = nil
        monitoringStatus = .paused
        
        #if DEBUG
        print("⏸️ [UserIdentityMonitor] 监控已暂停")
        #endif
    }
    
    /// 恢复监控
    func resumeMonitoring() {
        guard monitoringStatus == .paused else { return }
        startMonitoring()
    }
    
    // MARK: - 监控检查
    
    /// 轻量级定期检查（性能优化）
    private func performLightweightCheck() async {
        let startTime = CFAbsoluteTimeGetCurrent()
        defer {
            let duration = CFAbsoluteTimeGetCurrent() - startTime
            performanceMetrics.lastCheckDuration = duration
            if duration > maxMonitoringDuration {
                #if DEBUG
                print("⚠️ [UserIdentityMonitor] 监控超时: \(duration * 1000)ms")
                #endif
            }
        }
        
        guard let currentUser = await getCurrentUser() else {
            await recordMonitoringEvent(type: .noCurrentUser, issue: "无当前用户", userId: nil)
            return
        }
        
        // 只执行基础检查，避免复杂操作
        let isUserSafe = integrityGuard.deepSafetyCheck(for: currentUser)
        
        if isUserSafe {
            // 正常状态，无需操作
            performanceMetrics.totalChecks += 1
        } else {
            // 检测到问题，记录并尝试修复
            await MainActor.run {
                self.lastIssueDetected = Date()
                self.autoRepairCount += 1
            }
            await recordMonitoringEvent(type: .integrityIssueDetected, issue: "用户身份完整性检查失败", userId: currentUser.id)
            
            // 尝试自动修复
            await attemptAutoRepair(for: currentUser, issue: "身份完整性问题")
        }
        
        performanceMetrics.totalChecks += 1
    }
    
    /// 尝试自动修复
    private func attemptAutoRepair(for user: EAUser, issue: String) async {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        let repairSuccess = await integrityGuard.repairUserIntegrity(for: user)
        
        let duration = CFAbsoluteTimeGetCurrent() - startTime
        
        if repairSuccess {
            await recordMonitoringEvent(type: .autoRepairSuccessful, issue: "修复成功: \(issue)", userId: user.id)
            #if DEBUG
            print("✅ [UserIdentityMonitor] 自动修复成功: \(issue) (\(duration * 1000)ms)")
            #endif
        } else {
            await recordMonitoringEvent(type: .autoRepairFailed, issue: "修复失败: \(issue)", userId: user.id)
            #if DEBUG
            print("❌ [UserIdentityMonitor] 自动修复失败: \(issue) (\(duration * 1000)ms)")
            #endif
        }
    }
    
    // MARK: - 事件记录
    
    /// 记录监控事件
    private func recordMonitoringEvent(type: MonitoringEventType, issue: String, userId: UUID?) async {
        let event = MonitoringEvent(
            type: type,
            issue: issue,
            userId: userId,
            timestamp: Date()
        )
        
        // 简化记录，避免复杂操作
        #if DEBUG
        print("📊 [UserIdentityMonitor] 监控事件: \(type) - \(issue)")
        #endif
        
        // 存储到本地用于后续分析
        performanceMetrics.recordEvent(event)
    }
    
    // MARK: - 辅助方法
    
    /// 获取当前用户（简化版）
    private func getCurrentUser() async -> EAUser? {
        do {
            return try await repositoryContainer.userRepository.fetchCurrentUser()
        } catch {
            #if DEBUG
            print("⚠️ [UserIdentityMonitor] 获取当前用户失败: \(error)")
            #endif
            return nil
        }
    }
    
    /// 获取监控统计信息
    func getMonitoringStatistics() -> MonitoringStatistics {
        return MonitoringStatistics(
            status: monitoringStatus,
            totalChecks: performanceMetrics.totalChecks,
            issuesDetected: performanceMetrics.events.count,
            autoRepairCount: autoRepairCount,
            lastCheckDuration: performanceMetrics.lastCheckDuration,
            lastIssueDetected: lastIssueDetected,
            monitoringStartTime: performanceMetrics.monitoringStartTime
        )
    }
}

// MARK: - 数据模型

/// 监控状态
enum MonitoringStatus {
    case active
    case paused
    case disabled
}

/// 监控事件类型
enum MonitoringEventType {
    case integrityIssueDetected
    case recoverableIssueDetected
    case criticalIssueDetected
    case autoRepairSuccessful
    case autoRepairFailed
    case performanceDegradation
    case noCurrentUser
}

/// 监控事件
struct MonitoringEvent {
    let type: MonitoringEventType
    let issue: String
    let userId: UUID?
    let timestamp: Date
}

/// 性能指标
struct PerformanceMetrics {
    var monitoringStartTime: Date?
    var lastCheckDuration: TimeInterval = 0
    var totalChecks: Int = 0
    var events: [MonitoringEvent] = []
    
    // 🔑 阶段5.3：启动性能监控
    var lastStartupDuration: TimeInterval = 0
    var startupCount: Int = 0
    var slowStartupCount: Int = 0
    
    mutating func recordEvent(_ event: MonitoringEvent) {
        events.append(event)
        
        // 保持最近100个事件，避免内存泄漏
        if events.count > 100 {
            events.removeFirst()
        }
    }
    
    /// 计算启动性能健康度
    var startupHealthScore: Double {
        guard startupCount > 0 else { return 1.0 }
        let slowRatio = Double(slowStartupCount) / Double(startupCount)
        return max(0.0, 1.0 - slowRatio)
    }
}

/// 监控统计信息
struct MonitoringStatistics {
    let status: MonitoringStatus
    let totalChecks: Int
    let issuesDetected: Int
    let autoRepairCount: Int
    let lastCheckDuration: TimeInterval
    let lastIssueDetected: Date?
    let monitoringStartTime: Date?
    
    /// 监控运行时间
    var monitoringDuration: TimeInterval? {
        guard let startTime = monitoringStartTime else { return nil }
        return Date().timeIntervalSince(startTime)
    }
    
    /// 平均检查间隔
    var averageCheckInterval: TimeInterval? {
        guard totalChecks > 1, let duration = monitoringDuration else { return nil }
        return duration / Double(totalChecks)
    }
}

// MARK: - Environment扩展已迁移到EnvironmentValues+Services.swift统一管理 