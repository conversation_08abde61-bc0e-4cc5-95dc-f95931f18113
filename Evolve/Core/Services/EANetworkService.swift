import Foundation
import Network

// MARK: - 网络错误枚举

enum EANetworkError: LocalizedError {
    case invalidURL
    case noData
    case decodingError(Error)
    case encodingError(Error)
    case httpError(statusCode: Int, message: String?)
    case networkUnavailable
    case timeout
    case serverError
    case unauthorized
    case forbidden
    case notFound
    case tooManyRequests
    case unknown(Error)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的请求地址"
        case .noData:
            return "服务器未返回数据"
        case .decodingError:
            return "数据解析失败"
        case .encodingError:
            return "数据编码失败"
        case .httpError(let statusCode, let message):
            return message ?? "请求失败 (状态码: \(statusCode))"
        case .networkUnavailable:
            return "网络连接不可用"
        case .timeout:
            return "请求超时"
        case .serverError:
            return "服务器内部错误"
        case .unauthorized:
            return "未授权访问"
        case .forbidden:
            return "访问被禁止"
        case .notFound:
            return "请求的资源不存在"
        case .tooManyRequests:
            return "请求过于频繁，请稍后重试"
        case .unknown(let error):
            return "未知错误: \(error.localizedDescription)"
        }
    }
}

// MARK: - HTTP方法枚举

enum EAHTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PUT = "PUT"
    case DELETE = "DELETE"
    case PATCH = "PATCH"
}

// MARK: - 请求配置

struct EANetworkRequest {
    let endpoint: String
    let method: EAHTTPMethod
    let headers: [String: String]
    let body: Data?
    let timeout: TimeInterval
    let requiresAuth: Bool
    
    init(
        endpoint: String,
        method: EAHTTPMethod = .GET,
        headers: [String: String] = [:],
        body: Data? = nil,
        timeout: TimeInterval = 30.0,
        requiresAuth: Bool = false
    ) {
        self.endpoint = endpoint
        self.method = method
        self.headers = headers
        self.body = body
        self.timeout = timeout
        self.requiresAuth = requiresAuth
    }
}

// MARK: - 网络响应

struct EANetworkResponse<T> {
    let data: T
    let statusCode: Int
    let headers: [AnyHashable: Any]
}

// MARK: - 通用网络服务

class EANetworkService: ObservableObject {
    
    // MARK: - 属性
    
    @Published var isNetworkAvailable = true
    
    private let session: URLSession
    private let monitor: NWPathMonitor
    private let monitorQueue = DispatchQueue(label: "NetworkMonitor")
    private let baseURL: String
    private var authToken: String?
    
    // MARK: - 初始化
    
    init() {
        // 配置URLSession
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30.0
        config.timeoutIntervalForResource = 60.0
        config.waitsForConnectivity = true
        config.allowsCellularAccess = true
        
        self.session = URLSession(configuration: config)
        self.monitor = NWPathMonitor()
        
        // 从配置文件读取baseURL，这里先用模拟地址
        self.baseURL = "https://api.evolve-app.com/v1"
        
        setupNetworkMonitoring()
    }
    
    deinit {
        monitor.cancel()
    }
    
    // MARK: - 网络监控
    
    private func setupNetworkMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            Task { @MainActor in
                self?.isNetworkAvailable = path.status == .satisfied
            }
        }
        monitor.start(queue: monitorQueue)
    }
    
    // MARK: - 认证管理
    
    func setAuthToken(_ token: String?) {
        self.authToken = token
    }
    
    func clearAuthToken() {
        self.authToken = nil
    }
    
    // MARK: - 主要请求方法
    
    /// 通用请求方法
    func request<T: Codable>(
        _ request: EANetworkRequest,
        responseType: T.Type
    ) async throws -> EANetworkResponse<T> {
        
        // 检查网络连接
        guard isNetworkAvailable else {
            throw EANetworkError.networkUnavailable
        }
        
        // 构建URL
        guard let url = URL(string: baseURL + request.endpoint) else {
            throw EANetworkError.invalidURL
        }
        
        // 创建URLRequest
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = request.method.rawValue
        urlRequest.timeoutInterval = request.timeout
        
        // 设置请求头
        var headers = request.headers
        headers["Content-Type"] = "application/json"
        headers["Accept"] = "application/json"
        headers["User-Agent"] = "Evolve-iOS/1.0"
        
        // 添加认证头
        if request.requiresAuth, let token = authToken {
            headers["Authorization"] = "Bearer \(token)"
        }
        
        for (key, value) in headers {
            urlRequest.setValue(value, forHTTPHeaderField: key)
        }
        
        // 设置请求体
        if let body = request.body {
            urlRequest.httpBody = body
        }
        
        // 执行请求（带重试机制）
        return try await performRequestWithRetry(urlRequest, responseType: responseType)
    }
    
    /// 带重试机制的请求执行
    private func performRequestWithRetry<T: Codable>(
        _ request: URLRequest,
        responseType: T.Type,
        retryCount: Int = 0,
        maxRetries: Int = 3
    ) async throws -> EANetworkResponse<T> {
        
        do {
            let (data, response) = try await session.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw EANetworkError.unknown(NSError(domain: "InvalidResponse", code: -1))
            }
            
            // 处理HTTP状态码
            try handleHTTPStatusCode(httpResponse.statusCode, data: data)
            
            // 解析响应数据
            let decodedData = try JSONDecoder().decode(responseType, from: data)
            
            return EANetworkResponse(
                data: decodedData,
                statusCode: httpResponse.statusCode,
                headers: httpResponse.allHeaderFields
            )
            
        } catch {
            // 判断是否需要重试
            if shouldRetry(error: error, retryCount: retryCount, maxRetries: maxRetries) {
                let delay = calculateRetryDelay(retryCount: retryCount)
                try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                
                return try await performRequestWithRetry(
                    request,
                    responseType: responseType,
                    retryCount: retryCount + 1,
                    maxRetries: maxRetries
                )
            }
            
            throw mapError(error)
        }
    }
    
    // MARK: - 便捷方法
    
    /// GET请求
    func get<T: Codable>(
        endpoint: String,
        headers: [String: String] = [:],
        requiresAuth: Bool = false,
        responseType: T.Type
    ) async throws -> EANetworkResponse<T> {
        
        let request = EANetworkRequest(
            endpoint: endpoint,
            method: .GET,
            headers: headers,
            requiresAuth: requiresAuth
        )
        
        return try await self.request(request, responseType: responseType)
    }
    
    /// POST请求
    func post<T: Codable, U: Codable>(
        endpoint: String,
        body: U,
        headers: [String: String] = [:],
        requiresAuth: Bool = false,
        responseType: T.Type
    ) async throws -> EANetworkResponse<T> {
        
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        
        let bodyData = try encoder.encode(body)
        
        let request = EANetworkRequest(
            endpoint: endpoint,
            method: .POST,
            headers: headers,
            body: bodyData,
            requiresAuth: requiresAuth
        )
        
        return try await self.request(request, responseType: responseType)
    }
    
    /// PUT请求
    func put<T: Codable, U: Codable>(
        endpoint: String,
        body: U,
        headers: [String: String] = [:],
        requiresAuth: Bool = true,
        responseType: T.Type
    ) async throws -> EANetworkResponse<T> {
        
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        
        let bodyData = try encoder.encode(body)
        
        let request = EANetworkRequest(
            endpoint: endpoint,
            method: .PUT,
            headers: headers,
            body: bodyData,
            requiresAuth: requiresAuth
        )
        
        return try await self.request(request, responseType: responseType)
    }
    
    /// DELETE请求
    func delete<T: Codable>(
        endpoint: String,
        headers: [String: String] = [:],
        requiresAuth: Bool = true,
        responseType: T.Type
    ) async throws -> EANetworkResponse<T> {
        
        let request = EANetworkRequest(
            endpoint: endpoint,
            method: .DELETE,
            headers: headers,
            requiresAuth: requiresAuth
        )
        
        return try await self.request(request, responseType: responseType)
    }
    
    // MARK: - 辅助方法
    
    private func handleHTTPStatusCode(_ statusCode: Int, data: Data) throws {
        switch statusCode {
        case 200...299:
            return // 成功
        case 400:
            let message = extractErrorMessage(from: data)
            throw EANetworkError.httpError(statusCode: statusCode, message: message)
        case 401:
            throw EANetworkError.unauthorized
        case 403:
            throw EANetworkError.forbidden
        case 404:
            throw EANetworkError.notFound
        case 408:
            throw EANetworkError.timeout
        case 429:
            throw EANetworkError.tooManyRequests
        case 500...599:
            throw EANetworkError.serverError
        default:
            let message = extractErrorMessage(from: data)
            throw EANetworkError.httpError(statusCode: statusCode, message: message)
        }
    }
    
    private func extractErrorMessage(from data: Data) -> String? {
        guard let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let message = json["message"] as? String else {
            return nil
        }
        return message
    }
    
    private func shouldRetry(error: Error, retryCount: Int, maxRetries: Int) -> Bool {
        guard retryCount < maxRetries else { return false }
        
        // 只对特定错误进行重试
        if let networkError = error as? EANetworkError {
            switch networkError {
            case .timeout, .networkUnavailable, .serverError:
                return true
            default:
                return false
            }
        }
        
        // 对URLError进行重试判断
        if let urlError = error as? URLError {
            switch urlError.code {
            case .timedOut, .networkConnectionLost, .notConnectedToInternet:
                return true
            default:
                return false
            }
        }
        
        return false
    }
    
    private func calculateRetryDelay(retryCount: Int) -> Double {
        // 指数退避算法：1秒、2秒、4秒
        return pow(2.0, Double(retryCount))
    }
    
    private func mapError(_ error: Error) -> EANetworkError {
        if let networkError = error as? EANetworkError {
            return networkError
        }
        
        if let urlError = error as? URLError {
            switch urlError.code {
            case .timedOut:
                return .timeout
            case .notConnectedToInternet, .networkConnectionLost:
                return .networkUnavailable
            default:
                return .unknown(urlError)
            }
        }
        
        if error is DecodingError {
            return .decodingError(error)
        }
        
        if error is EncodingError {
            return .encodingError(error)
        }
        
        return .unknown(error)
    }
}

// MARK: - 扩展：日志记录

extension EANetworkService {
    
    private func logRequest(_ request: URLRequest) {
        #if DEBUG
        // 日志请求头
        if let headers = request.allHTTPHeaderFields {
            // 开发阶段可用于调试网络请求
            _ = headers
        }
        // 日志请求体
        if let body = request.httpBody, let _ = String(data: body, encoding: .utf8) {
            // 开发阶段可用于调试请求内容
            _ = body
        }
        #endif
    }
    
    private func logResponse<T>(_ response: EANetworkResponse<T>) {
        #if DEBUG
        #endif
    }
    
    private func logError(_ error: Error) {
        #if DEBUG
        #endif
    }
} 