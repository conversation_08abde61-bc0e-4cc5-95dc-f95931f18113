import Foundation
import SwiftData

/// 星际能量奖励Repository协议
/// 专门处理能量计算和奖励逻辑，确保SwiftData并发安全
protocol EAEnergyRewardRepositoryProtocol {
    /// 奖励习惯创建能量
    func awardHabitCreationEnergy(userId: UUID, habitName: String, difficulty: String) async throws -> Int
    
    /// 奖励习惯完成能量
    func awardHabitCompletionEnergy(userId: UUID, habitId: UUID) async throws -> Int
    
    /// 更新用户星际能量
    func updateUserStellarEnergy(userId: UUID, energyGained: Int) async throws
    
    /// 检查等级升级
    func checkLevelUpgrade(userId: UUID) async throws
}

/// 星际能量奖励Repository实现
/// ✅ iOS 18.5真机修复：使用@ModelActor确保线程安全，避免Task.detached导致的上下文冲突
@ModelActor
actor EAEnergyRewardRepository: EAEnergyRewardRepositoryProtocol {
    
    // MARK: - 能量配置
    private struct EnergyConfig {
        static let baseHabitCreationEnergy = 10
        static let baseHabitCompletionEnergy = 5
        static let difficultyMultiplier: [String: Double] = [
            "简单": 1.0,
            "中等": 1.5,
            "困难": 2.0
        ]
    }
    
    // MARK: - 公开接口
    
    /// 奖励习惯创建能量（iOS 18.5性能优化版本）
    func awardHabitCreationEnergy(userId: UUID, habitName: String, difficulty: String) async throws -> Int {
        // 获取用户
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userId }
        )
        guard let user = try modelContext.fetch(userDescriptor).first else {
            throw EARepositoryError.userNotFound
        }

        // 确保用户有社交档案
        guard let socialProfile = user.socialProfile else {
            throw EARepositoryError.dataNotFound
        }

        // 计算能量奖励
        let baseEnergy = EnergyConfig.baseHabitCreationEnergy
        let multiplier = EnergyConfig.difficultyMultiplier[difficulty] ?? 1.0
        let energyGained = Int(Double(baseEnergy) * multiplier)

        // 更新用户星际能量
        let currentEnergy = socialProfile.totalStellarEnergy ?? 0
        socialProfile.totalStellarEnergy = currentEnergy + energyGained

        // 保存更改
        try modelContext.save()

        // ✅ 性能优化：将等级升级检查异步化，避免阻塞主流程
        Task.detached { [userId] in
            do {
                try await self.checkLevelUpgrade(userId: userId)
            } catch {
                // 等级升级失败不影响能量奖励的成功
                // 静默处理，避免影响主流程
            }
        }

        return energyGained
    }
    
    /// 奖励习惯完成能量
    func awardHabitCompletionEnergy(userId: UUID, habitId: UUID) async throws -> Int {
        // 获取用户和习惯
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userId }
        )
        guard let user = try modelContext.fetch(userDescriptor).first else {
            throw EARepositoryError.userNotFound
        }
        
        let habitDescriptor = FetchDescriptor<EAHabit>(
            predicate: #Predicate { $0.id == habitId }
        )
        guard let habit = try modelContext.fetch(habitDescriptor).first else {
            throw EARepositoryError.habitNotFound
        }
        
        // 确保用户有社交档案
        guard let socialProfile = user.socialProfile else {
            throw EARepositoryError.dataNotFound
        }
        
        // 计算能量奖励
        let baseEnergy = EnergyConfig.baseHabitCompletionEnergy
        let multiplier = EnergyConfig.difficultyMultiplier[habit.difficulty] ?? 1.0
        let energyGained = Int(Double(baseEnergy) * multiplier)
        
        // 更新用户星际能量
        let currentEnergy = socialProfile.totalStellarEnergy ?? 0
        socialProfile.totalStellarEnergy = currentEnergy + energyGained
        
        // 保存更改
        try modelContext.save()

        // 🚨 关键修复：使用Task而非Task.detached，避免SwiftData Context冲突
        // Task.detached会在不同的Context中执行，可能导致SwiftData崩溃
        Task { [weak self, userId] in
            do {
                try await self?.checkLevelUpgrade(userId: userId)
            } catch {
                // 等级升级失败不影响能量奖励的成功
                // 静默处理，避免影响主流程
            }
        }

        return energyGained
    }
    
    /// 更新用户星际能量
    func updateUserStellarEnergy(userId: UUID, energyGained: Int) async throws {
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userId }
        )
        guard let user = try modelContext.fetch(userDescriptor).first else {
            throw EARepositoryError.userNotFound
        }
        
        guard let socialProfile = user.socialProfile else {
            throw EARepositoryError.dataNotFound
        }
        
        let currentEnergy = socialProfile.totalStellarEnergy ?? 0
        socialProfile.totalStellarEnergy = currentEnergy + energyGained
        
        try modelContext.save()
    }
    
    /// 检查等级升级
    func checkLevelUpgrade(userId: UUID) async throws {
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userId }
        )
        guard let user = try modelContext.fetch(userDescriptor).first else {
            throw EARepositoryError.userNotFound
        }
        
        guard let socialProfile = user.socialProfile else {
            return
        }
        
        let currentEnergy = socialProfile.totalStellarEnergy ?? 0
        let currentLevel = socialProfile.stellarLevel ?? 1
        
        // 简化的等级计算：每1000能量升一级
        let newLevel = max(1, currentEnergy / 1000 + 1)
        
        if newLevel > currentLevel {
            socialProfile.stellarLevel = newLevel
            try modelContext.save()
            
            // 发送等级升级通知
            await MainActor.run {
                NotificationCenter.default.post(
                    name: NSNotification.Name("StellarLevelUpgrade"),
                    object: ["userId": userId, "newLevel": newLevel]
                )
            }
        }
    }
}
