import Foundation
import SwiftData
import os

// MARK: - 用户Repository实现
@ModelActor
actor EAUserRepositoryImpl: EAUserRepository {
    
    // MARK: - 性能监控器引用（阶段3新增）
    private weak var performanceMonitor: EARepositoryPerformanceMonitor?
    
    /// 设置性能监控器引用
    func setPerformanceMonitor(_ monitor: EARepositoryPerformanceMonitor) async {
        self.performanceMonitor = monitor
    }
    
    // MARK: - 基础用户操作（纯数据访问，无业务逻辑）
    func fetchUser(id: UUID) async throws -> EAUser? {
        // 🔑 性能监控集成
        let token = await performanceMonitor?.startMonitoring(operationType: "user_fetch")
        defer {
            if let token = token {
                Task { @MainActor in
                    await performanceMonitor?.endMonitoring(token: token, success: true)
                }
            }
        }
        
        let descriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == id }
        )
        let users = try modelContext.fetch(descriptor)
        return users.first
    }
    
    func fetchCurrentUser() async throws -> EAUser? {
        // 🔑 性能监控集成
        let token = await performanceMonitor?.startMonitoring(operationType: "user_current_fetch")
        defer {
            if let token = token {
                Task { @MainActor in
                    await performanceMonitor?.endMonitoring(token: token, success: true)
                }
            }
        }
        
        // 🔑 简化逻辑：纯数据访问，不包含业务验证
        if let userIdString = UserDefaults.standard.string(forKey: "currentUserId"),
           let userId = UUID(uuidString: userIdString) {
            return try await fetchUser(id: userId)
        }
        return nil
    }

    /// 🔑 新增：安全获取所有用户（用于好友功能查询）
    func fetchAllUsers() async throws -> [EAUser] {
        let descriptor = FetchDescriptor<EAUser>(
            sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
        )
        return try modelContext.fetch(descriptor)
    }

    func saveUser(_ user: EAUser) async throws {
        try modelContext.save()
    }
    
    func deleteUser(_ user: EAUser) async throws {
        // 通过数据档案获取认证信息（遵循.cursorrules关系模式要求）
        if let authInfo = user.dataProfile?.authInfo {
            modelContext.delete(authInfo)
        }
        
        // 删除用户（关联数据会通过deleteRule自动删除）
        modelContext.delete(user)
        try modelContext.save()
    }
    
    /// 创建新用户（简化版，纯数据操作）
    func createUser(username: String, email: String? = nil) async throws -> EAUser {
        // 🔑 步骤1：创建基础用户实体
        let user = EAUser(username: username, email: email)
        modelContext.insert(user)
        
        // 🔑 步骤2：创建用户数据档案
        let dataProfile = EAUserDataProfile()
        modelContext.insert(dataProfile)
        
        // 🔑 步骤3：创建用户社交档案
        let socialProfile = EAUserSocialProfile()
        socialProfile.stellarLevel = 1
        socialProfile.totalStellarEnergy = 0
        socialProfile.explorerTitle = "新手探索者"
        socialProfile.universeRegion = "银河系边缘"
        socialProfile.cosmicContribution = 0
        socialProfile.lastEnergyUpdateDate = Date()
        socialProfile.socialActivityScore = 0.0
        socialProfile.followingCount = 0
        socialProfile.followersCount = 0
        socialProfile.explorationBadges = []
        socialProfile.dailyEnergyHistory = []
        socialProfile.explorationMilestones = []
        
        // 🔑 初始化数字宇宙数据
        socialProfile.initializeDigitalUniverseData()
        modelContext.insert(socialProfile)
        
        // 🔑 步骤4：创建用户审核档案
        let moderationProfile = EAUserModerationProfile()
        modelContext.insert(moderationProfile)
        
        // 🔑 步骤5：建立关系
        user.dataProfile = dataProfile
        user.socialProfile = socialProfile
        user.moderationProfile = moderationProfile
        
        dataProfile.user = user
        socialProfile.user = user
        moderationProfile.user = user
        
        // 🔑 步骤6：保存到数据库
        try modelContext.save()
        
        return user
    }
    
    func updateUserAvatar(userId: UUID, avatarData: EAAvatarData?) async throws {
        guard let user = try await fetchUser(id: userId) else {
            throw EARepositoryError.userNotFound
        }
        
        // 🔑 系统性修复：使用计算属性进行数据操作，确保编码一致性
        // 不再直接操作 avatarDataEncoded，改用计算属性的setter
        user.avatarData = avatarData
        
        // 强制保存到数据库
        try modelContext.save()
    }
    
    // MARK: - AI数据桥接方法（实现协议要求）
    func fetchUser(by userId: UUID) async -> EAUser? {
        let descriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userId }
        )
        let users = (try? modelContext.fetch(descriptor)) ?? []
        return users.first
    }
    
    func fetchUserSettings(userId: UUID) async -> EAUserSettings? {
        guard let user = await fetchUser(by: userId) else { return nil }
        return user.settings
    }
    
    // MARK: - 扩展功能：认证相关方法（简化版）
    func createUserWithAuth(username: String, email: String?, phoneNumber: String, password: String) async throws -> EAUser {
        // 🔑 简化：基础重复检查，业务验证移至Service层
        if try await fetchUserByPhone(phoneNumber: phoneNumber) != nil {
            throw DataModelError.duplicateUser("手机号已被注册")
        }

        if let email = email, !email.isEmpty {
            if try await fetchUserByEmail(email: email) != nil {
                throw DataModelError.duplicateUser("邮箱已被注册")
            }
        }

        // 创建用户（已包含完整的档案初始化）
        let user = try await createUser(username: username, email: email)

        // 创建认证信息
        let authInfo = EAUserAuthInfo(
            phoneNumber: phoneNumber,
            passwordHash: hashPassword(password),
            createdAt: Date()
        )
        modelContext.insert(authInfo)

        // 建立认证信息与数据档案的关系
        user.dataProfile?.authInfo = authInfo
        authInfo.userDataProfile = user.dataProfile

        // 保存到数据库
        try modelContext.save()

        return user
    }
    
    func fetchUserByPhone(phoneNumber: String) async throws -> EAUser? {
        // 通过认证信息查找用户（使用关系访问）
        let authDescriptor = FetchDescriptor<EAUserAuthInfo>(
            predicate: #Predicate { $0.phoneNumber == phoneNumber }
        )
        let authInfos = try modelContext.fetch(authDescriptor)
        
        guard let authInfo = authInfos.first else { return nil }
        
        // 通过数据档案关系获取用户，而非外键查询
        return authInfo.userDataProfile?.user
    }
    
    func fetchUserByEmail(email: String) async throws -> EAUser? {
        let descriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.email == email }
        )
        let users = try modelContext.fetch(descriptor)
        return users.first
    }

    func fetchUserByUsername(_ username: String) async throws -> EAUser? {
        let descriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.username == username }
        )
        let users = try modelContext.fetch(descriptor)
        return users.first
    }
    
    func validateCredentials(phoneNumber: String, password: String) async throws -> EAUser? {
        // 查找认证信息
        let authDescriptor = FetchDescriptor<EAUserAuthInfo>(
            predicate: #Predicate { $0.phoneNumber == phoneNumber }
        )
        let authInfos = try modelContext.fetch(authDescriptor)
        
        guard let authInfo = authInfos.first else { return nil }
        
        // 验证密码
        guard verifyPassword(password, hash: authInfo.passwordHash) else { return nil }
        
        // 通过数据档案关系返回用户，而非外键查询
        return authInfo.userDataProfile?.user
    }
    
    // MARK: - 密码处理（简化版）
    private func hashPassword(_ password: String) -> String {
        let salt = "EvolveApp2025"
        let combinedString = "\(salt)_\(password)_\(salt)"
        let data = Data(combinedString.utf8)
        let hash = data.base64EncodedString()
        return "sha256_\(hash)"
    }
    
    private func verifyPassword(_ password: String, hash: String) -> Bool {
        guard hash.hasPrefix("sha256_") else {
            return hash.contains("\(password.hashValue)")
        }
        let expectedHash = hashPassword(password)
        return expectedHash == hash
    }
    
    // MARK: - 测试数据管理方法
    #if DEBUG
    func deleteAllTestUsers() async throws {
        let descriptor = FetchDescriptor<EAUser>()
        let users = try modelContext.fetch(descriptor)
        
        for user in users {
            modelContext.delete(user)
        }
        
        try modelContext.save()
    }
    #endif

    /// 🔑 简化版：确保社交档案存在（移除复杂业务逻辑）
    func ensureSocialProfile(for user: EAUser) async throws -> EAUserSocialProfile {
        if let existingProfile = user.socialProfile {
            return existingProfile
        }
        
        // 简单创建新档案，不进行复杂验证
        let socialProfile = EAUserSocialProfile()
        socialProfile.stellarLevel = 1
        socialProfile.totalStellarEnergy = 0
        socialProfile.explorerTitle = "新手探索者"
        socialProfile.universeRegion = "银河系边缘"
        socialProfile.cosmicContribution = 0
        socialProfile.lastEnergyUpdateDate = Date()
        socialProfile.socialActivityScore = 0.0
        socialProfile.followingCount = 0
        socialProfile.followersCount = 0
        socialProfile.explorationBadges = []
        socialProfile.dailyEnergyHistory = []
        socialProfile.explorationMilestones = []
        
        socialProfile.initializeDigitalUniverseData()
        
        modelContext.insert(socialProfile)
        user.socialProfile = socialProfile
        socialProfile.user = user
        try modelContext.save()
        
        return socialProfile
    }

    /// 🔑 协议兼容：findOrCreateSocialProfile 方法（与ensureSocialProfile相同逻辑）
    func findOrCreateSocialProfile(for user: EAUser) async throws -> EAUserSocialProfile {
        return try await ensureSocialProfile(for: user)
    }
}

// MARK: - 注意：EAUserAuthInfo模型已移动到Core/DataModels/EAUserAuthInfo.swift