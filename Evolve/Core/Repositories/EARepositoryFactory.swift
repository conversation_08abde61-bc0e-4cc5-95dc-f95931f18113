//
//  EARepositoryFactory.swift
//  Evolve
//
//  Created by AI Assistant on 2025-06-05.
//

import Foundation
import SwiftData

/// Repository工厂类
/// 负责创建和管理各种Repository实例，确保依赖注入的一致性
@MainActor
final class EARepositoryFactory {
    
    // MARK: - 社区Repository创建
    
    /// 创建社区Repository实例
    /// - Parameters:
    ///   - modelContext: SwiftData模型上下文
    /// - Returns: 社区Repository实例
    /// ✅ 修复：移除sessionManager依赖，Repository应该是纯净的数据访问层
    static func createCommunityRepository(modelContext: ModelContext) -> EACommunityRepositoryProtocol {
        // 从ModelContext获取ModelContainer
        let modelContainer = modelContext.container
        return EACommunityRepository(modelContainer: modelContainer)
    }
    
    // TODO: 其他Repository待实现
    // - EAUserRepository
    // - EAHabitRepository  
    // - EACompletionRepository
    // 等等...
} 