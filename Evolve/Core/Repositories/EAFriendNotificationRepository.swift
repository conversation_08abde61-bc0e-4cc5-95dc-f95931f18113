import Foundation
import SwiftData

// MARK: - 错误定义

/// 好友通知Repository错误类型
enum FriendNotificationRepositoryError: Error, LocalizedError {
    case notificationNotFound
    case invalidNotificationData
    case contextNotAvailable
    case saveFailed(Error)

    var errorDescription: String? {
        switch self {
        case .notificationNotFound:
            return "通知未找到"
        case .invalidNotificationData:
            return "无效的通知数据"
        case .contextNotAvailable:
            return "数据上下文不可用"
        case .saveFailed(let error):
            return "保存失败: \(error.localizedDescription)"
        }
    }
}

/// 好友通知Repository协议
/// 遵循项目Repository模式，使用@ModelActor确保线程安全
protocol EAFriendNotificationRepositoryProtocol {
    func createNotification(_ notification: EAFriendNotification) async throws
    func fetchUserNotifications(userProfileId: UUID) async throws -> [EAFriendNotification]
    func fetchUnreadNotifications(userProfileId: UUID) async throws -> [EAFriendNotification]
    func fetchNotificationsByType(userProfileId: UUID, type: EAFriendNotification.NotificationType) async throws -> [EAFriendNotification]
    func updateNotification(_ notification: EAFriendNotification) async throws
    func markNotificationAsRead(notificationId: UUID) async throws
    func markNotificationAsDeleted(notificationId: UUID) async throws
    func deleteNotification(notificationId: UUID) async throws
    func fetchRecentNotifications(userProfileId: UUID, limit: Int) async throws -> [EAFriendNotification]

    // 🔑 新增：安全的通知创建方法，避免跨Context问题
    func createFriendRequestNotificationSafely(senderProfileId: UUID, receiverProfileId: UUID, requestId: UUID, message: String?) async throws
    func createRequestAcceptedNotificationSafely(accepterProfileId: UUID, requesterProfileId: UUID, friendshipId: UUID, accepterUsername: String) async throws
}

// MARK: - Repository实现

/// 好友通知Repository实现
/// 使用@ModelActor确保线程安全，遵循项目开发规范
@ModelActor
actor EAFriendNotificationRepositoryImpl: EAFriendNotificationRepositoryProtocol {

    // MARK: - 创建操作

    /// 创建通知
    func createNotification(_ notification: EAFriendNotification) async throws {
        modelContext.insert(notification)
        try modelContext.save()

        #if DEBUG
        // 调试环境下记录通知创建成功，但不使用print
        #endif
    }
    
    // MARK: - 查询操作
    
    /// 🚨 致命修复：获取用户的所有通知（使用存储属性）
    func fetchUserNotifications(userProfileId: UUID) async throws -> [EAFriendNotification] {
        let descriptor = FetchDescriptor<EAFriendNotification>(
            predicate: #Predicate<EAFriendNotification> { notification in
                notification.receiverActivity?.socialProfile?.id == userProfileId &&
                !notification.isDeleted
            },
            sortBy: [SortDescriptor(\.createdAt, order: .reverse)]
        )
        
        let notifications = try modelContext.fetch(descriptor)

        #if DEBUG
        // 调试环境下记录通知获取信息，但不使用print
        #endif

        return notifications
    }
    
    /// 🚨 致命修复：获取用户的未读通知（使用存储属性）
    func fetchUnreadNotifications(userProfileId: UUID) async throws -> [EAFriendNotification] {
        let descriptor = FetchDescriptor<EAFriendNotification>(
            predicate: #Predicate<EAFriendNotification> { notification in
                notification.receiverActivity?.socialProfile?.id == userProfileId &&
                !notification.isRead &&
                !notification.isDeleted
            },
            sortBy: [SortDescriptor(\.createdAt, order: .reverse)]
        )
        
        let notifications = try modelContext.fetch(descriptor)

        #if DEBUG
        // 调试环境下记录未读通知获取信息，但不使用print
        #endif

        return notifications
    }
    
    /// 获取特定类型的通知
    func fetchNotificationsByType(
        userProfileId: UUID,
        type: EAFriendNotification.NotificationType
    ) async throws -> [EAFriendNotification] {
        let descriptor = FetchDescriptor<EAFriendNotification>(
            predicate: #Predicate<EAFriendNotification> { notification in
                notification.receiverActivity?.socialProfile?.id == userProfileId &&
                notification.type == type &&
                !notification.isDeleted
            },
            sortBy: [SortDescriptor(\.createdAt, order: .reverse)]
        )
        
        let notifications = try modelContext.fetch(descriptor)

        #if DEBUG
        // 调试环境下记录类型通知获取信息，但不使用print
        #endif

        return notifications
    }
    
    /// 根据ID获取通知（公开方法）
    func fetchNotification(by id: UUID) async throws -> EAFriendNotification? {
        let descriptor = FetchDescriptor<EAFriendNotification>(
            predicate: #Predicate<EAFriendNotification> { notification in
                notification.id == id
            }
        )

        let notifications = try modelContext.fetch(descriptor)
        return notifications.first
    }
    
    /// 获取需要推送的通知
    func fetchPendingPushNotifications() async throws -> [EAFriendNotification] {
        let descriptor = FetchDescriptor<EAFriendNotification>(
            predicate: #Predicate<EAFriendNotification> { notification in
                notification.shouldPush &&
                !notification.pushSent &&
                !notification.isDeleted
            },
            sortBy: [SortDescriptor(\.createdAt, order: .forward)]
        )
        
        let notifications = try modelContext.fetch(descriptor)

        #if DEBUG
        // 调试环境下记录待推送通知获取信息，但不使用print
        #endif

        return notifications
    }

    // MARK: - 私有辅助方法

    /// 🚨 致命修复：获取最近的通知（使用存储属性）
    func fetchRecentNotifications(userProfileId: UUID, limit: Int) async throws -> [EAFriendNotification] {
        let descriptor = FetchDescriptor<EAFriendNotification>(
            predicate: #Predicate<EAFriendNotification> { notification in
                notification.receiverActivity?.socialProfile?.id == userProfileId &&
                !notification.isDeleted
            },
            sortBy: [SortDescriptor(\.createdAt, order: .reverse)]
        )

        let allNotifications = try modelContext.fetch(descriptor)
        return Array(allNotifications.prefix(limit))
    }

    // MARK: - 🔑 安全通知创建方法（修复跨Context问题）

    /// 安全创建好友请求通知 - 避免跨Context关系错误
    func createFriendRequestNotificationSafely(
        senderProfileId: UUID,
        receiverProfileId: UUID,
        requestId: UUID,
        message: String?
    ) async throws {
        // 🔑 关键修复：在当前Context中重新获取Profile对象
        let safeSenderProfile = try await getSafeUserProfile(by: senderProfileId)
        let safeReceiverProfile = try await getSafeUserProfile(by: receiverProfileId)

        // 创建通知对象
        let senderName = safeSenderProfile.user?.username ?? "未知用户"
        let title = "新的好友请求"
        let content = message?.isEmpty == false ?
            "\(senderName) 想要添加您为好友：\(message!)" :
            "\(senderName) 想要添加您为好友"

        let notification = EAFriendNotification(
            type: .friendRequest,
            title: title,
            content: content,
            priority: .normal,
            expiresAt: Calendar.current.date(byAdding: .day, value: 30, to: Date())
        )

        // 🔑 修复：遵循安全赋值顺序（插入→赋值→保存）
        modelContext.insert(notification)

        // 🔑 第四阶段优化：建立关系（使用新的活动档案模型）
        notification.senderActivity = safeSenderProfile.socialActivity
        notification.receiverActivity = safeReceiverProfile.socialActivity
        // ✅ 修复：删除外键字段，如需关联请求，应通过关系属性

        // 保存到数据库
        try modelContext.save()

        #if DEBUG
        // 调试环境下记录通知创建成功，但不使用print
        #endif
    }

    /// 🔑 关键修复：安全创建请求接受通知 - 避免跨Context关系错误
    func createRequestAcceptedNotificationSafely(
        accepterProfileId: UUID,
        requesterProfileId: UUID,
        friendshipId: UUID,
        accepterUsername: String
    ) async throws {
        // 🔑 关键修复：在当前Context中重新获取Profile对象
        let safeAccepterProfile = try await getSafeUserProfile(by: accepterProfileId)
        let safeRequesterProfile = try await getSafeUserProfile(by: requesterProfileId)

        // 🔑 关键修复：在当前Context中重新获取Friendship对象
        let safeFriendship = try await getSafeFriendship(by: friendshipId)

        // 创建通知对象
        let title = "好友请求已接受"
        let content = "\(accepterUsername) 接受了您的好友请求，你们现在是星际伙伴了！"

        let notification = EAFriendNotification(
            type: .requestAccepted,
            title: title,
            content: content,
            priority: .high,
            expiresAt: Calendar.current.date(byAdding: .day, value: 7, to: Date())
        )

        // 🔑 修复：遵循安全赋值顺序（插入→赋值→保存）
        modelContext.insert(notification)

        // 🔑 关键修复：建立关系（使用当前Context中的对象）
        notification.senderActivity = safeAccepterProfile.socialActivity
        notification.receiverActivity = safeRequesterProfile.socialActivity
        notification.relatedFriendship = safeFriendship

        // 保存到数据库
        try modelContext.save()

        #if DEBUG
        // 调试环境下记录请求接受通知创建成功，但不使用print
        #endif
    }

    /// 在当前Context中安全获取用户社交档案
    private func getSafeUserProfile(by profileId: UUID) async throws -> EAUserSocialProfile {
        // 🔑 修复：使用Predicate直接查询，避免Context冲突
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate<EAUser> { user in
                user.socialProfile?.id == profileId
            }
        )

        guard let user = try modelContext.fetch(userDescriptor).first,
              let socialProfile = user.socialProfile else {
            throw NotificationServiceError.invalidNotificationData
        }

        #if DEBUG
        // 调试环境下记录Context安全获取用户档案，但不使用print
        #endif

        return socialProfile
    }

    /// 🔑 关键修复：在当前Context中安全获取Friendship对象
    private func getSafeFriendship(by friendshipId: UUID) async throws -> EAFriendship {
        let descriptor = FetchDescriptor<EAFriendship>(
            predicate: #Predicate<EAFriendship> { friendship in
                friendship.id == friendshipId
            }
        )

        guard let friendship = try modelContext.fetch(descriptor).first else {
            throw FriendshipError.friendshipNotFound
        }

        return friendship
    }

    // MARK: - 更新操作

    /// 更新通知
    func updateNotification(_ notification: EAFriendNotification) async throws {
        notification.updatedAt = Date()
        try modelContext.save()

        #if DEBUG
        // 调试环境下记录通知更新成功，但不使用print
        #endif
    }

    /// 标记通知为已读
    func markNotificationAsRead(notificationId: UUID) async throws {
        guard let notification = try await fetchNotification(by: notificationId) else {
            throw FriendNotificationRepositoryError.notificationNotFound
        }

        notification.markAsRead()
        try modelContext.save()

        #if DEBUG
        // 调试环境下记录通知标记已读成功，但不使用print
        #endif
    }

    /// 标记通知为已删除
    func markNotificationAsDeleted(notificationId: UUID) async throws {
        guard let notification = try await fetchNotification(by: notificationId) else {
            throw FriendNotificationRepositoryError.notificationNotFound
        }

        notification.markAsDeleted()
        try modelContext.save()

        #if DEBUG
        // 调试环境下记录通知标记删除成功，但不使用print
        #endif
    }

    /// 删除通知
    func deleteNotification(notificationId: UUID) async throws {
        guard let notification = try await fetchNotification(by: notificationId) else {
            throw FriendNotificationRepositoryError.notificationNotFound
        }

        modelContext.delete(notification)
        try modelContext.save()

        #if DEBUG
        // 调试环境下记录通知删除成功，但不使用print
        #endif
    }
    
    /// 批量标记为已读
    func markAllAsRead(userProfileId: UUID) async throws {
        let notifications = try await fetchUnreadNotifications(userProfileId: userProfileId)
        
        for notification in notifications {
            notification.markAsRead()
        }
        
        try modelContext.save()

        #if DEBUG
        // 调试环境下记录批量标记已读成功，但不使用print
        #endif
    }

    // MARK: - 删除操作

    /// 软删除通知（标记为已删除）
    func softDeleteNotification(_ notification: EAFriendNotification) async throws {
        notification.markAsDeleted()
        try modelContext.save()

        #if DEBUG
        // 调试环境下记录软删除通知成功，但不使用print
        #endif
    }

    /// 硬删除通知（物理删除）
    func hardDeleteNotification(_ notification: EAFriendNotification) async throws {
        modelContext.delete(notification)
        try modelContext.save()

        #if DEBUG
        // 调试环境下记录硬删除通知成功，但不使用print
        #endif
    }
    
    /// 清理过期通知
    func cleanupExpiredNotifications() async throws {
        // 🔑 修复：避免在Predicate中使用Date()函数，改为先获取当前时间
        let now = Date()
        let descriptor = FetchDescriptor<EAFriendNotification>(
            predicate: #Predicate<EAFriendNotification> { notification in
                notification.expiresAt != nil &&
                notification.expiresAt! < now
            }
        )

        let expiredNotifications = try modelContext.fetch(descriptor)

        for notification in expiredNotifications {
            notification.markAsDeleted()
        }

        try modelContext.save()

        #if DEBUG
        // 调试环境下记录清理过期通知成功，但不使用print
        #endif
    }

    /// 清理旧的已删除通知（物理删除）
    func cleanupOldDeletedNotifications(olderThan days: Int = 30) async throws {
        // 🔑 修复：先计算截止日期，避免在Predicate中使用复杂计算
        let cutoffDate = Calendar.current.date(byAdding: .day, value: -days, to: Date()) ?? Date()

        let descriptor = FetchDescriptor<EAFriendNotification>(
            predicate: #Predicate<EAFriendNotification> { notification in
                notification.isDeleted &&
                notification.updatedAt < cutoffDate
            }
        )

        let oldNotifications = try modelContext.fetch(descriptor)

        for notification in oldNotifications {
            modelContext.delete(notification)
        }

        try modelContext.save()

        #if DEBUG
        // 调试环境下记录清理旧删除通知成功，但不使用print
        #endif
    }
    
    // MARK: - 统计操作
    
    /// 🚨 致命修复：获取未读通知数量（使用存储属性）
    func getUnreadCount(userProfileId: UUID) async throws -> Int {
        let descriptor = FetchDescriptor<EAFriendNotification>(
            predicate: #Predicate<EAFriendNotification> { notification in
                notification.receiverActivity?.socialProfile?.id == userProfileId &&
                !notification.isRead &&
                !notification.isDeleted
            }
        )
        
        let notifications = try modelContext.fetch(descriptor)
        return notifications.count
    }
    
    /// 获取各类型通知的数量统计
    func getNotificationCountsByType(userProfileId: UUID) async throws -> [EAFriendNotification.NotificationType: Int] {
        let notifications = try await fetchUserNotifications(userProfileId: userProfileId)
        
        var counts: [EAFriendNotification.NotificationType: Int] = [:]
        
        for notification in notifications {
            counts[notification.type, default: 0] += 1
        }
        
        return counts
    }
    
}
