import Foundation
import SwiftData

// MARK: - 宇宙挑战Repository协议
@preconcurrency 
protocol EAUniverseChallengeRepositoryProtocol: Actor {
    func fetchActiveChallenges() async throws -> [EAUniverseChallenge]
    func fetchUserParticipations(for userId: UUID) async throws -> [EAUniverseChallengeParticipation]
    func createChallenge(_ challenge: EAUniverseChallenge) async throws
    func joinChallenge(_ challenge: EAUniverseChallenge, user: EAUser) async throws -> EAUniverseChallengeParticipation
    func updateParticipationProgress(_ participation: EAUniverseChallengeParticipation, progress: Int, targetValue: Int) async throws
    func fetchChallenge(by id: UUID) async throws -> EAUniverseChallenge?
    func fetchParticipation(challengeId: UUID, userId: UUID) async throws -> EAUniverseChallengeParticipation?
    func saveContext() async throws
    
    // Day 9新增：业务逻辑支持方法
    func fetchExpiredChallenges() async throws -> [EAUniverseChallenge]
    func fetchChallengeParticipations(challengeId: UUID) async throws -> [EAUniverseChallengeParticipation]
    func archiveChallenge(_ challenge: EAUniverseChallenge) async throws
}

// MARK: - 宇宙挑战Repository实现
@ModelActor
actor EAUniverseChallengeRepository: EAUniverseChallengeRepositoryProtocol {
    
    // MARK: - 挑战管理
    
    /// 获取活跃挑战列表
    func fetchActiveChallenges() async throws -> [EAUniverseChallenge] {
        let descriptor = FetchDescriptor<EAUniverseChallenge>(
            predicate: #Predicate { challenge in
                challenge.isActive == true
            },
            sortBy: [SortDescriptor(\.startDate, order: .reverse)]
        )
        
        let challenges = try modelContext.fetch(descriptor)
        
        // 更新挑战状态
        for challenge in challenges {
            challenge.updateStatus()
        }
        
        try modelContext.save()
        return challenges.filter { !$0.hasEnded }
    }
    
    /// 创建新挑战
    func createChallenge(_ challenge: EAUniverseChallenge) async throws {
        modelContext.insert(challenge)
        try modelContext.save()
    }
    
    /// 根据ID获取挑战
    func fetchChallenge(by id: UUID) async throws -> EAUniverseChallenge? {
        let descriptor = FetchDescriptor<EAUniverseChallenge>(
            predicate: #Predicate { challenge in
                challenge.id == id
            }
        )
        
        return try modelContext.fetch(descriptor).first
    }
    
    // MARK: - 参与管理
    
    /// 获取用户参与记录（使用UUID参数）
    func fetchUserParticipations(for userId: UUID) async throws -> [EAUniverseChallengeParticipation] {
        let descriptor = FetchDescriptor<EAUniverseChallengeParticipation>(
            predicate: #Predicate { participation in
                participation.user?.id == userId
            },
            sortBy: [SortDescriptor(\.joinDate, order: .reverse)]
        )
        
        return try modelContext.fetch(descriptor)
    }
    
    /// 获取用户参与记录（兼容EAUser对象）
    func fetchUserParticipations(for user: EAUser) async throws -> [EAUniverseChallengeParticipation] {
        return try await fetchUserParticipations(for: user.id)
    }
    
    /// 用户参与挑战
    func joinChallenge(_ challenge: EAUniverseChallenge, user: EAUser) async throws -> EAUniverseChallengeParticipation {
        // 检查是否已经参与
        if try await fetchParticipation(challengeId: challenge.id, userId: user.id) != nil {
            throw ChallengeError.alreadyParticipating
        }
        
        // 检查挑战是否可参与
        guard challenge.canParticipate() else {
            throw ChallengeError.cannotParticipate
        }
        
        // 创建参与记录
        let participation = EAUniverseChallengeParticipation()
        modelContext.insert(participation)
        
        // ✅ 正确：建立SwiftData关系
        participation.user = user
        participation.challenge = challenge
        
        // 更新挑战参与人数
        challenge.addParticipant()
        
        try modelContext.save()
        return participation
    }
    
    /// 获取特定用户在特定挑战的参与记录（使用UUID参数）
    func fetchParticipation(challengeId: UUID, userId: UUID) async throws -> EAUniverseChallengeParticipation? {
        let descriptor = FetchDescriptor<EAUniverseChallengeParticipation>(
            predicate: #Predicate { participation in
                participation.user?.id == userId && participation.challenge?.id == challengeId
            }
        )
        
        return try modelContext.fetch(descriptor).first
    }
    
    /// 获取特定用户在特定挑战的参与记录（兼容对象参数）
    func fetchParticipation(challenge: EAUniverseChallenge, user: EAUser) async throws -> EAUniverseChallengeParticipation? {
        return try await fetchParticipation(challengeId: challenge.id, userId: user.id)
    }
    
    /// 更新参与进度
    func updateParticipationProgress(_ participation: EAUniverseChallengeParticipation, progress: Int, targetValue: Int) async throws {
        participation.updateProgress(progress, targetValue: targetValue)
        try modelContext.save()
    }
    
    // MARK: - 数据持久化
    
    /// 保存上下文
    func saveContext() async throws {
        try modelContext.save()
    }
    
    // MARK: - Day 9新增：业务逻辑支持方法
    
    /// 获取已过期挑战列表
    func fetchExpiredChallenges() async throws -> [EAUniverseChallenge] {
        let descriptor = FetchDescriptor<EAUniverseChallenge>(
            predicate: #Predicate { challenge in
                challenge.isActive == false
            },
            sortBy: [SortDescriptor(\.endDate, order: .reverse)]
        )
        
        let challenges = try modelContext.fetch(descriptor)
        
        // 更新挑战状态
        for challenge in challenges {
            challenge.updateStatus()
        }
        
        try modelContext.save()
        return challenges.filter { !$0.hasEnded }
    }
    
    /// 获取挑战参与记录
    func fetchChallengeParticipations(challengeId: UUID) async throws -> [EAUniverseChallengeParticipation] {
        let descriptor = FetchDescriptor<EAUniverseChallengeParticipation>(
            predicate: #Predicate { participation in
                participation.challenge?.id == challengeId
            },
            sortBy: [SortDescriptor(\.joinDate, order: .reverse)]
        )
        
        return try modelContext.fetch(descriptor)
    }
    
    /// 归档挑战
    func archiveChallenge(_ challenge: EAUniverseChallenge) async throws {
        modelContext.delete(challenge)
        try modelContext.save()
    }
}

// MARK: - 挑战错误类型
enum ChallengeError: LocalizedError {
    case challengeNotFound
    case participationNotFound
    case alreadyParticipating
    case cannotParticipate
    case invalidProgress
    
    var errorDescription: String? {
        switch self {
        case .challengeNotFound:
            return "挑战不存在"
        case .participationNotFound:
            return "参与记录不存在"
        case .alreadyParticipating:
            return "已经参与此挑战"
        case .cannotParticipate:
            return "无法参与此挑战"
        case .invalidProgress:
            return "无效的进度数据"
        }
    }
} 