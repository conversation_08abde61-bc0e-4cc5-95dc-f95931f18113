import SwiftData
import Foundation

@ModelActor
actor EAHabitRepositoryImpl: EAHabitRepository {
    
    // MARK: - Core Habit CRUD（性能优化版）
    
    func fetchHabits(for userID: UUID) async throws -> [EAHabit] {
        // 🔑 性能优化：添加排序以利用潜在索引，只获取活跃习惯
        let descriptor = FetchDescriptor<EAHabit>(
            predicate: #Predicate<EAHabit> { habit in
                habit.user?.id == userID && habit.isActive == true
            },
            sortBy: [
                SortDescriptor(\.creationDate, order: .reverse),
                SortDescriptor(\.name, order: .forward)
            ]
        )
        
        do {
            return try modelContext.fetch(descriptor)
        } catch {
            throw EARepositoryError.dataNotFound
        }
    }
    
    func fetchActiveHabits(for userID: UUID) async throws -> [EAHabit] {
        // 委托给fetchHabits，因为它已经过滤了isActive
        return try await fetchHabits(for: userID)
    }
    
    func fetchHabit(id: UUID) async throws -> EAHabit? {
        let descriptor = FetchDescriptor<EAHabit>(
            predicate: #Predicate<EAHabit> { $0.id == id }
        )
        
        do {
            let habits = try modelContext.fetch(descriptor)
            return habits.first
        } catch {
            throw EARepositoryError.dataNotFound
        }
    }
    
    func saveHabit(_ habit: EAHabit) async throws {
        do {
            try modelContext.save()
        } catch {
            throw EARepositoryError.saveFailed
        }
    }
    
    func deleteHabit(_ habit: EAHabit) async throws {
        do {
            modelContext.delete(habit)
            try modelContext.save()
        } catch {
            throw EARepositoryError.deleteError
        }
    }
    
    func createHabit(
        name: String,
        iconName: String,
        targetFrequency: Int,
        frequencyType: String,
        category: String,
        difficulty: String,
        for userID: UUID
    ) async throws -> EAHabit {
        // 首先获取用户
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate<EAUser> { $0.id == userID }
        )
        
        guard let user = try modelContext.fetch(userDescriptor).first else {
            throw EARepositoryError.userNotFound
        }
        
        // 创建习惯（使用正确的init方法）
        let habit = EAHabit(
            name: name,
            iconName: iconName,
            targetFrequency: targetFrequency
        )
        
        // 设置额外的属性
        habit.frequencyType = frequencyType
        habit.category = category
        habit.difficulty = difficulty
        
        // 🔑 iOS 18+安全关系赋值：先插入Context，再赋值关系
        modelContext.insert(habit)
        habit.user = user
        
        do {
            try modelContext.save()
            return habit
        } catch {
            throw EARepositoryError.saveFailed
        }
    }
    
    func createHabitSafely(
        name: String,
        iconName: String,
        targetFrequency: Int,
        frequencyType: String,
        selectedWeekdays: [Int],
        dailyTarget: Int,
        monthlyTarget: Int,
        monthlyMode: String,
        selectedMonthlyDates: [Int],
        selectedOneTimeDates: [String],
        category: String,
        difficulty: String,
        reminderTimes: [String],
        reminderEnabled: Bool,
        preferredTimeSlot: String?,
        for userID: UUID
    ) async throws -> EAHabit {
        // 首先获取用户
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate<EAUser> { $0.id == userID }
        )
        
        guard let user = try modelContext.fetch(userDescriptor).first else {
            throw EARepositoryError.userNotFound
        }
        
        // 创建习惯（使用正确的init方法）
        let habit = EAHabit(
            name: name,
            iconName: iconName,
            targetFrequency: targetFrequency,
            preferredTimeSlot: preferredTimeSlot
        )
        
        // 设置其他属性
        habit.frequencyType = frequencyType
        habit.selectedWeekdays = selectedWeekdays
        habit.dailyTarget = dailyTarget
        habit.monthlyTarget = monthlyTarget
        habit.monthlyMode = monthlyMode
        habit.selectedMonthlyDates = selectedMonthlyDates
        habit.selectedOneTimeDates = selectedOneTimeDates
        habit.category = category
        habit.difficulty = difficulty
        habit.reminderTimes = reminderTimes
        habit.reminderEnabled = reminderEnabled
        
        // 🔑 iOS 18+安全关系赋值：先插入Context，再赋值关系
        modelContext.insert(habit)
        habit.user = user
        
        do {
            try modelContext.save()
            return habit
        } catch {
            throw EARepositoryError.saveFailed
        }
    }
    
    // MARK: - AI数据桥接方法
    
    func fetchUserHabits(userId: UUID) async -> [EAHabit] {
        do {
            return try await fetchHabits(for: userId)
        } catch {
            #if DEBUG
            print("🔍 [EAHabitRepository] 获取用户习惯失败: \\(error)")
            #endif
            return []
        }
    }
    
    func fetchRecentCompletions(userId: UUID, days: Int) async -> [EACompletion] {
        let calendar = Calendar.current
        let endDate = Date()
        guard let startDate = calendar.date(byAdding: .day, value: -days, to: endDate) else {
            return []
        }
        
        let descriptor = FetchDescriptor<EACompletion>(
            predicate: #Predicate<EACompletion> { completion in
                completion.habit?.user?.id == userId &&
                completion.date >= startDate &&
                completion.date <= endDate
            },
            sortBy: [SortDescriptor(\.date, order: .reverse)]
        )
        
        do {
            return try modelContext.fetch(descriptor)
        } catch {
            #if DEBUG
            print("🔍 [EAHabitRepository] 获取最近完成记录失败: \\(error)")
            #endif
            return []
        }
    }
}

// MARK: - Error Types
enum EAHabitRepositoryError: LocalizedError {
    case dataFetchFailed
    case saveFailed
    case deleteError
    case userNotFound
    
    var errorDescription: String? {
        switch self {
        case .dataFetchFailed:
            return "习惯数据获取失败"
        case .saveFailed:
            return "习惯保存失败"
        case .deleteError:
            return "习惯删除失败"
        case .userNotFound:
            return "用户不存在"
        }
    }
} 