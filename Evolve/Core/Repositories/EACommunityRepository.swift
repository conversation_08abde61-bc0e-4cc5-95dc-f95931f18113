//
//  EACommunityRepository.swift
//  Evolve
//
//  Created by AI Assistant on 2025-06-05.
//

import Foundation
import SwiftData
import SwiftUI

// MARK: - Repository Error Types
enum EACommunityRepositoryError: LocalizedError {
    case contextMismatch
    case userNotFound
    case postNotFound
    case commentNotFound
    case likeNotFound
    case dataFetchFailed
    case dataCreateFailed
    case dataSaveFailed
    case invalidParameters
    
    var errorDescription: String? {
        switch self {
        case .contextMismatch:
            return "数据上下文不匹配"
        case .userNotFound:
            return "用户未找到"
        case .postNotFound:
            return "帖子未找到"
        case .commentNotFound:
            return "评论未找到"
        case .likeNotFound:
            return "点赞未找到"
        case .dataFetchFailed:
            return "数据获取失败"
        case .dataCreateFailed:
            return "数据创建失败"
        case .dataSaveFailed:
            return "数据保存失败"
        case .invalidParameters:
            return "参数无效"
        }
    }
}

// MARK: - Community Repository Protocol
protocol EACommunityRepositoryProtocol {
    // 帖子管理
    func fetchPosts(limit: Int, offset: Int) async throws -> [EACommunityPost]
    func fetchPost(by id: UUID) async throws -> EACommunityPost?
    func fetchUserPosts(userId: UUID, limit: Int, includeHidden: Bool) async throws -> [EACommunityPost]
    func fetchUserFollowing(userId: UUID, limit: Int) async throws -> [EACommunityFollow]
    func fetchUserFollowers(userId: UUID, limit: Int) async throws -> [EACommunityFollow]
    func isUserFollowing(followerId: UUID, followeeId: UUID) async throws -> Bool
    func fetchMutualFollows(userId: UUID) async throws -> [EACommunityFollow]
    func createPost(_ post: EACommunityPost, authorId: UUID) async throws -> EACommunityPost
    func updatePost(_ post: EACommunityPost) async throws -> EACommunityPost
    func deletePost(id: UUID) async throws
    
    // 评论管理
    func fetchComments(for postId: UUID) async throws -> [EACommunityComment]
    func createComment(_ comment: EACommunityComment, for postId: UUID, authorId: UUID) async throws -> EACommunityComment
    func createComment(content: String, authorId: UUID, postId: UUID, parentCommentId: UUID?) async throws -> EACommunityComment
    func deleteComment(id: UUID) async throws
    
    // 点赞管理
    func toggleLike(postId: UUID, userId: UUID) async throws -> Int
    func fetchLikes(for postId: UUID) async throws -> [EACommunityLike]
    func findExistingLike(commentId: UUID, userId: UUID) async throws -> EACommunityLike?
    func deleteLike(_ likeId: UUID) async throws
    func createLike(targetType: String, targetCommentId: UUID?, userId: UUID, userEnergyLevel: Int) async throws -> EACommunityLike
    
    // 用户关系
    func followUser(followerId: UUID, followingId: UUID) async throws
    func unfollowUser(followerId: UUID, followingId: UUID) async throws
    func isFollowing(followerId: UUID, followingId: UUID) async throws -> Bool

    // ✅ 新增：数据同步方法
    func syncAllPostCounts() async throws
    
    // 搜索功能
    func searchPosts(query: String, limit: Int) async throws -> [EACommunityPost]
    func searchUsers(query: String, limit: Int) async throws -> [EAUser]
    
    // 🔑 新增：分类和标签筛选功能
    func fetchPostsByCategory(category: String, limit: Int, offset: Int) async throws -> [EACommunityPost]
    func fetchPostsByTags(tags: [String], limit: Int, offset: Int) async throws -> [EACommunityPost]
    func fetchPostsWithFilters(category: String?, tags: [String]?, limit: Int, offset: Int) async throws -> [EACommunityPost]
    func fetchAvailableCategories() async throws -> [String]
    func fetchPopularTags(limit: Int) async throws -> [String]

    // MARK: - User Social Profile Management
    
    /// 更新用户社交档案
    /// - Parameter socialProfile: 要更新的社交档案
    /// - Returns: 更新后的社交档案
    func updateUserSocialProfile(_ socialProfile: EAUserSocialProfile) async throws -> EAUserSocialProfile
    
    /// 获取用户社交档案
    /// - Parameter userId: 用户ID
    /// - Returns: 用户社交档案
    func fetchUserSocialProfile(by userId: UUID) async throws -> EAUserSocialProfile?
    
    /// 创建用户社交档案
    /// - Parameter userId: 用户ID
    /// - Returns: 新创建的社交档案
    func createUserSocialProfile(for userId: UUID) async throws -> EAUserSocialProfile
    
    /// ✅ 新增：获取用户信息
    /// - Parameter userId: 用户ID
    /// - Returns: 用户对象
    func fetchUser(by userId: UUID) async throws -> EAUser?
}

// MARK: - Community Repository Implementation
@ModelActor
actor EACommunityRepository: EACommunityRepositoryProtocol {
    
    // MARK: - Helper Methods
    
    /// 根据用户ID获取用户
    private func getUser(by userId: UUID) throws -> EAUser {
        let descriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate<EAUser> { user in
                user.id == userId
            }
        )
        
        guard let user = try modelContext.fetch(descriptor).first else {
            throw EACommunityRepositoryError.userNotFound
        }
        
        return user
    }
    
    /// 安全保存Context
    private func saveContext() throws {
        do {
            try modelContext.save()
        } catch {
            throw EACommunityRepositoryError.dataSaveFailed
        }
    }
    
    // MARK: - Post Management（性能优化版）
    
    func fetchPosts(limit: Int, offset: Int) async throws -> [EACommunityPost] {
        // 🔑 性能优化：使用数据库级别分页，避免加载所有数据到内存
        var descriptor = FetchDescriptor<EACommunityPost>(
            predicate: #Predicate<EACommunityPost> { $0.isVisible == true },
            sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
        )
        descriptor.fetchLimit = limit
        descriptor.fetchOffset = offset
        
        do {
            return try modelContext.fetch(descriptor)
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func fetchPost(by id: UUID) async throws -> EACommunityPost? {
        let descriptor = FetchDescriptor<EACommunityPost>(
            predicate: #Predicate<EACommunityPost> { post in
                post.id == id
            }
        )
        
        do {
            return try modelContext.fetch(descriptor).first
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    /// ✅ 性能优化：专门的用户帖子查询方法
    func fetchUserPosts(userId: UUID, limit: Int = 10, includeHidden: Bool = false) async throws -> [EACommunityPost] {
        do {
            // ✅ 优化：使用更高效的查询策略
            // 首先获取用户的社交档案
            let userDescriptor = FetchDescriptor<EAUser>(
                predicate: #Predicate<EAUser> { user in
                    user.id == userId
                }
            )
            
            guard let user = try modelContext.fetch(userDescriptor).first,
                  let socialProfile = user.socialProfile else {
                return []
            }
            
            // 通过社交档案的关系获取帖子
            let userPosts = socialProfile.posts.filter { post in
                includeHidden || post.isVisible
            }
            
            // 按创建时间排序并限制数量
            let sortedPosts = userPosts.sorted { $0.creationDate > $1.creationDate }
            return Array(sortedPosts.prefix(limit))
            
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    /// ✅ 性能优化：用户关注列表查询
    func fetchUserFollowing(userId: UUID, limit: Int = 10) async throws -> [EACommunityFollow] {
        do {
            // ✅ 优化：通过用户关系直接访问，避免复杂查询
            let userDescriptor = FetchDescriptor<EAUser>(
                predicate: #Predicate<EAUser> { user in
                    user.id == userId
                }
            )
            
            guard let user = try modelContext.fetch(userDescriptor).first,
                  let _ = user.socialProfile else {
                return []
            }
            
            // ✅ 修复：通过Repository查询关注列表，因为following关系已移至Repository模式
            let descriptor = FetchDescriptor<EACommunityFollow>(
                predicate: #Predicate<EACommunityFollow> { follow in
                    follow.followerProfile?.user?.id == userId && follow.isActive
                },
                sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
            )
            
            let following = try modelContext.fetch(descriptor)
            return Array(following.prefix(limit))
            
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    /// ✅ 性能优化：用户粉丝列表查询
    func fetchUserFollowers(userId: UUID, limit: Int = 10) async throws -> [EACommunityFollow] {
        do {
            // ✅ 优化：通过用户关系直接访问
            let userDescriptor = FetchDescriptor<EAUser>(
                predicate: #Predicate<EAUser> { user in
                    user.id == userId
                }
            )
            
            guard let user = try modelContext.fetch(userDescriptor).first,
                  let socialProfile = user.socialProfile else {
                return []
            }
            
            // 通过关系获取粉丝列表
            let followers = socialProfile.followers.filter { $0.isActive }
            let sortedFollowers = followers.sorted { $0.creationDate > $1.creationDate }
            
            return Array(sortedFollowers.prefix(limit))
            
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func isUserFollowing(followerId: UUID, followeeId: UUID) async throws -> Bool {
        do {
            // 🔑 修复：简化查询，在内存中过滤
            let descriptor = FetchDescriptor<EACommunityFollow>(
                predicate: #Predicate<EACommunityFollow> { follow in
                    follow.isActive
                }
            )
            
            let allFollows = try modelContext.fetch(descriptor)
            
            // 在内存中查找匹配的关注关系
            let matchingFollow = allFollows.first { follow in
                follow.followerProfile?.user?.id == followerId &&
                follow.followeeProfile?.user?.id == followeeId
            }
            
            return matchingFollow != nil
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func fetchMutualFollows(userId: UUID) async throws -> [EACommunityFollow] {
        do {
            // 🔑 修复：简化查询，在内存中处理
            let descriptor = FetchDescriptor<EACommunityFollow>(
                predicate: #Predicate<EACommunityFollow> { follow in
                    follow.isActive
                }
            )
            
            let allFollows = try modelContext.fetch(descriptor)
            
            // 在内存中找出用户的关注和粉丝
            let userFollowing = allFollows.filter { follow in
                follow.followerProfile?.user?.id == userId
            }
            
            let userFollowers = allFollows.filter { follow in
                follow.followeeProfile?.user?.id == userId
            }
            
            // 找出互相关注的用户
            let followingUserIds = Set(userFollowing.compactMap { $0.followeeProfile?.user?.id })
            let mutualFollows = userFollowers.filter { follow in
                guard let followerUserId = follow.followerProfile?.user?.id else { return false }
                return followingUserIds.contains(followerUserId)
            }
            
            return mutualFollows
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func createPost(_ post: EACommunityPost, authorId: UUID) async throws -> EACommunityPost {
        do {
            // 获取当前用户
            let currentUser = try getUser(by: authorId)
            
            // 插入帖子到Context
            modelContext.insert(post)
            
            // 🔑 修复：使用新的关系模式，通过社交档案建立关系
            // 由于EACommunityPost.authorSocialProfile有正确的inverse配置
            post.authorSocialProfile = currentUser.socialProfile
            
            // 保存Context
            try saveContext()
            
            return post
        } catch {
            if error is EACommunityRepositoryError {
                throw error
            } else {
                throw EACommunityRepositoryError.dataCreateFailed
            }
        }
    }
    
    func updatePost(_ post: EACommunityPost) async throws -> EACommunityPost {
        do {
            // 验证帖子存在
            guard let _ = try await fetchPost(by: post.id) else {
                throw EACommunityRepositoryError.postNotFound
            }
            
            // 更新时间戳
            post.lastEditDate = Date()
            
            // 保存Context
            try saveContext()
            
            return post
        } catch {
            if error is EACommunityRepositoryError {
                throw error
            } else {
                throw EACommunityRepositoryError.dataSaveFailed
            }
        }
    }
    
    func deletePost(id: UUID) async throws {
        guard let post = try await fetchPost(by: id) else {
            throw EACommunityRepositoryError.postNotFound
        }
        
        do {
            modelContext.delete(post)
            try saveContext()
        } catch {
            throw EACommunityRepositoryError.dataSaveFailed
        }
    }
    
    // MARK: - Comment Management
    
    func fetchComments(for postId: UUID) async throws -> [EACommunityComment] {
        let descriptor = FetchDescriptor<EACommunityComment>(
            predicate: #Predicate<EACommunityComment> { comment in
                comment.post?.id == postId
            },
            sortBy: [SortDescriptor(\.creationDate, order: .forward)]
        )
        
        do {
            return try modelContext.fetch(descriptor)
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func createComment(_ comment: EACommunityComment, for postId: UUID, authorId: UUID) async throws -> EACommunityComment {
        do {
            // 获取当前用户和目标帖子
            let currentUser = try getUser(by: authorId)
            guard let targetPost = try await fetchPost(by: postId) else {
                throw EACommunityRepositoryError.postNotFound
            }
            
            // 插入评论到Context
            modelContext.insert(comment)
            
            // 🔑 修复：只设置必要的关系，避免重复设置
            comment.author = currentUser
            comment.post = targetPost
            
            // 🔑 关键修复：同步更新帖子的commentCount字段
            targetPost.syncCommentCount()
            
            // 保存Context
            try saveContext()
            
            return comment
        } catch {
            if error is EACommunityRepositoryError {
                throw error
            } else {
                throw EACommunityRepositoryError.dataCreateFailed
            }
        }
    }
    
    func createComment(content: String, authorId: UUID, postId: UUID, parentCommentId: UUID?) async throws -> EACommunityComment {
        do {
            // 获取作者和目标帖子
            let author = try getUser(by: authorId)
            
            guard let targetPost = try await fetchPost(by: postId) else {
                throw EACommunityRepositoryError.postNotFound
            }
            
            // 创建评论
            let newComment = EACommunityComment(content: content)
            modelContext.insert(newComment)
            
            // 建立关系
            newComment.author = author
            newComment.post = targetPost
            
            // 如果有父评论，建立父子关系
            if let parentId = parentCommentId {
                let parentDescriptor = FetchDescriptor<EACommunityComment>(
                    predicate: #Predicate<EACommunityComment> { comment in comment.id == parentId }
                )
                if let parentComment = try modelContext.fetch(parentDescriptor).first {
                    newComment.parentComment = parentComment
                }
            }
            
            // 🔑 关键修复：同步更新帖子的commentCount字段
            targetPost.syncCommentCount()
            
            try saveContext()
            return newComment
        } catch {
            if error is EACommunityRepositoryError {
                throw error
            } else {
                throw EACommunityRepositoryError.dataCreateFailed
            }
        }
    }
    
    func deleteComment(id: UUID) async throws {
        let descriptor = FetchDescriptor<EACommunityComment>(
            predicate: #Predicate<EACommunityComment> { comment in
                comment.id == id
            }
        )
        
        do {
            guard let comment = try modelContext.fetch(descriptor).first else {
                throw EACommunityRepositoryError.commentNotFound
            }
            
            // 🔑 关键修复：删除评论前获取关联的帖子，用于同步commentCount
            let targetPost = comment.post
            
            modelContext.delete(comment)
            
            // 🔑 关键修复：同步更新帖子的commentCount字段
            if let post = targetPost {
                post.syncCommentCount()
            }
            
            try saveContext()
        } catch {
            if error is EACommunityRepositoryError {
                throw error
            } else {
                throw EACommunityRepositoryError.dataSaveFailed
            }
        }
    }
    
    // MARK: - Like Management
    
    func toggleLike(postId: UUID, userId: UUID) async throws -> Int {
        do {
            // 首先获取目标帖子，确保存在
            guard let targetPost = try await fetchPost(by: postId) else {
                throw EACommunityRepositoryError.postNotFound
            }

            // ✅ 修复：通过关系获取用户，确保Context一致性
            let userDescriptor = FetchDescriptor<EAUser>(
                predicate: #Predicate<EAUser> { user in user.id == userId }
            )
            
            guard let currentUser = try modelContext.fetch(userDescriptor).first else {
                throw EACommunityRepositoryError.userNotFound
            }

            // 查找现有的点赞记录（包括已软删除的）
            let likesDescriptor = FetchDescriptor<EACommunityLike>(
                predicate: #Predicate<EACommunityLike> { like in
                    like.targetType == "post"
                }
            )

            let allLikes = try modelContext.fetch(likesDescriptor)

            // 在内存中查找该用户对该帖子的点赞记录
            let existingLike = allLikes.first { like in
                like.user?.id == userId && like.targetPost?.id == postId
            }

            if let like = existingLike {
                // 切换点赞状态
                like.isActive.toggle()
                
                // 如果重新激活，更新创建时间
                if like.isActive {
                    like.creationDate = Date()
                }
            } else {
                // 没有记录，创建新的点赞
                let newLike = EACommunityLike(
                    targetType: "post"
                )
                
                // ✅ 修复：确保Context一致性 - 先插入再建立关系
                modelContext.insert(newLike)
                
                // 建立关系（iOS 18+要求：插入后再赋值关系）
                newLike.user = currentUser
                newLike.targetPost = targetPost
                newLike.isActive = true
            }

            // 同步更新帖子的点赞数量
            targetPost.syncLikeCount()
            
            // 保存更改
            try modelContext.save()
            
            return targetPost.likeCount
        } catch {
            if error is EACommunityRepositoryError {
                throw error
            } else {
                throw EACommunityRepositoryError.dataSaveFailed
            }
        }
    }
    
    func fetchLikes(for postId: UUID) async throws -> [EACommunityLike] {
        // 🔑 修复：简化查询，在内存中过滤
        let descriptor = FetchDescriptor<EACommunityLike>(
            predicate: #Predicate<EACommunityLike> { like in
                like.targetType == "post"
            },
            sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
        )
        
        do {
            let allLikes = try modelContext.fetch(descriptor)
            
            // 在内存中过滤特定帖子的点赞
            let postLikes = allLikes.filter { like in
                like.targetPost?.id == postId
            }
            
            return postLikes
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func findExistingLike(commentId: UUID, userId: UUID) async throws -> EACommunityLike? {
        // 🔑 修复：简化查询，在内存中过滤
        let descriptor = FetchDescriptor<EACommunityLike>(
            predicate: #Predicate<EACommunityLike> { like in
                like.targetType == "comment"
            }
        )
        
        do {
            let allLikes = try modelContext.fetch(descriptor)
            
            // 在内存中查找匹配的点赞
            let matchingLike = allLikes.first { like in
                like.targetComment?.id == commentId && like.user?.id == userId
            }
            
            return matchingLike
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func deleteLike(_ likeId: UUID) async throws {
        let descriptor = FetchDescriptor<EACommunityLike>(
            predicate: #Predicate<EACommunityLike> { like in like.id == likeId }
        )
        
        do {
            guard let like = try modelContext.fetch(descriptor).first else {
                throw EACommunityRepositoryError.likeNotFound
            }
            
            modelContext.delete(like)
            try saveContext()
        } catch {
            if error is EACommunityRepositoryError {
                throw error
            } else {
                throw EACommunityRepositoryError.dataSaveFailed
            }
        }
    }
    
    func createLike(targetType: String, targetCommentId: UUID?, userId: UUID, userEnergyLevel: Int) async throws -> EACommunityLike {
        do {
            // 获取用户
            let user = try getUser(by: userId)
            
            // 创建点赞
            let newLike = EACommunityLike(targetType: targetType, userEnergyLevel: userEnergyLevel)
            modelContext.insert(newLike)
            
            // 建立关系
            newLike.user = user
            
            // 如果是评论点赞，建立与评论的关系
            if let commentId = targetCommentId {
                let commentDescriptor = FetchDescriptor<EACommunityComment>(
                    predicate: #Predicate<EACommunityComment> { comment in comment.id == commentId }
                )
                if let comment = try modelContext.fetch(commentDescriptor).first {
                    newLike.targetComment = comment
                }
            }
            
            try saveContext()
            return newLike
        } catch {
            if error is EACommunityRepositoryError {
                throw error
            } else {
                throw EACommunityRepositoryError.dataCreateFailed
            }
        }
    }
    
    // MARK: - User Social Profile Management
    
    /// 更新用户社交档案
    /// - Parameter socialProfile: 要更新的社交档案
    /// - Returns: 更新后的社交档案
    func updateUserSocialProfile(_ socialProfile: EAUserSocialProfile) async throws -> EAUserSocialProfile {
        do {
            // 更新时间戳
            socialProfile.lastEnergyUpdateDate = Date()
            
            // 保存更改
            try saveContext()
            
            return socialProfile
        } catch {
            if error is EACommunityRepositoryError {
                throw error
            } else {
                throw EACommunityRepositoryError.dataSaveFailed
            }
        }
    }
    
    /// 🔑 关键修复：获取用户社交档案（增强版数据完整性验证）
    /// - Parameter userId: 用户ID
    /// - Returns: 用户社交档案（确保包含完整的双向关系）
    func fetchUserSocialProfile(by userId: UUID) async throws -> EAUserSocialProfile? {
        do {
            #if DEBUG
            print("🔍 [CommunityRepository] fetchUserSocialProfile开始 - userId: \(userId)")
            #endif

            // 🔑 第一步：安全获取用户对象
            let user = try getUser(by: userId)

            #if DEBUG
            print("✅ [CommunityRepository] 获取到用户 - 用户名: \(user.username)")
            #endif

            // 🔑 第二步：验证社交档案存在性
            guard let socialProfile = user.socialProfile else {
                #if DEBUG
                print("❌ [CommunityRepository] 用户无社交档案 - 用户名: \(user.username)")
                #endif
                return nil
            }

            // 🔑 第三步：验证双向关系完整性
            if let profileUser = socialProfile.user,
               profileUser.id == user.id {
                // 关系正常，继续验证
            } else {
                #if DEBUG
                print("❌ [CommunityRepository] 社交档案关系不一致")
                print("   - socialProfile.user: \(socialProfile.user?.username ?? "nil")")
                print("   - 期望用户ID: \(user.id)")
                print("   - 实际用户ID: \(socialProfile.user?.id ?? UUID())")
                #endif

                // 🚨 关键修复：尝试修复断裂的关系
                socialProfile.user = user
                try saveContext()

                #if DEBUG
                print("🔧 [CommunityRepository] 已修复社交档案关系")
                #endif
            }

            // 🔑 第四步：验证数据完整性
            guard !socialProfile.id.uuidString.isEmpty,
                  socialProfile.id != UUID(uuidString: "00000000-0000-0000-0000-000000000000") else {
                #if DEBUG
                print("❌ [CommunityRepository] 社交档案ID无效")
                #endif
                throw EACommunityRepositoryError.dataFetchFailed
            }

            // 🔑 第五步：确保数字宇宙数据完整性
            if socialProfile.stellarLevel == nil || socialProfile.totalStellarEnergy == nil {
                #if DEBUG
                print("🔧 [CommunityRepository] 初始化数字宇宙数据 - 用户名: \(user.username)")
                #endif
                socialProfile.initializeDigitalUniverseData()
                try saveContext()
            }

            #if DEBUG
            print("✅ [CommunityRepository] 社交档案验证完成 - 用户名: \(user.username), 档案ID: \(socialProfile.id)")
            #endif

            return socialProfile

        } catch let error as EACommunityRepositoryError {
            // 重新抛出已知错误
            throw error
        } catch {
            #if DEBUG
            print("❌ [CommunityRepository] fetchUserSocialProfile异常 - \(error.localizedDescription)")
            #endif
            throw EACommunityRepositoryError.userNotFound
        }
    }
    
    /// 🔑 废弃方法：防止重复社交档案创建
    /// ⚠️ 此方法已废弃，请使用 UserRepository.findOrCreateSocialProfile(for:) 替代
    ///
    /// 迁移指南：
    /// ```swift
    /// // 旧用法（已废弃）
    /// let profile = try await communityRepository.createUserSocialProfile(for: userId)
    ///
    /// // 新用法（推荐）
    /// let user = try await userRepository.fetchUser(id: userId)
    /// let profile = try await userRepository.findOrCreateSocialProfile(for: user)
    /// ```
    @available(*, deprecated, message: "使用 UserRepository.findOrCreateSocialProfile(for:) 替代，避免重复档案创建")
    func createUserSocialProfile(for userId: UUID) async throws -> EAUserSocialProfile {
        throw EACommunityRepositoryError.dataCreateFailed
    }
    
    /// ✅ 新增：获取用户信息
    /// - Parameter userId: 用户ID
    /// - Returns: 用户对象
    func fetchUser(by userId: UUID) async throws -> EAUser? {
        do {
            let user = try getUser(by: userId)
            return user
        } catch {
            throw EACommunityRepositoryError.userNotFound
        }
    }
    
    // MARK: - User Relationship Management
    
    func followUser(followerId: UUID, followingId: UUID) async throws {
        do {
            // 防止自己关注自己
            guard followerId != followingId else {
                throw EACommunityRepositoryError.invalidParameters
            }
            
            // 🔑 修复：简化查询，在内存中处理
            let usersDescriptor = FetchDescriptor<EAUser>(
                predicate: #Predicate<EAUser> { user in
                    user.id == followerId || user.id == followingId
                }
            )
            
            let users = try modelContext.fetch(usersDescriptor)
            guard let followerUser = users.first(where: { $0.id == followerId }),
                  let followingUser = users.first(where: { $0.id == followingId }) else {
                throw EACommunityRepositoryError.userNotFound
            }
            
            // 确保用户有社交档案，如果没有则创建
            if followerUser.socialProfile == nil {
                let socialProfile = EAUserSocialProfile()
                socialProfile.user = followerUser
                modelContext.insert(socialProfile)
                followerUser.socialProfile = socialProfile
            }
            
            if followingUser.socialProfile == nil {
                let socialProfile = EAUserSocialProfile()
                socialProfile.user = followingUser
                modelContext.insert(socialProfile)
                followingUser.socialProfile = socialProfile
            }
            
            // 检查是否已经关注（在内存中检查）
            let followsDescriptor = FetchDescriptor<EACommunityFollow>()
            let allFollows = try modelContext.fetch(followsDescriptor)
            
            let existingFollow = allFollows.first { follow in
                follow.followerProfile?.user?.id == followerId &&
                follow.followeeProfile?.user?.id == followingId
            }
            
            guard existingFollow == nil else {
                // 已经关注了，无需重复操作
                return
            }
            
            // 创建关注关系
            let newFollow = EACommunityFollow()
            
            // 插入到Context
            modelContext.insert(newFollow)
            
            // 建立关系（通过社交档案）
            newFollow.followerProfile = followerUser.socialProfile
            newFollow.followeeProfile = followingUser.socialProfile
            
            try saveContext()
        } catch {
            if error is EACommunityRepositoryError {
                throw error
            } else {
                throw EACommunityRepositoryError.dataSaveFailed
            }
        }
    }
    
    func unfollowUser(followerId: UUID, followingId: UUID) async throws {
        do {
            // 🔑 修复：简化查询，在内存中处理
            let followsDescriptor = FetchDescriptor<EACommunityFollow>()
            let allFollows = try modelContext.fetch(followsDescriptor)
            
            // 在内存中查找要取消的关注关系
            let followToRemove = allFollows.first { follow in
                follow.followerProfile?.user?.id == followerId &&
                follow.followeeProfile?.user?.id == followingId
            }
            
            guard let follow = followToRemove else {
                // 没有关注关系，无需取消
                return
            }
            
            modelContext.delete(follow)
            try saveContext()
        } catch {
            throw EACommunityRepositoryError.dataSaveFailed
        }
    }
    
    func isFollowing(followerId: UUID, followingId: UUID) async throws -> Bool {
        do {
            // 🔑 修复：简化查询，在内存中处理
            let followsDescriptor = FetchDescriptor<EACommunityFollow>()
            let allFollows = try modelContext.fetch(followsDescriptor)
            
            // 在内存中查找匹配的关注关系
            let matchingFollow = allFollows.first { follow in
                follow.followerProfile?.user?.id == followerId &&
                follow.followeeProfile?.user?.id == followingId
            }
            
            return matchingFollow != nil
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    // MARK: - Search Functionality
    
    func searchPosts(query: String, limit: Int = 20) async throws -> [EACommunityPost] {
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedQuery.isEmpty else { return [] }
        
        // 🔑 修复：简化Predicate，避免编译器超时
        let descriptor = FetchDescriptor<EACommunityPost>(
            sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
        )
        
        do {
            let allPosts = try modelContext.fetch(descriptor)
            // 在内存中进行搜索，避免复杂的Predicate
            let filteredPosts = allPosts.filter { post in
                post.content.localizedCaseInsensitiveContains(trimmedQuery)
            }
            return Array(filteredPosts.prefix(limit))
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func searchUsers(query: String, limit: Int = 20) async throws -> [EAUser] {
        return try await searchUsers(query: query, limit: limit, offset: 0)
    }

    func searchUsers(query: String, limit: Int = 20, offset: Int = 0) async throws -> [EAUser] {
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedQuery.isEmpty else { return [] }

        // 🔑 修复：简化Predicate，避免编译器超时
        let descriptor = FetchDescriptor<EAUser>(
            sortBy: [SortDescriptor(\.username, order: .forward)]
        )

        do {
            let allUsers = try modelContext.fetch(descriptor)
            // 在内存中进行搜索，避免复杂的Predicate
            let filteredUsers = allUsers.filter { user in
                // 支持用户名和邮箱搜索
                user.username.localizedCaseInsensitiveContains(trimmedQuery) ||
                (user.email?.localizedCaseInsensitiveContains(trimmedQuery) ?? false)
            }

            // 应用分页
            let startIndex = offset
            let endIndex = min(startIndex + limit, filteredUsers.count)

            guard startIndex < filteredUsers.count else { return [] }

            return Array(filteredUsers[startIndex..<endIndex])
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }

    // MARK: - 数据同步方法

    /// ✅ 新增：同步所有帖子的计数字段与实际关系数组
    func syncAllPostCounts() async throws {
        do {
            let descriptor = FetchDescriptor<EACommunityPost>()
            let allPosts = try modelContext.fetch(descriptor)

            for post in allPosts {
                post.syncLikeCount()
                post.syncCommentCount()
            }

            try saveContext()
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }

    // 🔑 新增：分类和标签筛选功能（完全简化版）
    func fetchPostsByCategory(category: String, limit: Int, offset: Int) async throws -> [EACommunityPost] {
        // 🔑 性能优化：使用数据库级别分页和过滤
        var descriptor = FetchDescriptor<EACommunityPost>(
            predicate: #Predicate<EACommunityPost> { post in
                post.category == category && post.isVisible == true
            },
            sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
        )
        descriptor.fetchLimit = limit
        descriptor.fetchOffset = offset
        
        do {
            return try modelContext.fetch(descriptor)
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func fetchPostsByTags(tags: [String], limit: Int, offset: Int) async throws -> [EACommunityPost] {
        // 🔑 性能优化：使用数据库级别分页，但标签过滤仍需在内存中进行
        var descriptor = FetchDescriptor<EACommunityPost>(
            predicate: #Predicate<EACommunityPost> { $0.isVisible == true },
            sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
        )
        // 预加载更多数据以补偿标签过滤的损失
        descriptor.fetchLimit = limit * 3
        descriptor.fetchOffset = offset
        
        do {
            let allPosts = try modelContext.fetch(descriptor)
            
            // 在内存中进行标签匹配
            let filteredPosts = allPosts.filter { post in
                for tag in tags {
                    if post.tags.contains(tag) {
                        return true
                    }
                }
                return false
            }
            
            // 应用最终分页限制
            return Array(filteredPosts.prefix(limit))
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func fetchPostsWithFilters(category: String?, tags: [String]?, limit: Int, offset: Int) async throws -> [EACommunityPost] {
        // 🔑 性能优化：组合数据库级别和内存级别过滤
        var predicate: Predicate<EACommunityPost>
        
        if let category = category {
            predicate = #Predicate<EACommunityPost> { post in
                post.category == category && post.isVisible == true
            }
        } else {
            predicate = #Predicate<EACommunityPost> { post in
                post.isVisible == true
            }
        }
        
        var descriptor = FetchDescriptor<EACommunityPost>(
            predicate: predicate,
            sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
        )
        
        // 如果有标签过滤，预加载更多数据
        let multiplier = (tags?.isEmpty == false) ? 3 : 1
        descriptor.fetchLimit = limit * multiplier
        descriptor.fetchOffset = offset
        
        do {
            let allPosts = try modelContext.fetch(descriptor)
            
            // 如果有标签过滤，在内存中进行
            var filteredPosts = allPosts
            if let tags = tags, !tags.isEmpty {
                filteredPosts = allPosts.filter { post in
                    for tag in tags {
                        if post.tags.contains(tag) {
                            return true
                        }
                    }
                    return false
                }
            }
            
            // 应用最终分页限制
            return Array(filteredPosts.prefix(limit))
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func fetchAvailableCategories() async throws -> [String] {
        let descriptor = FetchDescriptor<EACommunityPost>()
        let allPosts = try modelContext.fetch(descriptor)
        let categories = Set(allPosts.map { $0.category })
        return Array(categories).sorted()
    }
    
    func fetchPopularTags(limit: Int) async throws -> [String] {
        let descriptor = FetchDescriptor<EACommunityPost>()
        let allPosts = try modelContext.fetch(descriptor)
        
        // 简化的标签统计逻辑
        var tagCounts: [String: Int] = [:]
        for post in allPosts {
            for tag in post.tags {
                tagCounts[tag, default: 0] += 1
            }
        }
        
        let sortedTags = tagCounts.sorted { $0.value > $1.value }
        return Array(sortedTags.prefix(limit).map { $0.key })
    }

}