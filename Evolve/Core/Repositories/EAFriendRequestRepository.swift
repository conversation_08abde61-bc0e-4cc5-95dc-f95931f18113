import Foundation
import SwiftData

// MARK: - Repository协议定义

/// 好友请求Repository协议
/// 遵循项目Repository模式，使用@ModelActor确保线程安全
protocol EAFriendRequestRepositoryProtocol {
    /// 🔑 修复：采用"传ID，勿传对象"模式，避免跨Context关系建立崩溃
    func createFriendRequest(senderProfileId: UUID, receiverProfileId: UUID, message: String?) async throws -> EAFriendRequest
    func fetchReceivedRequests(userProfileId: UUID) async throws -> [EAFriendRequest]
    func fetchSentRequests(userProfileId: UUID) async throws -> [EAFriendRequest]
    func fetchFriendRequest(by requestId: UUID) async throws -> EAFriendRequest?
    func acceptFriendRequest(requestId: UUID) async throws -> EAFriendRequest

    /// 🔑 新增：原子性方法 - 接受好友申请并创建好友关系
    /// 在单一事务中完成：1) 更新请求状态为.accepted 2) 创建EAFriendship实例 3) 统一保存
    func acceptFriendRequestAndCreateFriendship(requestId: UUID) async throws -> (EAFriendRequest, EAFriendship)

    func rejectFriendRequest(requestId: UUID, reason: String?) async throws -> EAFriendRequest
    func cancelFriendRequest(requestId: UUID) async throws -> EAFriendRequest
    func cleanupExpiredRequests() async throws
}

// MARK: - Repository实现

/// 好友请求Repository实现
/// 使用@ModelActor确保线程安全，遵循项目开发规范
@ModelActor
actor EAFriendRequestRepositoryImpl: EAFriendRequestRepositoryProtocol {
    
    /// 🔑 修复：创建好友请求 - 采用"传ID，勿传对象"模式确保Context安全
    func createFriendRequest(senderProfileId: UUID, receiverProfileId: UUID, message: String?) async throws -> EAFriendRequest {
        #if DEBUG
        // 调试环境下记录好友请求创建开始，但不使用print
        #endif

        // 🔑 第一层防御：检查社交档案ID是否相同
        if senderProfileId == receiverProfileId {
            #if DEBUG
            // 调试环境下记录社交档案ID相同错误，但不使用print
            #endif
            throw FriendRequestError.cannotRequestSelf
        }

        #if DEBUG
        // 调试环境下记录安全用户档案获取开始，但不使用print
        #endif

        // 🔑 关键修复：在Repository自己的Context中根据ID获取对象，确保Context一致性
        let safeSenderProfile = try await getSafeUserProfile(by: senderProfileId)
        let safeReceiverProfile = try await getSafeUserProfile(by: receiverProfileId)

        #if DEBUG
        print("✅ [FriendRequestRepo] 安全用户档案获取成功")
        #endif

        // 🔑 第二层防御：检查用户ID是否相同（防止同一用户的不同档案发送请求）
        guard let senderUserId = safeSenderProfile.user?.id,
              let receiverUserId = safeReceiverProfile.user?.id else {
            #if DEBUG
            print("❌ [FriendRequestRepo] 用户ID获取失败")
            print("   - 发送者档案用户ID: \(safeSenderProfile.user?.id.uuidString ?? "nil")")
            print("   - 接收者档案用户ID: \(safeReceiverProfile.user?.id.uuidString ?? "nil")")
            #endif
            throw FriendRequestError.invalidProfiles
        }

        #if DEBUG
        print("🔍 [FriendRequestRepo] 用户ID验证")
        print("   - 发送者用户ID: \(senderUserId)")
        print("   - 接收者用户ID: \(receiverUserId)")
        #endif

        if senderUserId == receiverUserId {
            #if DEBUG
            print("❌ [FriendRequestRepo] 防御性检查失败：发送者和接收者是同一用户")
            print("   - 发送者用户ID: \(senderUserId)")
            print("   - 接收者用户ID: \(receiverUserId)")
            print("   - 发送者档案ID: \(senderProfileId)")
            print("   - 接收者档案ID: \(receiverProfileId)")
            #endif
            throw FriendRequestError.cannotRequestSelf
        }

        #if DEBUG
        print("✅ [FriendRequestRepo] 用户ID验证通过，开始检查现有请求")
        #endif

        // 检查是否已存在待处理的请求
        let existingRequest = try await checkExistingPendingRequest(
            senderId: senderProfileId,
            receiverId: receiverProfileId
        )

        if existingRequest != nil {
            #if DEBUG
            print("❌ [FriendRequestRepo] 已存在待处理请求")
            #endif
            throw FriendRequestError.requestAlreadyExists
        }

        #if DEBUG
        print("✅ [FriendRequestRepo] 无现有请求，开始创建新请求")
        #endif

        // ✅ 修复：使用正确的初始化方法，遵循SwiftData最佳实践
        let request = EAFriendRequest(requestMessage: message)

        #if DEBUG
        print("🔍 [FriendRequestRepo] 好友请求对象创建成功，ID: \(request.id)")
        #endif

        // 🕵️‍♂️ [FORENSIC LOG] --- 在数据入库前捕获证据 ---
        #if DEBUG
        // 调试环境下记录好友请求插入前的状态检查，但不使用print
        #endif

        // ✅ 修复：遵循安全赋值顺序（插入→赋值→保存）
        modelContext.insert(request)

        #if DEBUG
        print("🔍 [FriendRequestRepo] 请求已插入Context，开始建立关系")
        #endif

        // 🔑 修复：所有对象都在同一Context中，安全建立关系
        request.senderProfile = safeSenderProfile
        request.receiverProfile = safeReceiverProfile

        // 🕵️‍♂️ [FORENSIC LOG] --- 关系建立后的状态检查 ---
        #if DEBUG
        // 调试环境下记录关系建立后的状态检查，但不使用print
        #endif

        #if DEBUG
        print("🔍 [FriendRequestRepo] 关系建立完成，开始保存到数据库")
        print("   - 请求ID: \(request.id)")
        print("   - 发送者: \(request.senderProfile?.user?.username ?? "未知")")
        print("   - 接收者: \(request.receiverProfile?.user?.username ?? "未知")")
        #endif

        // 保存到数据库
        try modelContext.save()

        #if DEBUG
        print("✅ [FriendRequestRepo] 好友请求创建并保存成功")
        print("   - 请求ID: \(request.id)")
        print("   - 状态: \(request.status)")
        #endif

        return request
    }
    
    /// 🔑 ID规范修复：获取用户收到的好友请求（避免三元运算符崩溃）
    /// 🚨 重要：此方法接收用户ID，不是社交档案ID
    /// 为保持API兼容性，参数名保持不变，但添加明确注释
    func fetchReceivedRequests(userProfileId: UUID) async throws -> [EAFriendRequest] {
        #if DEBUG
        print("🔍 [FriendRequestRepo] fetchReceivedRequests开始 - 用户ID查询")
        print("   - 传入的用户ID: \(userProfileId)")
        #endif

        // 🔑 ID规范说明：虽然参数名为userProfileId，但实际接收的是用户ID
        // 这是为了保持现有API的兼容性，避免破坏性更改
        let targetUserId = userProfileId

        #if DEBUG
        print("🔍 [FriendRequestRepo] 处理用户ID: \(targetUserId)")
        #endif

        // 🚨 致命修复：避免在#Predicate中使用可选链，改为分步查询
        // 原因：request.receiverProfile?.user?.id 会生成TERNARY表达式导致崩溃

        // 第一步：获取所有好友请求
        let allRequestsDescriptor = FetchDescriptor<EAFriendRequest>(
            sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
        )
        let allRequests = try modelContext.fetch(allRequestsDescriptor)

        // 第二步：在内存中进行安全过滤（避免#Predicate的三元运算符问题）
        let filteredRequests = allRequests.filter { request in
            // 安全检查：确保接收者是目标用户
            guard let receiverProfile = request.receiverProfile,
                  let receiverUser = receiverProfile.user,
                  receiverUser.id == targetUserId else {
                return false
            }

            // 状态和过期检查
            return request.status == .pending && !request.isExpired()
        }

        #if DEBUG
        print("✅ [FriendRequestRepo] 用户ID查询完成")
        print("   - 数据库总请求数: \(allRequests.count)")
        print("   - 目标用户ID: \(targetUserId)")
        print("   - 匹配的待处理请求数: \(filteredRequests.count)")

        // 🔍 关键调试：显示所有请求的详细信息
        print("🔍 [FriendRequestRepo] 数据库中所有请求的详细信息：")
        for (index, request) in allRequests.enumerated() {
            let receiverUserId = request.receiverProfile?.user?.id.uuidString ?? "nil"
            let senderUserId = request.senderProfile?.user?.id.uuidString ?? "nil"
            let receiverUsername = request.receiverProfile?.user?.username ?? "未知"
            let senderUsername = request.senderProfile?.user?.username ?? "未知"
            let senderProfileId = request.senderProfile?.id.uuidString ?? "nil"
            let receiverProfileId = request.receiverProfile?.id.uuidString ?? "nil"

            print("   - 请求\(index + 1):")
            print("     * ID: \(request.id)")
            print("     * 状态: \(request.status)")
            print("     * 发送者: \(senderUsername) (用户ID: \(senderUserId), 档案ID: \(senderProfileId))")
            print("     * 接收者: \(receiverUsername) (用户ID: \(receiverUserId), 档案ID: \(receiverProfileId))")
            print("     * 是否匹配目标用户ID: \(receiverUserId == targetUserId.uuidString)")
            print("     * 是否pending: \(request.status == .pending)")
            print("     * 是否过期: \(request.isExpired())")
        }

        for (index, request) in filteredRequests.enumerated() {
            print("   - 匹配请求\(index + 1): 来自 \(request.senderProfile?.user?.username ?? "未知")")
        }
        #endif

        return filteredRequests
    }
    
    /// 获取用户发送的好友请求
    func fetchSentRequests(userProfileId: UUID) async throws -> [EAFriendRequest] {
        // 🔑 第三阶段优化：使用高效的数据库级别Predicate查询
        let userProfileID = userProfileId // 提取为常量以在Predicate中使用

        // 🔑 性能优化：使用Predicate在数据库层面过滤
        let predicate = #Predicate<EAFriendRequest> { request in
            request.senderProfile?.id == userProfileID
        }

        let descriptor = FetchDescriptor<EAFriendRequest>(
            predicate: predicate,
            sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
        )

        return try modelContext.fetch(descriptor)
    }
    
    /// 根据ID获取好友请求
    func fetchFriendRequest(by requestId: UUID) async throws -> EAFriendRequest? {
        // 🔑 第三阶段优化：使用高效的Predicate查询
        let requestID = requestId // 提取为常量以在Predicate中使用
        let descriptor = FetchDescriptor<EAFriendRequest>(
            predicate: #Predicate { $0.id == requestID }
        )

        let requests = try modelContext.fetch(descriptor)
        return requests.first
    }
    
    /// 接受好友请求
    func acceptFriendRequest(requestId: UUID) async throws -> EAFriendRequest {
        guard let request = try await fetchFriendRequest(by: requestId) else {
            throw FriendRequestError.requestNotFound
        }

        // 检查请求是否可以被处理
        guard request.canBeProcessed() else {
            throw FriendRequestError.requestCannotBeProcessed
        }

        // 🔑 关键修复：验证请求的发送者和接收者档案存在
        guard let _ = request.senderProfile,
              let _ = request.receiverProfile else {
            throw FriendRequestError.invalidRequestProfiles
        }

        // 更新请求状态
        request.accept()

        // 🔑 关键修复：同步保存，确保事务一致性和时序正确性
        // 移除异步保存模式，避免与后续好友关系创建的时序冲突
        try modelContext.save()

        // 好友请求接受成功

        return request
    }

    /// 🔑 新增：原子性方法 - 接受好友申请并创建好友关系
    /// 在单一事务中完成完整的好友添加流程，确保数据一致性
    func acceptFriendRequestAndCreateFriendship(requestId: UUID) async throws -> (EAFriendRequest, EAFriendship) {
        // 第一步：获取并验证好友请求
        guard let request = try await fetchFriendRequest(by: requestId) else {
            throw FriendRequestError.requestNotFound
        }

        // 检查请求是否可以被处理
        guard request.canBeProcessed() else {
            throw FriendRequestError.requestCannotBeProcessed
        }

        // 🔑 关键验证：确保请求的发送者和接收者档案存在
        guard let senderProfile = request.senderProfile,
              let receiverProfile = request.receiverProfile else {
            throw FriendRequestError.invalidRequestProfiles
        }

        // 第二步：更新请求状态为已接受
        request.accept()

        // 第三步：创建好友关系
        // 🔑 关键：在当前Context中重新获取用户档案，确保Context一致性
        let safeSenderProfile = try await getSafeUserProfile(by: senderProfile.id)
        let safeReceiverProfile = try await getSafeUserProfile(by: receiverProfile.id)

        // 检查是否已存在好友关系（防止重复创建）
        let existingFriendship = try await checkExistingFriendship(
            userProfile1Id: safeSenderProfile.id,
            userProfile2Id: safeReceiverProfile.id
        )

        if existingFriendship != nil {
            throw FriendRequestError.friendshipAlreadyExists
        }

        // 创建新的好友关系
        let friendship = EAFriendship()

        // 🔑 关键修复：使用正确的方式建立关系
        // 由于EAFriendship端定义了@Relationship，需要通过正确的方式设置关系
        friendship.setupRelationship(
            initiator: safeSenderProfile,
            friend: safeReceiverProfile
        )

        modelContext.insert(friendship)

        // 🔑 关键：单一保存操作，确保原子性
        // 所有操作（请求状态更新 + 好友关系创建）在同一事务中完成
        try modelContext.save()

        return (request, friendship)
    }

    /// 拒绝好友请求
    func rejectFriendRequest(requestId: UUID, reason: String?) async throws -> EAFriendRequest {
        guard let request = try await fetchFriendRequest(by: requestId) else {
            throw FriendRequestError.requestNotFound
        }
        
        // 检查请求是否可以被处理
        guard request.canBeProcessed() else {
            throw FriendRequestError.requestCannotBeProcessed
        }
        
        // 更新请求状态
        request.reject(reason: reason)
        
        // 保存更改
        try modelContext.save()
        
        return request
    }
    
    /// 取消好友请求
    func cancelFriendRequest(requestId: UUID) async throws -> EAFriendRequest {
        guard let request = try await fetchFriendRequest(by: requestId) else {
            throw FriendRequestError.requestNotFound
        }
        
        // 只有发送者可以取消待处理的请求
        guard request.status == .pending else {
            throw FriendRequestError.requestCannotBeCancelled
        }
        
        // 更新请求状态
        request.cancel()
        
        // 保存更改
        try modelContext.save()
        
        return request
    }
    
    /// 清理过期的请求
    func cleanupExpiredRequests() async throws {
        let descriptor = FetchDescriptor<EAFriendRequest>()

        let allRequests = try modelContext.fetch(descriptor)

        // 在内存中过滤待处理的请求
        let pendingRequests = allRequests.filter { request in
            request.status == .pending
        }

        for request in pendingRequests {
            if request.isExpired() {
                request.markAsExpired()
            }
        }

        try modelContext.save()
    }
    
    // MARK: - 私有辅助方法

    /// 在当前Context中安全获取用户社交档案
    private func getSafeUserProfile(by profileId: UUID) async throws -> EAUserSocialProfile {
        // 🔑 修复：使用Predicate直接查询，避免Context冲突
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate<EAUser> { user in
                user.socialProfile?.id == profileId
            }
        )

        guard let user = try modelContext.fetch(userDescriptor).first,
              let socialProfile = user.socialProfile else {
            throw FriendRequestError.requestNotFound
        }

        // ✅ 修复：移除Context验证，因为对象来自当前Context，不会有冲突
        // 这个socialProfile是通过当前modelContext查询得到的，天然属于当前Context

        #if DEBUG
        // 调试环境下记录Context安全获取用户档案，但不使用print
        #endif

        return socialProfile
    }

    /// 检查是否存在待处理的请求
    private func checkExistingPendingRequest(senderId: UUID, receiverId: UUID) async throws -> EAFriendRequest? {
        let descriptor = FetchDescriptor<EAFriendRequest>()

        let allRequests = try modelContext.fetch(descriptor)

        // 在内存中查找匹配的请求
        return allRequests.first { request in
            request.senderProfile?.id == senderId &&
            request.receiverProfile?.id == receiverId &&
            request.status == .pending
        }
    }

    // MARK: - 私有辅助方法

    /// 🔑 新增：检查现有好友关系（用于原子性操作）
    private func checkExistingFriendship(userProfile1Id: UUID, userProfile2Id: UUID) async throws -> EAFriendship? {
        let descriptor = FetchDescriptor<EAFriendship>()
        let allFriendships = try modelContext.fetch(descriptor)

        // 在内存中完成状态和关系匹配
        return allFriendships.first { friendship in
            friendship.status == .active &&
            ((friendship.initiatorProfile?.id == userProfile1Id && friendship.friendProfile?.id == userProfile2Id) ||
             (friendship.initiatorProfile?.id == userProfile2Id && friendship.friendProfile?.id == userProfile1Id))
        }
    }
}

// MARK: - 错误定义

/// 好友请求相关错误
enum FriendRequestError: Error, LocalizedError {
    case requestNotFound
    case requestAlreadyExists
    case requestCannotBeProcessed
    case requestCannotBeCancelled
    case cannotRequestSelf
    case requestExpired
    case invalidRequestProfiles  // 🔑 新增：无效的请求档案
    case invalidProfiles         // 🔑 新增：档案数据无效
    case friendshipAlreadyExists // 🔑 新增：好友关系已存在

    var errorDescription: String? {
        switch self {
        case .requestNotFound:
            return "好友请求不存在"
        case .requestAlreadyExists:
            return "好友请求已存在"
        case .requestCannotBeProcessed:
            return "好友请求无法处理"
        case .requestCannotBeCancelled:
            return "好友请求无法取消"
        case .cannotRequestSelf:
            return "不能向自己发送好友请求"
        case .requestExpired:
            return "好友请求已过期"
        case .invalidRequestProfiles:
            return "请求的用户信息无效"
        case .invalidProfiles:
            return "用户档案数据无效"
        case .friendshipAlreadyExists:
            return "好友关系已存在"
        }
    }
}
