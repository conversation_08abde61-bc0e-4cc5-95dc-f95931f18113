import Foundation
import SwiftData

// MARK: - Repository协议定义

/// 好友消息Repository协议
/// 遵循项目Repository模式，使用@ModelActor确保线程安全
/// 🔑 修复：采用"传ID，勿传对象"模式，避免跨Context关系建立崩溃
protocol EAFriendMessageRepositoryProtocol {
    func sendMessage(content: String, messageType: EAFriendMessage.MessageType, senderProfileId: UUID, receiverProfileId: UUID, friendshipId: UUID, sequenceId: Int64) async throws -> EAFriendMessage
    func fetchChatHistory(friendship: EAFriendship, limit: Int, offset: Int) async throws -> [EAFriendMessage]
    func fetchUnreadMessages(userProfileId: UUID) async throws -> [EAFriendMessage]
    func markMessagesAsRead(messageIds: [UUID]) async throws
    func markMessageAsRead(messageId: UUID) async throws
    func deleteMessage(messageId: UUID) async throws
    func revokeMessage(messageId: UUID) async throws
    func createRevokeNotification(for message: EAFriendMessage) async throws -> EAFriendMessage?
    func editMessage(messageId: UUID, newContent: String) async throws -> EAFriendMessage
    func fetchMessage(by messageId: UUID) async throws -> EAFriendMessage?
    func searchMessages(in friendship: EAFriendship, query: String, limit: Int) async throws -> [EAFriendMessage]
    
    // ✅ 新增：用户要求的核心方法
    func fetchMessages(for friendship: EAFriendship, page: Int, limit: Int) async throws -> [EAFriendMessage]
    func countUnreadMessages(for userProfileID: UUID) async throws -> Int
    func fetchUserSentMessages(for userProfileID: UUID) async throws -> [EAFriendMessage]
    func fetchUserReceivedMessages(for userProfileID: UUID) async throws -> [EAFriendMessage]
    func sendMessage(content: String, from senderProfile: EAUserSocialProfile, to receiverProfile: EAUserSocialProfile, in friendship: EAFriendship) async throws -> EAFriendMessage
}

// MARK: - Repository实现

/// 好友消息Repository实现
/// 使用@ModelActor确保线程安全，遵循项目开发规范
@ModelActor
actor EAFriendMessageRepositoryImpl: EAFriendMessageRepositoryProtocol {
    
    // 🔑 性能优化：增强缓存机制，减少重复查询
    private var profileCache: [UUID: EAUserSocialProfile] = [:]
    private var friendshipCache: [UUID: EAFriendship] = [:]
    private var queryTimestamps: [String: Date] = [:] // 查询时间戳，用于缓存失效
    private let cacheTimeout: TimeInterval = 180 // 🚨 优化：缩短缓存时间为3分钟，减少内存占用
    private var lastCacheUpdate: Date = Date()
    private let maxCacheSize = 50 // 🚨 优化：减少最大缓存条目数，防止内存泄露
    
    // 🔑 性能优化：查询频率统计，智能预热热点数据
    private var queryFrequency: [UUID: Int] = [:]
    private var lastOptimization: Date = Date()
    
    /// 🔑 性能优化：批量查询接口
    
    /// 🔑 性能优化：批量预热Profile缓存
    func preloadUserProfiles(profileIds: [UUID]) async throws {
        let uncachedIds = profileIds.filter { profileCache[$0] == nil }
        
        if !uncachedIds.isEmpty {
            let descriptor = FetchDescriptor<EAUserSocialProfile>(
                predicate: #Predicate<EAUserSocialProfile> { profile in
                    uncachedIds.contains(profile.id)
                }
            )
            
            let profiles = try modelContext.fetch(descriptor)
            for profile in profiles {
                profileCache[profile.id] = profile
                queryTimestamps["profile_\(profile.id)"] = Date()
            }
        }
    }
    
    /// 🔑 性能优化：批量预热Friendship缓存
    func preloadFriendships(friendshipIds: [UUID]) async throws {
        let uncachedIds = friendshipIds.filter { friendshipCache[$0] == nil }
        
        if !uncachedIds.isEmpty {
            let descriptor = FetchDescriptor<EAFriendship>(
                predicate: #Predicate<EAFriendship> { friendship in
                    uncachedIds.contains(friendship.id)
                }
            )
            
            let friendships = try modelContext.fetch(descriptor)
            for friendship in friendships {
                friendshipCache[friendship.id] = friendship
                queryTimestamps["friendship_\(friendship.id)"] = Date()
            }
        }
    }
    
    /// 🔑 性能优化：获取缓存统计信息（用于监控和调试）
    func getCacheStatistics() -> (profileCacheSize: Int, friendshipCacheSize: Int, queryCount: Int) {
        return (
            profileCacheSize: profileCache.count,
            friendshipCacheSize: friendshipCache.count,
            queryCount: queryFrequency.values.reduce(0, +)
        )
    }
    
    /// 获取指定好友关系的最后序列ID
    private func getLastSequenceId(for friendshipId: UUID) async throws -> Int64 {
        var descriptor = FetchDescriptor<EAFriendMessage>(
            predicate: #Predicate<EAFriendMessage> { message in
                message.friendship?.id == friendshipId && !message.isDeleted
            },
            sortBy: [SortDescriptor(\.sequenceId, order: .reverse)]
        )
        descriptor.fetchLimit = 1
        
        let lastMessages = try modelContext.fetch(descriptor)
        return lastMessages.first?.sequenceId ?? 0
    }
    
    /// 🔑 修复：发送消息 - 采用"传ID，勿传对象"模式确保Context安全，添加缓存优化
    /// ✅ 新增：支持序列ID参数，解决消息排序问题
    func sendMessage(content: String, messageType: EAFriendMessage.MessageType, senderProfileId: UUID, receiverProfileId: UUID, friendshipId: UUID, sequenceId: Int64 = 0) async throws -> EAFriendMessage {
        
        // 🔑 性能优化：清理过期缓存
        await clearExpiredCache()
        
        // 🔑 关键修复：在Repository自己的Context中根据ID获取对象，使用缓存优化
        guard let localSenderProfile = try await getCachedUserSocialProfile(by: senderProfileId) else {
            throw FriendMessageError.senderNotFound
        }
        
        guard let localReceiverProfile = try await getCachedUserSocialProfile(by: receiverProfileId) else {
            throw FriendMessageError.receiverNotFound
        }
        
        guard let localFriendship = try await getCachedFriendship(by: friendshipId) else {
            throw FriendMessageError.friendshipNotFound
        }
        
        // 验证好友关系
        guard localFriendship.isValidFriendship() else {
            throw FriendMessageError.invalidFriendship
        }
        
        // 验证发送者和接收者是否在好友关系中
        let isValidSender = localFriendship.initiatorProfile?.id == senderProfileId || localFriendship.friendProfile?.id == senderProfileId
        let isValidReceiver = localFriendship.initiatorProfile?.id == receiverProfileId || localFriendship.friendProfile?.id == receiverProfileId
        
        guard isValidSender && isValidReceiver else {
            throw FriendMessageError.invalidParticipants
        }
        
        // ✅ 修复：使用正确的初始化方法，遵循SwiftData最佳实践
        let message = EAFriendMessage(
            content: content,
            messageType: messageType
        )
        
        // ✅ 设置序列ID - 如果未提供则生成新的
        if sequenceId == 0 {
            // 生成新的序列ID，确保消息顺序正确
            let lastSequenceId = try await getLastSequenceId(for: friendshipId)
            message.sequenceId = lastSequenceId + 1
        } else {
            message.sequenceId = sequenceId
        }
        
        // 插入到ModelContext
        modelContext.insert(message)

        // 🔑 第四阶段优化：使用新的活动档案模型建立关系
        // 🚨 关键修复：确保socialActivity存在，如果不存在则创建（遵循原子性操作原则）
        let senderActivity: EAUserSocialActivity
        if let existingActivity = localSenderProfile.socialActivity {
            senderActivity = existingActivity
        } else {
            senderActivity = localSenderProfile.ensureSocialActivity()
            // 🔑 修复：将新创建的activity插入到Context，并建立关系
            modelContext.insert(senderActivity)
            senderActivity.socialProfile = localSenderProfile
            localSenderProfile.socialActivity = senderActivity
        }

        let receiverActivity: EAUserSocialActivity
        if let existingActivity = localReceiverProfile.socialActivity {
            receiverActivity = existingActivity
        } else {
            receiverActivity = localReceiverProfile.ensureSocialActivity()
            // 🔑 修复：将新创建的activity插入到Context，并建立关系
            modelContext.insert(receiverActivity)
            receiverActivity.socialProfile = localReceiverProfile
            localReceiverProfile.socialActivity = receiverActivity
        }

        message.senderActivity = senderActivity
        message.receiverActivity = receiverActivity
        message.friendship = localFriendship
        
        // 更新好友关系的消息统计
        localFriendship.updateMessageStats()
        
        // 保存到数据库
        try modelContext.save()

        #if DEBUG
        // 调试环境记录：消息已保存到数据库，ID: \(message.id), 序列ID: \(message.sequenceId)
        #endif

        // 关键修复: 为了确保返回的对象在其关系属性被完全填充后返回，
        // 我们在保存后使用其ID重新获取它。
        // 这可以解决在跨actor边界传递对象时，关系可能未被正确加载的问题。
        let persistentID = message.persistentModelID
        guard let refetchedMessage = modelContext.model(for: persistentID) as? EAFriendMessage else {
            // 如果重获取失败，返回原始消息作为备用
        return message
        }

        return refetchedMessage
    }
    
    /// 🔑 核心修复：根据friendshipId查询聊天历史 - 解决双向消息问题
    /// 这是解决"对方无法接收"问题的核心方法
    func fetchChatHistory(for friendshipID: UUID) throws -> [EAFriendMessage] {
        let predicate = #Predicate<EAFriendMessage> {
            $0.friendship?.id == friendshipID
        }
        let descriptor = FetchDescriptor(
            predicate: predicate,
            sortBy: [
                SortDescriptor(\.sequenceId, order: .forward),
                SortDescriptor(\.creationDate, order: .forward)
            ]
        )
        return try modelContext.fetch(descriptor)
    }

    /// 获取聊天历史记录 - 🔑 修复：使用正确的#Predicate语法和FetchDescriptor参数
    func fetchChatHistory(friendship: EAFriendship, limit: Int = 50, offset: Int = 0) async throws -> [EAFriendMessage] {
        // 🔑 修复：使用friendshipId进行查询，避免Context不一致问题
        let friendshipID = friendship.id // 将ID提取为非可选常量，以安全地在闭包中使用

        // 🔑 修复：使用正确的FetchDescriptor语法，确保数据库级别过滤
        var descriptor = FetchDescriptor<EAFriendMessage>(
            predicate: #Predicate<EAFriendMessage> { message in
                // ✅ 恢复核心过滤逻辑：查询所有消息，其中消息的friendship.id等于我们正在聊天的这个friendshipId
                message.friendship?.id == friendshipID && !message.isDeleted
            },
            // ✅ 优先按序列ID排序，确保消息顺序绝对正确
            sortBy: [
                SortDescriptor(\.sequenceId, order: .forward),     // 主要排序：序列ID
                SortDescriptor(\.creationDate, order: .forward)    // 次要排序：创建时间
            ]
        )
        descriptor.fetchOffset = offset
        descriptor.fetchLimit = limit

        let messages = try modelContext.fetch(descriptor)

        #if DEBUG
        // 调试环境记录：从数据库获取消息，好友关系ID: \(friendshipID), 消息数量: \(messages.count), offset: \(offset), limit: \(limit)
        #endif

        return messages
    }
    
    /// 🚨 致命修复：获取用户的未读消息 - 使用存储属性而非计算属性
    func fetchUnreadMessages(userProfileId: UUID) async throws -> [EAFriendMessage] {
        let descriptor = FetchDescriptor<EAFriendMessage>(
            predicate: #Predicate<EAFriendMessage> { message in
                message.receiverActivity?.socialProfile?.id == userProfileId &&
                !message.isRead &&
                !message.isDeleted
            },
            sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
        )

        return try modelContext.fetch(descriptor)
    }
    
    /// 批量标记消息为已读
    func markMessagesAsRead(messageIds: [UUID]) async throws {
        for messageId in messageIds {
            try await markMessageAsRead(messageId: messageId)
        }
    }
    
    /// 标记单个消息为已读
    func markMessageAsRead(messageId: UUID) async throws {
        guard let message = try await fetchMessage(by: messageId) else {
            throw FriendMessageError.messageNotFound
        }
        
        message.markAsRead()
        try modelContext.save()
    }
    
    /// 删除消息（软删除）
    func deleteMessage(messageId: UUID) async throws {
        guard let message = try await fetchMessage(by: messageId) else {
            throw FriendMessageError.messageNotFound
        }
        
        message.softDelete()
        try modelContext.save()
    }
    
    /// 撤销消息
    func revokeMessage(messageId: UUID) async throws {
        guard let message = try await fetchMessage(by: messageId) else {
            throw FriendMessageError.messageNotFound
        }
        
        guard message.canBeRevoked() else {
            throw FriendMessageError.messageCannotBeRevoked
        }
        
        message.revoke()
        try modelContext.save()
    }
    
    /// 创建撤销通知消息
    func createRevokeNotification(for message: EAFriendMessage) async throws -> EAFriendMessage? {
        guard let senderProfile = message.senderProfile,
              let receiverProfile = message.receiverProfile,
              let friendship = message.friendship else {
            return nil
        }
        
        // 创建撤销通知消息
        let senderName = senderProfile.user?.username ?? "用户"
        let notificationContent = "\(senderName)撤回了一条消息"
        let notificationMessage = EAFriendMessage(
            content: notificationContent,
            messageType: .text
        )
        
        // 插入到ModelContext
        modelContext.insert(notificationMessage)
        
        // 🔑 第四阶段优化：建立关系（使用新的活动档案模型）
        notificationMessage.senderActivity = senderProfile.socialActivity
        notificationMessage.receiverActivity = receiverProfile.socialActivity
        notificationMessage.friendship = friendship
        
        // 标记为系统消息（撤销通知）
        notificationMessage.isRevoked = true // 使用revoked标记系统消息
        
        try modelContext.save()
        
        return notificationMessage
    }
    
    /// 编辑消息
    func editMessage(messageId: UUID, newContent: String) async throws -> EAFriendMessage {
        guard let message = try await fetchMessage(by: messageId) else {
            throw FriendMessageError.messageNotFound
        }
        
        guard message.canBeEdited() else {
            throw FriendMessageError.messageCannotBeEdited
        }
        
        message.editContent(newContent)
        try modelContext.save()
        
        return message
    }
    
    /// 根据ID获取消息
    func fetchMessage(by messageId: UUID) async throws -> EAFriendMessage? {
        let descriptor = FetchDescriptor<EAFriendMessage>(
            predicate: #Predicate { $0.id == messageId }
        )

        let messages = try modelContext.fetch(descriptor)
        return messages.first
    }
    
    // MARK: - 扩展功能方法
    
    /// 获取好友关系的最后一条消息 - 🔑 修复：使用正确的#Predicate语法和FetchDescriptor参数
    func fetchLastMessage(for friendship: EAFriendship) async throws -> EAFriendMessage? {
        let friendshipID = friendship.id // 将ID提取为非可选常量
        
        var descriptor = FetchDescriptor<EAFriendMessage>(
            predicate: #Predicate<EAFriendMessage> { message in
                // ✅ 恢复核心过滤逻辑
                message.friendship?.id == friendshipID && !message.isDeleted
            },
            sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
        )
        descriptor.fetchLimit = 1

        let messages = try modelContext.fetch(descriptor)
        return messages.first
    }
    
    /// 获取用户在特定好友关系中的未读消息数量 - 🔑 修复：使用正确的#Predicate语法
    func getUnreadCount(for friendship: EAFriendship, receiverId: UUID) async throws -> Int {
        let friendshipID = friendship.id // 将ID提取为非可选常量
        
        let descriptor = FetchDescriptor<EAFriendMessage>(
            predicate: #Predicate<EAFriendMessage> { message in
                // 🚨 致命修复：使用存储属性而非计算属性
                message.friendship?.id == friendshipID &&
                message.receiverActivity?.socialProfile?.id == receiverId &&
                !message.isRead &&
                !message.isDeleted
            }
        )

        let messages = try modelContext.fetch(descriptor)
        return messages.count
    }
    
    /// 搜索消息内容 - 🔑 修复：使用正确的#Predicate语法和FetchDescriptor参数
    func searchMessages(in friendship: EAFriendship, query: String, limit: Int = 20) async throws -> [EAFriendMessage] {
        let friendshipID = friendship.id // 将ID提取为非可选常量
        
        var descriptor = FetchDescriptor<EAFriendMessage>(
            predicate: #Predicate<EAFriendMessage> { message in
                // ✅ 恢复核心过滤逻辑
                message.friendship?.id == friendshipID &&
                message.content.localizedStandardContains(query) &&
                !message.isDeleted
            },
            sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
        )
        descriptor.fetchLimit = limit

        return try modelContext.fetch(descriptor)
    }
    
    // MARK: - 🔑 安全对象获取方法（避免跨Context问题）

    /// 安全获取用户社交档案（在当前Context中）
    private func getSafeUserSocialProfile(by profileId: UUID) async throws -> EAUserSocialProfile? {
        let descriptor = FetchDescriptor<EAUserSocialProfile>(
            predicate: #Predicate { $0.id == profileId }
        )
        let profiles = try modelContext.fetch(descriptor)
        return profiles.first
    }

    /// 安全获取好友关系（在当前Context中）
    private func getSafeFriendship(by friendshipId: UUID) async throws -> EAFriendship? {
        let descriptor = FetchDescriptor<EAFriendship>(
            predicate: #Predicate { $0.id == friendshipId }
        )
        let friendships = try modelContext.fetch(descriptor)
        return friendships.first
    }
    
    // MARK: - 🔑 性能优化：缓存机制
    
    /// 🔑 性能优化：智能缓存的用户社交档案获取
    private func getCachedUserSocialProfile(by profileId: UUID) async throws -> EAUserSocialProfile? {
        // 🔑 统计查询频率，用于智能预热
        queryFrequency[profileId, default: 0] += 1
        
        // 先检查缓存
        if let cachedProfile = profileCache[profileId] {
            // 🔑 更新访问时间戳
            queryTimestamps["profile_\(profileId)"] = Date()
            return cachedProfile
        }
        
        // 缓存未命中，从数据库获取
        if let profile = try await getSafeUserSocialProfile(by: profileId) {
            // 🔑 智能缓存管理：检查缓存大小限制
            if profileCache.count >= maxCacheSize {
                await cleanupOldCacheEntries()
            }
            
            profileCache[profileId] = profile
            queryTimestamps["profile_\(profileId)"] = Date()
            return profile
        }
        
        return nil
    }
    
    /// 🔑 性能优化：智能缓存的好友关系获取
    private func getCachedFriendship(by friendshipId: UUID) async throws -> EAFriendship? {
        // 🔑 统计查询频率
        queryFrequency[friendshipId, default: 0] += 1
        
        // 先检查缓存
        if let cachedFriendship = friendshipCache[friendshipId] {
            // 🔑 更新访问时间戳
            queryTimestamps["friendship_\(friendshipId)"] = Date()
            return cachedFriendship
        }
        
        // 缓存未命中，从数据库获取
        if let friendship = try await getSafeFriendship(by: friendshipId) {
            // 🔑 智能缓存管理：检查缓存大小限制
            if friendshipCache.count >= maxCacheSize {
                await cleanupOldCacheEntries()
            }
            
            friendshipCache[friendshipId] = friendship
            queryTimestamps["friendship_\(friendshipId)"] = Date()
            return friendship
        }
        
        return nil
    }
    
    /// 🔑 性能优化：智能缓存清理和预热机制
    private func clearExpiredCache() async {
        let now = Date()

        // 🚨 性能优化：缩短优化间隔为15分钟，更频繁地清理内存
        if now.timeIntervalSince(lastOptimization) > 900 { // 15分钟
            await performIntelligentCacheOptimization()
            lastOptimization = now
        }

        // 🔑 基础缓存清理：超时清理
        if now.timeIntervalSince(lastCacheUpdate) > cacheTimeout {
            // 🔑 智能清理：只清理低频访问的数据
            await cleanupLowFrequencyCache()
            lastCacheUpdate = now
        }

        // 🚨 新增：强制内存限制检查
        await enforceMemoryLimits()
    }
    
    /// 🔑 性能优化：清理旧的缓存条目（基于LRU策略）
    private func cleanupOldCacheEntries() async {
        let now = Date()
        let maxAge: TimeInterval = 600 // 10分钟
        
        // 清理过期的Profile缓存
        let expiredProfileKeys = queryTimestamps.compactMap { key, timestamp -> UUID? in
            if key.hasPrefix("profile_") && now.timeIntervalSince(timestamp) > maxAge {
                let idString = String(key.dropFirst(8)) // 移除"profile_"前缀
                return UUID(uuidString: idString)
            }
            return nil
        }
        
        for key in expiredProfileKeys {
            profileCache.removeValue(forKey: key)
            queryTimestamps.removeValue(forKey: "profile_\(key)")
        }
        
        // 清理过期的Friendship缓存
        let expiredFriendshipKeys = queryTimestamps.compactMap { key, timestamp -> UUID? in
            if key.hasPrefix("friendship_") && now.timeIntervalSince(timestamp) > maxAge {
                let idString = String(key.dropFirst(11)) // 移除"friendship_"前缀
                return UUID(uuidString: idString)
            }
            return nil
        }
        
        for key in expiredFriendshipKeys {
            friendshipCache.removeValue(forKey: key)
            queryTimestamps.removeValue(forKey: "friendship_\(key)")
        }
    }
    
    /// 🔑 性能优化：智能缓存优化策略
    private func performIntelligentCacheOptimization() async {
        // 🔑 批量预热：预加载高频访问的数据
        let hotProfileIds = queryFrequency
            .filter { $0.value >= 3 } // 访问次数>=3的为热点数据
            .sorted { $0.value > $1.value } // 按频次降序
            .prefix(20) // 只预热前20个
            .map { $0.key }
        
        // 🔑 批量预热Profile数据
        for profileId in hotProfileIds {
            if profileCache[profileId] == nil {
                // 只预热未缓存的热点数据
                _ = try? await getCachedUserSocialProfile(by: profileId)
            }
        }
        
        // 🔑 重置查询频率统计（避免无限增长）
        for (key, count) in queryFrequency {
            queryFrequency[key] = max(1, count / 2) // 减半，保持相对频率
        }
    }
    
    /// 🔑 性能优化：清理低频访问的缓存数据
    private func cleanupLowFrequencyCache() async {
        // 只清理访问频率低于2的数据
        let lowFrequencyIds = queryFrequency.filter { $0.value < 2 }.map { $0.key }
        
        for id in lowFrequencyIds {
            profileCache.removeValue(forKey: id)
            friendshipCache.removeValue(forKey: id)
            queryTimestamps.removeValue(forKey: "profile_\(id)")
            queryTimestamps.removeValue(forKey: "friendship_\(id)")
            queryFrequency.removeValue(forKey: id)
        }
    }
    
    // MARK: - 🔑 核心修复方法：用户要求的关键查询方法
    
    /// 🔑 修复：获取好友关系的分页消息（正确的#Predicate实现）
    func fetchMessages(for friendship: EAFriendship, page: Int, limit: Int) async throws -> [EAFriendMessage] {
        let friendshipID = friendship.id // 将ID提取为非可选常量
        
        var descriptor = FetchDescriptor<EAFriendMessage>(
            predicate: #Predicate<EAFriendMessage> { message in
                // ✅ 恢复核心过滤逻辑
                message.friendship?.id == friendshipID && !message.isDeleted
            },
            sortBy: [SortDescriptor(\.creationDate, order: .reverse)] // 聊天记录通常倒序获取，最新在前
        )
        descriptor.fetchOffset = page * limit
        descriptor.fetchLimit = limit
        
        return try modelContext.fetch(descriptor)
    }
    
    /// 🚨 致命修复：统计用户未读消息数量（使用存储属性）
    func countUnreadMessages(for userProfileID: UUID) async throws -> Int {
        let descriptor = FetchDescriptor<EAFriendMessage>(
            predicate: #Predicate<EAFriendMessage> { message in
                message.receiverActivity?.socialProfile?.id == userProfileID &&
                !message.isRead &&
                !message.isDeleted
            }
        )

        let messages = try modelContext.fetch(descriptor)
        return messages.count
    }
    
    /// 🚨 致命修复：获取用户发送的消息（使用存储属性）
    func fetchUserSentMessages(for userProfileID: UUID) async throws -> [EAFriendMessage] {
        let descriptor = FetchDescriptor<EAFriendMessage>(
            predicate: #Predicate<EAFriendMessage> { message in
                message.senderActivity?.socialProfile?.id == userProfileID && !message.isDeleted
            },
            sortBy: [SortDescriptor(\.creationDate, order: .forward)]
        )

        return try modelContext.fetch(descriptor)
    }
    
    /// 🚨 致命修复：获取用户接收的消息（使用存储属性）
    func fetchUserReceivedMessages(for userProfileID: UUID) async throws -> [EAFriendMessage] {
        let descriptor = FetchDescriptor<EAFriendMessage>(
            predicate: #Predicate<EAFriendMessage> { message in
                message.receiverActivity?.socialProfile?.id == userProfileID && !message.isDeleted
            },
            sortBy: [SortDescriptor(\.creationDate, order: .forward)]
        )

        return try modelContext.fetch(descriptor)
    }
    
    /// 🔑 修复：发送消息到好友关系（兼容性方法，建议使用传ID的版本）
    func sendMessage(content: String, from senderProfile: EAUserSocialProfile, to receiverProfile: EAUserSocialProfile, in friendship: EAFriendship) async throws -> EAFriendMessage {
        // 委托给安全的传ID版本
        return try await sendMessage(
            content: content,
            messageType: .text,
            senderProfileId: senderProfile.id,
            receiverProfileId: receiverProfile.id,
            friendshipId: friendship.id
        )
    }

    /// 🚨 新增：强制内存限制检查，防止缓存过度增长
    private func enforceMemoryLimits() async {
        // 检查缓存大小是否超过限制
        let totalCacheSize = profileCache.count + friendshipCache.count

        if totalCacheSize > maxCacheSize {
            // 强制清理最旧的缓存条目
            let excessCount = totalCacheSize - maxCacheSize
            await forceCleanupOldestEntries(count: excessCount)
        }
    }

    /// 🚨 新增：强制清理最旧的缓存条目
    private func forceCleanupOldestEntries(count: Int) async {
        // 按时间戳排序，清理最旧的条目
        let sortedTimestamps = queryTimestamps.sorted { $0.value < $1.value }
        let toRemove = Array(sortedTimestamps.prefix(count))

        for (key, _) in toRemove {
            queryTimestamps.removeValue(forKey: key)

            if key.hasPrefix("profile_") {
                let idString = String(key.dropFirst(8))
                if let uuid = UUID(uuidString: idString) {
                    profileCache.removeValue(forKey: uuid)
                }
            } else if key.hasPrefix("friendship_") {
                let idString = String(key.dropFirst(11))
                if let uuid = UUID(uuidString: idString) {
                    friendshipCache.removeValue(forKey: uuid)
                }
            }
        }
    }
}

// MARK: - 错误定义

/// 好友消息相关错误
enum FriendMessageError: Error, LocalizedError {
    case messageNotFound
    case invalidFriendship
    case invalidParticipants
    case messageCannotBeEdited
    case messageCannotBeRevoked
    case messageTooLong
    case emptyMessage
    // 🔑 新增：Context安全相关错误
    case senderNotFound
    case receiverNotFound
    case friendshipNotFound
    
    var errorDescription: String? {
        switch self {
        case .messageNotFound:
            return "消息不存在"
        case .invalidFriendship:
            return "无效的好友关系"
        case .invalidParticipants:
            return "无效的参与者"
        case .messageCannotBeEdited:
            return "消息无法编辑"
        case .messageCannotBeRevoked:
            return "消息无法撤销"
        case .messageTooLong:
            return "消息内容过长"
        case .emptyMessage:
            return "消息内容不能为空"
        case .senderNotFound:
            return "发送者不存在"
        case .receiverNotFound:
            return "接收者不存在"
        case .friendshipNotFound:
            return "好友关系不存在"
        }
    }
}
