import SwiftData
import Foundation
import UIKit
import os

/// 功能可用性管理器
/// 根据当前SwiftData模型容器的配置，智能管理功能的可用性
@MainActor
class EAFeatureManager: ObservableObject {
    
    // MARK: - 日志记录器
    private let logger = OSLog(subsystem: "com.evolve.features", category: "EAFeatureManager")
    
    // MARK: - 功能可用性状态
    @Published var isAIFeaturesAvailable: Bool = false
    @Published var isPaymentFeaturesAvailable: Bool = false
    @Published var isAdvancedAnalyticsAvailable: Bool = false
    @Published var isHabitPathsAvailable: Bool = false
    @Published var isContentLibraryAvailable: Bool = false
    @Published var isNotificationSettingsAvailable: Bool = false
    @Published var isProMember: Bool = false
    @Published var availableFeatures: Set<String> = []
    
    // MARK: - 系统信息
    @Published var currentIOSVersion: String = ""
    @Published var iosVersionComponents: [Int] = []
    @Published var isIOS17Compatible: Bool = true
    @Published var isIOS18Compatible: Bool = false
    @Published var deviceType: UIUserInterfaceIdiom = .phone
    
    // MARK: - 版本特定功能可用性
    @Published var isWidgetConfigurationAvailable: Bool = false
    @Published var isInteractiveWidgetsAvailable: Bool = false
    @Published var isControlCenterWidgetsAvailable: Bool = false
    @Published var isAdvancedAnimationsAvailable: Bool = false
    
    // MARK: - 模型可用性
    private var availableModels: Set<String> = []
    private var modelContainer: ModelContainer?
    
    init() {
        loadFeatureConfiguration()
    }
    
    // MARK: - 配置方法
    
    /// 根据模型容器配置功能可用性
    func configureFeatures(with modelContainer: ModelContainer) {
        self.modelContainer = modelContainer
        detectSystemInfo()
        detectAvailableModels(in: modelContainer)
        updateFeatureAvailability()
        logFeatureStatus()
    }
    
    /// 检测系统信息
    private func detectSystemInfo() {
        currentIOSVersion = UIDevice.current.systemVersion
        deviceType = UIDevice.current.userInterfaceIdiom
        
        // 解析iOS版本号
        iosVersionComponents = currentIOSVersion.split(separator: ".").compactMap { Int($0) }
        
        // 版本兼容性检测
        if iosVersionComponents.count >= 2 {
            let majorVersion = iosVersionComponents[0]
            // minorVersion暂时保留用于未来的细粒度版本检测
            let _ = iosVersionComponents.count > 1 ? iosVersionComponents[1] : 0

            isIOS17Compatible = majorVersion >= 17
            isIOS18Compatible = majorVersion >= 18
            
            // iOS 18+ 特定功能
            if majorVersion >= 18 {
                isInteractiveWidgetsAvailable = true
                isControlCenterWidgetsAvailable = true
                isAdvancedAnimationsAvailable = true
            }
            
            // iOS 17+ 特定功能
            if majorVersion >= 17 {
                isWidgetConfigurationAvailable = true
            }
        }
        
        #if DEBUG
        os_log(.info, log: logger, "📱 系统兼容性: iOS %{public}@ - 设备类型: %{public}@", currentIOSVersion, String(describing: deviceType))
        os_log(.info, log: logger, "🔧 iOS17兼容: %{public}@, iOS18兼容: %{public}@", isIOS17Compatible ? "是" : "否", isIOS18Compatible ? "是" : "否")
        #endif
    }
    
    /// 检测可用的模型
    private func detectAvailableModels(in container: ModelContainer) {
        availableModels.removeAll()
        
        // 检测各个模型是否可用
        let modelTypes: [(String, any PersistentModel.Type)] = [
            ("EAUser", EAUser.self),
            ("EAHabit", EAHabit.self),
            ("EACompletion", EACompletion.self),
            ("EAUserSettings", EAUserSettings.self),
            ("EAAIMessage", EAAIMessage.self),
            ("EAPayment", EAPayment.self),
            ("EAContent", EAContent.self),
            ("EAAnalytics", EAAnalytics.self),
            ("EAPath", EAPath.self)
        ]
        
        for (name, modelType) in modelTypes {
            if isModelAvailable(modelType, in: container) {
                availableModels.insert(name)
            }
        }
    }
    
    /// 检查特定模型是否在容器中可用
    private func isModelAvailable(_ modelType: any PersistentModel.Type, in container: ModelContainer) -> Bool {
        do {
            let context = container.mainContext
            
            // 根据模型类型创建相应的FetchDescriptor
            switch String(describing: modelType) {
            case "EAUser":
                let descriptor = FetchDescriptor<EAUser>(predicate: #Predicate { _ in false })
                _ = try context.fetch(descriptor)
                return true
            case "EAHabit":
                let descriptor = FetchDescriptor<EAHabit>(predicate: #Predicate { _ in false })
                _ = try context.fetch(descriptor)
                return true
            case "EACompletion":
                let descriptor = FetchDescriptor<EACompletion>(predicate: #Predicate { _ in false })
                _ = try context.fetch(descriptor)
                return true
            case "EAUserSettings":
                let descriptor = FetchDescriptor<EAUserSettings>(predicate: #Predicate { _ in false })
                _ = try context.fetch(descriptor)
                return true
            case "EAAIMessage":
                let descriptor = FetchDescriptor<EAAIMessage>(predicate: #Predicate { _ in false })
                _ = try context.fetch(descriptor)
                return true
            case "EAPayment":
                let descriptor = FetchDescriptor<EAPayment>(predicate: #Predicate { _ in false })
                _ = try context.fetch(descriptor)
                return true
            case "EAContent":
                let descriptor = FetchDescriptor<EAContent>(predicate: #Predicate { _ in false })
                _ = try context.fetch(descriptor)
                return true
            case "EAAnalytics":
                let descriptor = FetchDescriptor<EAAnalytics>(predicate: #Predicate { _ in false })
                _ = try context.fetch(descriptor)
                return true
            case "EAPath":
                let descriptor = FetchDescriptor<EAPath>(predicate: #Predicate { _ in false })
                _ = try context.fetch(descriptor)
                return true
            default:
                // 未知模型类型
                #if DEBUG
                #if DEBUG
                os_log(.error, log: logger, "⚠️ 未知模型类型: %{public}@", String(describing: modelType))
                #endif
                #endif
                return false
            }
        } catch {
            // 模型检测失败，静默处理
            return false
        }
    }
    
    /// 更新功能可用性
    private func updateFeatureAvailability() {
        // 核心模型检查
        let hasCoreModels = availableModels.contains("EAUser") && 
                           availableModels.contains("EAHabit") && 
                           availableModels.contains("EACompletion")
        
        // 所有功能在iOS 17.0+上都应该完全可用
        // 只有在模型真正不可用时才禁用功能
        
        // AI功能：需要AI对话模型
        isAIFeaturesAvailable = availableModels.contains("EAAIMessage")
        
        // 支付功能：需要支付记录模型
        isPaymentFeaturesAvailable = availableModels.contains("EAPayment")
        
        // 高级分析：需要用户分析模型
        isAdvancedAnalyticsAvailable = availableModels.contains("EAAnalytics")
        
        // 习惯路径：需要路径相关模型
        isHabitPathsAvailable = availableModels.contains("EAPath")
        
        // 内容库：需要内容模型
        isContentLibraryAvailable = availableModels.contains("EAContent")
        
        // 通知设置：需要通知设置模型
        isNotificationSettingsAvailable = availableModels.contains("EAUserSettings")
        
        // 如果核心模型不可用，这是严重问题，需要报告
        if !hasCoreModels {
            #if DEBUG
            #if DEBUG
            os_log(.fault, log: logger, "🚨 严重错误：核心模型不可用，这可能是数据损坏或配置错误")
            #endif
            #endif
        }
    }
    
    /// 记录功能状态
    private func logFeatureStatus() {
        #if DEBUG
        #if DEBUG
        os_log(.info, log: logger, "📊 功能可用性状态:")
        os_log(.info, log: logger, "   🤖 AI功能: %{public}@", isAIFeaturesAvailable ? "✅" : "❌")
        os_log(.info, log: logger, "   💳 支付功能: %{public}@", isPaymentFeaturesAvailable ? "✅" : "❌")
        os_log(.info, log: logger, "   📈 高级分析: %{public}@", isAdvancedAnalyticsAvailable ? "✅" : "❌")
        os_log(.info, log: logger, "   🛤️ 习惯路径: %{public}@", isHabitPathsAvailable ? "✅" : "❌")
        os_log(.info, log: logger, "   📚 内容库: %{public}@", isContentLibraryAvailable ? "✅" : "❌")
        #endif
        os_log(.info, log: logger, "   🔔 通知设置: %{public}@", isNotificationSettingsAvailable ? "✅" : "❌")
        os_log(.info, log: logger, "   📦 可用模型: %{public}@", availableModels.sorted().joined(separator: ", "))
        os_log(.info, log: logger, "   📱 系统版本: iOS %{public}@ - 完全兼容SwiftData", currentIOSVersion)
        
        // 如果有模型不可用，提供诊断信息
        let allExpectedModels: Set<String> = [
            "EAUser", "EAHabit", "EACompletion", "EAAIMessage",
            "EAPayment", "EAContent", "EAPath", "EAAnalytics",
            "EAUserSettings"
        ]
        
        let missingModels = allExpectedModels.subtracting(availableModels)
        if !missingModels.isEmpty {
            os_log(.error, log: logger, "⚠️ 缺失模型: %{public}@", missingModels.sorted().joined(separator: ", "))
            os_log(.info, log: logger, "💡 建议：检查模型定义和容器配置")
        }
        #endif
    }
    
    // MARK: - 功能检查方法
    
    /// 检查AI功能是否可用
    func checkAIFeatures() -> Bool {
        return isAIFeaturesAvailable
    }
    
    /// 检查支付功能是否可用
    func checkPaymentFeatures() -> Bool {
        return isPaymentFeaturesAvailable
    }
    
    /// 检查分析功能是否可用
    func checkAnalyticsFeatures() -> Bool {
        return isAdvancedAnalyticsAvailable
    }
    
    /// 检查习惯路径功能是否可用
    func checkHabitPaths() -> Bool {
        return isHabitPathsAvailable
    }
    
    /// 检查内容库功能是否可用
    func checkContentLibrary() -> Bool {
        return isContentLibraryAvailable
    }
    
    /// 检查通知设置功能是否可用
    func checkNotificationSettings() -> Bool {
        return isNotificationSettingsAvailable
    }
    
    /// 获取功能可用性详情
    func getFeatureAvailabilityDetails() -> [String: Any] {
        return [
            "ai": isAIFeaturesAvailable,
            "payment": isPaymentFeaturesAvailable,
            "analytics": isAdvancedAnalyticsAvailable,
            "habitPaths": isHabitPathsAvailable,
            "content": isContentLibraryAvailable,
            "notifications": isNotificationSettingsAvailable,
            "system": [
                "version": currentIOSVersion,
                "swiftDataSupported": true // iOS 17.0+都支持SwiftData
            ],
            "availableModels": Array(availableModels),
            "totalModels": availableModels.count
        ]
    }
    
    // MARK: - 状态信息方法
    
    /// 获取AI功能的状态信息
    func getAIStatusMessage() -> String {
        if isAIFeaturesAvailable {
            return "AI功能完全可用"
        } else {
            return "AI功能暂时不可用，请检查数据配置"
        }
    }
    
    /// 获取分析功能的状态信息
    func getAnalyticsStatusMessage() -> String {
        if isAdvancedAnalyticsAvailable {
            return "高级分析功能完全可用"
        } else {
            return "高级分析功能暂时不可用，请检查数据配置"
        }
    }
    
    /// 获取内容库的状态信息
    func getContentLibraryStatusMessage() -> String {
        if isContentLibraryAvailable {
            return "智慧宝库完全可用"
        } else {
            return "智慧宝库暂时不可用，请检查数据配置"
        }
    }
    
    /// 获取支付功能的状态信息
    func getPaymentStatusMessage() -> String {
        if isPaymentFeaturesAvailable {
            return "Pro功能完全可用"
        } else {
            return "Pro功能暂时不可用，请检查数据配置"
        }
    }
    
    /// 获取功能状态信息（智能版本）
    func getFeatureStatusMessage(for feature: String) -> String {
        switch feature {
        case "ai":
            return getAIStatusMessage()
        case "analytics":
            return getAnalyticsStatusMessage()
        case "content":
            return getContentLibraryStatusMessage()
        case "payment":
            return getPaymentStatusMessage()
        case "paths":
            return isHabitPathsAvailable ? "习惯路径功能可用" : "习惯路径功能暂时不可用，请检查数据配置"
        case "notifications":
            return isNotificationSettingsAvailable ? "高级通知设置可用" : "高级通知设置暂时不可用，请检查数据配置"
        default:
            return "功能状态未知"
        }
    }
    
    /// 是否所有功能都可用
    var allFeaturesAvailable: Bool {
        return isAIFeaturesAvailable && 
               isPaymentFeaturesAvailable && 
               isAdvancedAnalyticsAvailable && 
               isHabitPathsAvailable && 
               isContentLibraryAvailable && 
               isNotificationSettingsAvailable
    }
    
    /// 获取整体状态信息
    var overallStatusMessage: String {
        if allFeaturesAvailable {
            return "所有功能在iOS \(currentIOSVersion)上完全可用"
        } else {
            let availableCount = [
                isAIFeaturesAvailable,
                isPaymentFeaturesAvailable,
                isAdvancedAnalyticsAvailable,
                isHabitPathsAvailable,
                isContentLibraryAvailable,
                isNotificationSettingsAvailable
            ].filter { $0 }.count
            
            return "\(availableCount)/6 个功能模块可用，其余功能可能需要检查数据配置"
        }
    }
    
    // MARK: - 功能可用性检查扩展
    
    /// 安全执行AI相关操作
    func safeExecuteAIOperation<T>(_ operation: () async throws -> T, fallback: () -> T) async -> T {
        if isAIFeaturesAvailable {
            do {
                return try await operation()
            } catch {
                #if DEBUG
                os_log(.error, log: logger, "AI操作失败: %{public}@", error.localizedDescription)
                #endif
                return fallback()
            }
        } else {
            #if DEBUG
            os_log(.info, log: logger, "AI功能不可用，使用降级方案")
            #endif
            return fallback()
        }
    }
    
    /// 安全执行支付相关操作
    func safeExecutePaymentOperation<T>(_ operation: () async throws -> T, fallback: () -> T) async -> T {
        if isPaymentFeaturesAvailable {
            do {
                return try await operation()
            } catch {
                #if DEBUG
                os_log(.error, log: logger, "支付操作失败: %{public}@", error.localizedDescription)
                #endif
                return fallback()
            }
        } else {
            #if DEBUG
            os_log(.info, log: logger, "支付功能不可用，使用降级方案")
            #endif
            return fallback()
        }
    }
    
    // MARK: - 功能检查
    
    func isFeatureEnabled(_ feature: String) -> Bool {
        return availableFeatures.contains(feature)
    }
    
    func enableProFeatures() {
        isProMember = true
        availableFeatures.formUnion([
            "unlimited_habits",
            "ai_coaching",
            "advanced_analytics",
            "custom_themes",
            "data_export"
        ])
    }
    
    func disableProFeatures() {
        isProMember = false
        availableFeatures = Set([
            "basic_habits",
            "simple_tracking"
        ])
    }
    
    // MARK: - 配置加载
    
    private func loadFeatureConfiguration() {
        // 基础功能始终可用
        availableFeatures = Set([
            "basic_habits",
            "simple_tracking"
        ])
        
        // 检查Pro会员状态
        checkProMembershipStatus()
    }
    
    private func checkProMembershipStatus() {
        // 这里可以添加Pro会员状态检查逻辑
        // 暂时设为false
        isProMember = false
    }
    
    // MARK: - 数据模型验证
    
    func validateDataModels() -> Bool {
        let modelTypes: [(String, any PersistentModel.Type)] = [
            ("EAUser", EAUser.self),
            ("EAHabit", EAHabit.self),
            ("EACompletion", EACompletion.self),
            ("EAUserSettings", EAUserSettings.self),
            ("EAAIMessage", EAAIMessage.self),
            ("EAPayment", EAPayment.self),
            ("EAContent", EAContent.self),
            ("EAAnalytics", EAAnalytics.self),
            ("EAPath", EAPath.self)
        ]
        
        // 验证所有模型类型是否正确注册
        for (name, type) in modelTypes {
            #if DEBUG
            os_log(.info, log: logger, "✅ 验证模型: %{public}@ - %{public}@", name, String(describing: type))
            #endif
        }
        
        return true
    }
    
    // MARK: - 数据库健康检查
    
    func performDatabaseHealthCheck(context: ModelContext) -> Bool {
        // 检查核心模型
        do {
            let descriptor = FetchDescriptor<EAUser>(predicate: #Predicate { _ in false })
            _ = try context.fetch(descriptor)
        } catch {
            #if DEBUG
            os_log(.error, log: logger, "❌ EAUser模型检查失败: %{public}@", error.localizedDescription)
            #endif
            return false
        }
        
        do {
            let descriptor = FetchDescriptor<EACompletion>(predicate: #Predicate { _ in false })
            _ = try context.fetch(descriptor)
        } catch {
            #if DEBUG
            os_log(.error, log: logger, "❌ EACompletion模型检查失败: %{public}@", error.localizedDescription)
            #endif
            return false
        }
        
        do {
            let descriptor = FetchDescriptor<EAAIMessage>(predicate: #Predicate { _ in false })
            _ = try context.fetch(descriptor)
        } catch {
            #if DEBUG
            os_log(.error, log: logger, "❌ EAAIMessage模型检查失败: %{public}@", error.localizedDescription)
            #endif
            return false
        }
        
        do {
            let descriptor = FetchDescriptor<EAPayment>(predicate: #Predicate { _ in false })
            _ = try context.fetch(descriptor)
        } catch {
            #if DEBUG
            os_log(.error, log: logger, "❌ EAPayment模型检查失败: %{public}@", error.localizedDescription)
            #endif
            return false
        }
        
        // 检查独立模型
        do {
            let descriptor = FetchDescriptor<EAPath>(predicate: #Predicate { _ in false })
            _ = try context.fetch(descriptor)
        } catch {
            #if DEBUG
            os_log(.error, log: logger, "❌ EAPath模型检查失败: %{public}@", error.localizedDescription)
            #endif
            return false
        }
        
        do {
            let descriptor = FetchDescriptor<EAAnalytics>(predicate: #Predicate { _ in false })
            _ = try context.fetch(descriptor)
        } catch {
            #if DEBUG
            os_log(.error, log: logger, "❌ EAAnalytics模型检查失败: %{public}@", error.localizedDescription)
            #endif
            return false
        }
        
        #if DEBUG
        os_log(.info, log: logger, "✅ 数据库健康检查通过")
        #endif
        return true
    }
} 