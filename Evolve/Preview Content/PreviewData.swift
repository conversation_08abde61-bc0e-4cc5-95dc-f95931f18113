import Foundation
import SwiftData
import SwiftUI

/// 提供SwiftUI预览时使用的模拟数据
struct PreviewData {
    /// 模拟数据的ModelContainer - 使用AppSchema统一管理
    @MainActor
    static let container: ModelContainer = {
        do {
            let container = try EAAppSchema.createPreviewContainer()
            
            let context = container.mainContext
            
            // 创建示例用户
            let user = EAUser(username: "预览用户", email: "<EMAIL>")
            context.insert(user)
            // fetch一次，确保上下文一致
            let userId = user.id
            let userDescriptor = FetchDescriptor<EAUser>(predicate: #Predicate { $0.id == userId })
            guard let contextUser = try? context.fetch(userDescriptor).first else {
                fatalError("预览数据：无法fetch用户")
            }
            
            // 创建用户设置
            let settings = EAUserSettings()
            settings.user = contextUser
            context.insert(settings)
            
            // 创建示例习惯
            let habit1 = EAHabit(name: "晨间阅读", iconName: "book.fill", targetFrequency: 1)
            context.insert(habit1)
            habit1.user = contextUser
            
            let habit2 = EAHabit(name: "健身锻炼", iconName: "figure.run", targetFrequency: 3)
            context.insert(habit2)
            habit2.user = contextUser
            
            // 创建完成记录
            let completion = EACompletion()
            context.insert(completion)
            completion.habit = habit1
            
            try context.save()
            return container
        } catch {
            fatalError("预览容器创建失败: \(error)")
        }
    }()
    
    /// 模拟数据的ModelContext
    @MainActor
    static var modelContext: ModelContext = {
        return ModelContext(container)
    }()
    
    /// 示例习惯 - 用于详情页预览
    static var sampleHabit: EAHabit = {
        let habit = EAHabit(
            name: "冥想练习",
            iconName: "brain.head.profile",
            targetFrequency: 7
        )
        habit.preferredTimeSlot = "morning"
        
        // 添加一些完成记录用于统计展示
        let calendar = Calendar.current
        let today = Date()
        
        // 创建最近30天的一些完成记录
        for i in 0..<15 {
            if let date = calendar.date(byAdding: .day, value: -i, to: today) {
                let completion = EACompletion()
                completion.habit = habit
                completion.date = date
            }
        }
        
        // 设置基本信息
        habit.name = "晨间冥想"
        habit.habitDescription = "每天早晨进行10分钟的冥想练习"
        habit.iconName = "brain.head.profile"
        habit.category = "健康"
        habit.targetFrequency = 1
        habit.frequencyType = "daily"
        habit.reminderEnabled = true
        habit.color = "#14B8A6"
        
        return habit
    }()
    
    /// 示例用户资料
    static var sampleUserProfile: EAUser = {
        let user = EAUser(username: "预览用户", email: "<EMAIL>")
        user.isPro = false
        return user
    }()
        
    /// 示例习惯列表
    static var sampleHabits: [EAHabit] {
        let habit1 = EAHabit(name: "晨间阅读", iconName: "book.fill", targetFrequency: 1)
        let habit2 = EAHabit(name: "健身锻炼", iconName: "figure.run", targetFrequency: 3)
        let habit3 = EAHabit(name: "冥想练习", iconName: "leaf.fill", targetFrequency: 7)
        
        // 为习惯添加完成记录
        for habit in [habit1, habit2, habit3] {
            let completion = EACompletion()
            completion.habit = habit
        }
        
        return [habit1, habit2, habit3]
    }
    
    /// 示例用户
    static var sampleUser: EAUser = {
        let user = EAUser(username: "叶同学", email: "<EMAIL>")
        return user
    }()
    
    /// 初始化预览数据
    @MainActor
    static func initializePreviewData() {
        let context = modelContext
        
        // 检查是否已有数据，避免重复创建
        let existingHabits = try? context.fetch(FetchDescriptor<EAHabit>())
        if existingHabits?.isEmpty == false {
            return
        }
        
        // 创建示例用户
        let user = sampleUser
        context.insert(user)
        
        // 创建示例习惯
        let habit = EAHabit(name: "晨间阅读", iconName: "book.fill", targetFrequency: 1)
        context.insert(habit)
        
        // 创建示例完成记录 - 使用新的初始化方法建立正确关系
        let completion = EACompletion()
        context.insert(completion)
        completion.habit = habit
        
        // 保存数据
        try? context.save()
    }
    
    static var sampleUserSettings: EAUserSettings = {
        let settings = EAUserSettings()
        settings.preferredCoachStyle = "温柔鼓励型"
        settings.notificationsEnabled = true
        settings.preferredReminderTimes = ["09:00", "18:00"]
        return settings
    }()
    
    static var sampleContent: [EAContent] {
        return [
            EAContent(title: "计划的力量", content: "小计划，大改变", contentType: "motivation", isPro: false),
            EAContent(title: "专注冥想", content: "5分钟专注练习", contentType: "psychology_exercise", isPro: true),
            EAContent(title: "成功故事", content: "用户分享的成功经验", contentType: "success_story", isPro: false)
        ]
    }
    
    static var sampleAIMessages: [EAAIMessage] {
        return [
            EAAIMessage(userMessage: "今天感觉有点累", aiResponse: "理解你的感受，要不要试试轻松一点的活动？", conversationType: "daily_checkin"),
            EAAIMessage(userMessage: "我想养成阅读计划", aiResponse: "很棒的想法！我们可以从每天10分钟开始", conversationType: "habit_creation")
        ]
    }
    
    @MainActor
    static func createPreviewContainer() -> ModelContainer? {
        do {
            let container = try EAAppSchema.createPreviewContainer()
            let context = container.mainContext
            
            // 创建示例用户
            let user = EAUser(username: "预览用户", email: "<EMAIL>")
            context.insert(user)
            // fetch一次，确保上下文一致
            let userId = user.id
            let userDescriptor = FetchDescriptor<EAUser>(predicate: #Predicate { $0.id == userId })
            guard let contextUser = try? context.fetch(userDescriptor).first else {
                fatalError("预览数据：无法fetch用户")
            }
            
            // 创建用户设置
            let settings = EAUserSettings()
            settings.user = contextUser
            context.insert(settings)
            
            // 创建示例习惯
            let habit1 = EAHabit(name: "晨间阅读", iconName: "book.fill", targetFrequency: 1)
            context.insert(habit1)
            habit1.user = contextUser
            
            let habit2 = EAHabit(name: "健身锻炼", iconName: "figure.run", targetFrequency: 3)
            context.insert(habit2)
            habit2.user = contextUser
            
            // 创建完成记录
            let completion = EACompletion()
            context.insert(completion)
            completion.habit = habit1
            
            try context.save()
            return container
        } catch {
            fatalError("预览容器创建失败: \(error)")
        }
    }
    
    // MARK: - 社区功能示例数据
    
    /// 示例社区用户
    static var sampleCommunityUsers: [EAUser] {
        let users = [
            ("华", "<EMAIL>", true),
            ("小明", "<EMAIL>", false),
            ("晓雨", "<EMAIL>", false),
            ("志强", "<EMAIL>", true)
        ]
        
        return users.map { (username, email, isPro) in
            let user = EAUser(username: username, email: email)
            user.isPro = isPro
            user.creationDate = Calendar.current.date(byAdding: .day, value: -Int.random(in: 1...90), to: Date()) ?? Date()
            
            // 为用户创建社交档案
            let socialProfile = EAUserSocialProfile()
            socialProfile.user = user
            user.socialProfile = socialProfile
            
            return user
        }
    }
    
    /// 示例社区帖子
    static var sampleCommunityPosts: [EACommunityPost] {
        let sampleContents = [
            ("今天成功完成了早起计划的第30天！感觉整个人的精神状态都有了很大提升，早晨的阳光格外美好。坚持真的会带来改变，感谢大家的支持和鼓励！💪🌅", "早起计划", "achievement", 9),
            ("分享一个小技巧：把运动鞋放在床边，这样起床后就会想到要去运动！这个方法对我很有效，希望对大家也有帮助。", "晨练", "general", 6),
            ("连续100天完成所有设定的计划！关键是要从小计划开始，循序渐进。每个人都可以做到的，相信自己！🌟", "多计划管理", "achievement", 10),
            ("今天有点想偷懒，但还是坚持完成了阅读。虽然只读了10分钟，但至少没有中断连续记录。小步前进也是进步！📚", "阅读", "general", 5),
            ("冥想练习真的让我内心更平静了。推荐大家试试，从5分钟开始就可以。", "冥想", "general", 7),
            ("背单词是我的第三个计划，今天开始执行第一个，希望后续可以持续坚持。", "背单词", "general", 5)
        ]
        
        let users = sampleCommunityUsers
        var posts: [EACommunityPost] = []
        
        for (index, (content, habitName, category, energyLevel)) in sampleContents.enumerated() {
            let post = EACommunityPost(
                title: "我的\(habitName)分享", // 基于习惯名称生成标题
                content: content,
                habitName: habitName,
                category: category,
                energyLevel: energyLevel
            )
            
            // 设置随机的创建时间（最近7天内）
            post.creationDate = Calendar.current.date(byAdding: .hour, value: -Int.random(in: 1...168), to: Date()) ?? Date()
            post.lastEditDate = post.creationDate
            
            // 🔑 修复：添加可靠的测试图片URL
            if index % 3 == 0 { // 每3个帖子中有1个包含图片
                post.imageURLs = [
                    "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop",
                    "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=300&fit=crop"
                ]
            } else if index % 4 == 0 { // 每4个帖子中有1个包含单张图片
                post.imageURLs = [
                    "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop"
                ]
            }
            
            // 模拟一些互动数据
            post.likeCount = Int.random(in: 0...15)
            post.commentCount = Int.random(in: 0...8)
            post.energyBoost = post.likeCount + post.commentCount * 2
            
            // 分配作者（循环分配）- 使用新的关系模式
            if index < users.count {
                // 通过authorSocialProfile建立关系
                post.authorSocialProfile = users[index].socialProfile
            } else {
                post.authorSocialProfile = users[index % users.count].socialProfile
            }
            
            posts.append(post)
        }
        
        return posts
    }
    
    /// 示例点赞记录
    static var sampleCommunityLikes: [EACommunityLike] {
        let users = sampleCommunityUsers
        let posts = sampleCommunityPosts
        var likes: [EACommunityLike] = []
        
        for post in posts.prefix(3) { // 为前3个帖子创建点赞
            let likeCount = min(post.likeCount, users.count)
            let likingUsers = users.shuffled().prefix(likeCount)
            
            for likingUser in likingUsers {
                let like = EACommunityLike(
                    targetType: "post",
                    userEnergyLevel: Int.random(in: 3...8),
                    interactionContext: "timeline"
                )
                
                like.creationDate = Calendar.current.date(byAdding: .minute, value: -Int.random(in: 1...1440), to: Date()) ?? Date()
                like.user = likingUser
                like.targetPost = post
                
                likes.append(like)
            }
        }
        
        return likes
    }
    
    /// 示例评论
    static var sampleCommunityComments: [EACommunityComment] {
        let users = sampleCommunityUsers
        let posts = sampleCommunityPosts
        var comments: [EACommunityComment] = []
        
        let sampleCommentContents = [
            "太棒了，很有启发！",
            "我也在养成这个计划，一起加油！",
            "这个方法很实用，谢谢分享",
            "坚持不易，为你点赞！",
            "请问有什么具体的技巧吗？",
            "我试试这个方法"
        ]
        
        for post in posts.prefix(2) { // 为前2个帖子创建评论
            let commentCount = min(post.commentCount, 3)
            
            for i in 0..<commentCount {
                let comment = EACommunityComment(
                    content: sampleCommentContents[i % sampleCommentContents.count],
                    replyToUsername: nil
                )
                
                comment.creationDate = Calendar.current.date(byAdding: .minute, value: -Int.random(in: 30...720), to: post.creationDate) ?? Date()
                comment.author = users[i % users.count]
                comment.post = post
                
                comments.append(comment)
            }
        }
        
        return comments
    }
    
    /// 创建完整的社区预览容器
    @MainActor
    static func createCommunityPreviewContainer() -> ModelContainer {
        do {
            let container = try EAAppSchema.createPreviewContainer()
            
            let context = container.mainContext
            
            // 插入示例用户
            let users = sampleCommunityUsers
            for user in users {
                context.insert(user)
                // 插入用户的社交档案
                if let socialProfile = user.socialProfile {
                    context.insert(socialProfile)
                }
            }
            
            // 插入示例帖子
            let posts = sampleCommunityPosts
            for post in posts {
                context.insert(post)
            }
            
            // 插入示例点赞
            let likes = sampleCommunityLikes
            for like in likes {
                context.insert(like)
            }
            
            // 插入示例评论
            let comments = sampleCommunityComments
            for comment in comments {
                context.insert(comment)
            }
            
            try context.save()
            return container
            
        } catch {
            fatalError("社区预览容器创建失败: \(error)")
        }
    }
}

// MARK: - 中文输入测试视图
struct ChineseInputTestView: View {
    @State private var testText = ""
    
    var body: some View {
        VStack {
            TextField("请输入中文测试", text: $testText)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .padding()
            
            Text("输入内容：\(testText)")
                .padding()
            
            Button("清空") {
                testText = ""
            }
            .padding()
        }
        .navigationTitle("中文输入测试")
    }
}

#Preview("中文输入测试") {
    NavigationView {
        ChineseInputTestView()
    }
}