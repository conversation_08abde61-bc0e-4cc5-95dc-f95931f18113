import SwiftUI

// MARK: - 能量进度条组件
struct EAEnergyMeter: View {
    // MARK: - Properties
    let progress: Double
    let height: CGFloat
    let showPercentage: Bool
    
    // MARK: - Animation State
    @State private var animationOffset: CGFloat = 0
    @State private var isAnimating: Bool = false
    
    // MARK: - Initialization
    init(
        progress: Double,
        height: CGFloat = 30,
        showPercentage: Bool = true
    ) {
        self.progress = max(0, min(1, progress))
        self.height = height
        self.showPercentage = showPercentage
    }
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: 8) {
            // 能量进度条
            energyProgressBar
            
            // 百分比显示
            if showPercentage {
                percentageText
            }
        }
        .onAppear {
            startFlowAnimation()
        }
    }
    
    // MARK: - Components
    
    /// 能量进度条主体
    private var energyProgressBar: some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                // 背景轨道
                backgroundTrack(width: geometry.size.width)
                
                // 进度填充
                progressFill(width: geometry.size.width)
            }
        }
        .frame(height: height)
    }
    
    /// 背景轨道
    private func backgroundTrack(width: CGFloat) -> some View {
        RoundedRectangle(cornerRadius: height / 2)
            .fill(
                LinearGradient(
                    colors: [
                        Color.black.opacity(0.3),
                        Color.black.opacity(0.2)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .frame(width: width, height: height)
            .overlay(
                RoundedRectangle(cornerRadius: height / 2)
                    .stroke(Color.white.opacity(0.1), lineWidth: 1)
            )
    }
    
    /// 进度填充
    private func progressFill(width: CGFloat) -> some View {
        let fillWidth = width * progress
        
        return ZStack {
            // 主要渐变填充
            RoundedRectangle(cornerRadius: (height - 4) / 2)
                .fill(energyGradient)
                .frame(width: fillWidth)
                .overlay(
                    // 流动光效
                    flowingLightEffect(fillWidth: fillWidth)
                )
                .shadow(color: Color.hexColor("FF6B47").opacity(0.5), radius: 4, x: 0, y: 0)
        }
        .padding(2)
        .animation(.easeInOut(duration: 0.8), value: progress)
    }
    
    /// 流动光效
    private func flowingLightEffect(fillWidth: CGFloat) -> some View {
        RoundedRectangle(cornerRadius: (height - 4) / 2)
            .fill(
                LinearGradient(
                    colors: [
                        Color.white.opacity(0),
                        Color.white.opacity(0.3),
                        Color.white.opacity(0.5),
                        Color.white.opacity(0.3),
                        Color.white.opacity(0)
                    ],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .frame(width: fillWidth * 0.3)
            .offset(x: animationOffset)
            .opacity(progress > 0 ? 0.8 : 0)
    }
    
    /// 能量渐变色
    private var energyGradient: LinearGradient {
        LinearGradient(
            colors: [
                Color.hexColor("FF7F50"), // 珊瑚橙
                Color.hexColor("FF6347"), // 番茄红
                Color.hexColor("40E0D0"), // 荧光青
                Color.hexColor("AFEEEE")  // 淡青色
            ],
            startPoint: .leading,
            endPoint: .trailing
        )
    }
    
    /// 百分比文字
    private var percentageText: some View {
        HStack {
            Spacer()
            
            Text("\(Int(progress * 100))%")
                .font(.system(size: 12, weight: .medium, design: .rounded))
                .foregroundColor(.white.opacity(0.9))
                .padding(.horizontal, 8)
                .padding(.vertical, 2)
                .background(
                    Capsule()
                        .fill(Color.black.opacity(0.4))
                )
        }
    }
    
    // MARK: - Methods
    
    /// 启动流动动画
    private func startFlowAnimation() {
        guard progress > 0 else { return }
        
        withAnimation(
            Animation
                .linear(duration: 2.5)
                .repeatForever(autoreverses: false)
        ) {
            animationOffset = UIScreen.main.bounds.width
        }
    }
}

// MARK: - Energy Meter Styles
extension EAEnergyMeter {
    /// 紧凑样式
    static func compact(progress: Double) -> EAEnergyMeter {
        EAEnergyMeter(
            progress: progress,
            height: 20,
            showPercentage: false
        )
    }
    
    /// 标准样式
    static func standard(progress: Double) -> EAEnergyMeter {
        EAEnergyMeter(
            progress: progress,
            height: 30,
            showPercentage: true
        )
    }
    
    /// 大尺寸样式
    static func large(progress: Double) -> EAEnergyMeter {
        EAEnergyMeter(
            progress: progress,
            height: 40,
            showPercentage: true
        )
    }
}

// MARK: - Blur View Helper
struct BlurView: UIViewRepresentable {
    let style: UIBlurEffect.Style
    
    func makeUIView(context: Context) -> UIVisualEffectView {
        UIVisualEffectView(effect: UIBlurEffect(style: style))
    }
    
    func updateUIView(_ uiView: UIVisualEffectView, context: Context) {}
}

// MARK: - 预览
#Preview {
    VStack(spacing: 20) {
        EAEnergyMeter(progress: 0.75)
        EAEnergyMeter(progress: 0.30)
        EAEnergyMeter(progress: 1.0)
    }
    .padding()
    .background(Color.black)
} 