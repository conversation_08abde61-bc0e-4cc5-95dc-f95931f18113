import SwiftUI
import AVKit
import AVFoundation

/// 🎥 星域风格视频播放器
/// 支持全屏播放、手势控制、播放进度显示
struct EAVideoPlayerView: View {
    let videoPath: String
    @State private var player: AVPlayer?
    @State private var isPlaying = false
    @State private var showControls = true
    @State private var currentTime: Double = 0
    @State private var duration: Double = 0
    @State private var isDragging = false
    
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        ZStack {
            // 🌌 星域背景
            Color.black
                .ignoresSafeArea()
            
            if let player = player {
                // 视频播放器
                VideoPlayer(player: player)
                    .onAppear {
                        setupPlayer()
                    }
                    .onDisappear {
                        player.pause()
                    }
                    .onTapGesture {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            showControls.toggle()
                        }
                    }
                
                // 控制界面
                if showControls {
                    controlsOverlay
                }
            } else {
                // 加载状态
                loadingView
            }
        }
        .onAppear {
            loadVideo()
        }
        .onDisappear {
            cleanupPlayer()
        }
    }
    
    // MARK: - 子视图
    
    /// 控制界面覆盖层
    private var controlsOverlay: some View {
        VStack {
            // 顶部控制栏
            HStack {
                Button(action: { dismiss() }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.white)
                        .background(Color.black.opacity(0.6))
                        .clipShape(Circle())
                }
                
                Spacer()
                
                Text("视频播放")
                    .font(.headline)
                    .foregroundColor(.white)
                
                Spacer()
                
                // 占位符保持对称
                Color.clear
                    .frame(width: 44, height: 44)
            }
            .padding()
            
            Spacer()
            
            // 底部播放控制
            VStack(spacing: 16) {
                // 播放进度条
                progressSlider
                
                // 播放控制按钮
                HStack(spacing: 30) {
                    // 播放/暂停按钮
                    Button(action: togglePlayPause) {
                        Image(systemName: isPlaying ? "pause.circle.fill" : "play.circle.fill")
                            .font(.system(size: 50))
                            .foregroundColor(.white)
                    }
                }
            }
            .padding()
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.clear,
                        Color.black.opacity(0.8)
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
        }
        .transition(.opacity)
    }
    
    /// 播放进度条
    private var progressSlider: some View {
        VStack(spacing: 8) {
            // 进度条
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // 背景轨道
                    Rectangle()
                        .fill(Color.white.opacity(0.3))
                        .frame(height: 4)
                        .cornerRadius(2)
                    
                    // 播放进度
                    Rectangle()
                        .fill(Color.hexColor("40E0D0"))
                        .frame(width: geometry.size.width * (duration > 0 ? currentTime / duration : 0), height: 4)
                        .cornerRadius(2)
                    
                    // 拖拽指示器
                    Circle()
                        .fill(Color.white)
                        .frame(width: 16, height: 16)
                        .offset(x: geometry.size.width * (duration > 0 ? currentTime / duration : 0) - 8)
                }
            }
            .frame(height: 16)
            .gesture(
                DragGesture()
                    .onChanged { value in
                        isDragging = true
                        let progress = value.location.x / UIScreen.main.bounds.width
                        currentTime = max(0, min(duration, progress * duration))
                    }
                    .onEnded { _ in
                        isDragging = false
                        seekToTime(currentTime)
                    }
            )
            
            // 时间显示
            HStack {
                Text(formatTime(currentTime))
                    .font(.caption)
                    .foregroundColor(.white)
                
                Spacer()
                
                Text(formatTime(duration))
                    .font(.caption)
                    .foregroundColor(.white)
            }
        }
    }
    
    /// 加载视图
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: Color.hexColor("40E0D0")))
                .scaleEffect(1.5)
            
            Text("加载视频中...")
                .font(.headline)
                .foregroundColor(.white)
        }
    }
    
    // MARK: - 私有方法
    
    /// 加载视频
    private func loadVideo() {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!.path
        let fullPath = "\(documentsPath)/\(videoPath)"
        
        guard FileManager.default.fileExists(atPath: fullPath) else {
            #if DEBUG
            print("❌ 视频文件不存在: \(fullPath)")
            #endif
            return
        }
        
        let url = URL(fileURLWithPath: fullPath)
        let newPlayer = AVPlayer(url: url)
        self.player = newPlayer
        
        setupPlayer()
    }
    
    /// 设置播放器
    private func setupPlayer() {
        guard let player = player else { return }

        // 获取视频时长
        if let currentItem = player.currentItem {
            duration = currentItem.duration.seconds
        }

        // 添加时间观察器
        let interval = CMTime(seconds: 0.1, preferredTimescale: CMTimeScale(NSEC_PER_SEC))
        player.addPeriodicTimeObserver(forInterval: interval, queue: .main) { time in
            DispatchQueue.main.async {
                if !self.isDragging {
                    self.currentTime = time.seconds
                }
            }
        }

        // 监听播放状态变化
        NotificationCenter.default.addObserver(
            forName: .AVPlayerItemDidPlayToEndTime,
            object: player.currentItem,
            queue: .main
        ) { _ in
            self.isPlaying = false
        }

        // 自动隐藏控制界面
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            if isPlaying {
                withAnimation(.easeInOut(duration: 0.3)) {
                    showControls = false
                }
            }
        }
    }
    
    /// 清理播放器
    private func cleanupPlayer() {
        player?.pause()
        NotificationCenter.default.removeObserver(self)
        player = nil
    }
    
    /// 切换播放/暂停
    private func togglePlayPause() {
        guard let player = player else { return }
        
        if isPlaying {
            player.pause()
        } else {
            player.play()
        }
        
        isPlaying.toggle()
    }
    
    /// 跳转到指定时间
    private func seekToTime(_ time: Double) {
        guard let player = player else { return }
        let cmTime = CMTime(seconds: time, preferredTimescale: CMTimeScale(NSEC_PER_SEC))
        player.seek(to: cmTime)
    }
    
    /// 格式化时间显示
    private func formatTime(_ time: Double) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
}





#Preview {
    EAVideoPlayerView(videoPath: "sample_video.mp4")
}
