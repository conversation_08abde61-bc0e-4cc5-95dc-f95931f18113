import SwiftUI
import SwiftData

// MARK: - 星际能量展示组件（Phase 3 Day 6新增）

/// 星际能量主视图：用户等级、能量统计、成就徽章的综合展示
struct EAStellarEnergyView: View {
    
    // MARK: - 属性
    
    let user: EAUser
    @StateObject private var energyService: EAStellarEnergyService
    @Environment(\.sheetManager) private var sheetManager
    @State private var energyOverview: EAUserEnergyOverview?
    @State private var isLoading = true
    
    // MARK: - 初始化
    
    init(user: EAUser, repositoryContainer: EARepositoryContainer) {
        self.user = user
        self._energyService = StateObject(wrappedValue: EAStellarEnergyService(
            repositoryContainer: repositoryContainer,
            cacheManager: EAAICacheManager()
        ))
    }
    
    /// @deprecated 使用接收完整用户对象的初始化方法替代
    @available(*, deprecated, message: "Use init(user:repositoryContainer:) instead")
    init(userId: UUID, repositoryContainer: EARepositoryContainer) {
        // 这个初始化方法已被弃用，但为了兼容性暂时保留
        // 在实际使用中应该传递完整的用户对象
        fatalError("Deprecated initializer used. Please use init(user:repositoryContainer:) instead.")
    }
    
    // MARK: - 视图主体
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 星际等级卡片
                stellarLevelCard
                
                // 能量统计卡片
                energyStatisticsCard
                
                // 成就徽章区域
                achievementBadgesSection
                
                // 等级进度详情
                levelProgressDetail
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
        }
        .background(universalBackground)
        .navigationTitle("星际档案")
        .navigationBarTitleDisplayMode(.large)
        .task {
            await loadEnergyData()
        }
        .onReceive(energyService.$recentLevelUpEvent) { event in
            if let event = event {
                sheetManager.showLevelUpCelebration(
                    oldLevel: event.oldLevel,
                    newLevel: event.newLevel,
                    newTitle: event.newTitle
                )
            }
        }
        .onReceive(energyService.$recentBadgeEarned) { badge in
            if let badge = badge {
                sheetManager.showBadgeEarned(
                    title: badge.title,
                    description: badge.description
                )
            }
        }
    }
    
    // MARK: - 子视图组件
    
    /// 星际等级卡片
    private var stellarLevelCard: some View {
        VStack(spacing: 16) {
            // 等级标题
            HStack {
                Image(systemName: "star.circle.fill")
                    .font(.title2)
                    .foregroundStyle(.yellow)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("星际等级")
                        .font(.headline)
                        .foregroundStyle(.primary)
                    
                    if let overview = energyOverview {
                        Text("等级 \(overview.stellarLevel) - \(overview.explorerTitle)")
                            .font(.subheadline)
                            .foregroundStyle(.secondary)
                    }
                }
                
                Spacer()
            }
            
            // 等级进度环
            if let overview = energyOverview {
                levelProgressRing(overview: overview)
            } else {
                ProgressView()
                    .scaleEffect(1.5)
                    .frame(height: 120)
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(.purple.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    /// 等级进度环
    private func levelProgressRing(overview: EAUserEnergyOverview) -> some View {
        ZStack {
            // 背景圆环
            Circle()
                .stroke(.gray.opacity(0.3), lineWidth: 8)
                .frame(width: 120, height: 120)
            
            // 进度圆环
            Circle()
                .trim(from: 0, to: overview.progressToNextLevel)
                .stroke(
                    LinearGradient(
                        colors: [.blue, .purple, .pink],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    style: StrokeStyle(lineWidth: 8, lineCap: .round)
                )
                .frame(width: 120, height: 120)
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 1.0), value: overview.progressToNextLevel)
            
            // 中心内容
            VStack(spacing: 4) {
                Text("LV.\(overview.stellarLevel)")
                    .font(.title2.bold())
                    .foregroundStyle(.primary)
                
                Text("\(Int(overview.progressToNextLevel * 100))%")
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }
        }
    }
    
    /// 能量统计卡片
    private var energyStatisticsCard: some View {
        VStack(spacing: 16) {
            // 标题
            HStack {
                Image(systemName: "bolt.circle.fill")
                    .font(.title2)
                    .foregroundStyle(.yellow)
                
                Text("星际能量统计")
                    .font(.headline)
                    .foregroundStyle(.primary)
                
                Spacer()
            }
            
            // 统计网格
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                energyStatCard(
                    title: "总能量",
                    value: "\(energyOverview?.totalStellarEnergy ?? 0)",
                    icon: "bolt.fill",
                    color: .orange
                )
                
                energyStatCard(
                    title: "完成习惯",
                    value: "\(energyService.energyStatistics.habitsCompleted)",
                    icon: "checkmark.circle.fill",
                    color: .green
                )
                
                energyStatCard(
                    title: "社区分享",
                    value: "\(energyService.energyStatistics.postsShared)",
                    icon: "square.and.arrow.up.fill",
                    color: .blue
                )
                
                energyStatCard(
                    title: "获得徽章",
                    value: "\(energyService.energyStatistics.badgesEarned)",
                    icon: "medal.fill",
                    color: .purple
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(.blue.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    /// 能量统计卡片
    private func energyStatCard(title: String, value: String, icon: String, color: Color) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundStyle(color)
            
            Text(value)
                .font(.title2.bold())
                .foregroundStyle(.primary)
            
            Text(title)
                .font(.caption)
                .foregroundStyle(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(color.opacity(0.1))
        )
    }
    
    /// 成就徽章区域
    private var achievementBadgesSection: some View {
        VStack(spacing: 16) {
            // 标题
            HStack {
                Image(systemName: "trophy.circle.fill")
                    .font(.title2)
                    .foregroundStyle(.yellow)
                
                Text("成就徽章")
                    .font(.headline)
                    .foregroundStyle(.primary)
                
                Spacer()
                
                Button("查看全部") {
                    // TODO: 导航到完整徽章页面
                }
                .font(.caption)
                .foregroundStyle(.blue)
            }
            
            // 徽章网格
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 12) {
                ForEach(Array(EABadgeType.allCases.prefix(8)), id: \.self) { badgeType in
                    badgeCard(badgeType: badgeType, isEarned: hasEarnedBadge(badgeType))
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(.yellow.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    /// 徽章卡片
    private func badgeCard(badgeType: EABadgeType, isEarned: Bool) -> some View {
        VStack(spacing: 6) {
            ZStack {
                Circle()
                    .fill(isEarned ? .yellow.opacity(0.2) : .gray.opacity(0.1))
                    .frame(width: 44, height: 44)
                
                Image(systemName: getBadgeIcon(badgeType))
                    .font(.title3)
                    .foregroundStyle(isEarned ? .yellow : .gray)
            }
            
            Text(badgeType.title)
                .font(.caption2)
                .foregroundStyle(isEarned ? .primary : .secondary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
        }
        .opacity(isEarned ? 1.0 : 0.5)
        .scaleEffect(isEarned ? 1.0 : 0.9)
        .animation(.easeInOut(duration: 0.3), value: isEarned)
    }
    
    /// 等级进度详情
    private var levelProgressDetail: some View {
        VStack(spacing: 16) {
            // 标题
            HStack {
                Image(systemName: "chart.line.uptrend.xyaxis.circle.fill")
                    .font(.title2)
                    .foregroundStyle(.green)
                
                Text("升级进度")
                    .font(.headline)
                    .foregroundStyle(.primary)
                
                Spacer()
            }
            
            if let overview = energyOverview {
                VStack(spacing: 12) {
                    // 当前等级信息
                    HStack {
                        Text("当前等级")
                            .foregroundStyle(.secondary)
                        
                        Spacer()
                        
                        Text("等级 \(overview.stellarLevel)")
                            .font(.headline.weight(.semibold))
                            .foregroundStyle(.primary)
                    }
                    
                    // 进度条
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("升级进度")
                                .font(.caption)
                                .foregroundStyle(.secondary)
                            
                            Spacer()
                            
                            Text("\(Int(overview.progressToNextLevel * 100))%")
                                .font(.caption.bold())
                                .foregroundStyle(.blue)
                        }
                        
                        ProgressView(value: overview.progressToNextLevel)
                            .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                            .scaleEffect(y: 2)
                    }
                    
                    // 下一级要求
                    HStack {
                        Text("下一级要求")
                            .foregroundStyle(.secondary)
                        
                        Spacer()
                        
                        Text("\(overview.nextLevelRequirement) 星际能量")
                            .font(.headline.weight(.semibold))
                            .foregroundStyle(.primary)
                    }
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(.green.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    /// 宇宙背景
    private var universalBackground: some View {
        LinearGradient(
            colors: [
                Color.black.opacity(0.9),
                Color.purple.opacity(0.3),
                Color.blue.opacity(0.2)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }
    
    // MARK: - 辅助方法
    
    /// 加载能量数据
    private func loadEnergyData() async {
        isLoading = true
        defer { isLoading = false }
        
        energyOverview = await energyService.getUserEnergyOverview(for: user)
    }
    
    /// 获取徽章图标
    private func getBadgeIcon(_ badgeType: EABadgeType) -> String {
        switch badgeType {
        case .weekStreak, .monthStreak, .hundredDayStreak, .yearStreak:
            return "flame.fill"
        case .sharingNovice, .sharingExpert, .sharingMaster:
            return "square.and.arrow.up.fill"
        case .socialNovice, .socialExpert, .socialMaster:
            return "person.2.fill"
        case .noviceExplorer, .stellarTraveler, .cosmicNavigator:
            return "star.fill"
        }
    }
    
    /// 检查是否已获得徽章
    private func hasEarnedBadge(_ badgeType: EABadgeType) -> Bool {
        // TODO: 实现真实的徽章获得状态检查
        // 这里暂时返回随机值用于演示
        return [true, false].randomElement() ?? false
    }
}

// MARK: - 星际能量快速视图组件

/// 星际能量快速展示组件：用于在其他页面嵌入使用
struct EAStellarEnergyQuickView: View {
    
    let user: EAUser
    @StateObject private var energyService: EAStellarEnergyService
    @State private var energyOverview: EAUserEnergyOverview?
    
    init(user: EAUser, repositoryContainer: EARepositoryContainer) {
        self.user = user
        self._energyService = StateObject(wrappedValue: EAStellarEnergyService(
            repositoryContainer: repositoryContainer,
            cacheManager: EAAICacheManager()
        ))
    }
    
    /// @deprecated 使用接收完整用户对象的初始化方法替代
    @available(*, deprecated, message: "Use init(user:repositoryContainer:) instead")
    init(userId: UUID, repositoryContainer: EARepositoryContainer) {
        fatalError("Deprecated initializer used. Please use init(user:repositoryContainer:) instead.")
    }
    
    var body: some View {
        HStack(spacing: 16) {
            // 等级环
            ZStack {
                Circle()
                    .stroke(.gray.opacity(0.3), lineWidth: 4)
                    .frame(width: 50, height: 50)
                
                Circle()
                    .trim(from: 0, to: energyOverview?.progressToNextLevel ?? 0)
                    .stroke(.blue, style: StrokeStyle(lineWidth: 4, lineCap: .round))
                    .frame(width: 50, height: 50)
                    .rotationEffect(.degrees(-90))
                
                Text("LV.\(energyOverview?.stellarLevel ?? 1)")
                    .font(.caption.bold())
                    .foregroundStyle(.primary)
            }
            
            // 能量信息
            VStack(alignment: .leading, spacing: 4) {
                Text(energyOverview?.explorerTitle ?? "新手探索者")
                    .font(.headline)
                    .foregroundStyle(.primary)
                
                HStack(spacing: 8) {
                    Image(systemName: "bolt.fill")
                        .font(.caption)
                        .foregroundStyle(.yellow)
                    
                    Text("\(energyOverview?.totalStellarEnergy ?? 0) 星际能量")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
            }
            
            Spacer()
            
            // 进度指示
            VStack(alignment: .trailing, spacing: 4) {
                Text("\(Int((energyOverview?.progressToNextLevel ?? 0) * 100))%")
                    .font(.caption.bold())
                    .foregroundStyle(.blue)
                
                Text("到下一级")
                    .font(.caption2)
                    .foregroundStyle(.secondary)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(.blue.opacity(0.3), lineWidth: 1)
                )
        )
        .task {
            energyOverview = await energyService.getUserEnergyOverview(for: user)
        }
    }
}

// MARK: - SwiftUI预览

#Preview {
    @Previewable @State var sampleContainer = EARepositoryContainerImpl(modelContainer: try! EAAppSchema.createPreviewContainer())
    
    // 创建示例用户
    let sampleUser = EAUser(username: "示例用户", email: "<EMAIL>")
    
    NavigationView {
        EAStellarEnergyView(
            user: sampleUser,
            repositoryContainer: sampleContainer
        )
    }
}

#Preview("Quick View") {
    @Previewable @State var sampleContainer = EARepositoryContainerImpl(modelContainer: try! EAAppSchema.createPreviewContainer())
    
    // 创建示例用户
    let sampleUser = EAUser(username: "示例用户", email: "<EMAIL>")
    
    VStack {
        EAStellarEnergyQuickView(
            user: sampleUser,
            repositoryContainer: sampleContainer
        )
        .padding()
        
        Spacer()
    }
} 