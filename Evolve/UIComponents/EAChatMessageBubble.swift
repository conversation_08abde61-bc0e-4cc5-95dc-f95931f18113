import SwiftUI

/// 统一聊天消息气泡组件 - 纯SwiftUI现代化架构
/// 完全替代UIKit实现，解决状态同步和样式问题
struct EAChatMessageBubble: View {
    
    // MARK: - 属性
    
    let message: EAFriendMessage
    let isFromCurrentUser: Bool
    let avatarData: EAAvatarData?
    let showTimestamp: Bool
    var onImageTap: ((String) -> Void)?
    var getMediaURL: ((String) -> URL?)?  // 🔧 修复：通过回调获取URL，避免直接实例化服务
    
    // MARK: - 私有计算属性
    
    /// 消息内容是否为图片或视频
    private var isMediaContent: Bool {
        let content = message.content
        return EAChatMediaManager.isImageFile(content) || EAChatMediaManager.isVideoFile(content)
    }
    
    /// 气泡最大宽度 - 统一的自适应策略
    private var bubbleMaxWidth: CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        // 统一使用70%屏幕宽度，适配所有设备
        return screenWidth * 0.7
    }

    /// 🔑 简化的短文本检测 - 统一策略适配所有设备
    private var isShortText: Bool {
        let content = message.content
        let characterCount = content.count
        let hasLineBreaks = content.contains("\n")

        // 简化判断：20个字符以内且无换行符即为短文本
        return characterCount <= 20 && !hasLineBreaks
    }
    
    // MARK: - 主体
    
    var body: some View {
        VStack(spacing: 8) {
            // 时间戳（如果需要显示）
            if showTimestamp {
                timestampView
            }
            
            // 消息主体
            HStack(alignment: .bottom, spacing: 8) {
                if isFromCurrentUser {
                    // 发送方：头像在右侧
                    Spacer(minLength: 32)
                    messageBubble
                    avatarView
                } else {
                    // 接收方：头像在左侧
                    avatarView
                    messageBubble
                    Spacer(minLength: 32)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 4)
        }
    }
    
    // MARK: - 子组件
    
    /// 时间戳视图
    private var timestampView: some View {
        HStack {
            Spacer()
            Text(formatMessageTime(message.creationDate))
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.white.opacity(0.7))
                .padding(.horizontal, 12)
                .padding(.vertical, 4)
                .background(
                    Capsule()
                        .fill(Color.black.opacity(0.3))
                )
            Spacer()
        }
    }
    
    /// 头像视图
    private var avatarView: some View {
        EAAvatarView(
            avatarData: avatarData ?? EAAvatarData(type: .systemPerson),
            size: 40
        )
        .shadow(color: .black.opacity(0.2), radius: 2, x: 0, y: 1)
    }
    
    /// 消息气泡 - 🚨 修复：智能气泡尺寸自适应和文本对齐
    private var messageBubble: some View {
        Group {
            if isMediaContent {
                // 媒体内容 - 保持原有布局
                VStack(alignment: isFromCurrentUser ? .trailing : .leading, spacing: 0) {
                    mediaContentView
                }
                .padding(4)
                .background(bubbleBackground)
                .clipShape(RoundedRectangle(cornerRadius: 20))
                // 媒体内容使用固定最大宽度
                .frame(maxWidth: bubbleMaxWidth, alignment: isFromCurrentUser ? .trailing : .leading)
            } else {
                // 文本内容 - 简化的自适应布局
                textContentView
                    .padding(16)
                    .background(bubbleBackground)
                    .clipShape(RoundedRectangle(cornerRadius: 20))
                    // 🔑 统一的气泡宽度策略：短文本自适应，长文本限制最大宽度
                    .frame(maxWidth: isShortText ? nil : bubbleMaxWidth, alignment: isFromCurrentUser ? .trailing : .leading)
            }
        }
    }

    /// 🔑 简化的文本内容视图：让SwiftUI自然处理布局
    private var textContentView: some View {
        Text(message.content)
            .font(.system(size: 16, weight: .regular))
            .foregroundColor(.white)
            .multilineTextAlignment(textAlignment)
            .lineLimit(nil)
            // 🔑 简化布局：短文本使用fixedSize自适应，长文本自然换行
            .fixedSize(horizontal: isShortText, vertical: false)
    }

    /// 🔑 简化的文本对齐方式
    private var textAlignment: TextAlignment {
        // 统一使用leading对齐，避免复杂的对齐逻辑
        return .leading
    }
    
    /// 🔑 修复缩略图边框：媒体内容视图
    private var mediaContentView: some View {
        Group {
            let content = message.content
            if !content.isEmpty {
                if EAChatMediaManager.isImageFile(content) {
                    // 🔑 关键修复：使用LocalImageView替代AsyncImage，解决首次点击失败问题
                    if let mediaURL = getMediaURL?(content) {
                        LocalImageView(url: mediaURL)
                            .frame(width: 200, height: 150)
                            .cornerRadius(16)
                            .onTapGesture {
                                onImageTap?(content)
                            }
                    } else {
                        // URL获取失败的占位符
                        Rectangle()
                            .fill(Color.gray.opacity(0.3))
                            .frame(width: 200, height: 150)
                            .cornerRadius(16)
                            .overlay(
                                Image(systemName: "photo.fill")
                                    .font(.system(size: 30))
                                    .foregroundColor(.white.opacity(0.5))
                            )
                    }
                } else if EAChatMediaManager.isVideoFile(content) {
                    // 视频缩略图
                    ZStack {
                        Rectangle()
                            .fill(Color.black.opacity(0.7))
                            .frame(width: 200, height: 150)
                        
                        Image(systemName: "play.circle.fill")
                            .font(.system(size: 40))
                            .foregroundColor(.white)
                    }
                    .cornerRadius(16) // 🔑 关键修复：移除边框，只保留圆角
                    .onTapGesture {
                        onImageTap?(content)
                    }
                }
            }
        }
    }
    
    /// 气泡背景渐变
    private var bubbleBackground: some View {
        LinearGradient(
            gradient: Gradient(colors: isFromCurrentUser ? senderColors : receiverColors),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    /// 发送方颜色（青色渐变）
    private var senderColors: [Color] {
        [
            Color.hexColor("40BFB4"),
            Color.hexColor("40BFB4").opacity(0.8)
        ]
    }
    
    /// 接收方颜色（灰蓝渐变）
    private var receiverColors: [Color] {
        [
            Color.hexColor("2C3E50").opacity(0.95),
            Color.hexColor("34495E").opacity(0.9)
        ]
    }
    
    // MARK: - 私有方法
    
    /// 格式化消息时间
    private func formatMessageTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        let calendar = Calendar.current
        
        if calendar.isDateInToday(date) {
            formatter.dateFormat = "HH:mm"
        } else if calendar.isDateInYesterday(date) {
            formatter.dateFormat = "'昨天' HH:mm"
        } else {
            formatter.dateFormat = "MM/dd HH:mm"
        }
        
        return formatter.string(from: date)
    }
}

// MARK: - 本地图片加载视图

/// 🔑 新增：专门用于加载本地文件的图片视图，解决AsyncImage的本地文件加载问题
private struct LocalImageView: View {
    let url: URL
    @State private var image: UIImage?
    @State private var isLoading = true
    
    var body: some View {
        Group {
            if let image = image {
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } else if isLoading {
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
                    .overlay(
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    )
            } else {
                // 加载失败的占位符
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
                    .overlay(
                        Image(systemName: "exclamationmark.triangle")
                            .font(.system(size: 30))
                            .foregroundColor(.white.opacity(0.5))
                    )
            }
        }
        .onAppear {
            loadImage()
        }
    }
    
    private func loadImage() {
        // 在后台线程加载图片
        DispatchQueue.global(qos: .userInitiated).async {
            do {
                let data = try Data(contentsOf: url)
                if let loadedImage = UIImage(data: data) {
                    DispatchQueue.main.async {
                        self.image = loadedImage
                        self.isLoading = false
                    }
                } else {
                    DispatchQueue.main.async {
                        self.isLoading = false
                    }
                }
            } catch {
                DispatchQueue.main.async {
                    self.isLoading = false
                }
            }
        }
    }
}

// MARK: - 预览

#Preview("聊天气泡 - 发送方") {
    VStack(spacing: 16) {
        // 测试短文本自适应
        EAChatMessageBubble(
            message: EAFriendMessage(content: "你好"),
            isFromCurrentUser: true,
            avatarData: EAAvatarData(type: .systemPerson),
            showTimestamp: false
        )

        EAChatMessageBubble(
            message: EAFriendMessage(content: "短消息"),
            isFromCurrentUser: true,
            avatarData: EAAvatarData(type: .systemPerson),
            showTimestamp: false
        )

        // 测试长文本换行
        EAChatMessageBubble(
            message: EAFriendMessage(content: "这是一条来自发送方的测试消息，用来检验长文本的换行和对齐效果，应该能够正确地在iPhone和iPad上显示。"),
            isFromCurrentUser: true,
            avatarData: EAAvatarData(type: .systemPerson),
            showTimestamp: true
        )
    }
    .padding()
    .background(Color.hexColor("0C1B2A"))
}

#Preview("聊天气泡 - 接收方") {
    VStack(spacing: 16) {
        // 测试短文本自适应
        EAChatMessageBubble(
            message: EAFriendMessage(content: "好的"),
            isFromCurrentUser: false,
            avatarData: EAAvatarData(type: .systemStar),
            showTimestamp: false
        )

        EAChatMessageBubble(
            message: EAFriendMessage(content: "简短回复"),
            isFromCurrentUser: false,
            avatarData: EAAvatarData(type: .systemStar),
            showTimestamp: false
        )

        // 测试长文本换行
        EAChatMessageBubble(
            message: EAFriendMessage(content: "这是一条来自接收方的测试消息，同样用来检验长文本的换行和左对齐效果，确保在不同设备上表现一致。"),
            isFromCurrentUser: false,
            avatarData: EAAvatarData(type: .systemStar),
            showTimestamp: true
        )
    }
    .padding()
    .background(Color.hexColor("0C1B2A"))
}