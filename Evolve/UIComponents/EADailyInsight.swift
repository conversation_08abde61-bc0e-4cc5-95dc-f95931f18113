import SwiftUI

// MARK: - 每日洞察组件
struct EADailyInsight: View {
    // MARK: - Properties
    let insight: String
    let style: InsightStyle
    
    // MARK: - Animation State
    @State private var textOpacity: Double = 0.0
    @State private var sparkleRotation: Double = 0.0
    @State private var glowIntensity: Double = 0.5
    
    // MARK: - Insight Style
    enum InsightStyle {
        case card       // 卡片样式，适用于独立显示
        case banner     // 横幅样式，适用于页面顶部
        case minimal    // 最小样式，仅显示文字
    }
    
    // MARK: - Initialization
    init(
        insight: String,
        style: InsightStyle = .card
    ) {
        self.insight = insight
        self.style = style
    }
    
    // MARK: - Body
    var body: some View {
        switch style {
        case .card:
            cardView
        case .banner:
            bannerView
        case .minimal:
            minimalView
        }
    }
    
    // MARK: - Card View
    private var cardView: some View {
        VStack(spacing: 12) {
            // 智慧图标
            wisdomIcon
            
            // 洞察文字
            insightText
                .multilineTextAlignment(.center)
                .opacity(textOpacity)
        }
        .padding(20)
        .background(cardBackground)
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(
            color: Color.black.opacity(0.1),
            radius: 8,
            x: 0,
            y: 4
        )
        .onAppear {
            animateAppearance()
        }
    }
    
    // MARK: - Banner View
    private var bannerView: some View {
        HStack(spacing: 12) {
            // 智慧图标（小尺寸）
            Image(systemName: "sparkles")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(Color("PrimaryTurquoise"))
                .rotationEffect(.degrees(sparkleRotation))
            
            // 洞察文字
            insightText
                .font(.system(size: 14, weight: .medium))
                .opacity(textOpacity)
            
            Spacer()
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
        .background(bannerBackground)
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .onAppear {
            animateAppearance()
        }
    }
    
    // MARK: - Minimal View
    private var minimalView: some View {
        insightText
            .font(.system(size: 16, weight: .medium))
            .opacity(textOpacity)
            .onAppear {
                animateAppearance()
            }
    }
    
    // MARK: - Wisdom Icon
    private var wisdomIcon: some View {
        ZStack {
            // 发光背景
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color("PrimaryTurquoise").opacity(0.3),
                            Color("PrimaryTurquoise").opacity(0.1),
                            Color.clear
                        ]),
                        center: .center,
                        startRadius: 0,
                        endRadius: 30
                    )
                )
                .frame(width: 60, height: 60)
                .opacity(glowIntensity)
            
            // 智慧图标
            Image(systemName: "sparkles")
                .font(.system(size: 24, weight: .medium))
                .foregroundColor(Color("PrimaryTurquoise"))
                .rotationEffect(.degrees(sparkleRotation))
                .shadow(
                    color: Color("PrimaryTurquoise").opacity(0.5),
                    radius: 8,
                    x: 0,
                    y: 0
                )
        }
    }
    
    // MARK: - Insight Text
    private var insightText: some View {
        Text(insight)
            .font(.system(size: style == .card ? 16 : 14, weight: .medium))
            .foregroundColor(.white)
            .lineLimit(style == .minimal ? 1 : nil)
    }
    
    // MARK: - Card Background
    private var cardBackground: some View {
        ZStack {
            // 主背景
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.1),
                            Color.white.opacity(0.05)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            
            // 边框发光
            RoundedRectangle(cornerRadius: 16)
                .stroke(
                    Color("PrimaryTurquoise").opacity(0.2),
                    lineWidth: 1
                )
        }
    }
    
    // MARK: - Banner Background
    private var bannerBackground: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.white.opacity(0.08),
                        Color.white.opacity(0.04)
                    ]),
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(
                        Color("PrimaryTurquoise").opacity(0.15),
                        lineWidth: 1
                    )
            )
    }
    
    // MARK: - Animation Methods
    private func animateAppearance() {
        // 文字淡入动画
        withAnimation(.easeInOut(duration: 1.0).delay(0.3)) {
            textOpacity = 1.0
        }
        
        // 图标旋转动画
        withAnimation(
            Animation.linear(duration: 8.0)
                .repeatForever(autoreverses: false)
        ) {
            sparkleRotation = 360.0
        }
        
        // 发光强度动画
        withAnimation(
            Animation.easeInOut(duration: 3.0)
                .repeatForever(autoreverses: true)
        ) {
            glowIntensity = 1.0
        }
    }
}

// MARK: - Preview
#Preview("每日洞察 - 不同样式") {
    VStack(spacing: 30) {
        VStack(alignment: .leading, spacing: 15) {
            Text("卡片样式")
                .foregroundColor(.white)
                .font(.headline)
            
            EADailyInsight(
                insight: "每一个小计划都是通向更好自己的种子",
                style: .card
            )
        }
        
        VStack(alignment: .leading, spacing: 15) {
            Text("横幅样式")
                .foregroundColor(.white)
                .font(.headline)
            
            EADailyInsight(
                insight: "坚持不是一天的英雄，而是每天的选择",
                style: .banner
            )
        }
        
        VStack(alignment: .leading, spacing: 15) {
            Text("最小样式")
                .foregroundColor(.white)
                .font(.headline)
            
            EADailyInsight(
                insight: "今天的努力，是明天的礼物",
                style: .minimal
            )
        }
    }
    .padding(30)
    .background(
        EABackgroundView(style: .ecosystem)
    )
}

#Preview("每日洞察 - 智慧语录集") {
    ScrollView {
        VStack(spacing: 20) {
            Text("每日智慧语录")
                .foregroundColor(.white)
                .font(.title2)
                .fontWeight(.bold)
                .padding(.bottom, 10)
            
            ForEach(sampleInsights, id: \.self) { insight in
                EADailyInsight(
                    insight: insight,
                    style: .card
                )
            }
        }
        .padding(20)
    }
    .background(
        EABackgroundView(style: .ecosystem)
    )
}

// MARK: - Sample Data
private let sampleInsights = [
    "每一个小计划都是通向更好自己的种子",
    "坚持不是一天的英雄，而是每天的选择",
    "今天的努力，是明天的礼物",
    "计划的力量在于重复，而非完美",
    "每一次坚持，都在为未来的你投资",
    "改变从微小的行动开始，伟大从日常的坚持诞生",
    "你的计划塑造你的未来，你的选择决定你的命运",
    "不是因为看到希望才坚持，而是因为坚持才看到希望"
] 