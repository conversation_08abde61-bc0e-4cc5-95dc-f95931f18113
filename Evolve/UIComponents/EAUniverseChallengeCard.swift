import SwiftUI

// MARK: - 宇宙挑战卡片组件
struct EAUniverseChallengeCard: View {
    
    // MARK: - 属性
    
    let challenge: EAUniverseChallenge
    let isParticipating: Bool
    let participation: EAUniverseChallengeParticipation?
    let onTap: () -> Void
    let onJoin: () -> Void
    
    // MARK: - 状态
    
    @State private var isPressed = false
    
    // MARK: - 视图主体
    
    var body: some View {
        Button(action: onTap) {
            ZStack {
                // 背景容器
                cardBackground
                
                VStack(spacing: 0) {
                    // 卡片头部
                    cardHeader
                    
                    // 卡片内容
                    cardContent
                    
                    // 卡片底部
                    cardFooter
                }
                .padding(16)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .onLongPressGesture(minimumDuration: 0) {
            // 处理按压效果
        } onPressingChanged: { pressing in
            isPressed = pressing
        }
    }
    
    // MARK: - 卡片背景
    
    private var cardBackground: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(
                LinearGradient(
                    colors: [
                        difficultyColor.opacity(0.2),
                        difficultyColor.opacity(0.05),
                        Color.clear
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .overlay {
                RoundedRectangle(cornerRadius: 16)
                    .stroke(
                        LinearGradient(
                            colors: [
                                difficultyColor.opacity(0.6),
                                difficultyColor.opacity(0.3)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            }
            .background {
                // 光晕效果
                RoundedRectangle(cornerRadius: 16)
                    .fill(difficultyColor.opacity(0.1))
                    .blur(radius: 8)
                    .offset(x: 0, y: 2)
            }
    }
    
    // MARK: - 卡片头部
    
    private var cardHeader: some View {
        HStack {
            // 挑战徽章
            challengeBadge
            
            Spacer()
            
            // 难度标签
            difficultyBadge
        }
        .padding(.bottom, 12)
    }
    
    // MARK: - 挑战徽章
    
    private var challengeBadge: some View {
        HStack(spacing: 8) {
            Image(systemName: challenge.badgeIcon)
                .font(.title2)
                .foregroundColor(difficultyColor)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(challenge.universeRegionDescription)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                
                Text(challenge.challengeTypeDescription)
                    .font(.caption2)
                    .foregroundColor(.white.opacity(0.5))
            }
        }
    }
    
    // MARK: - 难度标签
    
    private var difficultyBadge: some View {
        HStack(spacing: 4) {
            ForEach(0..<challenge.cosmicDifficulty, id: \.self) { _ in
                Image(systemName: "star.fill")
                    .font(.caption2)
                    .foregroundColor(difficultyColor)
            }
            
            Text(challenge.difficultyDescription)
                .font(.caption2)
                .foregroundColor(.white.opacity(0.8))
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            Capsule()
                .fill(difficultyColor.opacity(0.2))
                .overlay(
                    Capsule()
                        .stroke(difficultyColor.opacity(0.4), lineWidth: 0.5)
                )
        )
    }
    
    // MARK: - 卡片内容
    
    private var cardContent: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 挑战标题
            Text(challenge.title)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .multilineTextAlignment(.leading)
            
            // 挑战描述
            Text(challenge.challengeDescription)
                .font(.subheadline)
                .foregroundColor(.white.opacity(0.8))
                .lineLimit(2)
                .multilineTextAlignment(.leading)
            
            // 进度信息
            if isParticipating, let participation = participation {
                challengeProgress(participation)
            }
            
            // 挑战统计
            challengeStats
        }
    }
    
    // MARK: - 挑战进度
    
    private func challengeProgress(_ participation: EAUniverseChallengeParticipation) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("当前进度")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                
                Spacer()
                
                Text("\(participation.currentProgress)/\(challenge.targetValue)")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(difficultyColor)
            }
            
            // 进度条
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // 背景
                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color.white.opacity(0.2))
                        .frame(height: 6)
                    
                    // 进度
                    RoundedRectangle(cornerRadius: 4)
                        .fill(
                            LinearGradient(
                                colors: [difficultyColor, difficultyColor.opacity(0.7)],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(
                            width: geometry.size.width * progressPercentage(participation),
                            height: 6
                        )
                        .animation(.easeInOut(duration: 0.3), value: progressPercentage(participation))
                }
            }
            .frame(height: 6)
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.black.opacity(0.3))
        )
    }
    
    // MARK: - 挑战统计
    
    private var challengeStats: some View {
        HStack(spacing: 16) {
            // 参与人数
            statItem(
                icon: "person.2.fill",
                value: "\(challenge.participantCount)",
                label: "参与者"
            )
            
            // 星际能量奖励
            statItem(
                icon: "sparkles",
                value: "\(challenge.calculateBaseStellarReward())",
                label: "星际能量"
            )
            
            // 剩余时间
            statItem(
                icon: "clock.fill",
                value: timeRemaining,
                label: "剩余时间"
            )
        }
    }
    
    // MARK: - 统计项
    
    private func statItem(icon: String, value: String, label: String) -> some View {
        VStack(spacing: 4) {
            HStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.caption2)
                    .foregroundColor(difficultyColor)
                
                Text(value)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
            }
            
            Text(label)
                .font(.caption2)
                .foregroundColor(.white.opacity(0.6))
        }
    }
    
    // MARK: - 卡片底部
    
    private var cardFooter: some View {
        HStack {
            // 挑战状态
            challengeStatusBadge
            
            Spacer()
            
            // 操作按钮
            if !isParticipating && challenge.canParticipate() {
                joinButton
            } else if isParticipating {
                participatingBadge
            } else {
                unavailableBadge
            }
        }
        .padding(.top, 16)
    }
    
    // MARK: - 状态标签
    
    private var challengeStatusBadge: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(statusColor)
                .frame(width: 6, height: 6)
            
            Text(challenge.status.capitalized)
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
        }
    }
    
    // MARK: - 参与按钮
    
    private var joinButton: some View {
        Button(action: {
            onJoin()
        }) {
            HStack(spacing: 6) {
                Image(systemName: "plus.circle.fill")
                    .font(.caption)
                
                Text("参与挑战")
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                Capsule()
                    .fill(difficultyColor.opacity(0.8))
            )
            .foregroundColor(.white)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - 已参与标签
    
    private var participatingBadge: some View {
        HStack(spacing: 6) {
            Image(systemName: "checkmark.circle.fill")
                .font(.caption)
                .foregroundColor(.green)
            
            Text("已参与")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.green)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(
            Capsule()
                .fill(Color.green.opacity(0.2))
                .overlay(
                    Capsule()
                        .stroke(Color.green.opacity(0.4), lineWidth: 0.5)
                )
        )
    }
    
    // MARK: - 不可用标签
    
    private var unavailableBadge: some View {
        HStack(spacing: 6) {
            Image(systemName: "exclamationmark.circle")
                .font(.caption)
                .foregroundColor(.orange)
            
            Text("名额已满")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.orange)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(
            Capsule()
                .fill(Color.orange.opacity(0.2))
                .overlay(
                    Capsule()
                        .stroke(Color.orange.opacity(0.4), lineWidth: 0.5)
                )
        )
    }
    
    // MARK: - 计算属性
    
    private var difficultyColor: Color {
        switch challenge.difficulty {
        case "easy":
            return .green
        case "normal":
            return .blue
        case "hard":
            return .purple
        case "extreme":
            return .red
        default:
            return .gray
        }
    }
    
    private var statusColor: Color {
        switch challenge.status {
        case "upcoming":
            return .yellow
        case "active":
            return .green
        case "completed", "ended":
            return .blue
        case "expired":
            return .red
        default:
            return .gray
        }
    }
    
    private var timeRemaining: String {
        let remaining = challenge.remainingTime
        let days = Int(remaining) / 86400
        let hours = Int(remaining) % 86400 / 3600
        
        if days > 0 {
            return "\(days)天"
        } else if hours > 0 {
            return "\(hours)小时"
        } else {
            return "即将结束"
        }
    }
    
    private func progressPercentage(_ participation: EAUniverseChallengeParticipation) -> Double {
        guard challenge.targetValue > 0 else { return 0 }
        return min(Double(participation.currentProgress) / Double(challenge.targetValue), 1.0)
    }
}

// MARK: - 预览
#Preview {
    VStack(spacing: 20) {
        // 简单挑战
        EAUniverseChallengeCard(
            challenge: sampleEasyChallenge,
            isParticipating: false,
            participation: nil,
            onTap: {},
            onJoin: {}
        )
        
        // 已参与的困难挑战
        EAUniverseChallengeCard(
            challenge: sampleHardChallenge,
            isParticipating: true,
            participation: sampleParticipation,
            onTap: {},
            onJoin: {}
        )
    }
    .padding()
    .background(Color.black)
    .preferredColorScheme(.dark)
}

// MARK: - 预览数据
private let sampleEasyChallenge: EAUniverseChallenge = {
    let challenge = EAUniverseChallenge(
        title: "银河系早起探索者",
        challengeDescription: "连续7天早起完成晨间习惯，探索银河系的奥秘",
        targetHabitCategory: "morning_routine",
        startDate: Date(),
        endDate: Calendar.current.date(byAdding: .day, value: 7, to: Date()) ?? Date(),
        stellarReward: 500
    )
    challenge.difficulty = "easy"
    challenge.cosmicDifficulty = 2
    challenge.participantCount = 42
    return challenge
}()

private let sampleHardChallenge: EAUniverseChallenge = {
    let challenge = EAUniverseChallenge(
        title: "宇宙战士训练营",
        challengeDescription: "30天健身挑战，锻造宇宙战士体魄",
        targetHabitCategory: "fitness",
        startDate: Date(),
        endDate: Calendar.current.date(byAdding: .day, value: 30, to: Date()) ?? Date(),
        stellarReward: 1500
    )
    challenge.difficulty = "extreme"
    challenge.cosmicDifficulty = 5
    challenge.participantCount = 15
    return challenge
}()

private let sampleParticipation: EAUniverseChallengeParticipation = {
    let participation = EAUniverseChallengeParticipation()
    participation.currentProgress = 12
    return participation
}() 