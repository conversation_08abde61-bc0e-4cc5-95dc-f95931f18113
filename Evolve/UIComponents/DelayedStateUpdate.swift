import SwiftUI
import Combine

/// 一个视图修饰符，用于在特定值变化时，延迟一小段时间后执行一个操作。
/// 这对于避开与系统动画（如键盘）的布局冲突非常有用。
///
/// 🎯 设计目标：解决iPad键盘弹出时的约束冲突问题
/// 🔧 核心原理：通过微小延迟将我们的布局更新与系统键盘动画解耦
/// 📱 适用场景：聊天输入框、表单输入等需要与键盘交互的组件
///
/// 严格遵循Evolve项目AI开发审查规则和iOS开发规范
struct OnChangeDelayedModifier<V: Equatable>: ViewModifier {
    let value: V
    let action: (V) -> Void

    // 使用@State来持有上一次的值
    @State private var oldValue: V

    init(value: V, action: @escaping (V) -> Void) {
        self.value = value
        self.action = action
        self._oldValue = State(initialValue: value)
    }

    func body(content: Content) -> some View {
        content
            .onChange(of: value) { oldValue, newValue in
                // 仅当值真正发生变化时才触发
                guard newValue != self.oldValue else { return }

                // 🔑 关键修复：使用一个极短的延迟（一个主运行循环周期）
                // 这确保我们的布局更新发生在系统键盘动画之后，避开约束冲突
                DispatchQueue.main.async {
                    action(newValue)
                    self.oldValue = newValue // 在执行动作后更新旧值
                }
            }
    }
}

extension View {
    /// 监听一个值的变化，并在一个主运行循环周期后执行操作。
    /// 
    /// 🎯 主要用途：解决iPad键盘约束冲突问题
    /// 
    /// - Parameters:
    ///   - value: 要监听的值
    ///   - action: 值变化时要执行的操作
    /// - Returns: 应用了延迟更新修饰符的视图
    ///
    /// 使用示例：
    /// ```swift
    /// TextEditor(text: $internalText)
    ///     .onChangeDelayed(of: internalText) { newValue in
    ///         self.messageText = newValue
    ///     }
    /// ```
    func onChangeDelayed<V: Equatable>(of value: V, perform action: @escaping (V) -> Void) -> some View {
        self.modifier(OnChangeDelayedModifier(value: value, action: action))
    }
}

// MARK: - SwiftUI预览
#if DEBUG
struct OnChangeDelayedModifier_Previews: PreviewProvider {
    @State static private var text = ""
    @State static private var delayedText = ""
    
    static var previews: some View {
        VStack(spacing: 20) {
            Text("延迟更新修饰符测试")
                .font(.headline)
                .foregroundColor(.white)
            
            VStack(alignment: .leading, spacing: 8) {
                Text("实时输入:")
                    .foregroundColor(.gray)
                TextField("输入测试文本", text: $text)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .onChangeDelayed(of: text) { newValue in
                        delayedText = newValue
                    }
                
                Text("延迟更新结果:")
                    .foregroundColor(.gray)
                Text(delayedText.isEmpty ? "等待输入..." : delayedText)
                    .foregroundColor(.white)
                    .padding()
                    .background(Color.blue.opacity(0.2))
                    .cornerRadius(8)
            }
            
            Spacer()
        }
        .padding()
        .background(Color.black)
    }
}
#endif
