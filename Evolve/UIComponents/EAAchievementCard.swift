import SwiftUI

/// 成就卡片组件
/// 用于展示单个成就徽章，支持已解锁和未解锁状态的不同样式
struct EAAchievementCard: View {
    // MARK: - Properties
    let achievement: EAAchievement
    let onTap: () -> Void
    
    // MARK: - Body
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // 徽章图标
                achievementIcon
                
                // 徽章名称
                Text(achievement.name)
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(achievement.isUnlocked ? .white : Color.white.opacity(0.5))
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
                
                // 进度条（仅对未完成的已解锁成就显示）
                if achievement.isUnlocked && !achievement.isCompleted {
                    progressBar
                }
                
                // 解锁状态指示
                statusIndicator
            }
            .padding(16)
            .frame(maxWidth: .infinity)
            .background(cardBackground)
            .overlay(cardBorder)
            .scaleEffect(achievement.isUnlocked ? 1.0 : 0.95)
            .opacity(achievement.isUnlocked ? 1.0 : 0.7)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Achievement Icon
    private var achievementIcon: some View {
        ZStack {
            // 背景圆圈
            Circle()
                .fill(iconBackgroundGradient)
                .frame(width: 60, height: 60)
                .shadow(
                    color: achievement.isUnlocked ? 
                        Color.hexColor(achievement.iconColors.first ?? "#FFD700").opacity(0.4) : 
                        Color.clear,
                    radius: achievement.isUnlocked ? 8 : 0,
                    x: 0,
                    y: 0
                )
            
            // 徽章图标
            Text(achievement.icon)
                .font(.system(size: 28))
                .opacity(achievement.isUnlocked ? 1.0 : 0.4)
            
            // 锁定图标（未解锁时显示）
            if !achievement.isUnlocked {
                ZStack {
                    Circle()
                        .fill(Color.black.opacity(0.6))
                        .frame(width: 60, height: 60)
                    
                    Image(systemName: "lock.fill")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(Color.white.opacity(0.8))
                }
            }
        }
    }
    
    // MARK: - Progress Bar
    private var progressBar: some View {
        VStack(spacing: 4) {
            // 进度条
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // 背景
                    RoundedRectangle(cornerRadius: 2)
                        .fill(Color.white.opacity(0.2))
                        .frame(height: 4)
                    
                    // 进度
                    RoundedRectangle(cornerRadius: 2)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.hexColor(achievement.iconColors.first ?? "#FFD700"),
                                    Color.hexColor(achievement.iconColors.last ?? "#FFA500")
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(width: geometry.size.width * achievement.progressPercentage, height: 4)
                }
            }
            .frame(height: 4)
            
            // 进度文字
            Text("\(achievement.progress)/\(achievement.maxProgress)")
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(Color.white.opacity(0.7))
        }
    }
    
    // MARK: - Status Indicator
    private var statusIndicator: some View {
        Group {
            if achievement.isUnlocked {
                if achievement.isCompleted {
                    // 已完成
                    HStack(spacing: 4) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(Color.hexColor("32CD32"))
                        
                        Text("已获得")
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(Color.hexColor("32CD32"))
                    }
                } else {
                    // 进行中
                    HStack(spacing: 4) {
                        Image(systemName: "clock.fill")
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(Color.hexColor("FFD700"))
                        
                        Text("进行中")
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(Color.hexColor("FFD700"))
                    }
                }
            } else {
                // 未解锁
                HStack(spacing: 4) {
                    Image(systemName: "lock.fill")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(Color.white.opacity(0.5))
                    
                    Text("未解锁")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(Color.white.opacity(0.5))
                }
            }
        }
    }
    
    // MARK: - Computed Properties
    private var iconBackgroundGradient: LinearGradient {
        if achievement.isUnlocked {
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color.hexColor(achievement.iconColors.first ?? "#FFD700").opacity(0.8),
                    Color.hexColor(achievement.iconColors.last ?? "#FFA500").opacity(0.6)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        } else {
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color.white.opacity(0.1),
                    Color.white.opacity(0.05)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        }
    }
    
    private var cardBackground: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(
                achievement.isUnlocked ?
                Color.white.opacity(0.08) :
                Color.white.opacity(0.03)
            )
    }
    
    private var cardBorder: some View {
        RoundedRectangle(cornerRadius: 16)
            .stroke(
                achievement.isUnlocked ?
                Color.hexColor(achievement.iconColors.first ?? "#FFD700").opacity(0.3) :
                Color.white.opacity(0.1),
                lineWidth: 1
            )
    }
}

// MARK: - Preview
#Preview("成就卡片展示") {
    VStack(spacing: 20) {
        // 已解锁成就
        EAAchievementCard(
            achievement: EAAchievement(
        id: "first_light",
        name: "初光者",
        description: "完成第一个习惯",
        icon: "✨",
        iconColors: ["#FFD700", "#FFA500"],
        isUnlocked: true,
        progress: 1,
        maxProgress: 1,
        story: "每一段伟大的旅程都始于第一步。",
        unlockedDate: Date()
    )
        ) {
            // 成就点击处理 - 实际使用时会有具体逻辑
        }
        
        // 部分解锁成就
        EAAchievementCard(
            achievement: EAAchievement(
        id: "sprout_power",
        name: "萌芽之力",
        description: "连续完成任意习惯7天",
        icon: "🌿",
        iconColors: ["#32CD32", "#008B8B"],
        isUnlocked: true,
        progress: 3,
        maxProgress: 7,
        story: "每一株参天大树，都始于一颗微小的种子破土而出。",
        unlockedDate: nil
    )
        ) {
            // 成就点击处理 - 实际使用时会有具体逻辑
        }
        
        // 未解锁成就
        EAAchievementCard(
            achievement: EAAchievement(
        id: "mystery_badge",
        name: "神秘徽章",
        description: "???",
        icon: "❓",
        iconColors: ["#808080", "#696969"],
        isUnlocked: false,
        progress: 0,
        maxProgress: 1,
        story: "这是一个神秘的徽章，它的获得条件还未被发现...",
        unlockedDate: nil
    )
        ) {
            // 成就点击处理 - 实际使用时会有具体逻辑
        }
    }
    .padding()
    .background(Color.black)
} 