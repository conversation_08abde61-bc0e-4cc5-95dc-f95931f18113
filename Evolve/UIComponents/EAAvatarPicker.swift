import SwiftUI
import PhotosUI

/// 头像选择器
/// 支持选择系统预设头像和从相册上传自定义头像
struct EAAvatarPicker: View {
    @Environment(\.dismiss) private var dismiss
    
    // 绑定的头像数据（传统模式）
    @Binding var avatarData: EAAvatarData?
    
    // 回调模式的完成处理器
    private var onAvatarSelected: ((EAAvatarData?) -> Void)?
    
    // 当前选中的头像类型
    @State private var selectedAvatarType: EAAvatarType = .systemPerson
    
    // 相册选择器
    @State private var selectedPhotoItem: PhotosPickerItem?
    @State private var customAvatarImage: UIImage?
    
    // 加载状态
    @State private var isLoadingImage = false
    
    // 网格布局配置
    private let gridColumns = Array(repeating: GridItem(.flexible(), spacing: 16), count: 4)
    
    // MARK: - 构造函数
    
    /// 传统绑定模式构造函数
    init(avatarData: Binding<EAAvatarData?>) {
        self._avatarData = avatarData
        self.onAvatarSelected = nil
    }
    
    /// 回调模式构造函数（新增）
    init(avatarData: EAAvatarData?, onAvatarSelected: @escaping (EAAvatarData?) -> Void) {
        self._avatarData = .constant(avatarData)
        self.onAvatarSelected = onAvatarSelected
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // 背景
                EABackgroundView()
                    .ignoresSafeArea(.all)
                
                // 主要内容
                ScrollView {
                    VStack(spacing: 32) {
                        // 顶部间距
                        Spacer(minLength: 16)
                        
                        // 当前头像预览
                        currentAvatarPreview
                        
                        // 自定义头像选择
                        customAvatarSection
                        
                        // 系统头像选择
                        systemAvatarSection
                        
                        // 底部间距
                        Spacer(minLength: 32)
                    }
                    .padding(.horizontal, 16)
                }
            }
            .navigationTitle("选择头像")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                    .foregroundColor(.white)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        saveAvatarSelection()
                        dismiss()
                    }
                    .foregroundColor(Color.accentColor)
                    .fontWeight(.semibold)
                }
            }
        }
        .onAppear {
            // 初始化当前选中的头像类型
            if let currentAvatar = avatarData {
                selectedAvatarType = currentAvatar.type
                if currentAvatar.type == .custom, let imageData = currentAvatar.customImageData {
                    customAvatarImage = UIImage(data: imageData)
                }
            }
        }
        .onChange(of: selectedPhotoItem) { _, newItem in
            Task {
                await loadSelectedPhoto(newItem)
            }
        }
    }
    
    // MARK: - Current Avatar Preview
    private var currentAvatarPreview: some View {
        VStack(spacing: 16) {
            Text("当前头像")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)
            
            // 头像预览
            ZStack {
                Circle()
                    .fill(selectedAvatarType.avatarColor.opacity(0.2))
                    .frame(width: 120, height: 120)
                    .overlay(
                        Circle()
                            .stroke(selectedAvatarType.avatarColor, lineWidth: 3)
                    )
                
                if selectedAvatarType == .custom, let customImage = customAvatarImage {
                    // 自定义头像
                    Image(uiImage: customImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 110, height: 110)
                        .clipShape(Circle())
                } else {
                    // 系统头像
                    Image(systemName: selectedAvatarType.rawValue)
                        .font(.system(size: 50, weight: .medium))
                        .foregroundColor(selectedAvatarType.avatarColor)
                }
            }
            .shadow(
                color: selectedAvatarType.avatarColor.opacity(0.3),
                radius: 20,
                x: 0,
                y: 0
            )
            
            Text(selectedAvatarType.displayName)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(Color.white.opacity(0.8))
        }
    }
    
    // MARK: - Custom Avatar Section
    private var customAvatarSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("自定义头像")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            // 相册选择按钮
            PhotosPicker(
                selection: $selectedPhotoItem,
                matching: .images,
                photoLibrary: .shared()
            ) {
                HStack(spacing: 12) {
                    Image(systemName: "photo.on.rectangle")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(Color.accentColor)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("从相册选择")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.white)
                        
                        Text("选择一张照片作为头像")
                            .font(.system(size: 14, weight: .regular))
                            .foregroundColor(Color.white.opacity(0.7))
                    }
                    
                    Spacer()
                    
                    if isLoadingImage {
                        ProgressView()
                            .scaleEffect(0.8)
                            .tint(Color.accentColor)
                    } else {
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(Color.white.opacity(0.5))
                    }
                }
                .padding(16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.white.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.white.opacity(0.2), lineWidth: 1)
                        )
                )
            }
            .disabled(isLoadingImage)
        }
    }
    
    // MARK: - System Avatar Section
    private var systemAvatarSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("系统头像")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            // 系统头像网格
            LazyVGrid(columns: gridColumns, spacing: 16) {
                ForEach(EAAvatarType.systemAvatars, id: \.rawValue) { avatarType in
                    systemAvatarItem(avatarType)
                }
            }
        }
    }
    
    // MARK: - System Avatar Item
    private func systemAvatarItem(_ avatarType: EAAvatarType) -> some View {
        Button(action: {
            selectedAvatarType = avatarType
            customAvatarImage = nil // 清除自定义头像
            
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        }) {
            ZStack {
                Circle()
                    .fill(avatarType.avatarColor.opacity(0.2))
                    .frame(width: 70, height: 70)
                    .overlay(
                        Circle()
                            .stroke(
                                selectedAvatarType == avatarType ? avatarType.avatarColor : Color.white.opacity(0.3),
                                lineWidth: selectedAvatarType == avatarType ? 3 : 1
                            )
                    )
                
                Image(systemName: avatarType.rawValue)
                    .font(.system(size: 28, weight: .medium))
                    .foregroundColor(
                        selectedAvatarType == avatarType ? avatarType.avatarColor : Color.white.opacity(0.7)
                    )
                
                // 选中指示器
                if selectedAvatarType == avatarType {
                    Circle()
                        .fill(avatarType.avatarColor)
                        .frame(width: 20, height: 20)
                        .overlay(
                            Image(systemName: "checkmark")
                                .font(.system(size: 12, weight: .bold))
                                .foregroundColor(.white)
                        )
                        .offset(x: 25, y: -25)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Helper Methods
    
    /// 加载选中的照片
    private func loadSelectedPhoto(_ item: PhotosPickerItem?) async {
        guard let item = item else { return }
        
        await MainActor.run {
            isLoadingImage = true
        }
        
        do {
            if let data = try await item.loadTransferable(type: Data.self),
               let uiImage = UIImage(data: data) {
                
                // 压缩和调整图片尺寸
                let processedImage = await processImage(uiImage)
                
                await MainActor.run {
                    customAvatarImage = processedImage
                    selectedAvatarType = .custom
                    isLoadingImage = false
                }
            }
        } catch {
            // 静默处理图片加载失败
        }
    }
    
    /// 处理图片（压缩和调整尺寸）
    private func processImage(_ image: UIImage) async -> UIImage {
        return await Task.detached {
            let targetSize = CGSize(width: 200, height: 200)
            
            // 调整图片尺寸
            let renderer = UIGraphicsImageRenderer(size: targetSize)
            let resizedImage = renderer.image { _ in
                image.draw(in: CGRect(origin: .zero, size: targetSize))
            }
            
            // 压缩图片
            guard let compressedData = resizedImage.jpegData(compressionQuality: 0.8),
                  let compressedImage = UIImage(data: compressedData) else {
                return image
            }
            
            return compressedImage
        }.value
    }
    
    /// 保存头像选择
    private func saveAvatarSelection() {
        let newAvatarData: EAAvatarData?
        
        if selectedAvatarType == .custom {
            // 自定义头像
            if let customImage = customAvatarImage,
               let imageData = customImage.jpegData(compressionQuality: 0.8) {
                newAvatarData = EAAvatarData(type: .custom, customImageData: imageData)
            } else {
                newAvatarData = nil
            }
        } else {
            // 系统头像
            newAvatarData = EAAvatarData(type: selectedAvatarType, customImageData: nil)
        }
        
        // 根据模式更新数据
        if let onAvatarSelected = onAvatarSelected {
            // 回调模式：调用回调函数
            onAvatarSelected(newAvatarData)
        } else {
            // 传统绑定模式：更新绑定值
            avatarData = newAvatarData
        }
    }
}

// MARK: - Preview
#Preview {
    @Previewable @State var avatarData: EAAvatarData? = EAAvatarData(type: .systemPerson)
    
    return EAAvatarPicker(avatarData: $avatarData)
} 