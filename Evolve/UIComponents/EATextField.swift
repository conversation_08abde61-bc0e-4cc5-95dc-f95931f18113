import SwiftUI

// MARK: - 输入框类型枚举

enum EATextFieldType {
    case text          // 普通文本
    case email         // 邮箱
    case phone         // 手机号
    case username      // 用户名
    case password      // 密码（新密码）
    case confirmPassword // 确认密码
    case secureText    // 安全文本
    case number        // 数字
    case search        // 搜索
}

enum EATextFieldState {
    case normal        // 普通状态
    case focused       // 聚焦状态
    case error         // 错误状态
    case disabled      // 禁用状态
    case success       // 成功状态
}

// MARK: - 输入框组件

struct EATextField: View {
    // 绑定值
    @Binding var text: String
    
    // 必需参数
    let placeholder: String
    
    // 可选参数
    let type: EATextFieldType
    let isRequired: Bool
    let errorMessage: String?
    let helperText: String?
    let leftIcon: String?
    let rightIcon: String?
    let onRightIconTap: (() -> Void)?
    let maxLength: Int?
    let validator: ((String) -> Bool)?
    
    // 状态
    @State private var isSecureTextVisible = false
    @FocusState private var isFocused: Bool
    @State private var validationState: EATextFieldState = .normal

    // 🔑 新增：防抖处理状态
    @State private var debounceTimer: Timer?
    
    // 初始化方法
    init(
        text: Binding<String>,
        placeholder: String,
        type: EATextFieldType = .text,
        isRequired: Bool = false,
        errorMessage: String? = nil,
        helperText: String? = nil,
        leftIcon: String? = nil,
        rightIcon: String? = nil,
        onRightIconTap: (() -> Void)? = nil,
        maxLength: Int? = nil,
        validator: ((String) -> Bool)? = nil
    ) {
        self._text = text
        self.placeholder = placeholder
        self.type = type
        self.isRequired = isRequired
        self.errorMessage = errorMessage
        self.helperText = helperText
        self.leftIcon = leftIcon
        self.rightIcon = rightIcon
        self.onRightIconTap = onRightIconTap
        self.maxLength = maxLength
        self.validator = validator
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 输入框容器
            HStack(spacing: 12) {
                // 左侧图标
                if let leftIcon = leftIcon {
                    Image(systemName: leftIcon)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(iconColor)
                        .frame(width: 20, height: 20)
                }
                
                // 输入框
                ZStack(alignment: .leading) {
                    // 自定义占位符
                    if text.isEmpty {
                        Text(placeholder)
                            .font(.system(size: 15, weight: .regular))
                            .foregroundColor(Color.hexColor("94a3b8")) // 原型要求：Slate 400
                    }
                    
                    // 实际输入框
                    Group {
                        if type == .password || type == .confirmPassword || type == .secureText {
                            if isSecureTextVisible {
                                TextField("", text: $text)
                            } else {
                                SecureField("", text: $text)
                            }
                        } else {
                            TextField("", text: $text)
                        }
                    }
                    .font(.system(size: 15, weight: .regular)) // 原型要求：0.95rem ≈ 15px
                    .foregroundColor(textColor)
                    .focused($isFocused)
                    .keyboardType(keyboardType)
                    .textContentType(textContentType)
                    .textInputAutocapitalization(autoCapitalization)
                    .autocorrectionDisabled(disableAutocorrection)
                    .onChange(of: text) { _, newValue in
                        // 🔑 优化：防抖处理，避免频繁更新
                        handleTextChangeWithDebounce(newValue)
                    }
                    .onChange(of: isFocused) { _, focused in
                        updateValidationState()
                    }
                }
                
                // 右侧图标或密码可见性切换
                if type == .password || type == .secureText {
                    Button {
                        isSecureTextVisible.toggle()
                    } label: {
                        Image(systemName: isSecureTextVisible ? "eye.slash" : "eye")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(iconColor)
                    }
                    .frame(width: 20, height: 20)
                } else if let rightIcon = rightIcon {
                    Button {
                        onRightIconTap?()
                    } label: {
                        Image(systemName: rightIcon)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(iconColor)
                    }
                    .frame(width: 20, height: 20)
                }
            }
            .padding(.horizontal, 18) // 原型要求：14px 18px
            .padding(.vertical, 14)
            .background(backgroundView)
            .overlay(borderView)
            .clipShape(RoundedRectangle(cornerRadius: 16)) // 原型要求：16px圆角
            .animation(.easeInOut(duration: 0.3), value: validationState) // 原型要求：0.3s过渡
            .animation(.easeInOut(duration: 0.3), value: isFocused)
            
            // 帮助文本或错误信息
            if let message = displayMessage {
                Text(message)
                    .font(.caption)
                    .foregroundColor(messageColor)
                    .transition(.opacity.combined(with: .move(edge: .top)))
                    .animation(.easeInOut(duration: 0.2), value: displayMessage)
            }
        }
    }
    
    // MARK: - 计算属性
    
    private var currentState: EATextFieldState {
        // 如果有外部错误消息，优先显示错误状态
        if errorMessage != nil && !errorMessage!.isEmpty {
            return .error
        }
        
        // 如果文本为空，返回普通状态或聚焦状态
        if text.isEmpty {
            return isFocused ? .focused : .normal
        }
        
        // 如果有验证器，使用验证器结果
        if let validator = validator {
            let isValid = validator(text)
            if !isValid {
                return .error
            } else {
                // 验证通过，检查是否聚焦
                return isFocused ? .focused : .success
            }
        }
        
        // 默认状态
        return isFocused ? .focused : .normal
    }
    
    private var keyboardType: UIKeyboardType {
        switch type {
        case .email: return .emailAddress
        case .phone: return .phonePad
        case .number: return .numberPad
        default: return .default
        }
    }
    
    private var textContentType: UITextContentType? {
        switch type {
        case .email: return .emailAddress
        case .phone: return .telephoneNumber
        case .username: return .username
        case .password: return .newPassword  // 🔑 修复：使用.newPassword支持iOS密码建议
        case .confirmPassword: return .password  // 🔑 修复：确认密码使用.password，避免重复建议
        case .secureText: return nil  // 安全文本保持禁用
        default: return nil
        }
    }
    
    private var autoCapitalization: TextInputAutocapitalization {
        switch type {
        case .email, .username, .password, .confirmPassword, .secureText: return .never
        default: return .sentences
        }
    }
    
    private var disableAutocorrection: Bool {
        switch type {
        case .email, .username, .password, .confirmPassword, .secureText, .phone, .number: return true
        default: return false
        }
    }
    
    // 原型要求的文字颜色：#e2e8f0
    private var textColor: Color {
        switch currentState {
        case .disabled: return Color.hexColor("e2e8f0").opacity(0.3)
        default: return Color.hexColor("e2e8f0")
        }
    }
    
    private var iconColor: Color {
        switch currentState {
        case .focused: return Color.hexColor("2dd4bf") // 原型要求：Teal 400
        case .error: return Color.red
        case .success: return Color.green
        case .disabled: return Color.hexColor("e2e8f0").opacity(0.3)
        default: return Color.hexColor("e2e8f0").opacity(0.6)
        }
    }
    
    @ViewBuilder
    private var backgroundView: some View {
        Group {
            switch currentState {
            case .disabled:
                Color.gray.opacity(0.1)
            default:
                // 原型要求：rgba(255, 255, 255, 0.08)
                Color.white.opacity(0.08)
            }
        }
    }
    
    @ViewBuilder
    private var borderView: some View {
        RoundedRectangle(cornerRadius: 16)
            .stroke(borderColor, lineWidth: borderWidth)
            .shadow(color: shadowColor, radius: shadowRadius, x: 0, y: 0) // 聚焦时的辉光效果
    }
    
    private var borderColor: Color {
        switch currentState {
        case .focused: return Color.hexColor("2dd4bf") // 原型要求：Teal 400
        case .error: return Color.red
        case .success: return Color.green
        case .disabled: return Color.gray.opacity(0.2)
        default: return Color.white.opacity(0.15) // 原型要求：rgba(255, 255, 255, 0.15)
        }
    }
    
    private var borderWidth: CGFloat {
        switch currentState {
        case .focused, .error, .success: return 1.0
        default: return 1.0
        }
    }
    
    // 原型要求的聚焦辉光效果：0 0 0 3px rgba(45, 212, 191, 0.3)
    private var shadowColor: Color {
        switch currentState {
        case .focused: return Color.hexColor("2dd4bf").opacity(0.3)
        default: return Color.clear
        }
    }
    
    private var shadowRadius: CGFloat {
        switch currentState {
        case .focused: return 3
        default: return 0
        }
    }
    
    private var displayMessage: String? {
        if let errorMessage = errorMessage, currentState == .error {
            return errorMessage
        }
        return helperText
    }
    
    private var messageColor: Color {
        switch currentState {
        case .error: return Color.red
        case .success: return Color.green
        default: return Color.hexColor("e2e8f0").opacity(0.6)
        }
    }
    
    // MARK: - 方法

    /// 🔑 新增：防抖处理文本变化
    private func handleTextChangeWithDebounce(_ newValue: String) {
        // 取消之前的定时器
        debounceTimer?.invalidate()

        // 立即处理长度限制（不需要防抖）
        if let maxLength = maxLength, newValue.count > maxLength {
            text = String(newValue.prefix(maxLength))
            return
        }

        // 设置新的防抖定时器（300ms延迟）
        debounceTimer = Timer.scheduledTimer(withTimeInterval: 0.3, repeats: false) { _ in
            handleTextChange(newValue)
        }
    }

    private func handleTextChange(_ newValue: String) {
        // 长度限制
        if let maxLength = maxLength, newValue.count > maxLength {
            text = String(newValue.prefix(maxLength))
            return
        }
        
        // 类型特定的过滤
        switch type {
        case .phone:
            // 只允许数字和常见的电话号码字符
            let filtered = newValue.filter { "0123456789+-() ".contains($0) }
            if filtered != newValue {
                text = filtered
            }
        case .number:
            // 只允许数字
            let filtered = newValue.filter { $0.isNumber }
            if filtered != newValue {
                text = filtered
            }
        default:
            break
        }
        
        updateValidationState()
    }
    
    private func updateValidationState() {
        withAnimation(.easeInOut(duration: 0.2)) {
            validationState = currentState
        }
    }
}



// MARK: - 预定义验证器

extension EATextField {
    static func emailValidator(_ email: String) -> Bool {
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format:"SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
    
    static func phoneValidator(_ phone: String) -> Bool {
        // 移除所有非数字字符
        let cleanPhone = phone.filter { $0.isNumber }
        
        // 中国手机号格式：11位数字，以1开头，第二位是3-9
        let phoneRegex = "^1[3-9]\\d{9}$"
        let phonePredicate = NSPredicate(format:"SELF MATCHES %@", phoneRegex)
        
        return phonePredicate.evaluate(with: cleanPhone)
    }
    
    static func passwordValidator(_ password: String) -> Bool {
        // 至少6位，包含字母和数字
        return password.count >= 6 && 
               password.contains(where: { $0.isLetter }) && 
               password.contains(where: { $0.isNumber })
    }
}

// MARK: - 预览

struct EATextField_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // 交互式预览 - 支持键盘输入
            EATextFieldInteractivePreview()
                .previewDisplayName("Interactive TextField")
            
            // 静态预览 - 展示各种状态
            EATextFieldStaticPreview()
                .previewDisplayName("TextField States")
        }
    }
}

// 交互式预览 - 专门用于测试键盘输入
private struct EATextFieldInteractivePreview: View {
    @State private var text = ""
    @State private var email = ""
    @State private var password = ""
    
    var body: some View {
        NavigationView {
            ZStack {
                // 使用认证页面背景
                EABackgroundView(style: .authentication, showParticles: true)
                
                VStack(spacing: 20) {
                    Text("输入框测试")
                        .font(.title)
                        .foregroundColor(.white)
                        .padding(.top, 50)
                    
                    VStack(spacing: 20) {
                    EATextField(
                        text: $text,
                        placeholder: "输入任意文本",
                        type: .text,
                        leftIcon: "textformat"
                    )
                    
                    EATextField(
                        text: $email,
                        placeholder: "输入邮箱地址",
                        type: .email,
                        leftIcon: "envelope",
                        validator: EATextField.emailValidator
                    )
                    
                    EATextField(
                        text: $password,
                        placeholder: "输入密码",
                        type: .password,
                        leftIcon: "lock",
                        validator: EATextField.passwordValidator
                    )
                }
                    .padding(.horizontal, 28)
                
                    Spacer()
                }
            }
            .preferredColorScheme(.dark)
        }
    }
}

// 静态预览 - 展示各种状态
private struct EATextFieldStaticPreview: View {
    @State private var normalText = ""
    @State private var focusedText = "聚焦状态"
    @State private var errorText = "错误文本"
    @State private var successText = "<EMAIL>"
    
    var body: some View {
        ZStack {
            EABackgroundView(style: .authentication, showParticles: true)
            
            VStack(spacing: 20) {
                Text("输入框状态展示")
                    .font(.title2)
                    .foregroundColor(.white)
                    .padding(.top, 50)
                
                VStack(spacing: 16) {
                EATextField(
                        text: $normalText,
                        placeholder: "普通状态",
                        type: .text,
                        leftIcon: "textformat"
                )
                
                EATextField(
                        text: $errorText,
                        placeholder: "错误状态",
                        type: .text,
                        errorMessage: "这是一个错误信息",
                        leftIcon: "exclamationmark.triangle"
                )
                
                EATextField(
                        text: $successText,
                        placeholder: "成功状态",
                    type: .email,
                    leftIcon: "envelope",
                    validator: EATextField.emailValidator
                )
                }
                .padding(.horizontal, 28)
                
                Spacer()
            }
        }
        .preferredColorScheme(.dark)
    }
} 