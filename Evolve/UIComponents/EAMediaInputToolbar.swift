import SwiftUI
import PhotosUI

/// 多媒体输入工具栏 - 微信风格输入界面
/// 集成文本输入、语音录制、图片视频选择功能
struct EAMediaInputToolbar: View {

    // MARK: - 输入状态

    @Binding var messageText: String

    // 🔑 iPad键盘约束冲突修复：创建内部文本状态用于TextEditor
    @State private var internalText: String

    @State private var isShowingMediaOptions = false
    @State private var selectedImages: [PhotosPickerItem] = []
    @State private var selectedVideos: [PhotosPickerItem] = []
    @State private var isShowingCamera = false
    @State private var isShowingVideoRecorder = false
    @State private var isRecordingVoice = false
    @State private var dragOffset: CGSize = .zero
    
    // MARK: - 回调
    
    let onSendText: (String) -> Void
    let onSendVoice: (URL) -> Void
    let onSendImages: ([UIImage]) -> Void
    let onSendVideo: (URL) -> Void
    
    // MARK: - 依赖注入

    @ObservedObject private var mediaService: EAMediaService
    @ObservedObject private var permissionManager: EAPermissionManager
    let showMediaButtons: Bool
    
    // MARK: - 录音状态
    
    @State private var recordingStartTime: Date?
    @State private var recordingTimer: Timer?
    
    init(
        messageText: Binding<String>,
        mediaService: EAMediaService,
        permissionManager: EAPermissionManager,
        showMediaButtons: Bool = true,
        onSendText: @escaping (String) -> Void,
        onSendVoice: @escaping (URL) -> Void = { _ in },
        onSendImages: @escaping ([UIImage]) -> Void = { _ in },
        onSendVideo: @escaping (URL) -> Void = { _ in }
    ) {
        self._messageText = messageText
        // 🔑 iPad键盘约束冲突修复：用外部状态初始化内部状态
        self._internalText = State(initialValue: messageText.wrappedValue)
        self._mediaService = ObservedObject(wrappedValue: mediaService)
        self._permissionManager = ObservedObject(wrappedValue: permissionManager)
        self.showMediaButtons = showMediaButtons
        self.onSendText = onSendText
        self.onSendVoice = onSendVoice
        self.onSendImages = onSendImages
        self.onSendVideo = onSendVideo
    }
    
    var body: some View {

        VStack(spacing: 0) {
            // 多媒体选项面板（条件显示）
            if showMediaButtons && isShowingMediaOptions {
                mediaOptionsPanel
                    .transition(.opacity.combined(with: .move(edge: .bottom)))
                    // 🔑 关键修复：限制媒体选项面板高度
                    .frame(maxHeight: 150) // 减小高度，为输入框预留空间
            }

            // 主输入栏 - 🎨 统一间距设计
            HStack(spacing: EAChatInputStyles.Spacing.horizontal) {
                // 添加按钮 - 🎨 统一样式设计（条件显示）
                if showMediaButtons {
                    EAChatCircleButton(
                        systemName: isShowingMediaOptions ? "xmark" : "plus",
                        action: {
                            withAnimation(EAChatInputStyles.Animations.panelExpand) {
                                isShowingMediaOptions.toggle()
                            }
                        }
                    )
                }
                
                // 文本输入框 - 🎨 统一背景样式，微信风格设计
                // 🔑 iPad键盘约束冲突修复：TextEditor现在绑定到internalText
                EACustomTextInput(
                    text: $internalText,
                    placeholder: "输入消息...",
                    maxLines: 6,
                    fontSize: EAChatInputStyles.Fonts.input,
                    textColor: EAChatInputStyles.Colors.text,
                    backgroundColor: .clear,
                    placeholderColor: EAChatInputStyles.Colors.placeholder
                )
                // 🔑 移除多余的水平padding，textContainerInset已经处理了内边距
                .background(EAChatInputBackground())
                .accentColor(EAChatInputStyles.Colors.primary)

                // 🎨 外置按钮设计 - 智能切换发送/语音按钮
                // 🔑 iPad键盘约束冲突修复：使用internalText判断按钮状态
                if internalText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    // 语音按钮（条件显示）
                    if showMediaButtons {
                        voiceRecordButton
                            .transition(.scale.combined(with: .opacity))
                    }
                } else {
                    // 发送按钮 - 统一样式
                    EAChatSendButton(
                        isEnabled: true,
                        action: sendTextMessage
                    )
                    .transition(.scale.combined(with: .opacity))
                }
            }
            .padding(.horizontal, EAChatInputStyles.Spacing.containerPadding)
            .padding(.vertical, EAChatInputStyles.Spacing.vertical)
            // 🚨 现代布局修复：由父视图的safeAreaInset统一管理布局
            // 🔑 关键修复：移除外部高度限制，让EACustomTextInput自己控制高度
        .onChange(of: selectedImages) {
            processSelectedImages()
            }
        }
        .onChange(of: selectedVideos) {
            processSelectedVideos()
        }
        .sheet(isPresented: $isShowingCamera) {
            EACameraPickerView { image in
                onSendImages([image])
            }
        }
        .sheet(isPresented: $isShowingVideoRecorder) {
            EAVideoRecorderView { videoURL in
                onSendVideo(videoURL)
            }
        }
        // 🔑 关键修复：移除固定高度限制，允许输入框动态调整高度
        // 只在展开媒体选项时限制最大高度，正常状态下让EACustomTextInput自由控制高度
        .frame(maxHeight: isShowingMediaOptions ? 320 : nil)
        .clipped() // 确保内容不会超出边界
        // 🔑 iPad键盘约束冲突修复：使用延迟更新将内部状态同步回外部状态
        .onChangeDelayed(of: internalText) { newValue in
            self.messageText = newValue
        }
        // 🔑 同步外部状态变化到内部状态（处理外部清空等情况）
        .onChange(of: messageText) { oldValue, newValue in
            if newValue != internalText {
                internalText = newValue
            }
        }
    }
    
    // MARK: - 多媒体选项面板
    
    private var mediaOptionsPanel: some View {
        HStack(spacing: 24) {
            // 拍照
            MediaOptionButton(
                icon: "camera.fill",
                title: "拍照",
                color: .blue
            ) {
                isShowingCamera = true
                withAnimation {
                    isShowingMediaOptions = false
                }
            }
            
            // 相册
            PhotosPicker(
                selection: $selectedImages,
                maxSelectionCount: 9,
                matching: .images
            ) {
                MediaOptionButtonContent(
                    icon: "photo.fill",
                    title: "相册",
                    color: .green
                )
            }
            .onChange(of: selectedImages) {
                withAnimation {
                    isShowingMediaOptions = false
                }
            }
            
            // 录制视频
            MediaOptionButton(
                icon: "video.fill",
                title: "视频",
                color: .red
            ) {
                isShowingVideoRecorder = true
                withAnimation {
                    isShowingMediaOptions = false
                }
            }
            
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.systemGray6))
    }
    
    // MARK: - 语音录制按钮
    
    private var voiceRecordButton: some View {
        ZStack {
            // 录音按钮
            Button {
                // 点击逻辑在手势中处理
            } label: {
                Image(systemName: isRecordingVoice ? "stop.fill" : "mic.fill")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(isRecordingVoice ? .red : Color.hexColor("40E0D0")) // 🔑 使用主题色
                    .frame(width: 32, height: 32) // 🔑 优化：减小按钮尺寸以符合32pt标准高度
                    .background(
                        Circle()
                            .fill(
                                isRecordingVoice 
                                ? Color.red.opacity(0.15) 
                                : Color.hexColor("40E0D0").opacity(0.15) // 🔑 星域风格背景
                            )
                            .overlay(
                                Circle()
                                    .stroke(
                                        isRecordingVoice 
                                        ? Color.red.opacity(0.3) 
                                        : Color.hexColor("40E0D0").opacity(0.3), // 🔑 主题色描边
                                        lineWidth: 1
                                    )
                            )
                            .scaleEffect(isRecordingVoice ? 1.2 : 1.0)
                    )
                    .scaleEffect(isRecordingVoice ? 1.1 : 1.0)
            }
            .animation(.easeInOut(duration: 0.2), value: isRecordingVoice)
            .simultaneousGesture(
                DragGesture(minimumDistance: 0)
                    .onChanged { value in
                        if !isRecordingVoice {
                            startVoiceRecording()
                        }
                        dragOffset = value.translation
                    }
                                            .onEnded { value in
                        if isRecordingVoice {
                            if value.translation.height < -50 {
                                // 上滑取消
                                cancelVoiceRecording()
                            } else {
                                // 正常发送
                                finishVoiceRecording()
                            }
                        }
                        dragOffset = .zero
                    }
            )
            
            // 录音提示
            if isRecordingVoice {
                VStack {
                    if dragOffset.height < -50 {
                        Text("松开取消")
                            .font(.caption)
                            .foregroundColor(.red)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                Capsule()
                                    .fill(Color.red.opacity(0.1))
                            )
                    } else {
                        Text("松开发送")
                            .font(.caption)
                            .foregroundColor(.blue)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                Capsule()
                                    .fill(Color.blue.opacity(0.1))
                            )
                    }
                    
                    Text(formatRecordingTime(mediaService.recordingTime))
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                .offset(y: -60)
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
    }
    
    // MARK: - 辅助方法
    
    private func sendTextMessage() {
        // 🔑 iPad键盘约束冲突修复：使用internalText发送消息
        let text = internalText.trimmingCharacters(in: .whitespacesAndNewlines)
        if !text.isEmpty {
            onSendText(text)
            // 清空内部状态，外部状态会通过onChange同步
            internalText = ""
            messageText = ""
        }
    }
    
    private func startVoiceRecording() {
        Task {
            let success = await mediaService.startRecording()
            if success {
                isRecordingVoice = true
            }
        }
    }
    
    private func finishVoiceRecording() {
        if let recordingURL = mediaService.stopRecording() {
            onSendVoice(recordingURL)
        }
        isRecordingVoice = false
    }
    
    private func cancelVoiceRecording() {
        mediaService.cancelRecording()
        isRecordingVoice = false
    }
    
    private func processSelectedImages() {
        Task {
            var images: [UIImage] = []
            
            for item in selectedImages {
                if let data = try? await item.loadTransferable(type: Data.self),
                   let image = UIImage(data: data) {
                    images.append(image)
                }
            }
            
            if !images.isEmpty {
                await MainActor.run {
                    onSendImages(images)
                    selectedImages.removeAll()
                }
            }
        }
    }
    
    private func processSelectedVideos() {
        Task {
            for item in selectedVideos {
                if let movie = try? await item.loadTransferable(type: Movie.self) {
                    await MainActor.run {
                        onSendVideo(movie.url)
                    }
                }
            }
            selectedVideos.removeAll()
        }
    }
    
    private func formatRecordingTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

// MARK: - 多媒体选项按钮

private struct MediaOptionButton: View {
    let icon: String
    let title: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            MediaOptionButtonContent(icon: icon, title: title, color: color)
        }
    }
}

private struct MediaOptionButtonContent: View {
    let icon: String
    let title: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 24, weight: .medium))
                .foregroundColor(.white)
                .frame(width: 56, height: 56)
                .background(
                    Circle()
                        .fill(color)
                )
            
            Text(title)
                .font(.caption2)
                .foregroundColor(.primary)
        }
    }
}

// MARK: - 辅助类型

struct Movie: Transferable {
    let url: URL
    
    static var transferRepresentation: some TransferRepresentation {
        FileRepresentation(contentType: .movie) { movie in
            SentTransferredFile(movie.url)
        } importing: { received in
            Movie(url: received.file)
        }
    }
}

#if DEBUG
#Preview {
    struct PreviewWrapper: View {
        @State private var messageText = ""
        
        var body: some View {
            VStack {
                Spacer()
                
                // 使用proper error handling替代try!
                if let fileStorageService = createFileStorageService() {
                    EAMediaInputToolbar(
                        messageText: $messageText,
                        mediaService: EAMediaService(
                            permissionManager: EAPermissionManager(),
                            fileStorageService: fileStorageService
                        ),
                        permissionManager: EAPermissionManager(),
                        onSendText: { text in
                            // 处理文本消息
                        },
                        onSendVoice: { url in
                            // 处理语音消息
                        },
                        onSendImages: { images in
                            // 处理图片消息
                        },
                        onSendVideo: { url in
                            // 处理视频消息
                        }
                    )
                } else {
                    Text("预览不可用")
                        .foregroundColor(.red)
                }
            }
        }
        
        private func createFileStorageService() -> EAFileStorageService? {
            do {
                return try EAFileStorageService()
            } catch {
                return nil
            }
        }
    }
    
    return PreviewWrapper()
}
#endif 