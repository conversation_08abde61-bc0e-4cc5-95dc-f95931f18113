import SwiftUI

// MARK: - 计划卡片组件
struct EAHabitCard: View {
    // MARK: - Properties
    let habit: EAHabit
    let isCompleted: Bool
    let streakCount: Int
    let onToggleCompletion: () -> Void
    let onReminderSettings: (() -> Void)?
    
    // MARK: - Animation State
    @State private var isPressed: Bool = false
    @State private var cardScale: CGFloat = 1.0
    
    // MARK: - Initialization
    init(
        habit: EAHabit,
        isCompleted: Bool,
        streakCount: Int = 0,
        onToggleCompletion: @escaping () -> Void,
        onReminderSettings: (() -> Void)? = nil
    ) {
        self.habit = habit
        self.isCompleted = isCompleted
        self.streakCount = streakCount
        self.onToggleCompletion = onToggleCompletion
        self.onReminderSettings = onReminderSettings
    }
    
    // MARK: - Body
    var body: some View {
        ZStack {
            HStack(spacing: 12) {
                // 计划图标
                habitIconSlot
                
                // 计划信息
                habitTextContent
                
                // 连续天数指示器（如果有连续天数）
                if streakCount > 0 {
                    EAStreakIndicator(
                        streakDays: streakCount,
                        style: .compact
                    )
                }
                
                // 完成按钮
                EACompletionButton(
                    isCompleted: .constant(isCompleted),
                    size: 32,
                    onToggle: onToggleCompletion
                )
            }
            
            // 提醒设置按钮（右上角）
            if let onReminderSettings = onReminderSettings {
                VStack {
                    HStack {
                        Spacer()
                        reminderSettingsButton(action: onReminderSettings)
                    }
                    Spacer()
                }
            }
        }
        .padding(16)
        .background(cardBackground)
        .clipShape(RoundedRectangle(cornerRadius: 24))
        .shadow(
            color: Color.black.opacity(0.2),
            radius: 8,
            x: 0,
            y: 4
        )
        .shadow(
            color: Color.black.opacity(0.1),
            radius: 2,
            x: 0,
            y: 1
        )
        .scaleEffect(cardScale)
        .onTapGesture {
            handleCardTap()
        }
    }
    
    // MARK: - Habit Icon Slot
    private var habitIconSlot: some View {
        ZStack {
            // 图标背景
            RoundedRectangle(cornerRadius: 12)
                .fill(iconBackgroundGradient)
                .frame(width: 44, height: 44)
            
            // 🎨 使用统一的彩色图标组件，确保与选择器显示一致
            EAColorfulIcon(habit.iconName, size: 20)
        }
    }
    
    // MARK: - Habit Text Content
    private var habitTextContent: some View {
        VStack(alignment: .leading, spacing: 2) {
            // 计划标题
            Text(habit.name)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
                .lineLimit(1)
            
            // 计划副标题
            Text(habitSubtitle)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(Color.white.opacity(0.8))
                .lineLimit(1)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - Card Background
    private var cardBackground: some View {
        ZStack {
            // 主背景渐变
            RoundedRectangle(cornerRadius: 24)
                .fill(backgroundGradient)
            
            // 完成状态的额外发光效果
            if isCompleted {
                RoundedRectangle(cornerRadius: 24)
                    .stroke(
                        Color.hexColor("40E0D0").opacity(0.3),
                        lineWidth: 1
                    )
            }
        }
    }
    
    // MARK: - Reminder Settings Button
    private func reminderSettingsButton(action: @escaping () -> Void) -> some View {
        Button(action: action) {
            Image(systemName: "bell.fill")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.white)
                .frame(width: 24, height: 24)
                .background(
                    Circle()
                        .fill(Color.hexColor("FFD700").opacity(0.8))
                        .overlay(
                            Circle()
                                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                        )
                )
                .shadow(
                    color: Color.hexColor("FFD700").opacity(0.3),
                    radius: 4,
                    x: 0,
                    y: 2
                )
        }
        .accessibilityLabel("提醒设置")
                        .accessibilityHint("点击设置计划提醒时间")
    }
    
    // MARK: - Computed Properties
    
    private var habitTheme: HabitTheme {
        // 根据计划类型或图标名称确定主题
        switch habit.iconName {
        case "brain.head.profile", "sparkles", "lightbulb", "moon.stars":
            return .mindfulness
        case "figure.walk", "figure.run", "dumbbell", "heart":
            return .fitness
        case "book", "graduationcap", "pencil", "doc.text":
            return .learning
        case "leaf", "drop", "sun.max", "wind":
            return .nature
        default:
            return .mindfulness
        }
    }
    
    private var backgroundGradient: LinearGradient {
        let baseColors = habitTheme.backgroundColors
        let opacity: Double = isCompleted ? 0.4 : 0.25
        
        return LinearGradient(
            gradient: Gradient(colors: [
                baseColors.0.opacity(opacity),
                baseColors.1.opacity(opacity * 0.8)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    private var iconBackgroundGradient: LinearGradient {
        let iconColors = habitTheme.iconColors
        
        return LinearGradient(
            gradient: Gradient(colors: [
                iconColors.0.opacity(0.15),
                iconColors.1.opacity(0.1)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    private var iconColor: Color {
        habitTheme.iconColors.0
    }
    
    private var habitSubtitle: String {
        // 🔑 优化：智能显示提醒时间，提升用户体验
        if habit.reminderEnabled && !habit.reminderTimes.isEmpty {
            // 智能显示最近的下一个提醒时间
            return getSmartReminderDisplay()
        } else if habit.reminderEnabled, let timeSlot = habit.preferredTimeSlot {
            // 如果只有时间段，显示本地化的时间段
            return "⏰ \(localizedTimeSlot(timeSlot))"
        } else {
            // 显示频率信息
            return getFrequencyDisplayText()
        }
    }
    
    /// 获取频率显示文本
    private func getFrequencyDisplayText() -> String {
        switch habit.frequencyType {
        case "daily":
            return "每日"
        case "weekly":
            return "每周"
        case "monthly":
            return "每月"
        case "custom":
            return "自定义"
        case "oneTime":  // ✅ 新增：一次性计划显示
            return "一次性计划"
        default:
            return habit.frequencyType
        }
    }
    
    /// 🔑 新增：格式化提醒时间用于卡片显示
    private func formatReminderTimeForCard(_ timeString: String) -> String {
        // 如果已经是格式化的时间（如"09:30"），直接返回
        if timeString.contains(":") {
            return timeString
        }
        
        // 尝试解析其他格式的时间
        let formatter = DateFormatter()
        let inputFormats = ["HH:mm", "h:mm a", "HH:mm:ss"]
        
        for format in inputFormats {
            formatter.dateFormat = format
            formatter.locale = Locale(identifier: "zh_CN")
            if let date = formatter.date(from: timeString) {
                formatter.dateFormat = "HH:mm"
                return formatter.string(from: date)
            }
        }
        
        // 如果无法解析，返回原始字符串
        return timeString
    }
    
    /// 🔑 新增：本地化时间段显示
    private func localizedTimeSlot(_ timeSlot: String) -> String {
        switch timeSlot.lowercased() {
        case "morning", "早晨":
            return "早晨"
        case "afternoon", "下午":
            return "下午"
        case "evening", "晚上":
            return "晚上"
        case "night", "深夜":
            return "深夜"
        case "早晨时段":
            return "早晨"
        case "下午时段":
            return "下午"
        case "晚上时段":
            return "晚上"
        case "深夜时段":
            return "深夜"
        default:
            // 如果是其他未知值，返回"自定义"
            return "自定义"
        }
    }
    
    /// 🔑 新增：智能提醒显示策略
    private func getSmartReminderDisplay() -> String {
        let reminderTimes = habit.reminderTimes
        
        // 如果只有一个提醒时间，直接显示
        if reminderTimes.count == 1 {
            return "⏰ \(formatReminderTimeForCard(reminderTimes[0]))"
        }
        
        // 多个提醒时间的智能显示策略
        let currentTime = Date()
        let calendar = Calendar.current
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "HH:mm"
        
        // 将字符串时间转换为今天的Date对象
        let todayReminderDates = reminderTimes.compactMap { timeString -> Date? in
            if let time = dateFormatter.date(from: timeString) {
                let components = calendar.dateComponents([.hour, .minute], from: time)
                return calendar.date(bySettingHour: components.hour ?? 0, 
                                   minute: components.minute ?? 0, 
                                   second: 0, 
                                   of: currentTime)
            }
            return nil
        }.sorted()
        
        // 找到最近的下一个提醒时间
        let nextReminder = todayReminderDates.first { $0 > currentTime }
        
        if let nextReminder = nextReminder {
            // 显示最近的下一个提醒时间
            let timeString = dateFormatter.string(from: nextReminder)
            let timeUntil = getTimeUntilReminder(nextReminder)
            return "⏰ \(timeString)\(timeUntil)"
        } else if let firstTomorrowReminder = todayReminderDates.first {
            // 今天的提醒都过了，显示明天的第一个提醒
            let timeString = dateFormatter.string(from: firstTomorrowReminder)
            return "⏰ 明天 \(timeString)"
        } else {
            // 显示提醒数量作为后备方案
            return "⏰ \(reminderTimes.count)个提醒"
        }
    }
    
    /// 🔑 新增：计算距离下次提醒的时间
    private func getTimeUntilReminder(_ reminderTime: Date) -> String {
        let currentTime = Date()
        let timeInterval = reminderTime.timeIntervalSince(currentTime)
        let minutes = Int(timeInterval / 60)
        
        if minutes <= 0 {
            return ""
        } else if minutes < 60 {
            return " (\(minutes)分钟后)"
        } else if minutes < 180 { // 3小时内显示小时数
            let hours = minutes / 60
            let remainingMinutes = minutes % 60
            if remainingMinutes == 0 {
                return " (\(hours)小时后)"
            } else {
                return " (\(hours)小时\(remainingMinutes)分钟后)"
            }
        } else {
            // 超过3小时就不显示倒计时，避免显示过长
            return ""
        }
    }
    
    // MARK: - Actions
    private func handleCardTap() {
        // 轻微的触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
        
        // 卡片按压动画 - 使用更精确的动画控制
        withAnimation(.easeInOut(duration: 0.1)) {
            cardScale = 0.98
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.easeInOut(duration: 0.1)) {
                cardScale = 1.0
            }
        }
    }
}

// MARK: - Habit Theme
private enum HabitTheme {
    case mindfulness
    case fitness
    case learning
    case nature
    
    var backgroundColors: (Color, Color) {
        switch self {
        case .mindfulness:
            return (Color.hexColor("#14B8A6"), Color.hexColor("#0D9488"))  // 青色系
        case .fitness:
            return (Color.hexColor("#3B82F6"), Color.hexColor("#1D4ED8"))  // 蓝色系
        case .learning:
            return (Color.hexColor("#F59E0B"), Color.hexColor("#D97706"))  // 黄色系
        case .nature:
            return (Color.hexColor("#10B981"), Color.hexColor("#059669"))  // 绿色系
        }
    }
    
    var iconColors: (Color, Color) {
        switch self {
        case .mindfulness:
            return (Color.hexColor("#F59E0B"), Color.hexColor("#D97706"))  // 金黄色
        case .fitness:
            return (Color.hexColor("#60A5FA"), Color.hexColor("#3B82F6"))  // 浅蓝色
        case .learning:
            return (Color.hexColor("#FDE047"), Color.hexColor("#FACC15"))  // 亮黄色
        case .nature:
            return (Color.hexColor("#34D399"), Color.hexColor("#10B981"))  // 翠绿色
        }
    }
}

// MARK: - Preview
#Preview("计划卡片 - 不同状态") {
    VStack(spacing: 16) {
        // 未完成状态
        EAHabitCard(
            habit: EAHabit(
                name: "冥想练习",
                iconName: "brain.head.profile",
                targetFrequency: 7
            ),
            isCompleted: false,
            streakCount: 0
        ) {
            // 切换冥想练习完成状态
        }
        
        // 已完成状态（有连续天数）
        EAHabitCard(
            habit: EAHabit(
                name: "晨间拉伸",
                iconName: "figure.walk",
                targetFrequency: 5
            ),
            isCompleted: true,
            streakCount: 7
        ) {
            // 切换晨间拉伸完成状态
        }
        
        // 学习类计划
        EAHabitCard(
            habit: EAHabit(
                name: "阅读30分钟",
                iconName: "book",
                targetFrequency: 5
            ),
            isCompleted: false,
            streakCount: 3
        ) {
            // 切换阅读完成状态
        }
        
        // 自然类计划
        EAHabitCard(
            habit: EAHabit(
                name: "户外散步",
                iconName: "leaf",
                targetFrequency: 3
            ),
            isCompleted: true,
            streakCount: 15
        ) {
            // 切换散步完成状态
        }
    }
    .padding(20)
    .background(
        EABackgroundView(style: .ecosystem)
    )
}

#Preview("计划卡片 - 交互演示") {
    struct InteractiveDemo: View {
        @State private var habits: [(EAHabit, Bool, Int)] = [
            ({
                let habit = EAHabit(name: "冥想练习", iconName: "brain.head.profile", targetFrequency: 7)
                return (habit, false, 0)
            }()),
            ({
                let habit = EAHabit(name: "晨间拉伸", iconName: "figure.walk", targetFrequency: 5)
                return (habit, true, 7)
            }()),
            ({
                let habit = EAHabit(name: "阅读30分钟", iconName: "book", targetFrequency: 5)
                return (habit, false, 3)
            }())
        ]
        
        var body: some View {
            ScrollView {
                LazyVStack(spacing: 16) {
                    ForEach(Array(habits.enumerated()), id: \.offset) { index, habitData in
                        EAHabitCard(
                            habit: habitData.0,
                            isCompleted: habitData.1,
                            streakCount: habitData.2
                        ) {
                            // 切换完成状态
                            habits[index].1.toggle()
                            
                            // 更新连续天数
                            if habits[index].1 {
                                habits[index].2 += 1
                            } else {
                                habits[index].2 = max(0, habits[index].2 - 1)
                            }
                        }
                    }
                }
                .padding(20)
            }
            .background(
                EABackgroundView(style: .ecosystem)
            )
        }
    }
    
    return InteractiveDemo()
} 