//
//  EACustomTextInput.swift
//  Evolve
//
//  Created by AI Assistant on 2025-07-09.
//  Copyright © 2025 Evolve. All rights reserved.
//

import SwiftUI
import UIKit

/// 🔧 自定义文本输入组件 - 微信风格动态高度输入框
///
/// 使用SwiftUI TextEditor实现动态高度，严格遵循微信/iMessage设计标准
/// 严格遵循Evolve项目AI开发审查规则和iOS开发规范
struct EACustomTextInput: View {
    @Binding var text: String
    let placeholder: String
    let maxLines: Int
    let fontSize: CGFloat
    let textColor: UIColor
    let backgroundColor: UIColor
    let placeholderColor: UIColor

    // MARK: - 初始化
    init(
        text: Binding<String>,
        placeholder: String = "输入消息...",
        maxLines: Int = 4,
        fontSize: CGFloat = 16,
        textColor: UIColor = .white,
        backgroundColor: UIColor = .clear,
        placeholderColor: UIColor = .lightGray
    ) {
        self._text = text
        self.placeholder = placeholder
        self.maxLines = maxLines
        self.fontSize = fontSize
        self.textColor = textColor
        self.backgroundColor = backgroundColor
        self.placeholderColor = placeholderColor
    }

    // MARK: - SwiftUI View Body
    var body: some View {
        ZStack(alignment: .topLeading) {
            // 占位符
            if text.isEmpty {
                Text(placeholder)
                    .font(.system(size: fontSize))
                    .foregroundColor(Color(placeholderColor))
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
            }

            // TextEditor - 核心输入组件
            TextEditor(text: $text)
                .font(.system(size: fontSize))
                .foregroundColor(Color(textColor))
                .background(Color(backgroundColor))
                .frame(minHeight: EAChatInputStyles.Heights.minimum, maxHeight: EAChatInputStyles.Heights.maximum)
                .fixedSize(horizontal: false, vertical: true)
                .scrollContentBackground(.hidden)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
        }
    }



}



// MARK: - SwiftUI预览
#if DEBUG
struct EACustomTextInput_Previews: PreviewProvider {
    @State static private var text = ""

    static var previews: some View {
        VStack {
            EACustomTextInput(
                text: $text,
                placeholder: "输入消息...",
                maxLines: 4,
                fontSize: 16,
                textColor: .white,
                backgroundColor: .clear,
                placeholderColor: .lightGray
            )
            .background(EAChatInputBackground())
            .padding()
            .background(Color.black)

            Text("输入内容: \(text)")
                .foregroundColor(.white)
                .padding()
        }
        .background(Color.black)
    }
}
#endif
