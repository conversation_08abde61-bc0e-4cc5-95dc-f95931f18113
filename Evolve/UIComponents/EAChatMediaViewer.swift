import SwiftUI
import AVKit
import Photos
import PhotosUI

/// 🔑 媒体查看器数据模型 - 使用item模式根治首次点击问题
struct EAMediaViewerItem: Identifiable {
    let id = UUID()
    let url: URL
    let mediaType: EAChatMediaViewer.MediaType

    init(url: URL, mediaType: EAChatMediaViewer.MediaType) {
        self.url = url
        self.mediaType = mediaType
    }
}

/// 聊天媒体查看器 - 简洁版本
/// 用于全屏查看聊天中的图片和视频，替代复杂的EAZoomableImageView
struct EAChatMediaViewer: View {
    let mediaURL: URL
    let mediaType: MediaType
    @Binding var isPresented: Bool

    @State private var scale: CGFloat = 1.0
    @State private var lastScale: CGFloat = 1.0
    @State private var offset: CGSize = .zero
    @State private var lastOffset: CGSize = .zero
    @State private var showingActionSheet = false

    enum MediaType {
        case image
        case video
    }
    
    var body: some View {
        ZStack {
            Color.black.ignoresSafeArea()

            switch mediaType {
            case .image:
                imageViewer
            case .video:
                videoViewer
            }

            // 关闭按钮
            VStack {
                HStack {
                    Spacer()
                    Button {
                        isPresented = false
                    } label: {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title2)
                            .foregroundColor(.white)
                            .background(Color.black.opacity(0.3))
                            .clipShape(Circle())
                    }
                    .padding()
                }
                Spacer()
            }
        }
        .confirmationDialog("图片操作", isPresented: $showingActionSheet, titleVisibility: .hidden) {
            // 🔧 优化：符合iOS HIG的操作菜单设计
            Button("保存到相册") {
                saveImageToPhotos()
            }

            Button("取消", role: .cancel) {
                // 取消操作，触觉反馈
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()
            }
        } message: {
            Text("选择要执行的操作")
        }
    }
    
    // MARK: - 图片查看器
    private var imageViewer: some View {
        // 🔧 修复：使用本地文件专用的Image加载方式
        Group {
            if let uiImage = loadLocalImage() {
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .scaleEffect(scale)
                    .offset(offset)
                    .gesture(
                        // 🔧 修复：统一的手势组合系统，解决长按手势冲突
                        ExclusiveGesture(
                            // 优先级1：长按手势（最高优先级）
                            LongPressGesture(minimumDuration: 0.5)
                                .onEnded { _ in
                                    // 长按显示操作菜单
                                    showingActionSheet = true

                                    // 触觉反馈
                                    let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                                    impactFeedback.impactOccurred()
                                },

                            // 优先级2：同时进行的缩放和拖拽手势
                            SimultaneousGesture(
                                // 缩放手势
                                MagnificationGesture()
                                    .onChanged { value in
                                        let newScale = lastScale * value
                                        scale = min(max(newScale, 1.0), 5.0) // 限制缩放范围1x-5x
                                    }
                                    .onEnded { _ in
                                        lastScale = scale
                                        if scale < 1.0 {
                                            withAnimation(.easeOut(duration: 0.3)) {
                                                scale = 1.0
                                                lastScale = 1.0
                                            }
                                        }
                                    },

                                // 拖拽手势
                                DragGesture()
                                    .onChanged { value in
                                        offset = CGSize(
                                            width: lastOffset.width + value.translation.width,
                                            height: lastOffset.height + value.translation.height
                                        )
                                    }
                                    .onEnded { _ in
                                        lastOffset = offset

                                        // 如果缩放比例为1，重置偏移
                                        if scale == 1.0 {
                                            withAnimation(.easeOut(duration: 0.3)) {
                                                offset = .zero
                                                lastOffset = .zero
                                            }
                                        }
                                    }
                            )
                        )
                    )
                    .onTapGesture(count: 2) {
                        // 双击缩放
                        withAnimation(.easeInOut(duration: 0.3)) {
                            if scale == 1.0 {
                                scale = 2.0
                                lastScale = 2.0
                            } else {
                                scale = 1.0
                                lastScale = 1.0
                                offset = .zero
                                lastOffset = .zero
                            }
                        }
                    }
            } else {
                // 💔 图片加载失败
                VStack(spacing: 16) {
                    Image(systemName: "photo.badge.exclamationmark")
                        .font(.system(size: 48))
                        .foregroundColor(.white.opacity(0.8))
                    
                    Text("无法加载图片")
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    Text(mediaURL.path)
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
    }
    
    // MARK: - 本地文件加载方法
    /// 加载本地图片文件
    private func loadLocalImage() -> UIImage? {
        // 本地文件加载：直接从路径读取
        do {
            let imageData = try Data(contentsOf: mediaURL)
            return UIImage(data: imageData)
        } catch {
            return nil
        }
    }
    
    // MARK: - 视频查看器
    private var videoViewer: some View {
        VideoPlayer(player: AVPlayer(url: mediaURL))
            .onAppear {
                // 自动播放视频
                let player = AVPlayer(url: mediaURL)
                player.play()
            }
    }

    // MARK: - 保存功能
    /// 保存图片到相册
    private func saveImageToPhotos() {
        guard mediaType == .image else {
            // 非图片类型，触觉反馈
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.error)
            return
        }

        guard let image = loadLocalImage() else {
            // 图片加载失败，触觉反馈
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.error)
            return
        }

        Task {
            // 检查相册权限
            let status = PHPhotoLibrary.authorizationStatus(for: .addOnly)

            switch status {
            case .authorized, .limited:
                await performSave(image: image)
            case .notDetermined:
                let newStatus = await PHPhotoLibrary.requestAuthorization(for: .addOnly)
                if newStatus == .authorized || newStatus == .limited {
                    await performSave(image: image)
                } else {
                    // 权限被拒绝，错误反馈
                    await MainActor.run {
                        let notificationFeedback = UINotificationFeedbackGenerator()
                        notificationFeedback.notificationOccurred(.error)
                    }
                }
            case .denied, .restricted:
                // 权限被拒绝，错误反馈
                await MainActor.run {
                    let notificationFeedback = UINotificationFeedbackGenerator()
                    notificationFeedback.notificationOccurred(.error)
                }
            @unknown default:
                await MainActor.run {
                    let notificationFeedback = UINotificationFeedbackGenerator()
                    notificationFeedback.notificationOccurred(.error)
                }
            }
        }
    }

    /// 执行保存操作
    private func performSave(image: UIImage) async {
        do {
            try await PHPhotoLibrary.shared().performChanges {
                PHAssetCreationRequest.creationRequestForAsset(from: image)
            }

            // 保存成功，触觉反馈
            await MainActor.run {
                let notificationFeedback = UINotificationFeedbackGenerator()
                notificationFeedback.notificationOccurred(.success)
            }
        } catch {
            // 保存失败，触觉反馈
            await MainActor.run {
                let notificationFeedback = UINotificationFeedbackGenerator()
                notificationFeedback.notificationOccurred(.error)
            }
        }
    }
}

// MARK: - 预览
#Preview {
    @Previewable @State var isPresented = true
    
    return EAChatMediaViewer(
        mediaURL: URL(string: "https://via.placeholder.com/300")!,
        mediaType: .image,
        isPresented: $isPresented
    )
} 