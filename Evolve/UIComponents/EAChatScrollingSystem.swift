import SwiftUI

// MARK: - 聊天界面滚动系统

// MARK: - PreferenceKey定义

/// 布局完成监听Key - 用于精确滚动定位
struct LayoutCompletionPreferenceKey: PreferenceKey {
    static var defaultValue: Bool = false
    static func reduce(value: inout Bool, nextValue: () -> Bool) {
        value = value || nextValue()
    }
}

/// 输入框高度监听Key - 用于精确计算滚动位置
/// 🔑 优化：添加防抖机制，减少频繁触发
struct InputBarHeightPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        let newValue = nextValue()
        // 🔑 优化：只在高度变化超过0.5pt时才更新，减少微小变化的触发
        if abs(value - newValue) > 0.5 {
            value = newValue
        }
    }
}

/// 消息容器高度监听Key - 用于计算滚动范围
/// 🔑 优化：添加防抖机制，减少频繁触发
struct MessageContainerHeightPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        let newValue = nextValue()
        // 🔑 优化：只在高度变化超过1.0pt时才更新，减少微小变化的触发
        if abs(value - newValue) > 1.0 {
            value = max(value, newValue)
        }
    }
}

// MARK: - 滚动管理器

/// 聊天滚动管理器 - 提供精确的滚动定位和智能滚动策略
struct ChatScrollManager {

    // MARK: - 智能滚动策略

    /// 🎯 智能滚动策略 - 减少不必要的滚动调用，提升性能
    /// - Parameters:
    ///   - messageId: 目标消息ID
    ///   - scrollAction: 滚动执行闭包
    ///   - completion: 滚动完成回调
    static func performSmartScroll(to messageId: UUID, using scrollAction: @escaping () -> Void, completion: (() -> Void)? = nil) {
        // 🔑 修复：立即执行第一次滚动
        scrollAction()

        // 🔑 优化：使用单次延迟验证，减少CPU占用
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
            // 执行验证性滚动，确保位置正确
            scrollAction()

            // 调用完成回调
            completion?()
        }
    }

    /// 🎯 传统多阶段滚动策略 - 仅在智能滚动失效时使用
    /// - Parameters:
    ///   - messageId: 目标消息ID
    ///   - scrollAction: 滚动执行闭包
    ///   - completion: 滚动完成回调
    static func performMultiStageScroll(to messageId: UUID, using scrollAction: @escaping () -> Void, completion: (() -> Void)? = nil) {
        // 第一阶段：立即滚动
        scrollAction()

        // 第二阶段：微调滚动位置
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.01) {
            scrollAction()
        }

        // 第三阶段：最终确认滚动位置
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
            scrollAction()
            completion?()
        }
    }

    // MARK: - 滚动位置计算
    // 滚动位置计算方法已集成到滚动策略中
}

// MARK: - 滚动状态管理

/// 聊天滚动状态管理器 - 管理滚动相关的状态变量
struct ChatScrollState {
    var inputBarHeight: CGFloat = 0
    var messageContainerHeight: CGFloat = 0
    var availableHeight: CGFloat = 0
    var needsPreciseScroll: Bool = false

    // 🔑 新增：滚动防抖相关状态
    var lastScrollTime: Date = Date.distantPast
    var isScrolling: Bool = false
    var pendingScrollId: UUID?

    /// 更新输入框高度
    mutating func updateInputBarHeight(_ height: CGFloat) {
        // 🔑 优化：只在高度真正变化时更新
        guard abs(inputBarHeight - height) > 0.5 else { return }
        inputBarHeight = height
    }

    /// 更新消息容器高度
    mutating func updateMessageContainerHeight(_ height: CGFloat) {
        // 🔑 优化：只在高度真正变化时更新
        guard abs(messageContainerHeight - height) > 0.5 else { return }
        messageContainerHeight = height
    }

    /// 更新可用高度
    mutating func updateAvailableHeight(_ height: CGFloat) {
        // 🔑 优化：只在高度真正变化时更新
        guard abs(availableHeight - height) > 0.5 else { return }
        availableHeight = height
    }

    /// 检查是否需要精确滚动
    mutating func checkNeedsPreciseScroll() {
        needsPreciseScroll = messageContainerHeight > availableHeight - inputBarHeight
    }

    /// 🔑 新增：检查是否应该执行滚动（防抖机制）
    mutating func shouldPerformScroll(for messageId: UUID) -> Bool {
        let now = Date()
        let timeSinceLastScroll = now.timeIntervalSince(lastScrollTime)

        // 如果距离上次滚动不到100ms，且不是新的消息ID，则跳过
        if timeSinceLastScroll < 0.1 && pendingScrollId == messageId {
            return false
        }

        lastScrollTime = now
        pendingScrollId = messageId
        return true
    }

    /// 🔑 新增：标记滚动开始
    mutating func startScrolling() {
        isScrolling = true
    }

    /// 🔑 新增：标记滚动完成
    mutating func finishScrolling() {
        isScrolling = false
        pendingScrollId = nil
    }
}