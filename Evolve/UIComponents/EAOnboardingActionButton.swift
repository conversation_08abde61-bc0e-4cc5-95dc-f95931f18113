import SwiftUI

// 根据 aura_onboarding.html 中的 .onboarding-button 样式
// background: linear-gradient(90deg, #FF7F50, #FF6347); /* Warm Coral to Sunset Orange */
// color: white;
// font-weight: 600; /* Semibold */
// padding: 14px 32px;
// border-radius: 30px; /* Pill shape */
// box-shadow: 0 4px 20px rgba(255,107,71,0.4);

struct EAOnboardingActionButton: View {
    let title: String
    let action: () -> Void

    // 定义原型中的颜色 - 使用主文件中已有的hexColor方法
    private let startColor = Color.hexColor("FF7F50") // Coral
    private let endColor = Color.hexColor("FF6347")   // Tomato (similar to Sunset Orange)
    private let shadowColor = Color.hexColor("FF6B47").opacity(0.4) // Shadow color from prototype

    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: 16, weight: .semibold)) // 原型: 1rem, semibold
                .foregroundColor(.white)
                .padding(.vertical, 14)
                .padding(.horizontal, 32)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [startColor, endColor]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(30) // Pill shape
                .shadow(color: shadowColor, radius: 10, x: 0, y: 4) // 原型 shadow, radius 10, y 4 for a bit softer look
        }
        .buttonStyle(PlainButtonStyle()) // 移除iOS按钮默认的一些视觉效果，以便自定义样式完全生效
    }
}

#Preview("Onboarding Action Button") {
    EAOnboardingActionButton(title: "开启我的微光之旅") {
                        // 按钮点击处理
    }
    .padding()
    .background(Color.gray.opacity(0.2)) // 给预览加个背景好看清阴影
} 