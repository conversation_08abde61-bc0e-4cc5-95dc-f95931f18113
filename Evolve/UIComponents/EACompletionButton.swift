import SwiftUI

// MARK: - 完成打卡按钮组件
struct EACompletionButton: View {
    // MARK: - Properties
    @Binding var isCompleted: Bool
    let size: CGFloat
    let onToggle: () -> Void
    
    // MARK: - Animation State
    @State private var isPressed: Bool = false
    @State private var pulseScale: CGFloat = 1.0
    @State private var checkmarkScale: CGFloat = 1.0
    
    // MARK: - Initialization
    init(
        isCompleted: Binding<Bool>,
        size: CGFloat = 32,
        onToggle: @escaping () -> Void
    ) {
        self._isCompleted = isCompleted
        self.size = size
        self.onToggle = onToggle
    }
    
    // MARK: - Body
    var body: some View {
        Button(action: handleTap) {
            ZStack {
                // 背景圆圈
                backgroundCircle
                
                // 勾选图标 - 始终存在，通过opacity控制可见性
                checkmarkIcon
            }
            .overlay(
                // 脉冲效果 - 使用overlay确保不影响主体布局
                Group {
                    if isCompleted {
                        pulseEffect
                    }
                }
            )
        }
        .buttonStyle(CompletionButtonStyle(isPressed: $isPressed))
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .onChange(of: isCompleted) { _, newValue in
            if newValue {
                triggerCompletionAnimation()
            } else {
                resetAnimation()
            }
        }
    }
    
    // MARK: - Components
    
    /// 背景圆圈
    private var backgroundCircle: some View {
        Circle()
            .fill(backgroundGradient)
            .frame(width: size, height: size)
            .overlay(
                Circle()
                    .stroke(borderColor, lineWidth: borderWidth)
            )
            .shadow(
                color: shadowColor,
                radius: isCompleted ? 6 : 2,
                x: 0,
                y: isCompleted ? 2 : 1
            )
    }
    
    /// 勾选图标
    private var checkmarkIcon: some View {
        Image(systemName: "checkmark")
            .font(.system(size: size * 0.5, weight: .bold, design: .rounded))
            .foregroundColor(.white)
            .opacity(isCompleted ? 1.0 : 0.0)
            .scaleEffect(checkmarkScale)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: checkmarkScale)
            .animation(.easeInOut(duration: 0.2), value: isCompleted)
    }
    
    /// 脉冲效果
    private var pulseEffect: some View {
        Circle()
            .stroke(Color.orange.opacity(0.3), lineWidth: 2)
            .frame(width: size * pulseScale, height: size * pulseScale)
            .opacity(2.0 - pulseScale)
            .animation(
                Animation
                    .easeOut(duration: 1.0)
                    .repeatForever(autoreverses: false),
                value: pulseScale
            )
            .allowsHitTesting(false) // 防止脉冲效果影响点击
    }
    
    // MARK: - Computed Properties
    
    /// 背景渐变
    private var backgroundGradient: LinearGradient {
        if isCompleted {
            return LinearGradient(
                colors: [
                    Color.orange,
                    Color.hexColor("FF8C00")
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        } else {
            return LinearGradient(
                colors: [
                    Color.white.opacity(0.1),
                    Color.white.opacity(0.05)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        }
    }
    
    /// 边框颜色
    private var borderColor: Color {
        isCompleted ? Color.orange : Color.white.opacity(0.3)
    }
    
    /// 边框宽度
    private var borderWidth: CGFloat {
        isCompleted ? 0 : 2
    }
    
    /// 阴影颜色
    private var shadowColor: Color {
        isCompleted ? Color.orange.opacity(0.4) : Color.black.opacity(0.1)
    }
    
    // MARK: - Methods
    
    /// 处理点击
    private func handleTap() {
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        // 执行回调
        onToggle()
    }
    
    /// 触发完成动画
    private func triggerCompletionAnimation() {
        // 勾选图标出现动画
        withAnimation(.spring(response: 0.3, dampingFraction: 0.6).delay(0.1)) {
            checkmarkScale = 1.0
        }
        
        // 脉冲动画 - 使用更稳定的动画
        pulseScale = 1.0
        withAnimation(.easeOut(duration: 1.0).repeatForever(autoreverses: false)) {
            pulseScale = 1.5
        }
        
        // 成功触觉反馈
        let successFeedback = UINotificationFeedbackGenerator()
        successFeedback.notificationOccurred(.success)
    }
    
    /// 重置动画
    private func resetAnimation() {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
            checkmarkScale = 1.0
        }
        // 立即停止脉冲动画
        pulseScale = 1.0
    }
}

// MARK: - Completion Button Style
struct CompletionButtonStyle: ButtonStyle {
    @Binding var isPressed: Bool
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .onChange(of: configuration.isPressed) { _, newValue in
                isPressed = newValue
            }
    }
}

// MARK: - Completion Button Sizes
extension EACompletionButton {
    /// 小尺寸按钮
    static func small(
        isCompleted: Binding<Bool>,
        onToggle: @escaping () -> Void
    ) -> EACompletionButton {
        EACompletionButton(
            isCompleted: isCompleted,
            size: 24,
            onToggle: onToggle
        )
    }
    
    /// 标准尺寸按钮
    static func standard(
        isCompleted: Binding<Bool>,
        onToggle: @escaping () -> Void
    ) -> EACompletionButton {
        EACompletionButton(
            isCompleted: isCompleted,
            size: 32,
            onToggle: onToggle
        )
    }
    
    /// 大尺寸按钮
    static func large(
        isCompleted: Binding<Bool>,
        onToggle: @escaping () -> Void
    ) -> EACompletionButton {
        EACompletionButton(
            isCompleted: isCompleted,
            size: 40,
            onToggle: onToggle
        )
    }
}

// MARK: - 预览
#Preview {
    VStack(spacing: 20) {
        EACompletionButton(
            isCompleted: .constant(false),
            onToggle: {}
        )
        
        EACompletionButton(
            isCompleted: .constant(true),
            onToggle: {}
        )
    }
    .padding()
    .background(Color.black)
}

// MARK: - Interactive Demo
struct InteractiveCompletionDemo: View {
    @State private var isCompleted: Bool = false
    
    var body: some View {
        VStack(spacing: 15) {
            Text("点击切换状态")
                .foregroundColor(.white.opacity(0.8))
                .font(.caption)
            
            EACompletionButton.standard(
                isCompleted: $isCompleted,
                onToggle: {
                    isCompleted.toggle()
                }
            )
            
            Text(isCompleted ? "已完成 ✅" : "未完成 ⭕")
                .foregroundColor(isCompleted ? .green : .orange)
                .font(.caption)
                .fontWeight(.medium)
        }
    }
} 