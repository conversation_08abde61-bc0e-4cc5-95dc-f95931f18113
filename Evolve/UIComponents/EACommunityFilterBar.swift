import SwiftUI

/// 社区筛选栏组件 - 提供分类和标签筛选功能
/// 遵循EA命名规范和数字宇宙主题设计
/// 🔑 重大优化：单行紧凑设计，减少屏幕空间占用，提升用户体验
struct EACommunityFilterBar: View {
    
    // MARK: - 绑定属性
    
    @Binding var selectedCategory: String?
    @Binding var selectedTags: [String]
    
    let availableCategories: [String]
    let popularTags: [String]
    let onFilterChanged: () -> Void
    let onClearFilters: () -> Void
    
    // MARK: - 状态属性
    
    @State private var showAdvancedFilters: Bool = false
    
    // MARK: - 分类映射
    
    private let categoryDisplayNames: [String: String] = [
        "habit": "习惯",
        "challenge": "挑战宣言", 
        "share": "分享"
    ]
    
    private let categoryIcons: [String: String] = [
        "habit": "target",
        "challenge": "flame.fill",
        "share": "heart.fill"
    ]
    
    var body: some View {
        VStack(spacing: 0) {
            // 🔑 主筛选栏：单行紧凑设计
            mainFilterRow
            
            // 🔑 高级筛选：仅在需要时显示
            if showAdvancedFilters {
                advancedFilterSection
                    .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .background(filterBarBackground)
        .animation(.easeInOut(duration: 0.25), value: showAdvancedFilters)
    }
    
    // MARK: - 主筛选栏（单行设计）
    
    private var mainFilterRow: some View {
        HStack(spacing: 8) {
            // 筛选状态指示器
            filterStatusIndicator
            
            // 水平滚动的筛选选项
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    // 全部按钮
                    compactFilterChip(
                        title: "全部",
                        icon: "circle.grid.3x3",
                        isSelected: selectedCategory == nil && selectedTags.isEmpty,
                        action: {
                            selectedCategory = nil
                            selectedTags.removeAll()
                            onFilterChanged()
                        }
                    )
                    
                    // 分类筛选
                    ForEach(availableCategories, id: \.self) { category in
                        compactFilterChip(
                            title: categoryDisplayNames[category] ?? category,
                            icon: categoryIcons[category] ?? "circle",
                            isSelected: selectedCategory == category,
                            action: {
                                selectedCategory = category
                                onFilterChanged()
                            }
                        )
                    }
                    
                    // 热门标签（最多显示3个）
                    ForEach(Array(popularTags.prefix(3)), id: \.self) { tag in
                        compactFilterChip(
                            title: tag,
                            icon: "tag.fill",
                            isSelected: selectedTags.contains(tag),
                            action: {
                                toggleTag(tag)
                            }
                        )
                    }
                    
                    // 更多选项按钮
                    if popularTags.count > 3 || hasActiveFilters {
                        moreOptionsButton
                    }
                }
                .padding(.horizontal, 12)
            }
            
            // 清除筛选按钮（仅在有筛选时显示）
            if hasActiveFilters {
                clearFiltersButton
            }
        }
        .padding(.vertical, 10)
    }
    
    // MARK: - 筛选状态指示器
    
    private var filterStatusIndicator: some View {
        HStack(spacing: 4) {
            Image(systemName: "line.3.horizontal.decrease.circle.fill")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(hasActiveFilters ? Color.hexColor("40E0D0") : .white.opacity(0.6))
            
            if hasActiveFilters {
                Text("\(activeFiltersCount)")
                    .font(.system(size: 12, weight: .bold))
                    .foregroundColor(.white)
                    .frame(minWidth: 16, minHeight: 16)
                    .background(
                        Circle()
                            .fill(Color.hexColor("40E0D0"))
                    )
            }
        }
        .padding(.leading, 12)
    }
    
    // MARK: - 紧凑筛选芯片
    
    private func compactFilterChip(title: String, icon: String, isSelected: Bool, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.system(size: 12, weight: .medium))
                
                Text(title)
                    .font(.system(size: 13, weight: .medium))
                    .lineLimit(1)
            }
            .padding(.horizontal, 10)
            .padding(.vertical, 6)
            .background(
                Capsule()
                    .fill(isSelected ? Color.hexColor("40E0D0") : Color.white.opacity(0.1))
            )
            .foregroundColor(isSelected ? .black : .white.opacity(0.9))
            .overlay(
                Capsule()
                    .stroke(
                        isSelected ? Color.clear : Color.white.opacity(0.2),
                        lineWidth: 1
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - 更多选项按钮
    
    private var moreOptionsButton: some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.25)) {
                showAdvancedFilters.toggle()
            }
        }) {
            HStack(spacing: 4) {
                Image(systemName: showAdvancedFilters ? "chevron.up" : "ellipsis")
                    .font(.system(size: 12, weight: .medium))
                
                Text("更多")
                    .font(.system(size: 13, weight: .medium))
            }
            .padding(.horizontal, 10)
            .padding(.vertical, 6)
            .background(
                Capsule()
                    .fill(Color.white.opacity(0.1))
            )
            .foregroundColor(.white.opacity(0.8))
            .overlay(
                Capsule()
                    .stroke(Color.white.opacity(0.2), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - 清除筛选按钮
    
    private var clearFiltersButton: some View {
        Button(action: {
            selectedCategory = nil
            selectedTags.removeAll()
            onClearFilters()
        }) {
            Image(systemName: "xmark.circle.fill")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white.opacity(0.6))
        }
        .padding(.trailing, 12)
    }
    
    // MARK: - 高级筛选区域
    
    private var advancedFilterSection: some View {
        VStack(spacing: 12) {
            Divider()
                .background(Color.white.opacity(0.1))
            
            // 所有标签选择
            if popularTags.count > 3 {
                allTagsSection
            }
            
            // 筛选操作
            filterActionsRow
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 12)
    }
    
    // MARK: - 所有标签区域
    
    private var allTagsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("所有标签")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                
                Spacer()
                
                if selectedTags.count > 0 {
                    Text("已选 \(selectedTags.count)")
                        .font(.system(size: 12))
                        .foregroundColor(Color.hexColor("40E0D0"))
                }
            }
            
            // 标签网格
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 8) {
                ForEach(popularTags, id: \.self) { tag in
                    compactTagButton(tag: tag)
                }
            }
        }
    }
    
    // MARK: - 紧凑标签按钮
    
    private func compactTagButton(tag: String) -> some View {
        Button(action: {
            toggleTag(tag)
        }) {
            Text(tag)
                .font(.system(size: 12, weight: .medium))
                .lineLimit(1)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    RoundedRectangle(cornerRadius: 6)
                        .fill(selectedTags.contains(tag) ? Color.hexColor("40E0D0") : Color.white.opacity(0.1))
                )
                .foregroundColor(selectedTags.contains(tag) ? .black : .white.opacity(0.8))
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - 筛选操作行
    
    private var filterActionsRow: some View {
        HStack {
            Button("重置筛选") {
                selectedCategory = nil
                selectedTags.removeAll()
                onClearFilters()
            }
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(.white.opacity(0.6))
            
            Spacer()
            
            Button("应用筛选") {
                onFilterChanged()
                withAnimation(.easeInOut(duration: 0.25)) {
                    showAdvancedFilters = false
                }
            }
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(Color.hexColor("40E0D0"))
        }
    }
    
    // MARK: - 背景样式
    
    private var filterBarBackground: some View {
        RoundedRectangle(cornerRadius: 8)
            .fill(Color.black.opacity(0.3))
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.hexColor("40E0D0").opacity(0.2),
                                Color.blue.opacity(0.1)
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        ),
                        lineWidth: 1
                    )
            )
    }
    
    // MARK: - 辅助方法
    
    private func toggleTag(_ tag: String) {
        if selectedTags.contains(tag) {
            selectedTags.removeAll { $0 == tag }
        } else {
            selectedTags.append(tag)
        }
        onFilterChanged()
    }
    
    // MARK: - 计算属性
    
    private var hasActiveFilters: Bool {
        selectedCategory != nil || !selectedTags.isEmpty
    }
    
    private var activeFiltersCount: Int {
        var count = 0
        if selectedCategory != nil { count += 1 }
        count += selectedTags.count
        return count
    }
}

/// 标签选择器弹窗
struct EATagSelectorSheet: View {
    let availableTags: [String]
    @Binding var selectedTags: [String]
    let onSelectionChanged: () -> Void
    
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 16) {
                // 已选标签区域
                if !selectedTags.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("已选标签 (\(selectedTags.count))")
                            .font(.headline)
                            .foregroundColor(.white)
                        
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                            ForEach(selectedTags, id: \.self) { tag in
                                tagChip(title: tag, isSelected: true) {
                                    selectedTags.removeAll { $0 == tag }
                                }
                            }
                        }
                    }
                    .padding()
                    .background(Color.black.opacity(0.3))
                    .cornerRadius(12)
                }
                
                // 可选标签区域
                VStack(alignment: .leading, spacing: 8) {
                    Text("热门标签")
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                        ForEach(availableTags, id: \.self) { tag in
                            tagChip(title: tag, isSelected: selectedTags.contains(tag)) {
                                if selectedTags.contains(tag) {
                                    selectedTags.removeAll { $0 == tag }
                                } else {
                                    selectedTags.append(tag)
                                }
                            }
                        }
                    }
                }
                
                Spacer()
            }
            .padding()
            .background(Color.black)
            .navigationTitle("选择标签")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        onSelectionChanged()
                        dismiss()
                    }
                    .foregroundColor(Color.hexColor("40E0D0"))
                }
            }
        }
    }
    
    private func tagChip(title: String, isSelected: Bool, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            Text("#\(title)")
                .font(.system(size: 12, weight: .medium))
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(isSelected ? Color.blue.opacity(0.2) : Color.black.opacity(0.3))
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(
                                    isSelected ? Color.blue : Color.white.opacity(0.2),
                                    lineWidth: 1
                                )
                        )
                )
                .foregroundColor(isSelected ? .blue : .white.opacity(0.8))
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 预览
#Preview {
    ZStack {
        Color.black.ignoresSafeArea()
        
        VStack {
            EACommunityFilterBar(
                selectedCategory: .constant(nil),
                selectedTags: .constant([]),
                availableCategories: ["habit", "challenge", "share"],
                popularTags: ["运动", "学习", "健康", "阅读", "冥想", "工作"],
                onFilterChanged: {},
                onClearFilters: {}
            )
            
            Spacer()
        }
        .padding()
    }
    .preferredColorScheme(.dark)
} 