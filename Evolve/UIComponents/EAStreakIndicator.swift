import SwiftUI

// MARK: - 连续天数指示器组件
struct EAStreakIndicator: View {
    // MARK: - Properties
    let streakDays: Int
    let style: StreakStyle
    
    // MARK: - Animation State
    @State private var flameScale: CGFloat = 1.0
    @State private var flameOpacity: Double = 1.0
    @State private var glowIntensity: Double = 0.5
    
    // MARK: - Streak Style
    enum StreakStyle {
        case compact    // 紧凑样式，只显示数字和小图标
        case detailed   // 详细样式，显示完整信息
        case minimal    // 最小样式，只显示数字
    }
    
    // MARK: - Initialization
    init(streakDays: Int, style: StreakStyle = .detailed) {
        self.streakDays = max(0, streakDays)
        self.style = style
    }
    
    // MARK: - Body
    var body: some View {
        Group {
            switch style {
            case .compact:
                compactView
            case .detailed:
                detailedView
            case .minimal:
                minimalView
            }
        }
        .onAppear {
            startFlameAnimation()
        }
    }
    
    // MARK: - Style Views
    
    /// 紧凑样式
    private var compactView: some View {
        HStack(spacing: 4) {
            flameIcon
                .font(.system(size: 12, weight: .medium))
            
            Text("\(streakDays)")
                .font(.system(size: 12, weight: .semibold, design: .rounded))
                .foregroundColor(streakTextColor)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            Capsule()
                .fill(streakBackgroundGradient)
                .shadow(color: streakShadowColor, radius: 2, x: 0, y: 1)
        )
    }
    
    /// 详细样式
    private var detailedView: some View {
        HStack(spacing: 6) {
            flameIcon
                .font(.system(size: 16, weight: .medium))
                .scaleEffect(flameScale)
                .opacity(flameOpacity)
            
            VStack(alignment: .leading, spacing: 1) {
                Text("\(streakDays)")
                    .font(.system(size: 16, weight: .bold, design: .rounded))
                    .foregroundColor(streakTextColor)
                
                Text(streakDays == 1 ? "天" : "天连续")
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(streakTextColor.opacity(0.8))
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(streakBackgroundGradient)
                .shadow(color: streakShadowColor, radius: 4, x: 0, y: 2)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(streakBorderColor, lineWidth: 1)
                )
        )
    }
    
    /// 最小样式
    private var minimalView: some View {
        Text("\(streakDays)")
            .font(.system(size: 14, weight: .semibold, design: .rounded))
            .foregroundColor(streakTextColor)
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(
                Capsule()
                    .fill(Color.black.opacity(0.2))
            )
    }
    
    // MARK: - Components
    
    /// 火焰图标
    private var flameIcon: some View {
        Image(systemName: flameIconName)
            .foregroundColor(flameColor)
            .shadow(color: flameColor.opacity(0.5), radius: glowIntensity * 2, x: 0, y: 0)
    }
    
    // MARK: - Computed Properties
    
    /// 火焰图标名称
    private var flameIconName: String {
        switch streakDays {
        case 0:
            return "flame"
        case 1...3:
            return "flame"
        case 4...7:
            return "flame.fill"
        case 8...14:
            return "flame.fill"
        case 15...30:
            return "flame.fill"
        default:
            return "flame.fill"
        }
    }
    
    /// 火焰颜色
    private var flameColor: Color {
        switch streakDays {
        case 0:
            return Color.gray.opacity(0.5)
        case 1...3:
            return Color.orange
        case 4...7:
            return Color.red
        case 8...14:
            return Color.hexColor("FF4500") // 橙红色
        case 15...30:
            return Color.hexColor("FF1493") // 深粉色
        default:
            return Color.hexColor("8A2BE2") // 蓝紫色
        }
    }
    
    /// 连续天数文字颜色
    private var streakTextColor: Color {
        switch streakDays {
        case 0:
            return Color.white.opacity(0.6)
        case 1...3:
            return Color.white
        case 4...7:
            return Color.white
        case 8...14:
            return Color.white
        default:
            return Color.white
        }
    }
    
    /// 背景渐变
    private var streakBackgroundGradient: LinearGradient {
        switch streakDays {
        case 0:
            return LinearGradient(
                colors: [
                    Color.black.opacity(0.2),
                    Color.black.opacity(0.1)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        case 1...3:
            return LinearGradient(
                colors: [
                    Color.orange.opacity(0.2),
                    Color.orange.opacity(0.1)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        case 4...7:
            return LinearGradient(
                colors: [
                    Color.red.opacity(0.25),
                    Color.orange.opacity(0.15)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        case 8...14:
            return LinearGradient(
                colors: [
                    Color.hexColor("FF4500").opacity(0.3),
                    Color.red.opacity(0.2)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        case 15...30:
            return LinearGradient(
                colors: [
                    Color.hexColor("FF1493").opacity(0.3),
                    Color.hexColor("FF4500").opacity(0.2)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        default:
            return LinearGradient(
                colors: [
                    Color.hexColor("8A2BE2").opacity(0.35),
                    Color.hexColor("FF1493").opacity(0.25)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        }
    }
    
    /// 阴影颜色
    private var streakShadowColor: Color {
        switch streakDays {
        case 0:
            return Color.clear
        case 1...3:
            return Color.orange.opacity(0.3)
        case 4...7:
            return Color.red.opacity(0.3)
        case 8...14:
            return Color.hexColor("FF4500").opacity(0.4)
        case 15...30:
            return Color.hexColor("FF1493").opacity(0.4)
        default:
            return Color.hexColor("8A2BE2").opacity(0.5)
        }
    }
    
    /// 边框颜色
    private var streakBorderColor: Color {
        switch streakDays {
        case 0:
            return Color.white.opacity(0.1)
        case 1...3:
            return Color.orange.opacity(0.3)
        case 4...7:
            return Color.red.opacity(0.3)
        case 8...14:
            return Color.hexColor("FF4500").opacity(0.4)
        case 15...30:
            return Color.hexColor("FF1493").opacity(0.4)
        default:
            return Color.hexColor("8A2BE2").opacity(0.5)
        }
    }
    
    // MARK: - Methods
    
    /// 启动火焰动画
    private func startFlameAnimation() {
        guard streakDays > 0 else { return }
        
        // 火焰跳动动画
        withAnimation(
            Animation
                .easeInOut(duration: 1.0)
                .repeatForever(autoreverses: true)
        ) {
            flameScale = 1.1
            flameOpacity = 0.8
        }
        
        // 发光强度动画
        withAnimation(
            Animation
                .easeInOut(duration: 1.5)
                .repeatForever(autoreverses: true)
        ) {
            glowIntensity = 1.0
        }
    }
}

// MARK: - Streak Indicator Convenience
extension EAStreakIndicator {
    /// 紧凑样式
    static func compact(streakDays: Int) -> EAStreakIndicator {
        EAStreakIndicator(streakDays: streakDays, style: .compact)
    }
    
    /// 详细样式
    static func detailed(streakDays: Int) -> EAStreakIndicator {
        EAStreakIndicator(streakDays: streakDays, style: .detailed)
    }
    
    /// 最小样式
    static func minimal(streakDays: Int) -> EAStreakIndicator {
        EAStreakIndicator(streakDays: streakDays, style: .minimal)
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 20) {
        EAStreakIndicator(streakDays: 7, style: .detailed)
        EAStreakIndicator(streakDays: 0, style: .detailed)
        EAStreakIndicator(streakDays: 30, style: .detailed)
    }
    .padding()
    .background(Color.black)
} 