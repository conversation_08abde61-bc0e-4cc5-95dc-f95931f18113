import SwiftUI

// MARK: - AI建议栏组件

/// AI智能建议栏 - 可复用的AI建议显示和交互组件
struct EAAISuggestionBar: View {
    
    // MARK: - 属性
    
    /// AI建议列表
    let suggestions: [String]
    
    /// 是否显示建议栏
    let isVisible: Bool
    
    /// 建议点击回调
    let onSuggestionTap: (String) -> Void
    
    /// 关闭建议栏回调
    let onDismiss: () -> Void
    
    // MARK: - 初始化
    
    /// 初始化AI建议栏
    /// - Parameters:
    ///   - suggestions: AI建议列表
    ///   - isVisible: 是否显示建议栏
    ///   - onSuggestionTap: 建议点击回调
    ///   - onDismiss: 关闭建议栏回调
    init(
        suggestions: [String],
        isVisible: Bool = true,
        onSuggestionTap: @escaping (String) -> Void,
        onDismiss: @escaping () -> Void
    ) {
        self.suggestions = suggestions
        self.isVisible = isVisible
        self.onSuggestionTap = onSuggestionTap
        self.onDismiss = onDismiss
    }
    
    // MARK: - 视图主体
    
    var body: some View {
        if isVisible && !suggestions.isEmpty {
            VStack(alignment: .leading, spacing: 8) {
                // 标题栏
                headerView
                
                // 建议列表
                suggestionsScrollView
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(backgroundView)
            .padding(.horizontal, 16)
            .transition(.move(edge: .bottom).combined(with: .opacity))
            .animation(.easeInOut(duration: 0.3), value: suggestions.isEmpty)
        }
    }
    
    // MARK: - 子视图
    
    /// 标题栏视图
    private var headerView: some View {
        HStack {
            Image(systemName: "brain.head.profile")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(Color.chatAccentPrimary)

            Text("AI智能建议")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(Color.chatAccentPrimary)
            
            Spacer()
            
            Button(action: onDismiss) {
                Image(systemName: "xmark")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.white.opacity(0.6))
            }
        }
    }
    
    /// 建议滚动视图
    private var suggestionsScrollView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                ForEach(suggestions, id: \.self) { suggestion in
                    suggestionButton(suggestion)
                }
            }
            .padding(.horizontal, 16)
        }
    }
    
    /// 建议按钮
    /// - Parameter suggestion: 建议内容
    /// - Returns: 建议按钮视图
    private func suggestionButton(_ suggestion: String) -> some View {
        Button(action: {
            onSuggestionTap(suggestion)
        }) {
            Text(suggestion)
                .font(.system(size: 14))
                .foregroundColor(.white)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.chatAISuggestionBackground)
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color.chatAISuggestionBorder, lineWidth: 1)
                        )
                )
        }
    }
    
    /// 背景视图
    private var backgroundView: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.chatBackgroundWarm.opacity(0.8),
                        Color.chatBackgroundTeal.opacity(0.6)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.chatAISuggestionBorder,
                                Color.chatAISuggestionBackground
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
    }
}

// MARK: - 预览

#Preview {
    VStack {
        EAAISuggestionBar(
            suggestions: ["你好！最近怎么样？", "一起加油完成今天的计划！", "分享一下你的进展吧"],
            isVisible: true,
            onSuggestionTap: { suggestion in
                print("选择了建议: \(suggestion)")
            },
            onDismiss: {
                print("关闭AI建议栏")
            }
        )
        
        Spacer()
    }
    .background(Color.chatBackgroundDeep)
    .preferredColorScheme(.dark)
} 