//
//  EAStartupLoadingView.swift
//  Evolve
//
//  Created by AI Assistant on 2025-01-26.
//  启动加载视图组件 - 阶段4.2实现
//

import SwiftUI

/// 启动加载视图
/// 在应用启动过程中提供优雅的加载体验
/// 集成用户状态检查进度指示
struct EAStartupLoadingView: View {
    
    // MARK: - 动画状态
    
    @State private var rotationAngle: Double = 0
    @State private var scaleEffect: Double = 1.0
    @State private var opacity: Double = 0.7
    @State private var currentStepIndex = 0
    
    // MARK: - 启动步骤
    
    private let startupSteps = [
        "正在初始化应用...",
        "正在恢复用户会话...",
        "正在检查用户完整性...",
        "正在加载用户数据...",
        "启动完成"
    ]
    
    // MARK: - 常量
    
    private let animationDuration: Double = 1.5
    private let stepChangeDuration: Double = 0.8
    
    var body: some View {
        ZStack {
            // 背景
            Color.black.opacity(0.95)
                .ignoresSafeArea()
            
            VStack(spacing: 40) {
                // 主加载指示器
                loadingIndicator
                
                // 步骤文本
                stepText
                
                // 进度指示器
                progressIndicator
            }
            .preferredColorScheme(.dark)
        }
        .onAppear {
            startAnimations()
            startStepProgression()
        }
    }
    
    // MARK: - 子视图
    
    /// 主加载指示器
    private var loadingIndicator: some View {
        ZStack {
            // 外环
            Circle()
                .stroke(Color.white.opacity(0.2), lineWidth: 3)
                .frame(width: 80, height: 80)
            
            // 内环（旋转）
            Circle()
                .trim(from: 0, to: 0.7)
                .stroke(
                    LinearGradient(
                        colors: [Color.blue, Color.purple, Color.blue],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    style: StrokeStyle(lineWidth: 4, lineCap: .round)
                )
                .frame(width: 80, height: 80)
                .rotationEffect(.degrees(rotationAngle))
            
            // 中心图标
            Image(systemName: "sparkles")
                .font(.title2)
                .foregroundColor(.white)
                .scaleEffect(scaleEffect)
                .opacity(opacity)
        }
    }
    
    /// 步骤文本
    private var stepText: some View {
        VStack(spacing: 12) {
            Text(currentStepIndex < startupSteps.count ? startupSteps[currentStepIndex] : "启动完成")
                .font(.headline)
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .animation(.easeInOut(duration: 0.3), value: currentStepIndex)
            
            Text("请稍候...")
                .font(.caption)
                .foregroundColor(.white.opacity(0.7))
        }
    }
    
    /// 进度指示器
    private var progressIndicator: some View {
        VStack(spacing: 8) {
            // 进度条
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // 背景
                    RoundedRectangle(cornerRadius: 2)
                        .fill(Color.white.opacity(0.2))
                        .frame(height: 4)
                    
                    // 进度
                    RoundedRectangle(cornerRadius: 2)
                        .fill(
                            LinearGradient(
                                colors: [Color.blue, Color.purple],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(
                            width: geometry.size.width * progress,
                            height: 4
                        )
                        .animation(.easeInOut(duration: stepChangeDuration), value: currentStepIndex)
                }
            }
            .frame(height: 4)
            .frame(maxWidth: 200)
            
            // 进度百分比
            Text("\(Int(progress * 100))%")
                .font(.caption2)
                .foregroundColor(.white.opacity(0.6))
                .animation(.easeInOut(duration: 0.3), value: currentStepIndex)
        }
    }
    
    // MARK: - 计算属性
    
    /// 当前进度（0.0 - 1.0）
    private var progress: Double {
        guard startupSteps.count > 0 else { return 1.0 }
        return Double(currentStepIndex) / Double(startupSteps.count - 1)
    }
    
    // MARK: - 动画方法
    
    /// 启动主动画
    private func startAnimations() {
        // 旋转动画
        withAnimation(.linear(duration: animationDuration).repeatForever(autoreverses: false)) {
            rotationAngle = 360
        }
        
        // 脉冲动画
        withAnimation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true)) {
            scaleEffect = 1.2
            opacity = 1.0
        }
    }
    
    /// 启动步骤进度
    private func startStepProgression() {
        Timer.scheduledTimer(withTimeInterval: stepChangeDuration, repeats: true) { timer in
            if currentStepIndex < startupSteps.count - 1 {
                withAnimation(.easeInOut(duration: 0.3)) {
                    currentStepIndex += 1
                }
            } else {
                timer.invalidate()
            }
        }
    }
}

// MARK: - 预览

#Preview {
    EAStartupLoadingView()
} 