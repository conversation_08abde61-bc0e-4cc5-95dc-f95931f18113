import SwiftUI

// MARK: - 聊天界面颜色系统

/// 聊天界面专用颜色扩展 - 符合UI设计规范，提升设计一致性
extension Color {

    // MARK: - 聊天背景色系

    /// 深空背景 - 深蓝夜空
    static let chatBackgroundDeep = Color.hexColor("0C1B2A")

    /// 中层背景 - 中层蓝灰
    static let chatBackgroundMid = Color.hexColor("1C2B3A")

    /// 温暖背景 - 温暖灰蓝
    static let chatBackgroundWarm = Color.hexColor("2C3E50")

    /// 深青背景 - 深青蓝
    static let chatBackgroundTeal = Color.hexColor("1A4A5C")

    // MARK: - 聊天强调色

    /// 主要强调色 - 荧光青色（AI智慧色）
    static let chatAccentPrimary = Color.hexColor("40E0D0")

    // MARK: - 聊天UI元素色

    /// 输入框边框色
    static let chatInputBorder = Color.hexColor("40E0D0").opacity(0.3)

    /// AI建议背景色
    static let chatAISuggestionBackground = Color.hexColor("40E0D0").opacity(0.3)

    /// AI建议边框色
    static let chatAISuggestionBorder = Color.hexColor("40E0D0").opacity(0.5)
} 