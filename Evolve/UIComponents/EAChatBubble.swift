import SwiftUI

/// AI对话聊天气泡组件
/// 支持多媒体消息类型：文本、图片、视频、语音
/// 复用项目现有的EAZoomableImageView和EAImageCacheService
struct EAChatBubble: View {
    let message: String
    let messageType: MessageType
    let isFromUser: Bool
    let timestamp: Date
    let onImageTap: ((String) -> Void)?
    
    @State private var isVisible = false
    @State private var thumbnailImage: UIImage?
    @State private var showImageViewer = false
    @State private var selectedImagePath: String?
    
    /// 消息类型枚举
    enum MessageType {
        case text
        case image(String) // 图片路径
        case video(String) // 视频路径
        case voice(String) // 语音路径
    }
    
    // 🔑 保持向后兼容的便利初始化器
    init(message: String, isFromUser: Bool, timestamp: Date) {
        self.message = message
        self.messageType = .text
        self.isFromUser = isFromUser
        self.timestamp = timestamp
        self.onImageTap = nil
    }
    
    // 🔑 完整的多媒体初始化器
    init(message: String, messageType: MessageType, isFromUser: Bool, timestamp: Date, onImageTap: ((String) -> Void)? = nil) {
        self.message = message
        self.messageType = messageType
        self.isFromUser = isFromUser
        self.timestamp = timestamp
        self.onImageTap = onImageTap
    }
    
    var body: some View {
        HStack {
            if isFromUser {
                Spacer(minLength: 60)
            }
            
            VStack(alignment: isFromUser ? .trailing : .leading, spacing: 4) {
                // 消息内容（根据类型显示）
                messageContentView
                
                // 🔑 移除气泡右下角的时间戳显示，时间已在上方显示
                // 保留VStack结构以维持布局一致性
            }
            
            if !isFromUser {
                Spacer(minLength: 60)
            }
        }
        .opacity(isVisible ? 1 : 0)
        .offset(y: isVisible ? 0 : 20)
        .onAppear {
            withAnimation(.easeOut(duration: 0.3)) {
                isVisible = true
            }
            loadThumbnailIfNeeded()
        }
    }
    
    /// 🖼️ 消息内容视图（根据类型）
    @ViewBuilder
    private var messageContentView: some View {
        switch messageType {
        case .text:
            textBubbleView
        case .image(let imagePath):
            imageBubbleView(imagePath: imagePath)
        case .video(let videoPath):
            videoBubbleView(videoPath: videoPath)
        case .voice(let voicePath):
            voiceBubbleView(voicePath: voicePath)
        }
    }
    
    /// 📝 文本气泡视图
    private var textBubbleView: some View {
        Text(message)
            .font(.system(size: 16, weight: .regular))
            .foregroundColor(isFromUser ? .white : Color("TextSecondary"))
            .multilineTextAlignment(isFromUser ? .trailing : .leading)
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(bubbleBackground)
    }
    
    /// 🖼️ 图片气泡视图（复用EAZoomableImageView风格）
    private func imageBubbleView(imagePath: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            // 图片预览区域
            Group {
                if let thumbnail = thumbnailImage {
                    Image(uiImage: thumbnail)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 200, height: 150)
                        .clipped()
                        .cornerRadius(12)
                } else {
                    // 🔑 修复：区分加载中和加载失败状态
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: 200, height: 150)
                        .overlay(
                            VStack(spacing: 8) {
                                Image(systemName: thumbnailImage == nil ? "photo.badge.exclamationmark" : "photo")
                                    .font(.title2)
                                    .foregroundColor(.white.opacity(0.6))
                                Text(thumbnailImage == nil ? "图片加载失败" : "加载中...")
                                    .font(.caption)
                                    .foregroundColor(.white.opacity(0.6))
                                if thumbnailImage == nil {
                                    Button("重试") {
                                        loadThumbnailIfNeeded()
                                    }
                                    .font(.caption)
                                    .foregroundColor(.blue)
                                } else {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                        .tint(.white)
                                }
                            }
                        )
                }
            }
                    .onTapGesture {
            // 🔑 修复：传递原始路径给EAZoomableImageView
            // EAZoomableImageView内部有完善的路径解析逻辑，包括好友聊天图片支持
            onImageTap?(imagePath)
        }
            
            // 如果有文本描述
            if !message.isEmpty {
                Text(message)
                    .font(.system(size: 14))
                    .foregroundColor(isFromUser ? .white : Color("TextSecondary"))
                    .multilineTextAlignment(isFromUser ? .trailing : .leading)
                    .padding(.horizontal, 12)
                    .padding(.bottom, 8)
            }
        }
        .background(bubbleBackground)
        .onAppear {
            loadThumbnailIfNeeded()
        }
    }
    
    /// 🎥 视频气泡视图
    private func videoBubbleView(videoPath: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            // 视频预览区域
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.3))
                .frame(width: 200, height: 150)
                .overlay(
                    VStack {
                        Image(systemName: "play.circle.fill")
                            .font(.system(size: 40))
                            .foregroundColor(.white)
                        Text("视频消息")
                            .font(.caption)
                            .foregroundColor(.white)
                    }
                )
            
            // 如果有文本描述
            if !message.isEmpty {
                Text(message)
                    .font(.system(size: 14))
                    .foregroundColor(isFromUser ? .white : Color("TextSecondary"))
                    .multilineTextAlignment(isFromUser ? .trailing : .leading)
                    .padding(.horizontal, 12)
                    .padding(.bottom, 8)
            }
        }
        .background(bubbleBackground)
    }
    
    /// 🎵 语音气泡视图
    private func voiceBubbleView(voicePath: String) -> some View {
        HStack(spacing: 12) {
            // 播放按钮
            Button(action: {
                // TODO: 实现语音播放功能
            }) {
                Image(systemName: "play.circle.fill")
                    .font(.system(size: 24))
                    .foregroundColor(isFromUser ? .white : Color("PrimaryTurquoise"))
            }
            
            // 语音波形指示器
            HStack(spacing: 2) {
                ForEach(0..<8, id: \.self) { _ in
                    Capsule()
                        .fill(isFromUser ? Color.white.opacity(0.7) : Color("PrimaryTurquoise").opacity(0.7))
                        .frame(width: 3, height: CGFloat.random(in: 10...20))
                }
            }
            
            // 语音时长
            Text("0:15")
                .font(.system(size: 12))
                .foregroundColor(isFromUser ? .white.opacity(0.8) : Color("TextSecondary").opacity(0.8))
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(bubbleBackground)
    }
    
    /// 🎨 统一的气泡背景样式
    private var bubbleBackground: some View {
        RoundedRectangle(cornerRadius: 20)
            .fill(isFromUser ? 
                  AnyShapeStyle(LinearGradient(
                    colors: [Color("PrimaryTurquoise"), Color("PrimaryTurquoise").opacity(0.8)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                  )) :
                  AnyShapeStyle(LinearGradient(
                    colors: [
                        Color.hexColor("2C3E50").opacity(0.9),
                        Color.hexColor("34495E").opacity(0.8)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                  ))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(
                        isFromUser ? Color.clear : Color.hexColor("40E0D0").opacity(0.3),
                        lineWidth: 1
                    )
            )
    }
    
    /// 🔑 修复：加载缩略图（使用好友聊天专用的路径解析）
    private func loadThumbnailIfNeeded() {
        guard case .image(let imagePath) = messageType else { return }
        
        Task {
            let thumbnail = await loadFriendChatThumbnail(from: imagePath)
            await MainActor.run {
                self.thumbnailImage = thumbnail
            }
        }
    }
    
    /// 🔑 好友聊天专用的缩略图加载方法
    private func loadFriendChatThumbnail(from path: String) async -> UIImage? {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                let fullPath = self.getFriendChatImagePath(from: path)
                
                guard FileManager.default.fileExists(atPath: fullPath),
                      let image = UIImage(contentsOfFile: fullPath) else {
                    continuation.resume(returning: nil)
                    return
                }
                
                // 🔑 生成缩略图以提高性能
                let thumbnailSize = CGSize(width: 200, height: 150)
                let thumbnail = image.preparingThumbnail(of: thumbnailSize) ?? image.resized(to: thumbnailSize)
                
                continuation.resume(returning: thumbnail)
            }
        }
    }
    
    /// 🔑 好友聊天专用路径解析（与EAFriendChatImageViewer一致）
    private func getFriendChatImagePath(from path: String) -> String {
        // 如果已经是完整路径，直接返回
        if path.hasPrefix("/") {
            return path
        }
        
        // 获取Documents目录
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!.path
        
        // 🔑 按优先级搜索好友聊天图片（与专用查看器保持一致）
        let searchPaths = [
            "\(documentsPath)/\(path)",
            "\(documentsPath)/Images/\(path)",
            "\(documentsPath)/ChatImages/\(path)",
            "\(NSTemporaryDirectory())\(path)",
            "\(NSTemporaryDirectory())Images/\(path)",
            "\(FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first!.path)/\(path)"
        ]
        
        for searchPath in searchPaths {
            if FileManager.default.fileExists(atPath: searchPath) {
                return searchPath
            }
        }
        
        // 默认返回Documents路径
        return "\(documentsPath)/\(path)"
    }
    
    /// 获取完整图片路径用于图片查看器
    private func getFullImagePath(from path: String) -> String? {
        // 如果已经是完整路径，先检查是否存在
        if path.hasPrefix("/") {
            if FileManager.default.fileExists(atPath: path) {
                return path
            }
        }
        
        // 检查Documents目录
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first?.path ?? ""
        let documentsFullPath = "\(documentsPath)/\(path)"
        if FileManager.default.fileExists(atPath: documentsFullPath) {
            return documentsFullPath
        }
        
        // 检查临时目录
        let tempPath = NSTemporaryDirectory() + path
        if FileManager.default.fileExists(atPath: tempPath) {
            return tempPath
        }
        
        // 检查缓存目录
        let cachesPath = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first?.path ?? ""
        let cachePath = "\(cachesPath)/\(path)"
        if FileManager.default.fileExists(atPath: cachePath) {
            return cachePath
        }
        
        // 🔑 新增：检查Application Support目录
        let appSupportPath = FileManager.default.urls(for: .applicationSupportDirectory, in: .userDomainMask).first?.path ?? ""
        let appSupportFullPath = "\(appSupportPath)/\(path)"
        if FileManager.default.fileExists(atPath: appSupportFullPath) {
            return appSupportFullPath
        }
        
        // 🔑 新增：检查路径是否包含文件名，如果是相对路径，尝试不同组合
        if !path.contains("/") {
            // 纯文件名，尝试在各个标准目录中查找
            let possiblePaths = [
                "\(documentsPath)/Images/\(path)",
                "\(documentsPath)/ChatImages/\(path)",
                "\(cachesPath)/Images/\(path)",
                "\(cachesPath)/ChatImages/\(path)",
                "\(NSTemporaryDirectory())Images/\(path)",
                "\(NSTemporaryDirectory())ChatImages/\(path)"
            ]
            
            for possiblePath in possiblePaths {
                if FileManager.default.fileExists(atPath: possiblePath) {
                    return possiblePath
                }
            }
        }
        
        return nil
    }
    
    /// 获取图片URL用于AsyncImage显示
    private func getImageURL(from imagePath: String) -> URL? {
        // 处理不同的路径格式
        if imagePath.hasPrefix("file://") {
            return URL(string: imagePath)
        } else if imagePath.hasPrefix("/") {
            // 绝对路径
            return URL(fileURLWithPath: imagePath)
        } else {
            // 相对路径，转换为Documents目录下的完整路径
            let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
            let fullURL = documentsPath.appendingPathComponent(imagePath)
            return fullURL
        }
    }
    
    // 格式化时间戳
    private func formatTimestamp(_ date: Date) -> String {
        let formatter = DateFormatter()
        let calendar = Calendar.current
        
        if calendar.isDateInToday(date) {
            formatter.dateFormat = "HH:mm"
        } else if calendar.isDateInYesterday(date) {
            return "昨天"
        } else {
            formatter.dateFormat = "MM/dd"
        }
        
        return formatter.string(from: date)
    }
    

}

// MARK: - 预览
#Preview {
    ZStack {
        Color("BackgroundDeepGreen")
            .ignoresSafeArea()
        
        ScrollView {
            VStack(spacing: 16) {
                // 文本消息
                EAChatBubble(
                    message: "你好，我想开始培养一个新的计划",
                    isFromUser: true,
                    timestamp: Date()
                )
                
                EAChatBubble(
                    message: "很高兴见到你！我是Aura，你的专属AI计划教练。",
                    isFromUser: false,
                    timestamp: Date().addingTimeInterval(-60)
                )
                
                // 图片消息
                EAChatBubble(
                    message: "分享一张图片",
                    messageType: .image("/sample/image.jpg"),
                    isFromUser: true,
                    timestamp: Date().addingTimeInterval(-120)
                )
                
                // 语音消息
                EAChatBubble(
                    message: "",
                    messageType: .voice("/sample/voice.m4a"),
                    isFromUser: false,
                    timestamp: Date().addingTimeInterval(-180)
                )
            }
            .padding()
        }
    }
}