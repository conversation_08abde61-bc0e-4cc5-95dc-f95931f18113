import SwiftUI

/// 点赞按钮组件
/// 基于EAButton架构定制，支持点赞状态切换和动画效果
/// 严格遵循项目UI设计规范，体现生态隐喻设计理念
struct EALikeButton: View {
    
    // MARK: - Properties
    
    /// 是否已点赞
    @Binding var isLiked: Bool
    
    /// 点赞数量
    let likeCount: Int
    
    /// 按钮尺寸
    let size: EALikeButtonSize
    
    /// 点击回调
    let onToggle: () -> Void
    
    /// 是否启用（可点击）
    let isEnabled: Bool
    
    // MARK: - Animation State
    
    @State private var isPressed = false
    @State private var animationScale: CGFloat = EAAppConstants.Community.LikeButton.normalScale
    @State private var heartBeat = false
    
    // MARK: - Initialization
    
    init(
        isLiked: Binding<Bool>,
        likeCount: Int = 0,
        size: EALikeButtonSize = .medium,
        isEnabled: Bool = true,
        onToggle: @escaping () -> Void
    ) {
        self._isLiked = isLiked
        self.likeCount = likeCount
        self.size = size
        self.isEnabled = isEnabled
        self.onToggle = onToggle
    }
    
    // MARK: - Body
    
    var body: some View {
        Button(action: {
            if isEnabled {
                handleLikeToggle()
            }
        }) {
            HStack(spacing: likeCountSpacing) {
                // 心形图标
                heartIcon
                
                // 点赞数量（如果大于0）
                if likeCount > 0 {
                    likeCountText
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(!isEnabled)
        .scaleEffect(animationScale)
        .animation(.easeInOut(duration: EAAppConstants.Community.LikeButton.scaleAnimationDuration), value: isPressed)
        .animation(.spring(response: EAAppConstants.Animation.springResponse, dampingFraction: EAAppConstants.Animation.springDampingFraction), value: heartBeat)
        .accessibilityLabel(accessibilityLabel)
        .accessibilityHint(accessibilityHint)
        .accessibilityValue(accessibilityValue)
    }
    
    // MARK: - Heart Icon
    
    private var heartIcon: some View {
        Image(systemName: isLiked ? "heart.fill" : "heart")
            .font(.system(size: iconSize, weight: .medium))
            .foregroundColor(heartColor)
            .scaleEffect(heartBeat ? EAAppConstants.Community.LikeButton.pulseScale : EAAppConstants.Community.LikeButton.normalScale)
            .animation(.spring(response: EAAppConstants.Animation.springResponse, dampingFraction: EAAppConstants.Animation.springDampingFraction), value: isLiked)
    }
    
    // MARK: - Like Count Text
    
    private var likeCountText: some View {
        Text(formattedLikeCount)
            .font(.system(size: textSize, weight: .medium))
            .foregroundColor(textColor)
            .transition(.opacity)
            .animation(.easeInOut(duration: EAAppConstants.Community.LikeButton.animationDuration), value: likeCount)
    }
    
    // MARK: - Actions
    
    private func handleLikeToggle() {
        // 触觉反馈 - 复用EAButton的反馈模式
        let impactFeedback = UIImpactFeedbackGenerator(style: isLiked ? .light : .medium)
        impactFeedback.impactOccurred()
        
        // 动画效果
        withAnimation(.easeInOut(duration: EAAppConstants.Community.LikeButton.scaleAnimationDuration)) {
            isPressed = true
        }
        
        // 心跳动画（仅在点赞时）
        if !isLiked {
            withAnimation(.spring(response: EAAppConstants.Animation.springResponse, dampingFraction: EAAppConstants.Animation.springDampingFraction)) {
                heartBeat = true
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + EAAppConstants.Animation.springResponse) {
                withAnimation(.spring(response: EAAppConstants.Animation.springResponse, dampingFraction: EAAppConstants.Animation.springDampingFraction)) {
                    heartBeat = false
                }
            }
        }
        
        // 执行点赞切换
        onToggle()
        
        // 重置按压状态
        DispatchQueue.main.asyncAfter(deadline: .now() + EAAppConstants.Community.LikeButton.scaleAnimationDuration) {
            withAnimation(.easeInOut(duration: EAAppConstants.Community.LikeButton.scaleAnimationDuration)) {
                isPressed = false
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var iconSize: CGFloat {
        switch size {
        case .small: 
            return EAAppConstants.Community.LikeButton.iconSize * 0.8
        case .medium: 
            return EAAppConstants.Community.LikeButton.iconSize
        case .large: 
            return EAAppConstants.Community.LikeButton.iconSize * 1.2
        }
    }
    
    private var textSize: CGFloat {
        switch size {
        case .small: 
            return EAAppConstants.Community.Typography.actionButtonFontSize * 0.85
        case .medium: 
            return EAAppConstants.Community.Typography.actionButtonFontSize
        case .large: 
            return EAAppConstants.Community.Typography.actionButtonFontSize * 1.15
        }
    }
    
    private var likeCountSpacing: CGFloat {
        switch size {
        case .small: 
            return EAAppConstants.Community.ActionBar.buttonSpacing
        case .medium: 
            return EAAppConstants.Community.ActionBar.buttonSpacing + 2
        case .large: 
            return EAAppConstants.Community.ActionBar.buttonSpacing + 4
        }
    }
    
    /// 心形图标颜色 - 遵循UI设计规范
    private var heartColor: Color {
        if !isEnabled {
            return Color.gray.opacity(0.5)
        }
        
        return isLiked ? 
            Color.hexColor("FF7F50") :     // 珊瑚粉（已点赞）
            Color.white.opacity(EAAppConstants.Community.ColorOpacity.secondaryWhite)       // 白色半透明（未点赞）
    }
    
    /// 文字颜色
    private var textColor: Color {
        if !isEnabled {
            return Color.gray.opacity(0.5)
        }
        
        return Color.white.opacity(EAAppConstants.Community.ColorOpacity.primaryWhite * 0.8)
    }
    
    /// 格式化点赞数量
    private var formattedLikeCount: String {
        if likeCount >= 10000 {
            return String(format: "%.1fW", Double(likeCount) / 10000.0)
        } else if likeCount >= 1000 {
            return String(format: "%.1fK", Double(likeCount) / 1000.0)
        } else {
            return "\(likeCount)"
        }
    }
    
    // MARK: - Accessibility
    
    private var accessibilityLabel: String {
        return isLiked ? "已点赞" : "点赞"
    }
    
    private var accessibilityHint: String {
        return isLiked ? "双击取消点赞" : "双击进行点赞"
    }
    
    private var accessibilityValue: String {
        return "\(likeCount)个赞"
    }
}

// MARK: - EALikeButtonSize

/// 点赞按钮尺寸枚举
enum EALikeButtonSize {
    case small   // 小尺寸：用于紧凑布局
    case medium  // 中等尺寸：用于标准布局（默认）
    case large   // 大尺寸：用于突出显示
}

// MARK: - Preview

struct EALikeButton_Previews: PreviewProvider {
    static var previews: some View {
    @Previewable @State var isLiked1 = false
    @Previewable @State var isLiked2 = true
    @Previewable @State var isLiked3 = false
    
    VStack(spacing: 30) {
        Text("点赞按钮预览")
            .font(.title2)
            .fontWeight(.semibold)
            .foregroundColor(.white)
        
        VStack(spacing: 20) {
            // 小尺寸按钮
            HStack {
                Text("小尺寸:")
                    .foregroundColor(.white)
                Spacer()
                EALikeButton(
                    isLiked: $isLiked1,
                    likeCount: 8,
                    size: .small,
                    onToggle: {
                        isLiked1.toggle()
                    }
                )
            }
            
            // 中等尺寸按钮（已点赞）
            HStack {
                Text("中等尺寸（已点赞）:")
                    .foregroundColor(.white)
                Spacer()
                EALikeButton(
                    isLiked: $isLiked2,
                    likeCount: 156,
                    size: .medium,
                    onToggle: {
                        isLiked2.toggle()
                    }
                )
            }
            
            // 大尺寸按钮
            HStack {
                Text("大尺寸:")
                    .foregroundColor(.white)
                Spacer()
                EALikeButton(
                    isLiked: $isLiked3,
                    likeCount: 2840,
                    size: .large,
                    onToggle: {
                        isLiked3.toggle()
                    }
                )
            }
            
            // 禁用状态
            HStack {
                Text("禁用状态:")
                    .foregroundColor(.white)
                Spacer()
                EALikeButton(
                    isLiked: .constant(true),
                    likeCount: 42,
                    size: .medium,
                    isEnabled: false,
                    onToggle: {}
                )
            }
        }
        .padding(.horizontal, 20)
        
        Spacer()
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .background(
        LinearGradient(
            gradient: Gradient(colors: [
                Color.hexColor("1A3A1D"),
                Color.hexColor("0F1E11")
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    )
    }
} 