import SwiftUI

/// AI消息提示指示器组件
/// 
/// 🔑 功能说明：
/// - 在AI头像右上角显示消息提示
/// - 当系统推送关闭时使用
/// - 支持消息数量显示（1-99条）
/// - 提供完整的无障碍支持
struct EAAIMessageIndicator: View {
    let hasMessages: Bool
    let messageCount: Int
    
    /// 初始化AI消息指示器
    /// - Parameters:
    ///   - hasMessages: 是否有未读消息
    ///   - messageCount: 未读消息数量
    init(hasMessages: Bool = false, messageCount: Int = 0) {
        self.hasMessages = hasMessages
        self.messageCount = messageCount
    }
    
    var body: some View {
        ZStack {
            if hasMessages {
                messageIndicatorView
                
                if messageCount > 1 {
                    messageCountText
                }
            }
        }
        .accessibilityLabel(accessibilityLabelText)
        .accessibilityHint(hasMessages ? "点击查看AI消息" : "")
    }
    
    // MARK: - Private Views
    
    /// 消息提示圆点
    private var messageIndicatorView: some View {
        Circle()
            .fill(Color.red)
            .frame(
                width: EAAppConstants.Today.messageIndicatorSize,
                height: EAAppConstants.Today.messageIndicatorSize
            )
            .overlay(
                Circle()
                    .stroke(Color.white, lineWidth: 2)
            )
            .shadow(
                color: Color.red.opacity(0.4),
                radius: 4,
                x: 0,
                y: 2
            )
    }
    
    /// 消息数量文本
    private var messageCountText: some View {
        Text("\(min(messageCount, 99))")
            .font(.system(size: 10, weight: .bold))
            .foregroundColor(.white)
            .minimumScaleFactor(0.8)
    }
    
    /// 无障碍标签文本
    private var accessibilityLabelText: String {
        hasMessages 
            ? String(format: EAAppConstants.Today.Text.messageIndicatorAccessibilityLabel, messageCount)
            : EAAppConstants.Today.Text.noMessageAccessibilityLabel
    }
}

/// AI头像组件增强版本，支持消息提示
/// 
/// 🔑 功能特性：
/// - 呼吸动画效果，体现生态活力
/// - 多层光晕效果，营造神秘氛围
/// - 消息提示指示器集成
/// - 完整的无障碍支持
/// - 可自定义尺寸和交互行为
struct EAAIAvatarWithIndicator: View {
    let size: CGFloat
    let hasMessages: Bool
    let messageCount: Int
    let onTap: (() -> Void)?
    
    @State private var isBreathing = false
    @State private var glowIntensity: Double = 0.3
    
    /// 初始化AI头像组件
    /// - Parameters:
    ///   - size: 头像尺寸，默认60pt
    ///   - hasMessages: 是否有未读消息
    ///   - messageCount: 未读消息数量
    ///   - onTap: 点击回调
    init(
        size: CGFloat = EAAppConstants.Today.aiAvatarSize,
        hasMessages: Bool = false,
        messageCount: Int = 0,
        onTap: (() -> Void)? = nil
    ) {
        self.size = size
        self.hasMessages = hasMessages
        self.messageCount = messageCount
        self.onTap = onTap
    }
    
    var body: some View {
        ZStack {
            avatarButton
            
            if hasMessages {
                messageIndicatorOverlay
            }
        }
        .onAppear {
            startAnimations()
        }
    }
    
    // MARK: - Private Views
    
    /// AI头像主体按钮
    private var avatarButton: some View {
        Button(action: {
            onTap?()
        }) {
            ZStack {
                outerGlowEffect
                mainAvatarCircle
                aiSparkleIcon
            }
            .scaleEffect(isBreathing ? 1.05 : 1.0)
            .animation(
                .easeInOut(duration: EAAppConstants.Today.breathingAnimationDuration)
                .repeatForever(autoreverses: true),
                value: isBreathing
            )
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel(EAAppConstants.Today.Text.aiAccessibilityLabel)
        .accessibilityHint(accessibilityHintText)
    }
    
    /// 外层光晕效果
    private var outerGlowEffect: some View {
        Circle()
            .fill(
                RadialGradient(
                    colors: [
                        Color.hexColor("40E0D0").opacity(glowIntensity),
                        Color.clear
                    ],
                    center: .center,
                    startRadius: size * 0.3,
                    endRadius: size * 0.8
                )
            )
            .frame(width: size * 1.6, height: size * 1.6)
            .blur(radius: EAAppConstants.Today.shadowRadius)
    }
    
    /// 主体圆形
    private var mainAvatarCircle: some View {
        Circle()
            .fill(
                LinearGradient(
                    colors: [
                        Color.hexColor("33FFDD"),
                        Color.hexColor("40E0D0")
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .frame(width: size, height: size)
            .overlay(
                Circle()
                    .stroke(
                        LinearGradient(
                            colors: [
                                Color.white.opacity(0.4),
                                Color.clear
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 2
                    )
            )
    }
    
    /// AI闪烁图标
    private var aiSparkleIcon: some View {
        Image(systemName: "sparkles")
            .font(.system(size: size * 0.4, weight: .medium))
            .foregroundColor(.white)
            .shadow(color: .black.opacity(0.2), radius: 2, x: 0, y: 1)
    }
    
    /// 消息提示指示器覆盖层
    private var messageIndicatorOverlay: some View {
        VStack {
            HStack {
                Spacer()
                EAAIMessageIndicator(hasMessages: hasMessages, messageCount: messageCount)
                    .offset(x: 4, y: -4)
            }
            Spacer()
        }
    }
    
    // MARK: - Private Methods
    
    /// 无障碍提示文本
    private var accessibilityHintText: String {
        hasMessages 
            ? EAAppConstants.Today.Text.aiAccessibilityHintWithMessage
            : EAAppConstants.Today.Text.aiAccessibilityHintNoMessage
    }
    
    /// 启动动画效果
    private func startAnimations() {
        isBreathing = true
        startGlowAnimation()
    }
    
    /// 启动光晕动画
    private func startGlowAnimation() {
        withAnimation(
            .easeInOut(duration: EAAppConstants.Today.glowAnimationDuration)
            .repeatForever(autoreverses: true)
        ) {
            glowIntensity = 0.6
        }
    }
}

// MARK: - Preview
#Preview("无消息状态") {
    EAAIMessageIndicator(hasMessages: false, messageCount: 0)
        .padding()
        .background(Color.black)
}

#Preview("1条未读消息") {
    EAAIMessageIndicator(hasMessages: true, messageCount: 1)
        .padding()
        .background(Color.black)
}

#Preview("5条未读消息") {
    EAAIMessageIndicator(hasMessages: true, messageCount: 5)
        .padding()
        .background(Color.black)
}

#Preview("99+条未读消息") {
    EAAIMessageIndicator(hasMessages: true, messageCount: 150)
        .padding()
        .background(Color.black)
}

#Preview("AI头像带消息提示") {
    VStack(spacing: EAAppConstants.Today.mainSpacing) {
        // 无消息状态
        EAAIAvatarWithIndicator(size: EAAppConstants.Today.aiAvatarSize, hasMessages: false) {
            // AI头像点击处理 - 实际使用时会有具体逻辑
        }
        
        // 有消息状态
        EAAIAvatarWithIndicator(size: EAAppConstants.Today.aiAvatarSize, hasMessages: true, messageCount: 3) {
            // AI头像点击处理 - 实际使用时会有具体逻辑
        }
        
        // 大尺寸头像
        EAAIAvatarWithIndicator(size: EAAppConstants.Today.aiAvatarLargeSize, hasMessages: true, messageCount: 99) {
            // AI头像点击处理 - 实际使用时会有具体逻辑
        }
    }
    .padding()
    .background(Color.black)
} 