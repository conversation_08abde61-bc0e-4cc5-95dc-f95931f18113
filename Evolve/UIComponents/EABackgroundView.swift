import SwiftUI

// MARK: - 背景样式枚举

enum EABackgroundStyle {
    case authentication   // 认证页面背景
    case onboarding      // 引导页面背景
    case main            // 主应用背景
    case modal           // 模态框背景
    case ecosystem       // 生态主题背景
    case auraSpace       // AuraSpace AI对话背景
    case contentLibrary  // 智慧宝库背景
}

// MARK: - 可复用背景组件

struct EABackgroundView: View {
    let style: EABackgroundStyle
    let showParticles: Bool
    
    init(style: EABackgroundStyle = .authentication, showParticles: Bool = true) {
        self.style = style
        self.showParticles = showParticles
    }
    
    var body: some View {
        ZStack {
            // 主背景渐变
            backgroundGradient
            
            // 增强的微光粒子效果层
            if showParticles {
                EAFloatingLightEffect(style: style)
                    .allowsHitTesting(false) // 确保不拦截用户交互
            }
        }
        .ignoresSafeArea()
    }
    
    // 背景渐变
    @ViewBuilder
    private var backgroundGradient: some View {
        switch style {
        case .authentication:
            // ✨ 新增：参考HTML原型的多层径向渐变背景
            ZStack {
                // 基础线性渐变
            LinearGradient(
                gradient: Gradient(colors: [
                        Color(red: 0.04, green: 0.18, blue: 0.32), // #0A2F51
                        Color(red: 0.0, green: 0.35, blue: 0.29),  // #005A4B
                        Color(red: 0.0, green: 0.17, blue: 0.13)   // #002b20
                    ]),
                    startPoint: .init(x: 0, y: 0.2),
                    endPoint: .bottom
                )
                
                // 多层径向渐变营造深度感
                RadialGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.0, green: 0.5, blue: 0.5).opacity(0.35),
                        Color.clear
                    ]),
                    center: .init(x: 0.15, y: 0.25),
                    startRadius: 20,
                    endRadius: 200
                )
                
                RadialGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.05, green: 0.28, blue: 0.63).opacity(0.25),
                        Color.clear
                    ]),
                    center: .init(x: 0.85, y: 0.6),
                    startRadius: 30,
                    endRadius: 250
                )
                
                RadialGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.22, green: 0.94, blue: 0.49).opacity(0.18),
                        Color.clear
                    ]),
                    center: .init(x: 0.2, y: 0.85),
                    startRadius: 15,
                    endRadius: 180
                )
                
                RadialGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.0, green: 0.41, blue: 0.36).opacity(0.22),
                        Color.clear
                    ]),
                    center: .init(x: 0.5, y: 0.55),
                    startRadius: 25,
                    endRadius: 280
                )
                
                RadialGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.28, green: 0.71, blue: 0.64).opacity(0.25),
                        Color.clear
                    ]),
                    center: .init(x: 0.7, y: 0.8),
                    startRadius: 20,
                    endRadius: 220
                )
                
                RadialGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.05, green: 0.28, blue: 0.63).opacity(0.2),
                        Color.clear
                    ]),
                    center: .init(x: 0.3, y: 0.1),
                    startRadius: 25,
                    endRadius: 240
                )
            }
            
        case .onboarding:
            LinearGradient(
                gradient: Gradient(colors: [
                    Color("PrimaryTurquoise").opacity(0.8),
                    Color("BackgroundDeepGreen"),
                    Color.black.opacity(0.9)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            
        case .main:
            LinearGradient(
                gradient: Gradient(colors: [
                    Color("BackgroundDeepGreen"),
                    Color.black.opacity(0.95)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
        case .modal:
            Color.black.opacity(0.85)
            
        case .ecosystem:
            RadialGradient(
                gradient: Gradient(colors: [
                    Color("PrimaryTurquoise").opacity(0.4),
                    Color("BackgroundDeepGreen"),
                    Color.black
                ]),
                center: .center,
                startRadius: 100,
                endRadius: 400
            )
            
        case .auraSpace:
            LinearGradient(
                gradient: Gradient(colors: [
                    Color("BackgroundDeepGreen"),
                    Color("PrimaryTurquoise").opacity(0.2),
                    Color.black.opacity(0.9)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
        case .contentLibrary:
            LinearGradient(
                gradient: Gradient(colors: [
                    Color("PrimaryTurquoise").opacity(0.3),
                    Color("BackgroundDeepGreen"),
                    Color.black.opacity(0.8)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        }
    }
}

// MARK: - 漂浮微光效果组件

/// ✨ 新增：增强的漂浮微光效果，参考HTML原型
struct EAFloatingLightEffect: View {
    let style: EABackgroundStyle
    @State private var lightParticles: [FloatingLightParticle] = []
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 漂浮的微光粒子
                ForEach(0..<lightParticles.count, id: \.self) { index in
                    if index < lightParticles.count {
                        FloatingLightView(
                            particle: lightParticles[index],
                            screenSize: geometry.size
                        )
                    }
                }
            }
        }
        .onAppear {
            initializeFloatingLights()
        }
    }
    
    private func initializeFloatingLights() {
        // 根据style调整粒子数量和特性
        let particleCount = style == .authentication ? 12 : 8
        
        lightParticles = (0..<particleCount).map { _ in
            FloatingLightParticle(
                initialX: Double.random(in: 0...1),
                initialY: Double.random(in: 0...1),
                size: Double.random(in: 4...12),
                opacity: Double.random(in: 0.3...0.7),
                speed: Double.random(in: 0.5...1.5),
                phase: Double.random(in: 0...2 * .pi),
                color: generateParticleColor(for: style)
            )
        }
    }
    
    private func generateParticleColor(for style: EABackgroundStyle) -> Color {
        switch style {
        case .authentication:
            // 蓝绿色系微光
            let colors = [
                Color(red: 0.68, green: 0.85, blue: 0.9), // 浅蓝色 #AFEEEE
                Color(red: 0.25, green: 0.88, blue: 0.82), // 青绿色 #40E0D0
                Color(red: 0.56, green: 0.93, blue: 0.56), // 浅绿色
                Color(red: 0.0, green: 0.8, blue: 0.8)     // 深青色
            ]
            return colors.randomElement() ?? colors[0]
        default:
            return Color.white
        }
    }
}

// MARK: - 单个漂浮微光视图

struct FloatingLightView: View {
    let particle: FloatingLightParticle
    let screenSize: CGSize
    
    @State private var offset: CGSize = .zero
    @State private var opacity: Double = 0
    @State private var scale: Double = 1
    
    var body: some View {
        Circle()
            .fill(
                RadialGradient(
                    gradient: Gradient(colors: [
                        particle.color.opacity(opacity),
                        particle.color.opacity(opacity * 0.5),
                        Color.clear
                    ]),
                    center: .center,
                    startRadius: 0,
                    endRadius: particle.size / 2
                )
            )
            .frame(width: particle.size, height: particle.size)
            .scaleEffect(scale)
            .offset(
                x: screenSize.width * particle.initialX + offset.width,
                y: screenSize.height * particle.initialY + offset.height
            )
            .onAppear {
                startFloatingAnimation()
            }
    }
    
    private func startFloatingAnimation() {
        // 初始透明度渐入
        withAnimation(.easeIn(duration: 1.0)) {
            opacity = particle.opacity
        }
        
        // 漂浮动画 - 参考HTML原型的float效果
        withAnimation(
            .easeInOut(duration: 8.0 + Double.random(in: -2...2))
            .repeatForever(autoreverses: true)
        ) {
            offset = CGSize(
                width: Double.random(in: -30...30),
                height: -40 + Double.random(in: -15...15)
            )
        }
        
        // 缩放呼吸效果
        withAnimation(
            .easeInOut(duration: 6.0 + Double.random(in: -1...1))
            .repeatForever(autoreverses: true)
        ) {
            scale = 0.8 + Double.random(in: 0...0.4)
            }
            
        // 透明度变化
        withAnimation(
            .easeInOut(duration: 7.0 + Double.random(in: -1.5...1.5))
            .repeatForever(autoreverses: true)
        ) {
            opacity = particle.opacity * Double.random(in: 0.4...1.0)
        }
    }
}

// MARK: - 漂浮微光粒子数据模型

struct FloatingLightParticle {
    let initialX: Double      // 初始X位置 (0-1)
    let initialY: Double      // 初始Y位置 (0-1)
    let size: Double          // 粒子大小
    let opacity: Double       // 基础透明度
    let speed: Double         // 移动速度
    let phase: Double         // 动画相位
    let color: Color          // 粒子颜色
}

// MARK: - SwiftUI预览

#Preview("认证背景") {
    ZStack {
        EABackgroundView(style: .authentication, showParticles: true)
        
        VStack(spacing: 30) {
            Text("欢迎回来")
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            VStack(spacing: 20) {
                // 模拟输入框
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.1))
                    .frame(height: 50)
                    .overlay(
                        Text("手机号码")
                            .foregroundColor(.white.opacity(0.7))
                            .padding(.leading)
                        , alignment: .leading
                    )
                
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.1))
                    .frame(height: 50)
                    .overlay(
                        Text("验证码")
                            .foregroundColor(.white.opacity(0.7))
                            .padding(.leading)
                        , alignment: .leading
                    )
                
                // 模拟按钮
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color("PrimaryTurquoise"))
                    .frame(height: 50)
                    .overlay(
                        Text("登录")
                            .foregroundColor(.white)
                            .fontWeight(.semibold)
                    )
            }
            .padding(.horizontal, 40)
        }
    }
}

#Preview("引导背景") {
    ZStack {
    EABackgroundView(style: .onboarding, showParticles: true)
        
        VStack(spacing: 20) {
            Text("开始您的计划之旅")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
            
            Text("通过AI教练的引导，\n让好计划自然生长")
                .font(.body)
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
        }
        .padding(.horizontal, 40)
    }
} 