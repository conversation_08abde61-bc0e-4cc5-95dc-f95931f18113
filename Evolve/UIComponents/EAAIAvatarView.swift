import SwiftUI

/// AI头像组件
/// 显示Aura AI的头像，包含呼吸动画效果和状态指示
struct EAAIAvatarView: View {
    let size: CGFloat
    let isActive: Bool
    
    @State private var breathingScale: CGFloat = 1.0
    @State private var glowOpacity: Double = 0.3
    
    init(size: CGFloat = 60, isActive: Bool = true) {
        self.size = size
        self.isActive = isActive
    }
    
    var body: some View {
        ZStack {
            // 外层光晕
            Circle()
                .fill(
                    RadialGradient(
                        colors: [
                            Color("PrimaryTurquoise").opacity(glowOpacity),
                            Color("PrimaryTurquoise").opacity(0.1),
                            Color.clear
                        ],
                        center: .center,
                        startRadius: size * 0.3,
                        endRadius: size * 0.8
                    )
                )
                .frame(width: size * 1.6, height: size * 1.6)
                .scaleEffect(breathingScale)
            
            // 主体圆形
            Circle()
                .fill(
                    LinearGradient(
                        colors: [
                            Color("PrimaryTurquoise"),
                            Color("PrimaryTurquoise").opacity(0.8)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: size, height: size)
                .overlay(
                    Circle()
                        .stroke(Color.white.opacity(0.3), lineWidth: 2)
                )
                .scaleEffect(breathingScale)
            
            // AI图标
            Image(systemName: "brain.head.profile")
                .font(.system(size: size * 0.4, weight: .medium))
                .foregroundColor(.white)
                .scaleEffect(breathingScale)
            
            // 活跃状态指示器
            if isActive {
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        Circle()
                            .fill(Color.green)
                            .frame(width: size * 0.2, height: size * 0.2)
                            .overlay(
                                Circle()
                                    .stroke(Color.white, lineWidth: 2)
                            )
                            .offset(x: -size * 0.1, y: -size * 0.1)
                    }
                }
                .frame(width: size, height: size)
            }
        }
        .onAppear {
            startBreathingAnimation()
        }
    }
    
    // 开始呼吸动画
    private func startBreathingAnimation() {
        guard isActive else { return }
        
        withAnimation(
            .easeInOut(duration: 2.0)
            .repeatForever(autoreverses: true)
        ) {
            breathingScale = 1.1
        }
        
        withAnimation(
            .easeInOut(duration: 1.5)
            .repeatForever(autoreverses: true)
        ) {
            glowOpacity = 0.6
        }
    }
}

/// AI头像状态枚举
enum EAAIAvatarState {
    case idle       // 空闲状态
    case thinking   // 思考状态
    case speaking   // 说话状态
    case listening  // 聆听状态
}

/// 带状态的AI头像组件
struct EAStatefulAIAvatarView: View {
    let size: CGFloat
    let state: EAAIAvatarState
    
    @State private var thinkingRotation: Double = 0
    @State private var speakingPulse: CGFloat = 1.0
    
    init(size: CGFloat = 60, state: EAAIAvatarState = .idle) {
        self.size = size
        self.state = state
    }
    
    var body: some View {
        ZStack {
            // 基础头像
            EAAIAvatarView(size: size, isActive: state != .idle)
            
            // 状态特效
            switch state {
            case .thinking:
                thinkingEffect
            case .speaking:
                speakingEffect
            case .listening:
                listeningEffect
            case .idle:
                EmptyView()
            }
        }
        .onAppear {
            startStateAnimation()
        }
        .onChange(of: state) { _, _ in
            startStateAnimation()
        }
    }
    
    // 思考状态特效
    private var thinkingEffect: some View {
        Circle()
            .stroke(Color("PrimaryTurquoise").opacity(0.5), lineWidth: 2)
            .frame(width: size * 1.3, height: size * 1.3)
            .rotationEffect(.degrees(thinkingRotation))
    }
    
    // 说话状态特效
    private var speakingEffect: some View {
        Circle()
            .fill(Color("PrimaryTurquoise").opacity(0.2))
            .frame(width: size * 1.4, height: size * 1.4)
            .scaleEffect(speakingPulse)
    }
    
    // 聆听状态特效
    private var listeningEffect: some View {
        ForEach(0..<3, id: \.self) { index in
            Circle()
                .stroke(Color("PrimaryTurquoise").opacity(0.3), lineWidth: 1)
                .frame(width: size * (1.2 + CGFloat(index) * 0.2), height: size * (1.2 + CGFloat(index) * 0.2))
                .opacity(0.7 - Double(index) * 0.2)
        }
    }
    
    // 开始状态动画
    private func startStateAnimation() {
        switch state {
        case .thinking:
            withAnimation(.linear(duration: 2.0).repeatForever(autoreverses: false)) {
                thinkingRotation = 360
            }
        case .speaking:
            withAnimation(.easeInOut(duration: 0.8).repeatForever(autoreverses: true)) {
                speakingPulse = 1.2
            }
        case .listening, .idle:
            break
        }
    }
}

#Preview {
    ZStack {
        Color("BackgroundDeepGreen")
            .ignoresSafeArea()
        
        VStack(spacing: 30) {
            // 基础头像
            EAAIAvatarView(size: 80, isActive: true)
            
            // 不同状态的头像
            HStack(spacing: 20) {
                VStack {
                    EAStatefulAIAvatarView(size: 60, state: .idle)
                    Text("空闲")
                        .font(.caption)
                        .foregroundColor(.white)
                }
                
                VStack {
                    EAStatefulAIAvatarView(size: 60, state: .thinking)
                    Text("思考")
                        .font(.caption)
                        .foregroundColor(.white)
                }
                
                VStack {
                    EAStatefulAIAvatarView(size: 60, state: .speaking)
                    Text("说话")
                        .font(.caption)
                        .foregroundColor(.white)
                }
                
                VStack {
                    EAStatefulAIAvatarView(size: 60, state: .listening)
                    Text("聆听")
                        .font(.caption)
                        .foregroundColor(.white)
                }
            }
        }
    }
}