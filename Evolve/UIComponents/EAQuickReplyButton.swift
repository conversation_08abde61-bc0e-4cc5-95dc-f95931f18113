import SwiftUI

/// 快速回复按钮组件
/// 用于AI对话界面的预设回复选项，支持点击动画和触觉反馈
struct EAQuickReplyButton: View {
    let text: String
    let icon: String?
    let action: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: {
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
            
            action()
        }) {
            HStack(alignment: .top) {
                if let icon = icon {
                    Image(systemName: icon)
                        .font(.system(size: 16))
                        .foregroundColor(Color("PrimaryTurquoise"))
                        .padding(.leading, 8)
                }
            Text(text)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(Color("PrimaryTurquoise"))
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
            }
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.white.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color("PrimaryTurquoise").opacity(0.5), lineWidth: 1)
                        )
                )
                .scaleEffect(isPressed ? 0.95 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        }, perform: {})
    }
}

/// 快速回复按钮组容器
/// 自动排列多个快速回复按钮，支持自适应换行
struct EAQuickReplyButtonGroup: View {
    let replies: [String]
    let onReplySelected: (String) -> Void
    
    var body: some View {
        LazyVGrid(columns: [
            GridItem(.adaptive(minimum: 120), spacing: 8)
        ], spacing: 8) {
            ForEach(replies, id: \.self) { reply in
                EAQuickReplyButton(text: reply, icon: nil) {
                    onReplySelected(reply)
                }
            }
        }
    }
}

#Preview {
    ZStack {
        Color("BackgroundDeepGreen")
            .ignoresSafeArea()
        
        VStack(spacing: 20) {
            // 单个按钮预览
            EAQuickReplyButton(
                text: "我想要增强专注力",
                icon: "brain.head.profile"
            ) {
                // 快速回复点击处理 - 实际使用时会有具体逻辑
            }
            
            EAQuickReplyButton(
                text: "最近觉得很累",
                icon: "heart.fill"
            ) {
                // 快速回复点击处理 - 实际使用时会有具体逻辑
            }
            
            EAQuickReplyButton(
                text: "需要一些动力",
                icon: "bolt.fill"
            ) {
                // 快速回复点击处理 - 实际使用时会有具体逻辑
            }
            
            EAQuickReplyButton(
                text: "想了解进展情况",
                icon: "chart.line.uptrend.xyaxis"
            ) {
                // 快速回复点击处理 - 实际使用时会有具体逻辑
            }
            
            // 按钮组预览
            EAQuickReplyButtonGroup(
                replies: [
                    "我想了解更多",
                    "开始新计划",
                    "查看我的进度",
                    "需要帮助"
                ]
            ) { reply in
                // 选择回复处理 - 实际使用时会有具体逻辑
            }
        }
        .padding()
    }
}