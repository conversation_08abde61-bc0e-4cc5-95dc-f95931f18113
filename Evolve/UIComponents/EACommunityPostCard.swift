import SwiftUI

/// 数字宇宙社区帖子卡片 - 星域信标设计（性能优化版）
/// 🚀 性能优化：移除复杂特效，保持星域主题，提升List滚动性能
/// 🌟 主题保持：星际探索者发布的宇宙信标设计理念
/// 🔑 关键优化：简化UI结构、减少状态变量、优化图片加载
/// ✅ 修复：按钮交互冲突、星域主题背景、真实用户数据集成、中文时间显示
@MainActor
struct EACommunityPostCard: View {

    // MARK: - Properties

    /// 帖子数据
    let post: EACommunityPost

    /// ViewModel引用（用于状态同步）
    let viewModel: EACommunityViewModel?

    /// 是否已点赞
    @State private var isLiked: Bool = false

    /// 点赞数量
    @State private var currentLikeCount: Int = 0

    /// 评论数量
    @State private var currentCommentCount: Int = 0

    /// 🔑 性能优化：减少状态变量，移除复杂动画状态
    @State private var isProcessingLike: Bool = false
    
    /// 🔑 新增：图片查看器状态管理
    @State private var showImageViewer: Bool = false
    @State private var selectedImageIndex: Int = 0

    /// 点赞回调
    let onLike: ((EACommunityPost) async throws -> Void)?

    /// 帖子点击回调
    let onPostTap: ((EACommunityPost) -> Void)?

    /// 评论回调
    let onComment: ((EACommunityPost) -> Void)?
    
    /// 分享回调
    let onShare: ((EACommunityPost) -> Void)?
    
    /// 用户头像点击回调
    let onUserProfileTap: () -> Void
    
    /// 删除回调
    let onDelete: ((EACommunityPost) async -> Void)?
    
    /// 是否可以删除
    let canDelete: Bool
    
    // MARK: - 🔑 性能优化：预计算常量，避免重复计算
    private let cardPadding: CGFloat = 16
    private let avatarSize: CGFloat = 40
    private let cornerRadius: CGFloat = 16
    // 🔑 修复：固定缩略图大小为80*80（参考微信朋友圈）
    private let thumbnailSize: CGFloat = 80
    private let thumbnailSpacing: CGFloat = 4
    
    // MARK: - 🌟 星域主题颜色常量
    private let stellarPrimary = Color.hexColor("40E0D0") // 荧光青色
    private let stellarSecondary = Color.hexColor("1E3A8A") // 深蓝色
    private let stellarBackground = Color.hexColor("0F172A") // 深空蓝
    private let stellarAccent = Color.hexColor("3B82F6") // 亮蓝色
    
    // MARK: - Initialization
    
    init(
        post: EACommunityPost,
        viewModel: EACommunityViewModel?,
        isLiked: Bool = false,
        onLike: ((EACommunityPost) async throws -> Void)? = nil,
        onComment: ((EACommunityPost) -> Void)? = nil,
        onShare: ((EACommunityPost) -> Void)? = nil,
        onPostTap: ((EACommunityPost) -> Void)? = nil,
        onDelete: ((EACommunityPost) async -> Void)? = nil,
        canDelete: Bool = false,
        onUserProfileTap: @escaping () -> Void
    ) {
        self.post = post
        self.viewModel = viewModel
        self.onLike = onLike
        self.onComment = onComment
        self.onShare = onShare
        self.onPostTap = onPostTap
        self.onDelete = onDelete
        self.canDelete = canDelete
        self.onUserProfileTap = onUserProfileTap
        
        // 初始化状态
        self._isLiked = State(initialValue: isLiked)
        self._currentLikeCount = State(initialValue: post.likeCount)
        self._currentCommentCount = State(initialValue: post.commentCount)
    }
    
    // MARK: - Body
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 用户信息区域
            userInfoSection
            
            // 内容区域 - ✅ 修复：移除单独的点击处理，统一到整个卡片
            contentSection
            
            // 图片区域（优化版）
            imageGrid
            
            // 互动按钮区域 - ✅ 修复：按钮区域独立处理，不响应卡片点击
            actionButtonsSection
        }
        .background(stellarCardBackground)
        .cornerRadius(cornerRadius)
        .shadow(color: stellarPrimary.opacity(0.2), radius: 6, x: 0, y: 3)
        // ✅ 修复：整个卡片响应点击
        .contentShape(Rectangle())
        .onTapGesture {
            onPostTap?(post)
        }
        // 🔑 修复：多图片查看器，支持左右滑动和正确的初始索引
        .fullScreenCover(isPresented: $showImageViewer) {
            if !post.imageURLs.isEmpty {
                EAZoomableImageView(
                    imageSources: validImageSources,
                    startIndex: min(selectedImageIndex, validImageSources.count - 1)
                )
            }
        }
        .onAppear {
            // ✅ 修复：避免在视图更新周期中发布状态变化
            // 使用Task将状态同步推迟到下一个运行循环
            Task { @MainActor in
                // 🔑 修复：优化状态初始化逻辑，减少不必要的状态重置
                if let viewModel = viewModel {
                    let vmIsLiked = viewModel.getPostLikeStatus(postId: post.id)
                    let vmLikeCount = viewModel.getPostLikeCount(postId: post.id)
                    let vmCommentCount = viewModel.getPostCommentCount(postId: post.id)

                    // 🔑 修复：确保状态同步，优先使用ViewModel状态
                    if vmIsLiked != isLiked {
                        isLiked = vmIsLiked
                    }
                    if vmLikeCount != currentLikeCount {
                        currentLikeCount = vmLikeCount
                    }
                    if vmCommentCount != currentCommentCount {
                        currentCommentCount = vmCommentCount
                    }
                } else {
                    // 🔑 备用方案：仅在没有ViewModel且当前状态为初始值时使用帖子原始数据
                    if currentLikeCount == 0 && currentCommentCount == 0 {
                        currentLikeCount = post.likeCount
                        currentCommentCount = post.commentCount
                    }
                }
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .postLikeStatusDidChange)) { notification in
            // 监听点赞状态变化，确保实时更新
            if let postId = notification.object as? UUID, postId == post.id,
               let newIsLiked = notification.userInfo?["isLiked"] as? Bool,
               let newCount = notification.userInfo?["newCount"] as? Int {
                isLiked = newIsLiked
                currentLikeCount = newCount
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .commentCountDidChange)) { notification in
            // 监听评论数量变化，确保实时更新
            if let postId = notification.object as? UUID, postId == post.id,
               let newCount = notification.userInfo?["newCount"] as? Int {
                currentCommentCount = newCount
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .communityDataDidUpdate)) { notification in
            // 监听社区数据更新确认通知
            if let postId = notification.object as? UUID, postId == post.id,
               let updateType = notification.userInfo?["type"] as? String {
                switch updateType {
                case "commentCount":
                    if let newCount = notification.userInfo?["newCount"] as? Int {
                        currentCommentCount = newCount
                    }
                case "likeCount":
                    if let newCount = notification.userInfo?["newCount"] as? Int {
                        currentLikeCount = newCount
                    }
                default:
                    break
                }
            }
        }
    }
    
    // MARK: - 计算属性
    
    /// 🔑 修复：有效的图片源列表 - 支持本地和网络图片
    private var validImageSources: [ImageSource] {
        return post.imageURLs.compactMap { urlString in
            guard !urlString.isEmpty else { return nil }

            // 🔑 修复：区分本地路径和网络URL
            if urlString.hasPrefix("http") {
                // 网络图片：验证URL格式
                guard let url = URL(string: urlString),
                      url.scheme != nil,
                      url.host != nil else {
                    return nil
                }
                return ImageSource.remote(url: url)
            } else {
                // 本地图片：直接使用相对路径
                return ImageSource.local(path: urlString)
            }
        }
    }
    
    // MARK: - 子视图组件（性能优化版）
    
    /// 用户信息区域 - ✅ 优化布局，集成真实用户数据
    private var userInfoSection: some View {
        HStack(spacing: 12) {
            // ✅ 真实用户头像
            realUserAvatarView
                .onTapGesture {
                    onUserProfileTap()
                }
            
            VStack(alignment: .leading, spacing: 6) {
                // ✅ 第一行：用户名和星际等级
                HStack(spacing: 8) {
                    Text(getRealUsername())
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                    
                    // 星际等级标识
                    if let stellarLevel = getRealStellarLevel() {
                        stellarLevelBadge(level: stellarLevel)
                    }
                    
                    Spacer()
                }
                
                // ✅ 第二行：探索者称号和发布时间
                HStack(spacing: 8) {
                    // 探索者称号
                    if let explorerTitle = getRealExplorerTitle() {
                        Text(explorerTitle)
                            .font(.system(size: 12, weight: .medium))
                            .padding(.horizontal, 8)
                            .padding(.vertical, 3)
                            .background(stellarPrimary.opacity(0.2))
                            .foregroundColor(stellarPrimary)
                            .cornerRadius(8)
                    }
                    
                    // 分类标签
                    if post.category == "challenge" {
                        Text("挑战")
                            .font(.system(size: 12, weight: .medium))
                            .padding(.horizontal, 8)
                            .padding(.vertical, 3)
                            .background(Color.yellow.opacity(0.2))
                            .foregroundColor(.yellow)
                            .cornerRadius(8)
                    }
                    
                    Spacer()
                    
                    // ✅ 修复：中文时间显示
                    Text(chineseTimeAgoString(from: post.creationDate))
                        .font(.system(size: 13))
                        .foregroundColor(.gray)
                }
            }
        }
        .padding(.horizontal, cardPadding)
        .padding(.top, cardPadding)
        .padding(.bottom, 12)
    }
    
    /// ✅ 真实用户头像视图
    private var realUserAvatarView: some View {
        Group {
            if let avatarData = getRealAvatarData() {
                // 使用真实头像
                EAAvatarView(avatarData: avatarData, size: avatarSize)
            } else {
                // 默认星际探索者头像
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [stellarPrimary.opacity(0.3), stellarSecondary.opacity(0.3)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: avatarSize, height: avatarSize)
                    .overlay(
                        Text(String(getRealUsername().prefix(1)).uppercased())
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(stellarPrimary)
                    )
                    .overlay(
                        Circle()
                            .stroke(stellarPrimary.opacity(0.4), lineWidth: 1)
                    )
            }
        }
    }
    
    /// 星际等级徽章
    private func stellarLevelBadge(level: Int) -> some View {
        HStack(spacing: 3) {
            Image(systemName: "star.fill")
                .font(.system(size: 10))
                .foregroundColor(stellarPrimary)
            
            Text("Lv.\(level)")
                .font(.system(size: 12, weight: .bold))
                .foregroundColor(stellarPrimary)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 3)
        .background(stellarPrimary.opacity(0.15))
        .cornerRadius(10)
    }
    
    /// 内容区域 - 简化版
    private var contentSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            if !post.content.isEmpty {
                // 🔑 修复：确保文字靠左对齐
                HStack {
                    Text(post.content)
                        .font(.system(size: 15, weight: .regular))
                        .foregroundColor(.white)
                        .lineLimit(nil)
                        .multilineTextAlignment(.leading)
                    Spacer()
                }
            }
            
            // 标签（如果有）
            if !post.tags.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        ForEach(post.tags, id: \.self) { tag in
                            Text("#\(tag)")
                                .font(.system(size: 12, weight: .medium))
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(stellarPrimary.opacity(0.1))
                                .foregroundColor(stellarPrimary)
                                .cornerRadius(8)
                        }
                    }
                    .padding(.horizontal, cardPadding)
                }
            }
        }
        .padding(.horizontal, cardPadding)
        .padding(.bottom, 12)
    }
    
    /// 🔑 修复：微信朋友圈风格的固定尺寸图片区域
    private var imageGrid: some View {
        let imageCount = post.imageURLs.count
        
        return VStack(alignment: .leading, spacing: 0) {
            if imageCount > 0 {
                // 🔑 修复：图片在文字内容左下方，从左边排列
                HStack {
                    fixedSizeImageGrid(images: post.imageURLs)
                    Spacer()
                }
                .padding(.horizontal, cardPadding)
                .padding(.top, 8)
            }
        }
    }
    
    /// 🔑 修复：固定尺寸图片网格布局（80*80，一行最多3张）
    @ViewBuilder
    private func fixedSizeImageGrid(images: [String]) -> some View {
        let imageCount = images.count
        let imagesToShow = Array(images.prefix(9)) // 最多显示9张图片
        
        VStack(alignment: .leading, spacing: thumbnailSpacing) {
            // 根据图片数量分行显示
            ForEach(0..<((imageCount + 2) / 3), id: \.self) { rowIndex in
                HStack(spacing: thumbnailSpacing) {
                    ForEach(0..<3, id: \.self) { colIndex in
                        let imageIndex = rowIndex * 3 + colIndex
                        if imageIndex < imagesToShow.count {
                            if imageIndex == 8 && imageCount > 9 {
                                // 第9张图片显示"+N"指示器
                                moreImagesIndicator(
                                    imagePath: imagesToShow[imageIndex], 
                                    remainingCount: imageCount - 8
                                )
                            } else {
                                fixedSizeThumbnail(
                                    imagePath: imagesToShow[imageIndex], 
                                    index: imageIndex
                                )
                            }
                        }
                    }
                }
            }
        }
    }
    
    /// 🔑 修复：固定80*80尺寸的缩略图（微信级别优化）
    private func fixedSizeThumbnail(imagePath: String, index: Int) -> some View {
        // 🚀 使用微信级别的高性能异步图片组件
        EAHighPerformanceAsyncImage.forCommunityThumbnail(
            url: getImageURL(from: imagePath)
        ) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
        } placeholder: {
            // 🚀 优化的占位符：星域主题设计
            Rectangle()
                .fill(
                    LinearGradient(
                        colors: [
                            stellarBackground.opacity(0.3),
                            stellarPrimary.opacity(0.1)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    // 🌟 星域加载指示器
                    ZStack {
                        Circle()
                            .stroke(stellarPrimary.opacity(0.3), lineWidth: 2)
                            .frame(width: 20, height: 20)
                        
                        Circle()
                            .trim(from: 0, to: 0.3)
                            .stroke(stellarPrimary, lineWidth: 2)
                            .frame(width: 20, height: 20)
                            .rotationEffect(.degrees(-90))
                            .animation(.linear(duration: 1).repeatForever(autoreverses: false), value: UUID())
                    }
                )
        }
        .frame(width: thumbnailSize, height: thumbnailSize)
        .clipped()
        .cornerRadius(6)
        .onTapGesture {
            // 🔑 新增：点击缩略图打开图片查看器
            selectedImageIndex = index
            showImageViewer = true
        }
    }
    
    /// 🔑 修复：更多图片指示器（固定80*80尺寸，微信级别优化）
    private func moreImagesIndicator(imagePath: String, remainingCount: Int) -> some View {
        ZStack {
            // 🚀 使用微信级别的高性能异步图片组件
            EAHighPerformanceAsyncImage.forCommunityThumbnail(
                url: getImageURL(from: imagePath)
            ) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                Rectangle()
                    .fill(stellarBackground.opacity(0.3))
            }
            .frame(width: thumbnailSize, height: thumbnailSize)
            .clipped()
            .cornerRadius(6)
            
            // 半透明遮罩
            Rectangle()
                .fill(Color.black.opacity(0.6))
                .cornerRadius(6)
            
            // 剩余数量文字
            if remainingCount > 0 {
                Text("+\(remainingCount)")
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(.white)
            }
        }
        .frame(width: thumbnailSize, height: thumbnailSize)
        .onTapGesture {
            // 🔑 新增：点击"更多图片"指示器打开查看器
            selectedImageIndex = 8 // 从第9张图片开始
            showImageViewer = true
        }
    }
    
    /// 🔑 修复：将本地路径转换为file:// URL - 与EAZoomableImageView保持一致
    private func getImageURL(from path: String) -> URL? {
        // 如果已经是完整URL，直接返回
        if path.hasPrefix("http") {
            return URL(string: path)
        }

        // 🔑 修复：使用与EAZoomableImageView相同的路径处理逻辑
        return getLocalImageURL(from: path)
    }
    
    /// ✅ 修复：互动按钮区域 - 独立的按钮处理，不冲突
    private var actionButtonsSection: some View {
        HStack(spacing: 24) {
            // ✅ 修复：点赞按钮 - 使用Button的action
            Button {
                Task {
                    await handleLike()
                }
            } label: {
                HStack(spacing: 6) {
                    Image(systemName: isLiked ? "heart.fill" : "heart")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(isLiked ? .red : stellarPrimary.opacity(0.8))
                    
                    Text("\(currentLikeCount)")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(stellarPrimary.opacity(0.8))
                }
            }
            .disabled(isProcessingLike)
            .buttonStyle(PlainButtonStyle())
            
            // ✅ 修复：评论按钮 - 使用Button的action
            Button {
                onComment?(post)
            } label: {
                HStack(spacing: 6) {
                    Image(systemName: "message")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(stellarPrimary.opacity(0.8))
                    
                    Text("\(currentCommentCount)")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(stellarPrimary.opacity(0.8))
                }
            }
            .buttonStyle(PlainButtonStyle())
            
            // ✅ 修复：分享按钮 - 移除数量显示，简化设计
            Button {
                onShare?(post)
            } label: {
                Image(systemName: "square.and.arrow.up")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(stellarPrimary.opacity(0.8))
            }
            .buttonStyle(PlainButtonStyle())
            
            Spacer()
        }
        .padding(.horizontal, cardPadding)
        .padding(.bottom, cardPadding)
    }
    
    /// 🌟 星域数字宇宙主题背景
    private var stellarCardBackground: some View {
        RoundedRectangle(cornerRadius: cornerRadius)
            .fill(
                LinearGradient(
                    colors: [
                        stellarBackground,
                        stellarSecondary.opacity(0.4),
                        stellarBackground
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .overlay(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .stroke(
                        LinearGradient(
                            colors: [stellarPrimary.opacity(0.4), stellarAccent.opacity(0.2)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
    }
    
    // MARK: - 真实用户数据获取方法
    
    /// 获取真实用户名
    private func getRealUsername() -> String {
        return post.authorSocialProfile?.user?.username ?? "星际探索者"
    }
    
    /// 获取真实头像数据
    private func getRealAvatarData() -> EAAvatarData? {
        return post.authorSocialProfile?.user?.avatarData
    }
    
    /// 获取真实星际等级
    private func getRealStellarLevel() -> Int? {
        // 🔑 修复：确保能正确获取星际等级
        if let stellarLevel = post.authorSocialProfile?.stellarLevel, stellarLevel > 0 {
            return stellarLevel
        }
        
        // 🔑 备用方案：如果没有社交档案，从用户基础信息获取
        if let user = post.authorSocialProfile?.user {
            // 基于用户习惯完成情况计算基础等级
            return calculateBasicStellarLevel(for: user)
        }
        
        // 🔑 默认等级：新用户默认为1级
        return 1
    }
    
    /// 获取真实探索者称号
    private func getRealExplorerTitle() -> String? {
        // 🔑 修复：确保能正确获取探索者称号
        if let explorerTitle = post.authorSocialProfile?.explorerTitle, !explorerTitle.isEmpty {
            return explorerTitle
        }
        
        // 🔑 备用方案：根据星际等级生成默认称号
        if let stellarLevel = getRealStellarLevel() {
            return generateDefaultExplorerTitle(for: stellarLevel)
        }
        
        // 🔑 默认称号
        return "新手探索者"
    }
    
    /// 🔑 新增：计算基础星际等级
    private func calculateBasicStellarLevel(for user: EAUser) -> Int {
        // 基于用户创建时间和基础活跃度计算等级
        let daysSinceCreation = Calendar.current.dateComponents([.day], from: user.creationDate, to: Date()).day ?? 0
        let baseLevel = max(1, daysSinceCreation / 30) // 每30天提升1级
        return min(baseLevel, 10) // 最高10级
    }
    
    /// 🔑 新增：生成默认探索者称号
    private func generateDefaultExplorerTitle(for level: Int) -> String {
        switch level {
        case 1...2:
            return "新手探索者"
        case 3...4:
            return "星际旅者"
        case 5...6:
            return "宇宙领航员"
        case 7...8:
            return "星域守护者"
        case 9...10:
            return "宇宙大师"
        default:
            return "传奇探索者"
        }
    }
    
    // MARK: - 辅助方法
    
    /// 处理点赞
    private func handleLike() async {
        guard !isProcessingLike else { return }
        
        isProcessingLike = true
        
        // 乐观更新UI
        let previousLiked = isLiked
        let previousCount = currentLikeCount
        
        isLiked.toggle()
        currentLikeCount += isLiked ? 1 : -1
        
        do {
            try await onLike?(post)
        } catch {
            // 回滚UI状态
            isLiked = previousLiked
            currentLikeCount = previousCount
        }
        
        isProcessingLike = false
    }
    
    /// ✅ 修复：中文时间格式化
    private func chineseTimeAgoString(from date: Date) -> String {
        let now = Date()
        let timeInterval = now.timeIntervalSince(date)
        
        if timeInterval < 60 {
            return "刚刚"
        } else if timeInterval < 3600 {
            let minutes = Int(timeInterval / 60)
            return "\(minutes)分钟前"
        } else if timeInterval < 86400 {
            let hours = Int(timeInterval / 3600)
            return "\(hours)小时前"
        } else if timeInterval < 172800 { // 2天
            return "昨天"
        } else if timeInterval < 604800 { // 7天
            let days = Int(timeInterval / 86400)
            return "\(days)天前"
        } else {
            let formatter = DateFormatter()
            formatter.dateFormat = "MM月dd日"
            return formatter.string(from: date)
        }
    }
}

/// 🔑 性能优化：移除复杂的样式枚举，使用简单设计
enum EACommunityPostCardStyle {
    case standard
}

// MARK: - 预览
#Preview("社区帖子卡片") {
    let samplePost = EACommunityPost(
        title: "我的晨跑成就",
        content: "今天完成了晨跑习惯，感觉很棒！",
        habitName: "晨跑",
        category: "achievement",
        energyLevel: 8
    )
    
    EACommunityPostCard(
        post: samplePost,
        viewModel: nil,
        onLike: { _ in },
        onComment: { _ in },
        onShare: { _ in },
        onUserProfileTap: { }
    )
    .padding()
    .background(Color.hexColor("000B1A"))
}
