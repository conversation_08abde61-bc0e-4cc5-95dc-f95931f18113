import SwiftUI

/// 内容卡片组件
/// 用于显示智慧宝库中的文章、练习等内容，支持Pro标识和点击动画
struct EAContentCard: View {
    let title: String
    let description: String
    let contentType: String
    let isPro: Bool
    let readingTime: String?
    let action: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: {
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
            
            action()
        }) {
            VStack(alignment: .leading, spacing: 12) {
                // 顶部标签区域
                HStack {
                    // 内容类型标签
                    Text(contentTypeLabel)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(Color("PrimaryTurquoise"))
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color("PrimaryTurquoise").opacity(0.2))
                        )
                    
                    Spacer()
                    
                    // Pro标识
                    if isPro {
                        EAProBadge()
                    }
                }
                
                // 标题
                Text(title)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.leading)
                    .lineLimit(2)
                
                // 描述
                Text(description)
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(Color("TextSecondary"))
                    .multilineTextAlignment(.leading)
                    .lineLimit(3)
                
                // 底部信息
                HStack {
                    if let readingTime = readingTime {
                        HStack(spacing: 4) {
                            Image(systemName: "clock")
                                .font(.system(size: 12))
                                .foregroundColor(Color("TextSecondary").opacity(0.7))
                            
                            Text(readingTime)
                                .font(.system(size: 12, weight: .regular))
                                .foregroundColor(Color("TextSecondary").opacity(0.7))
                        }
                    }
                    
                    Spacer()
                    
                    Image(systemName: "arrow.right")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color("PrimaryTurquoise"))
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white.opacity(0.08))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.white.opacity(0.1), lineWidth: 1)
                    )
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .opacity(isPressed ? 0.8 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        }, perform: {})
    }
    
    // 内容类型标签文本
    private var contentTypeLabel: String {
        switch contentType {
        case "motivation":
            return "动力包"
        case "psychology_exercise":
            return "心理练习"
        case "success_story":
            return "成功故事"
        case "wisdom":
            return "智慧"
        case "mindfulness":
            return "正念"
        default:
            return "内容"
        }
    }
}

/// Pro会员标识组件
struct EAProBadge: View {
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: "crown.fill")
                .font(.system(size: 10))
                .foregroundColor(.yellow)
            
            Text("PRO")
                .font(.system(size: 10, weight: .bold))
                .foregroundColor(.yellow)
        }
        .padding(.horizontal, 6)
        .padding(.vertical, 3)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(Color.yellow.opacity(0.2))
                .overlay(
                    RoundedRectangle(cornerRadius: 6)
                        .stroke(Color.yellow.opacity(0.5), lineWidth: 1)
                )
        )
    }
}

#Preview {
    ZStack {
        Color("BackgroundDeepGreen")
            .ignoresSafeArea()
        
        VStack(spacing: 16) {
            // 免费内容卡片
            EAContentCard(
                title: "晨间仪式的力量",
                description: "探索如何通过简单的晨间习惯来改变你的一整天，提升专注力和幸福感。",
                contentType: "motivation",
                isPro: false,
                readingTime: "5分钟"
            ) {
                // 点击处理 - 实际使用时会有具体逻辑
            }
            
            // Pro内容卡片
            EAContentCard(
                title: "深度冥想练习",
                description: "通过专业的冥想技巧，学会在忙碌的生活中找到内心的平静与专注。",
                contentType: "psychology_exercise",
                isPro: true,
                readingTime: "15分钟"
            ) {
                // 点击处理 - 实际使用时会有具体逻辑
            }
        }
        .padding()
    }
}