import SwiftUI

/// 🚀 图片网格显示组件 - 微信朋友圈级别优化
/// 核心功能：分层缓存 + 智能预加载 + 渐进式显示 + 性能监控
/// 用于在帖子中显示多张图片，支持点击查看大图
/// 自动适配1-9张图片的最佳布局
@MainActor
struct EAImageGridView: View {
    
    // MARK: - 属性
    
    /// 图片路径数组
    let imagePaths: [String]
    
    /// 点击查看大图回调
    let onImageTap: (String, Int) -> Void
    
    /// 🚀 新增：是否启用渐进式加载
    let enableProgressive: Bool
    
    /// 🚀 新增：是否启用预加载
    let enablePreload: Bool
    
    /// 最大显示高度
    private let maxHeight: CGFloat = 300
    
    /// ✅ 修复：通过Environment获取全局共享图片缓存服务
    @Environment(\.imageCacheService) private var imageCacheService
    
    // MARK: - 🚀 性能优化状态
    
    /// 当前可见的图片索引
    @State private var visibleImageIndices: Set<Int> = []
    
    /// 预加载任务
    @State private var preloadTask: Task<Void, Never>?
    
    // MARK: - 初始化
    
    init(
        imagePaths: [String],
        enableProgressive: Bool = true,
        enablePreload: Bool = true,
        onImageTap: @escaping (String, Int) -> Void = { _, _ in }
    ) {
        self.imagePaths = imagePaths
        self.enableProgressive = enableProgressive
        self.enablePreload = enablePreload
        self.onImageTap = onImageTap
    }
    
    // MARK: - 主视图
    
    var body: some View {
        if !imagePaths.isEmpty {
            imageGridContent
                .frame(maxHeight: maxHeight)
                .clipped()
                .onAppear {
                    startPreloadingIfNeeded()
                }
                .onDisappear {
                    cancelPreloading()
                }
        }
    }
    
    // MARK: - 🚀 预加载管理
    
    /// 开始预加载（如果启用）
    private func startPreloadingIfNeeded() {
        guard enablePreload, let cacheService = imageCacheService else { return }
        
        preloadTask = Task {
            // 🚀 智能预加载：优先加载前3张图片的缩略图
            let priorityUrls = Array(imagePaths.prefix(3)).compactMap { path in
                getImageURL(from: path)
            }
            
            cacheService.preloadImages(for: priorityUrls, tier: .thumbnail)
            
            // 🚀 延迟预加载：500ms后预加载剩余图片
            try? await Task.sleep(nanoseconds: 500_000_000)
            
            let remainingUrls = Array(imagePaths.dropFirst(3)).compactMap { path in
                getImageURL(from: path)
            }
            
            cacheService.preloadImages(for: remainingUrls, tier: .thumbnail)
        }
    }
    
    /// 取消预加载
    private func cancelPreloading() {
        preloadTask?.cancel()
        preloadTask = nil
    }
    
    // MARK: - 子视图
    
    /// 图片网格内容
    @ViewBuilder
    private var imageGridContent: some View {
        switch imagePaths.count {
        case 1:
            singleImageView
        case 2:
            twoImagesView
        case 3:
            threeImagesView
        case 4:
            fourImagesView
        default:
            multipleImagesView
        }
    }
    
    /// 🚀 单张图片视图（支持渐进式加载）
    private var singleImageView: some View {
        optimizedImageView(at: 0, tier: .standard)
            .aspectRatio(contentMode: .fit)
            .frame(maxHeight: maxHeight)
    }
    
    /// 两张图片视图
    private var twoImagesView: some View {
        HStack(spacing: 4) {
            optimizedImageView(at: 0)
            optimizedImageView(at: 1)
        }
        .frame(height: maxHeight * 0.6)
    }
    
    /// 三张图片视图
    private var threeImagesView: some View {
        HStack(spacing: 4) {
            // 左侧大图
            optimizedImageView(at: 0, tier: .standard)
                .frame(width: imageGridSize * 1.5)
            
            // 右侧两个小图
            VStack(spacing: 4) {
                optimizedImageView(at: 1)
                optimizedImageView(at: 2)
            }
            .frame(width: imageGridSize)
        }
        .frame(height: maxHeight * 0.7)
    }
    
    /// 四张图片视图
    private var fourImagesView: some View {
        VStack(spacing: 4) {
            HStack(spacing: 4) {
                optimizedImageView(at: 0)
                optimizedImageView(at: 1)
            }
            HStack(spacing: 4) {
                optimizedImageView(at: 2)
                optimizedImageView(at: 3)
            }
        }
        .frame(height: maxHeight * 0.8)
    }
    
    /// 多张图片视图（5-9张）
    private var multipleImagesView: some View {
        VStack(spacing: 4) {
            // 第一行
            HStack(spacing: 4) {
                ForEach(0..<min(3, imagePaths.count), id: \.self) { index in
                    optimizedImageView(at: index)
                }
            }
            
            // 第二行
            if imagePaths.count > 3 {
                HStack(spacing: 4) {
                    ForEach(3..<min(6, imagePaths.count), id: \.self) { index in
                        optimizedImageView(at: index)
                    }
                    
                    // 如果有第7张及以上，显示"+N"覆盖层
                    if imagePaths.count > 6 {
                        ZStack {
                            optimizedImageView(at: 5)
                            
                            // 🚀 优化的覆盖层设计
                            Rectangle()
                                .fill(.black.opacity(0.6))
                                .overlay(
                                    VStack(spacing: 2) {
                                        Image(systemName: "photo.stack")
                                            .font(.system(size: 16, weight: .medium))
                                            .foregroundColor(.white)
                                        
                                        Text("+\(imagePaths.count - 6)")
                                            .font(.system(size: 14, weight: .bold))
                                            .foregroundColor(.white)
                                    }
                                )
                        }
                        .onTapGesture {
                            onImageTap(imagePaths[5], 5)
                        }
                    }
                }
            }
            
            // 第三行（如果有7-9张图片且不需要显示"+N"）
            if imagePaths.count >= 7 && imagePaths.count <= 9 {
                HStack(spacing: 4) {
                    ForEach(6..<imagePaths.count, id: \.self) { index in
                        optimizedImageView(at: index)
                    }
                    
                    // 填充空白位置
                    ForEach(imagePaths.count..<9, id: \.self) { _ in
                        Color.clear
                            .frame(width: imageGridSize, height: imageGridSize)
                    }
                }
            }
        }
        .frame(height: maxHeight)
    }
    
    /// 🚀 优化的单个图片视图（微信级别）
    private func optimizedImageView(at index: Int, tier: EAImageCacheService.ImageTier = .thumbnail) -> some View {
        Group {
            if index < imagePaths.count {
                // 🚀 使用微信级别的高性能异步图片组件
                EAHighPerformanceAsyncImage(
                    url: getImageURL(from: imagePaths[index]),
                    targetSize: CGSize(width: imageGridSize, height: imageGridSize),
                    tier: tier,
                    enableProgressive: enableProgressive,
                    enablePreload: enablePreload
                ) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    // 🚀 优化的占位符：星域主题设计
                    Rectangle()
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.hexColor("0F172A").opacity(0.3), // stellarBackground
                                    Color.hexColor("40E0D0").opacity(0.1)  // stellarPrimary
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .overlay(
                            // 🌟 星域加载指示器
                            ZStack {
                                Circle()
                                    .stroke(Color.hexColor("40E0D0").opacity(0.3), lineWidth: 1.5)
                                    .frame(width: 16, height: 16)
                                
                                Circle()
                                    .trim(from: 0, to: 0.3)
                                    .stroke(Color.hexColor("40E0D0"), lineWidth: 1.5)
                                    .frame(width: 16, height: 16)
                                    .rotationEffect(.degrees(-90))
                                    .animation(.linear(duration: 1).repeatForever(autoreverses: false), value: UUID())
                            }
                        )
                }
                .frame(width: imageGridSize, height: imageGridSize)
                .clipped()
                .cornerRadius(8)
                .onTapGesture {
                    onImageTap(imagePaths[index], index)
                }
                .onAppear {
                    // 🚀 记录可见图片索引，用于性能监控
                    visibleImageIndices.insert(index)
                }
                .onDisappear {
                    visibleImageIndices.remove(index)
                }
            } else {
                Rectangle()
                    .fill(.clear)
                    .frame(width: imageGridSize, height: imageGridSize)
            }
        }
    }
    
    // MARK: - 计算属性
    
    /// 🚀 优化的网格图片大小计算
    private var imageGridSize: CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        let padding: CGFloat = 32 // 左右边距
        let spacing: CGFloat = 8 // 图片间距
        let calculatedSize = (screenWidth - padding - spacing) / 3
        
        // 🚀 确保尺寸符合微信朋友圈标准（80-120像素范围）
        return max(80, min(120, calculatedSize))
    }
    
    // MARK: - 🚀 辅助方法
    
    /// 🔑 修复：智能URL转换 - 与EAZoomableImageView保持一致
    private func getImageURL(from path: String) -> URL? {
        // 如果已经是完整URL，直接返回
        if path.hasPrefix("http") {
            return URL(string: path)
        }

        // 🔑 修复：使用与EAZoomableImageView相同的路径处理逻辑
        return getLocalImageURL(from: path)
    }
    
    /// 获取图片完整路径（兼容性方法）
    private func getFullImagePath(_ relativePath: String) -> String {
        let documentsPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)[0]
        return (documentsPath as NSString).appendingPathComponent(relativePath)
    }
}

// MARK: - 🚀 便捷初始化器

extension EAImageGridView {
    
    /// 社区帖子专用初始化器（启用所有优化）
    static func forCommunityPost(
        imagePaths: [String],
        onImageTap: @escaping (String, Int) -> Void = { _, _ in }
    ) -> EAImageGridView {
        return EAImageGridView(
            imagePaths: imagePaths,
            enableProgressive: true,
            enablePreload: true,
            onImageTap: onImageTap
        )
    }
    
    /// 高性能模式初始化器（仅缩略图，最快加载）
    static func forHighPerformance(
        imagePaths: [String],
        onImageTap: @escaping (String, Int) -> Void = { _, _ in }
    ) -> EAImageGridView {
        return EAImageGridView(
            imagePaths: imagePaths,
            enableProgressive: false,
            enablePreload: true,
            onImageTap: onImageTap
        )
    }
}

// MARK: - 预览

struct EAImageGridView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // 单张图片
            VStack {
                EAImageGridView.forCommunityPost(
                    imagePaths: ["Images/sample1.jpg"]
                )
                .padding()
                
                Spacer()
            }
            .background(Color.hexColor("002b20"))
            .previewDisplayName("单张图片")

            // 多张图片
            VStack {
                EAImageGridView.forCommunityPost(
                    imagePaths: [
                        "Images/sample1.jpg",
                        "Images/sample2.jpg",
                        "Images/sample3.jpg",
                        "Images/sample4.jpg",
                        "Images/sample5.jpg"
                    ]
                )
                .padding()
                
                Spacer()
            }
            .background(Color.hexColor("002b20"))
            .previewDisplayName("多张图片")
        }
    }
} 