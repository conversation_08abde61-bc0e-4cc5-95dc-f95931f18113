import SwiftUI

/// 🚀 高性能社区帖子卡片 - List滚动优化版
/// 使用EquatableView减少不必要的重新渲染
/// 专为解决List滚动卡顿问题设计
/// 🚀 微信朋友圈级别优化：内存管理、渲染优化、智能缓存
@MainActor
struct EAOptimizedCommunityPostCard: View, Equatable {
    
    // MARK: - Properties
    
    let post: EACommunityPost
    let isLiked: Bool
    let currentLikeCount: Int
    let currentCommentCount: Int
    
    // MARK: - Callbacks
    
    let onLike: (EACommunityPost) async throws -> Void
    let onComment: (EACommunityPost) -> Void
    let onShare: (EACommunityPost) -> Void
    let onPostTap: (EACommunityPost) -> Void
    let onUserProfileTap: () -> Void
    
    // MARK: - 🔑 新增：图片查看器状态管理
    @State private var showImageViewer: Bool = false
    @State private var selectedImageIndex: Int = 0
    
    // 🚀 新增：性能监控状态
    @State private var memoryUsage: Double = 0.0
    @State private var isMemoryWarningActive: Bool = false
    
    // MARK: - 🚀 性能优化：预计算常量
    private let cardPadding: CGFloat = 16
    private let avatarSize: CGFloat = 40
    private let cornerRadius: CGFloat = 16
    // 🔑 修复：固定缩略图大小为80*80（参考微信朋友圈）
    private let thumbnailSize: CGFloat = 80
    private let thumbnailSpacing: CGFloat = 4
    
    // MARK: - 🌟 星域主题颜色常量
    private let stellarPrimary = Color.hexColor("40E0D0")
    private let stellarSecondary = Color.hexColor("1E3A8A")
    private let stellarBackground = Color.hexColor("0F172A")
    private let stellarAccent = Color.hexColor("8B5CF6")
    
    /// ✅ 修复：通过Environment获取全局共享图片缓存服务
    @Environment(\.imageCacheService) private var imageCacheService
    
    // MARK: - 🚀 微信朋友圈级别的Equatable优化
    
    nonisolated static func == (lhs: EAOptimizedCommunityPostCard, rhs: EAOptimizedCommunityPostCard) -> Bool {
        // 🚀 关键优化：只比较影响UI的关键属性，减少不必要的重新渲染
        return lhs.post.id == rhs.post.id &&
               lhs.isLiked == rhs.isLiked &&
               lhs.currentLikeCount == rhs.currentLikeCount &&
               lhs.currentCommentCount == rhs.currentCommentCount &&
               lhs.post.content == rhs.post.content &&
               lhs.post.imageURLs == rhs.post.imageURLs &&
               lhs.post.creationDate == rhs.post.creationDate
    }
    
    // MARK: - Body
    
    var body: some View {
        VStack(spacing: 0) {
            // 用户信息区域
            userInfoSection
            
            // 内容区域
            if !post.content.isEmpty {
                contentSection
            }
            
            // 图片区域
            if !post.imageURLs.isEmpty {
                imageSection
            }
            
            // 互动按钮区域
            actionButtonsSection
        }
        .background(stellarCardBackground)
        .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
        .contentShape(Rectangle()) // 🚀 优化：明确定义点击区域
        .onTapGesture {
            onPostTap(post)
        }
        // 🔑 修复：多图片查看器，支持左右滑动
        .fullScreenCover(isPresented: $showImageViewer) {
            if !post.imageURLs.isEmpty {
                // 🔑 调试日志：记录社区场景传递的路径信息
                #if DEBUG
                let _ = {
                    print("🔍 [社区图片调试] post.imageURLs: \(post.imageURLs)")
                    let validSources = validImageSources
                    print("🔍 [社区图片调试] validImageSources count: \(validSources.count)")
                    for (index, source) in validSources.enumerated() {
                        switch source {
                        case .local(let path):
                            print("🔍 [社区图片调试] Source[\(index)]: local(path: \(path))")
                        case .remote(let url):
                            print("🔍 [社区图片调试] Source[\(index)]: remote(url: \(url))")
                        }
                    }
                }()
                #endif
                
                EAZoomableImageView(
                    imageSources: validImageSources,
                    startIndex: min(selectedImageIndex, validImageSources.count - 1)
                )
            }
        }
        // 🚀 微信朋友圈级别优化：内存和渲染优化
        .drawingGroup() // 🚀 关键优化：将复杂视图渲染为单个图层，提升滚动性能
        .clipped() // 🚀 优化：避免超出边界的渲染
        // 🚀 内存优化：监听内存压力
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("EAMemoryPressureWarning"))) { _ in
            // 触发图片缓存清理
            imageCacheService?.clearMemoryCache()
        }
        // 🚀 新增：系统内存警告监听
        .onReceive(NotificationCenter.default.publisher(for: UIApplication.didReceiveMemoryWarningNotification)) { _ in
            isMemoryWarningActive = true
            // 立即清理图片缓存
            imageCacheService?.clearMemoryCache()
            
            // 3秒后重置警告状态
            DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                isMemoryWarningActive = false
            }
        }
        // 🚀 新增：视图出现时的性能监控
        .onAppear {
            // 监控内存使用
            updateMemoryUsage()
        }
        // 🚀 新增：视图消失时的清理
        .onDisappear {
            // 清理不必要的缓存
            imageCacheService?.clearMemoryCache()
        }
    }
    
    // MARK: - 🚀 优化的子视图
    
    /// 用户信息区域 - 优化版
    private var userInfoSection: some View {
        HStack(spacing: 12) {
            // 用户头像
            Button(action: onUserProfileTap) {
                EAAvatarView(
                    avatarData: getRealAvatarData(),
                    size: avatarSize
                )
            }
            .buttonStyle(PlainButtonStyle())
            
            // 用户信息
            VStack(alignment: .leading, spacing: 4) {
                HStack(spacing: 8) {
                    Text(getRealUsername())
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                    
                    if let explorerTitle = getRealExplorerTitle() {
                        Text(explorerTitle)
                            .font(.system(size: 12, weight: .medium))
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(stellarPrimary.opacity(0.2))
                            .foregroundColor(stellarPrimary)
                            .cornerRadius(8)
                    }
                }
                
                Text(formatTimeAgo(post.creationDate))
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(.white.opacity(0.6))
            }
            
            Spacer()
        }
        .padding(.horizontal, cardPadding)
        .padding(.top, cardPadding)
        .padding(.bottom, 8)
    }
    
    /// 内容区域 - 优化版
    private var contentSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 🔑 修复：确保文字靠左对齐
            HStack {
                Text(post.content)
                    .font(.system(size: 15, weight: .regular))
                    .foregroundColor(.white)
                    .lineLimit(nil)
                    .multilineTextAlignment(.leading)
                Spacer()
            }
            
            // 标签
            if !post.tags.isEmpty {
                tagsView
            }
        }
        .padding(.horizontal, cardPadding)
        .padding(.bottom, 12)
    }
    
    /// 标签视图 - 优化版
    private var tagsView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                ForEach(post.tags, id: \.self) { tag in
                    Text("#\(tag)")
                        .font(.system(size: 12, weight: .medium))
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(stellarPrimary.opacity(0.1))
                        .foregroundColor(stellarPrimary)
                        .cornerRadius(8)
                }
            }
            .padding(.horizontal, cardPadding)
        }
    }
    
    /// 🔑 修复：微信朋友圈风格的固定尺寸图片区域
    private var imageSection: some View {
        let imageCount = post.imageURLs.count
        
        return VStack(alignment: .leading, spacing: 0) {
            if imageCount > 0 {
                // 🔑 修复：图片在文字内容左下方，从左边排列
                HStack {
                    fixedSizeImageGrid(images: post.imageURLs)
                    Spacer()
                }
                .padding(.horizontal, cardPadding)
                .padding(.bottom, 12)
            }
        }
    }
    
    /// 🔑 修复：固定尺寸图片网格布局（80*80，一行最多3张）
    @ViewBuilder
    private func fixedSizeImageGrid(images: [String]) -> some View {
        let imageCount = images.count
        let imagesToShow = Array(images.prefix(9)) // 最多显示9张图片
        
        VStack(alignment: .leading, spacing: thumbnailSpacing) {
            // 根据图片数量分行显示
            ForEach(0..<((imageCount + 2) / 3), id: \.self) { rowIndex in
                HStack(spacing: thumbnailSpacing) {
                    ForEach(0..<3, id: \.self) { colIndex in
                        let imageIndex = rowIndex * 3 + colIndex
                        if imageIndex < imagesToShow.count {
                            if imageIndex == 8 && imageCount > 9 {
                                // 第9张图片显示"+N"指示器
                                moreImagesIndicator(
                                    imagePath: imagesToShow[imageIndex], 
                                    remainingCount: imageCount - 8
                                )
                            } else {
                                fixedSizeThumbnail(
                                    imagePath: imagesToShow[imageIndex], 
                                    index: imageIndex
                                )
                            }
                        }
                    }
                }
            }
        }
    }
    
    /// 🔑 修复：固定80*80尺寸的缩略图（微信朋友圈级别优化）
    private func fixedSizeThumbnail(imagePath: String, index: Int) -> some View {
        // 🚀 使用微信级别的高性能异步图片组件
        EAHighPerformanceAsyncImage.forCommunityThumbnail(
            url: getImageURL(from: imagePath)
        ) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
        } placeholder: {
            // 🚀 优化的占位符：星域主题设计
            Rectangle()
                .fill(
                    LinearGradient(
                        colors: [
                            stellarBackground.opacity(0.3),
                            stellarPrimary.opacity(0.1)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    ProgressView()
                        .scaleEffect(0.6)
                        .tint(stellarPrimary)
                )
        }
        .frame(width: thumbnailSize, height: thumbnailSize)
        .clipped()
        .cornerRadius(6)
        .onTapGesture {
            // 🔑 新增：点击缩略图打开图片查看器，传递正确的索引
            selectedImageIndex = index
            showImageViewer = true
        }
    }
    
    /// 🔑 修复：更多图片指示器（微信朋友圈级别优化）
    private func moreImagesIndicator(imagePath: String, remainingCount: Int) -> some View {
        ZStack {
            // 🚀 使用微信级别的高性能异步图片组件
            EAHighPerformanceAsyncImage.forCommunityThumbnail(
                url: getImageURL(from: imagePath)
            ) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                Rectangle()
                    .fill(
                        LinearGradient(
                            colors: [
                                stellarBackground.opacity(0.3),
                                stellarPrimary.opacity(0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            }
            .frame(width: thumbnailSize, height: thumbnailSize)
            .clipped()
            .cornerRadius(6)
            
            // 半透明遮罩
            Rectangle()
                .fill(Color.black.opacity(0.6))
                .cornerRadius(6)
            
            // 剩余数量文字
            if remainingCount > 0 {
                Text("+\(remainingCount)")
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(.white)
            }
        }
        .frame(width: thumbnailSize, height: thumbnailSize)
        .onTapGesture {
            // 🔑 新增：点击"更多图片"指示器打开查看器
            selectedImageIndex = 8 // 从第9张图片开始
            showImageViewer = true
        }
    }
    
    /// 🚀 优化的互动按钮区域
    private var actionButtonsSection: some View {
        HStack(spacing: 24) {
            // 🔑 修复：点赞按钮 - 正确传递isLiked状态
            ActionButton(
                icon: isLiked ? "heart.fill" : "heart",
                count: currentLikeCount,
                color: isLiked ? .red : stellarPrimary.opacity(0.8),
                isActive: isLiked // 🔑 新增：传递激活状态
            ) {
                Task {
                    try await onLike(post)
                }
            }
            
            // 评论按钮
            ActionButton(
                icon: "message",
                count: currentCommentCount,
                color: stellarPrimary.opacity(0.8),
                isActive: false
            ) {
                onComment(post)
            }
            
            // 分享按钮
            Button {
                onShare(post)
            } label: {
                Image(systemName: "square.and.arrow.up")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(stellarPrimary.opacity(0.8))
            }
            .buttonStyle(PlainButtonStyle())
            
            Spacer()
        }
        .padding(.horizontal, cardPadding)
        .padding(.bottom, cardPadding)
    }
    
    /// 🌟 星域主题背景
    private var stellarCardBackground: some View {
        RoundedRectangle(cornerRadius: cornerRadius)
            .fill(
                LinearGradient(
                    colors: [
                        stellarBackground,
                        stellarSecondary.opacity(0.4),
                        stellarBackground
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .overlay(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .stroke(
                        LinearGradient(
                            colors: [stellarPrimary.opacity(0.4), stellarAccent.opacity(0.2)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
    }
    
    // MARK: - Helper Methods
    
    /// 🚀 新增：更新内存使用统计
    private func updateMemoryUsage() {
        var memoryInfo = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &memoryInfo) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            let memoryUsageBytes = Double(memoryInfo.resident_size)
            memoryUsage = memoryUsageBytes / (1024 * 1024) // 转换为MB
        }
    }
    
    /// 🔑 修复：将本地路径转换为file:// URL - 与EAZoomableImageView保持一致
    private func getImageURL(from path: String) -> URL? {
        // 如果已经是完整URL，直接返回
        if path.hasPrefix("http") {
            return URL(string: path)
        }

        // 🔑 修复：使用与EAZoomableImageView相同的路径处理逻辑
        return getLocalImageURL(from: path)
    }
    
    private func getRealUsername() -> String {
        return post.authorSocialProfile?.user?.username ?? "星际探索者"
    }
    
    private func getRealAvatarData() -> EAAvatarData? {
        return post.authorSocialProfile?.user?.avatarData
    }
    
    private func getRealStellarLevel() -> Int? {
        return post.authorSocialProfile?.stellarLevel ?? 1
    }
    
    private func getRealExplorerTitle() -> String? {
        return post.authorSocialProfile?.explorerTitle ?? "新手探索者"
    }
    
    private func formatTimeAgo(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.unitsStyle = .short
        return formatter.localizedString(for: date, relativeTo: Date())
    }
    
    /// 🔑 修复：有效的图片源列表 - 支持本地和网络图片
    private var validImageSources: [ImageSource] {
        return post.imageURLs.compactMap { urlString in
            guard !urlString.isEmpty else { return nil }

            // 🔑 修复：区分本地路径和网络URL
            if urlString.hasPrefix("http") {
                // 网络图片：验证URL格式
                guard let url = URL(string: urlString),
                      url.scheme != nil,
                      url.host != nil else {
                    return nil
                }
                return ImageSource.remote(url: url)
            } else {
                // 本地图片：直接使用相对路径
                return ImageSource.local(path: urlString)
            }
        }
    }
}

// MARK: - 🚀 优化的互动按钮组件

private struct ActionButton: View {
    let icon: String
    let count: Int
    let color: Color
    let isActive: Bool
    let action: () -> Void
    
    // 🔑 新增：状态管理
    @State private var isPressed = false
    
    var body: some View {
        Button(action: {
            // 🔑 新增：触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
            
            action()
        }) {
            HStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(color)
                    .scaleEffect(isPressed ? 0.9 : 1.0) // 🔑 新增：按压动画
                
                Text("\(count)")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(color)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isActive ? 1.1 : 1.0) // 🔑 新增：激活状态动画
        .animation(.easeInOut(duration: 0.15), value: isActive)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
} 