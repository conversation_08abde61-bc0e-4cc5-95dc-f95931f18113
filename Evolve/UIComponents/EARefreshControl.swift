import SwiftUI

/// 下拉刷新控件 - 遵循EA命名规范
/// 提供原生iOS下拉刷新体验，适配数字宇宙主题
struct RefreshControl: View {
    let coordinateSpaceName: String
    let onRefresh: () -> Void
    
    @State private var needRefresh: Bool = false
    
    var body: some View {
        GeometryReader { geometry in
            if geometry.frame(in: .named(coordinateSpaceName)).midY > 50 {
                Spacer()
                    .onAppear {
                        if !needRefresh {
                            needRefresh = true
                        }
                    }
            } else if geometry.frame(in: .named(coordinateSpaceName)).maxY < 10 {
                Spacer()
                    .onAppear {
                        if needRefresh {
                            needRefresh = false
                            onRefresh()
                        }
                    }
            }
            
            HStack {
                Spacer()
                
                if needRefresh {
                    VStack(spacing: 8) {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: Color.hexColor("40E0D0")))
                            .scaleEffect(0.8)
                        
                        Text("释放刷新星际网络")
                            .font(.system(size: 12))
                            .foregroundColor(Color.hexColor("40E0D0").opacity(0.8))
                    }
                } else {
                    VStack(spacing: 8) {
                        Image(systemName: "arrow.down")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(Color.hexColor("40E0D0").opacity(0.6))
                        
                        Text("下拉刷新")
                            .font(.system(size: 12))
                            .foregroundColor(Color.hexColor("40E0D0").opacity(0.6))
                    }
                }
                
                Spacer()
            }
        }
        .padding(.top, -50)
    }
}

// MARK: - Preview

#Preview {
    ScrollView {
        RefreshControl(coordinateSpaceName: "pullToRefresh") {
            // 刷新操作
        }
        
        LazyVStack {
            ForEach(0..<20) { index in
                Text("Item \(index)")
                    .padding()
                    .background(Color.gray.opacity(0.2))
                    .cornerRadius(8)
            }
        }
        .padding()
    }
    .coordinateSpace(name: "pullToRefresh")
    .background(Color.black)
} 