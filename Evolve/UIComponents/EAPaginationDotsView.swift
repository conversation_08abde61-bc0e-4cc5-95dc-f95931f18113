import SwiftUI

// 根据 aura_onboarding.html 中的 .pagination-dots 样式
// .pagination-dot: width: 8px, height: 8px, border-radius: 50%, background-color: rgba(255, 255, 255, 0.3);
// .pagination-dot.active: background-color: rgba(255, 255, 255, 0.8), transform: scale(1.2);

struct EAPaginationDotsView: View {
    let numberOfPages: Int
    @Binding var currentPage: Int

    private let inactiveColor = Color.white.opacity(0.3)
    private let activeColor = Color.white.opacity(0.8)
    private let dotSize: CGFloat = 8
    private let activeDotScale: CGFloat = 1.2

    var body: some View {
        HStack(spacing: 8) {
            ForEach(0..<numberOfPages, id: \.self) { index in
                Circle()
                    .fill(index == currentPage ? activeColor : inactiveColor)
                    .frame(width: dotSize, height: dotSize)
                    .scaleEffect(index == currentPage ? activeDotScale : 1.0)
                    .animation(.spring(response: 0.4, dampingFraction: 0.6), value: currentPage)
            }
        }
    }
}

// Preview需要一个State变量来绑定currentPage
struct EAPaginationDotsView_PreviewsContainer: View {
    @State private var previewCurrentPage = 0
    private let totalPages = 3

    var body: some View {
        VStack {
            EAPaginationDotsView(numberOfPages: totalPages, currentPage: $previewCurrentPage)
                .padding()
                .background(Color.black.opacity(0.7)) // 背景使白色的点更清晰
                .cornerRadius(10)
            
            Text("当前页: \(previewCurrentPage + 1)")
                .foregroundColor(.white)
                .padding(.top)
            
            HStack {
                Button("上一页") {
                    if previewCurrentPage > 0 {
                        previewCurrentPage -= 1
                    }
                }
                .padding()
                
                Button("下一页") {
                    if previewCurrentPage < totalPages - 1 {
                        previewCurrentPage += 1
                    }
                }
                .padding()
            }
            .foregroundColor(.white)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.gray)
    }
}

#Preview("Pagination Dots") {
    EAPaginationDotsView_PreviewsContainer()
} 