import SwiftUI

// MARK: - 按钮样式枚举

enum EAButtonStyle {
    case primary     // 主要按钮 - 能量渐变背景
    case secondary   // 次要按钮 - 透明背景带边框
    case tertiary    // 第三按钮 - 仅文字
    case destructive // 危险按钮 - 红色系
    case social      // 社交登录按钮 - 圆形图标
}

enum EAButtonSize {
    case large    // 50pt 高度
    case medium   // 44pt 高度
    case small    // 36pt 高度
    case icon     // 44x44pt 正方形
}

// MARK: - 按钮组件

struct EAButton: View {
    // 必需参数
    let title: String
    let action: () -> Void
    
    // 可选参数
    let style: EAButtonStyle
    let size: EAButtonSize
    let isEnabled: Bool
    let isLoading: Bool
    let icon: String? // SF Symbol 名称
    let fullWidth: Bool
    
    // 状态
    @State private var isPressed = false
    
    // 初始化方法
    init(
        title: String,
        style: EAButtonStyle = .primary,
        size: EAButtonSize = .medium,
        isEnabled: Bool = true,
        isLoading: Bool = false,
        icon: String? = nil,
        fullWidth: Bool = true,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.style = style
        self.size = size
        self.isEnabled = isEnabled
        self.isLoading = isLoading
        self.icon = icon
        self.fullWidth = fullWidth
        self.action = action
    }
    
    var body: some View {
        Button(action: {
            if isEnabled && !isLoading {
                // 触觉反馈
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()
                action()
            }
        }) {
            buttonContent
        }
        .buttonStyle(EAButtonPressStyle(style: style, isPressed: $isPressed))
        .disabled(!isEnabled || isLoading)
        .animation(.easeInOut(duration: 0.2), value: isPressed)
        .animation(.easeInOut(duration: 0.3), value: isLoading)
    }
    
    @ViewBuilder
    private var buttonContent: some View {
        HStack(spacing: 8) {
            // 图标或加载指示器
            if isLoading {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: textColor))
                    .scaleEffect(0.8)
            } else if let icon = icon {
                Image(systemName: icon)
                    .font(.system(size: iconSize, weight: .medium))
                    .foregroundColor(textColor)
            }
            
            // 标题文字
            if !title.isEmpty {
                Text(title)
                    .font(textFont)
                    .fontWeight(textWeight)
                    .foregroundColor(textColor)
                    .opacity(isLoading ? 0.6 : 1.0)
            }
        }
        .frame(width: fullWidth ? nil : buttonWidth, height: buttonHeight)
        .frame(maxWidth: fullWidth ? .infinity : nil)
        .background(buttonBackground)
        .overlay(buttonOverlay)
        .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
    }
    
    // MARK: - 计算属性
    
    private var buttonHeight: CGFloat {
        switch size {
        case .large: return 50
        case .medium: return 44
        case .small: return 36
        case .icon: return style == .social ? 48 : 44 // 原型要求：社交按钮48px
        }
    }
    
    private var buttonWidth: CGFloat? {
        switch size {
        case .icon: return style == .social ? 48 : 44 // 原型要求：社交按钮48px
        default: return nil
        }
    }
    
    private var cornerRadius: CGFloat {
        switch size {
        case .large: return 16 // 原型要求：16px圆角
        case .medium: return 16 // 原型要求：16px圆角
        case .small: return 16 // 保持一致性
        case .icon: return style == .social ? 24 : 22 // 原型要求：社交按钮圆形(50%)
        }
    }
    
    private var textFont: Font {
        switch size {
        case .large: return .system(size: 17, weight: .semibold) // 原型要求：1.05rem ≈ 17px
        case .medium: return .system(size: 17, weight: .semibold) // 原型要求：1.05rem ≈ 17px
        case .small: return .callout
        case .icon: return .caption
        }
    }
    
    private var textWeight: Font.Weight {
        switch style {
        case .primary, .destructive: return .semibold
        case .secondary, .tertiary: return .medium
        case .social: return .medium
        }
    }
    
    private var iconSize: CGFloat {
        switch size {
        case .large: return 18
        case .medium: return 16
        case .small: return 14
        case .icon: return style == .social ? 24 : 20 // 原型要求：社交按钮图标24px
        }
    }
    
    private var textColor: Color {
        if !isEnabled {
            return Color.primary.opacity(0.3)
        }
        
        switch style {
        case .primary, .destructive:
            return .white
        case .secondary:
            return Color.hexColor("40E0D0") // 荧光青
        case .tertiary:
            return Color.hexColor("67e8f9") // 天蓝
        case .social:
            return Color.hexColor("e2e8f0") // 原型要求：#e2e8f0 (Slate 200)
        }
    }
    
    @ViewBuilder
    private var buttonBackground: some View {
        Group {
            if !isEnabled {
                Color.gray.opacity(0.3)
            } else {
                switch style {
                case .primary:
                    // 能量渐变 - 珊瑚粉到荧光青
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.hexColor("FF7F50"), // 珊瑚粉
                            Color.hexColor("40E0D0")  // 荧光青
                        ]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                    
                case .destructive:
                    // 红色系渐变
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.hexColor("EF4444"), // 红色
                            Color.hexColor("F87171")  // 浅红
                        ]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                    
                case .secondary:
                    // 透明背景
                    Color.clear
                    
                case .tertiary:
                    // 完全透明
                    Color.clear
                    
                case .social:
                    // 原型要求：rgba(255, 255, 255, 0.1)
                    Color.white.opacity(0.1)
                }
            }
        }
        .shadow(
            color: shadowColor,
            radius: isPressed ? 2 : 4,
            x: 0,
            y: isPressed ? 1 : 2
        )
    }
    
    @ViewBuilder
    private var buttonOverlay: some View {
        Group {
            switch style {
            case .secondary:
                // 边框
                RoundedRectangle(cornerRadius: cornerRadius)
                    .stroke(
                        isEnabled ? Color.hexColor("40E0D0") : Color.gray.opacity(0.3),
                        lineWidth: 1.5
                    )
                    
            case .social:
                // 原型要求：1px solid rgba(255, 255, 255, 0.15)
                RoundedRectangle(cornerRadius: cornerRadius)
                    .stroke(
                        Color.white.opacity(isEnabled ? 0.15 : 0.1),
                        lineWidth: 1
                    )
                    
            default:
                EmptyView()
            }
        }
    }
    
    private var shadowColor: Color {
        if !isEnabled {
            return .clear
        }
        
        switch style {
        case .primary:
            return Color.hexColor("FF7F50").opacity(0.3)
        case .destructive:
            return Color.red.opacity(0.3)
        case .social:
            return Color.black.opacity(0.1)
        default:
            return .clear
        }
    }
}

// MARK: - 按钮样式

struct EAButtonPressStyle: ButtonStyle {
    let style: EAButtonStyle
    @Binding var isPressed: Bool
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.96 : 1.0)
            .opacity(configuration.isPressed ? 0.8 : 1.0)
            .onChange(of: configuration.isPressed) { _, newValue in
                isPressed = newValue
            }
    }
}

// MARK: - 社交登录按钮

struct EASocialButton: View {
    let provider: SocialProvider
    let action: () -> Void
    
    enum SocialProvider {
        case apple
        case wechat
        case phone
        
        var icon: String {
            switch self {
            case .apple: return "apple.logo"
            case .wechat: return "message.badge.waveform"
            case .phone: return "phone.fill"
            }
        }
        
        var accessibilityLabel: String {
            switch self {
            case .apple: return "通过Apple登录"
            case .wechat: return "通过微信登录"
            case .phone: return "通过手机号登录"
            }
        }
    }
    
    var body: some View {
        EAButton(
            title: "",
            style: .social,
            size: .icon,
            icon: provider.icon,
            fullWidth: false,
            action: action
        )
        .accessibilityLabel(provider.accessibilityLabel)
    }
}

// MARK: - 预览

#Preview("Button Styles") {
    VStack(spacing: 20) {
        // 主要按钮
        EAButton(title: "登录", style: .primary) { }
        
        // 次要按钮
        EAButton(title: "注册", style: .secondary) { }
        
        // 第三按钮
        EAButton(title: "忘记密码？", style: .tertiary, size: .small) { }
        
        // 危险按钮
        EAButton(title: "删除账户", style: .destructive) { }
        
        // 加载状态
        EAButton(title: "登录中...", style: .primary, isLoading: true) { }
        
        // 禁用状态
        EAButton(title: "登录", style: .primary, isEnabled: false) { }
        
        // 带图标的按钮
        EAButton(title: "添加计划", style: .primary, icon: "plus") { }
        
        // 社交登录按钮
        HStack(spacing: 20) {
            EASocialButton(provider: .apple) { }
            EASocialButton(provider: .wechat) { }
            EASocialButton(provider: .phone) { }
        }
    }
    .padding()
    .background(EABackgroundView(style: .authentication))
} 