import SwiftUI
import SwiftData

// MARK: - 数字宇宙探索者档案视图（Phase 3 Day 7新增）

/// 数字宇宙探索者档案主视图：升级版用户档案，集成星际能量系统和宇宙主题
struct EACosmicExplorerProfileView: View {
    
    // MARK: - 属性
    
    let user: EAUser
    @StateObject private var energyService: EAStellarEnergyService
    @StateObject private var viewModel: EAMeViewModel
    @EnvironmentObject var sessionManager: EASessionManager
    
    @State private var energyOverview: EAUserEnergyOverview?
    @State private var isLoading = true
    @State private var selectedTab: ProfileTab = .overview
    @State private var showTimelineFilter = false
    @State private var timelineFilter: TimelineFilter = .all
    
    // MARK: - 真实数据计算属性
    
    @State private var userPostsCount: Int = 0
    @State private var userLikesReceived: Int = 0
    @State private var userCommentsReceived: Int = 0
    @State private var realWeeklyEnergyGained: Int = 0
    @State private var realDailyAverageEnergy: Int = 0
    @State private var realMonthlyActiveDays: Int = 0
    @State private var realBestPerformanceTime: String = "分析中"
    @State private var realEnergyTrendData: [Int] = []

    // 🔑 重构：当前用户状态管理
    @State private var currentUser: EAUser?

    // MARK: - 服务和依赖
    
    private let repositoryContainer: EARepositoryContainer
    
    // MARK: - 初始化
    
    init(user: EAUser, repositoryContainer: EARepositoryContainer, viewModel: EAMeViewModel) {
        self.user = user
        self._energyService = StateObject(wrappedValue: EAStellarEnergyService(
            repositoryContainer: repositoryContainer,
            cacheManager: EAAICacheManager()
        ))
        self._viewModel = StateObject(wrappedValue: viewModel)
        self.repositoryContainer = repositoryContainer
    }
    
    /// @deprecated 使用接收完整用户对象的初始化方法替代
    @available(*, deprecated, message: "Use init(user:repositoryContainer:viewModel:) instead")
    init(userId: UUID, repositoryContainer: EARepositoryContainer, viewModel: EAMeViewModel) {
        fatalError("Deprecated initializer used. Please use init(user:repositoryContainer:viewModel:) instead.")
    }
    
    // MARK: - 视图主体
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 宇宙背景
                cosmicBackground
                    .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 24) {
                        // 探索者头部档案
                        explorerHeaderSection
                        
                        // 标签页选择器
                        profileTabSelector
                        
                        // 内容区域
                        switch selectedTab {
                        case .overview:
                            overviewContent
                        case .achievements:
                            achievementsContent
                        case .timeline:
                            timelineContent
                        case .statistics:
                            statisticsContent
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
            }
        }
        .navigationTitle("星际档案")
        .navigationBarTitleDisplayMode(.inline) // 🔑 使用inline模式，更适合星际主题
        .toolbarBackground(.ultraThinMaterial, for: .navigationBar) // 🔑 添加毛玻璃背景
        .toolbarColorScheme(.dark, for: .navigationBar) // 🔑 强制深色主题
        .navigationBarBackButtonHidden(false) // 🔑 确保显示系统默认返回按钮
        .tint(.white) // 🔑 设置返回按钮为白色，符合星际主题
        .toolbar {
            // 🔑 修复：只保留分享按钮，移除自定义返回按钮避免重复
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    shareProfile()
                }) {
                    HStack(spacing: 6) {
                        Image(systemName: "square.and.arrow.up")
                            .font(.system(size: 14, weight: .medium))

                        Text("分享")
                            .font(.system(size: 14, weight: .medium))
                    }
                    .foregroundStyle(.cyan)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        Capsule()
                            .fill(.cyan.opacity(0.15))
                            .overlay(
                                Capsule()
                                    .stroke(.cyan.opacity(0.4), lineWidth: 1)
                            )
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .task {
            await loadProfileData()
        }
    }
    
    // MARK: - 宇宙背景
    
    private var cosmicBackground: some View {
        ZStack {
            // 深邃宇宙背景
            LinearGradient(
                colors: [
                    Color.black,
                    Color.purple.opacity(0.4),
                    Color.blue.opacity(0.3),
                    Color.black
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            // 星空效果
            ForEach(0..<50, id: \.self) { _ in
                Circle()
                    .fill(Color.white.opacity(Double.random(in: 0.1...0.8)))
                    .frame(width: CGFloat.random(in: 1...3))
                    .position(
                        x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                        y: CGFloat.random(in: 0...UIScreen.main.bounds.height)
                    )
            }
        }
    }
    
    // MARK: - 探索者头部档案
    
    private var explorerHeaderSection: some View {
        VStack(spacing: 20) {
            // 头像和等级光环
            cosmicAvatarView
            
            // 探索者基本信息
            explorerBasicInfo
            
            // 星际能量快速概览
            if energyOverview != nil {
                EAStellarEnergyQuickView(
                    user: user,
                    repositoryContainer: energyService.repositoryContainerReference
                )
            }
        }
        .padding(24)
        .background(cosmicCardBackground)
    }
    
    // MARK: - 宇宙头像视图
    
    private var cosmicAvatarView: some View {
        ZStack {
            // 外层星际光环
            Circle()
                .stroke(
                    LinearGradient(
                        colors: [
                            stellarLevelColor.opacity(0.8),
                            stellarLevelColor.opacity(0.3),
                            Color.clear
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    style: StrokeStyle(lineWidth: 4, lineCap: .round)
                )
                .frame(width: 110, height: 110)
                .rotationEffect(.degrees(rotationAngle))
                .animation(
                    .linear(duration: 8).repeatForever(autoreverses: false),
                    value: rotationAngle
                )
            
            // 中层能量环
            Circle()
                .stroke(
                    stellarLevelColor.opacity(0.4),
                    style: StrokeStyle(lineWidth: 2, lineCap: .round, dash: [5, 5])
                )
                .frame(width: 95, height: 95)
                .rotationEffect(.degrees(-rotationAngle * 0.7))
            
            // 🔑 重构：用户头像（通过ViewModel安全访问）
            EAAvatarView(avatarData: currentUser?.avatarData, size: 80)
                .overlay(
                    Circle()
                        .stroke(Color.white.opacity(0.3), lineWidth: 2)
                )
            
            // 等级标识
            if let overview = energyOverview {
                VStack(spacing: 2) {
                    Text("LV")
                        .font(.caption2.bold())
                        .foregroundStyle(.white)
                    Text("\(overview.stellarLevel)")
                        .font(.caption.bold())
                        .foregroundStyle(stellarLevelColor)
                }
                .padding(6)
                .background(
                    Circle()
                        .fill(.ultraThinMaterial)
                        .overlay(
                            Circle()
                                .stroke(stellarLevelColor.opacity(0.6), lineWidth: 1)
                        )
                )
                .offset(x: 35, y: -35)
            }
        }
        .onAppear {
            withAnimation {
                rotationAngle = 360
            }
        }
    }
    
    @State private var rotationAngle: Double = 0
    
    // MARK: - 探索者基本信息
    
    private var explorerBasicInfo: some View {
        VStack(spacing: 8) {
            // 用户名和探索者称号
            VStack(spacing: 4) {
                Text(currentUser?.username ?? "星际探索者")
                    .font(.title2.bold())
                    .foregroundStyle(.white)
                
                if let overview = energyOverview {
                    Text(overview.explorerTitle)
                        .font(.subheadline)
                        .foregroundStyle(stellarLevelColor)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 4)
                        .background(
                            Capsule()
                                .fill(stellarLevelColor.opacity(0.2))
                                .overlay(
                                    Capsule()
                                        .stroke(stellarLevelColor.opacity(0.5), lineWidth: 1)
                                )
                        )
                }
            }
            
            // 宇宙区域和加入时间
            HStack(spacing: 20) {
                HStack(spacing: 6) {
                    Image(systemName: "location.circle.fill")
                        .foregroundStyle(.cyan)
                    Text("银河系边缘")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
                
                HStack(spacing: 6) {
                    Image(systemName: "clock.circle.fill")
                        .foregroundStyle(.orange)
                    Text("探索 \(explorationDays) 天")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
            }
        }
    }
    
    // MARK: - 标签页选择器
    
    private var profileTabSelector: some View {
        HStack(spacing: 0) {
            ForEach(ProfileTab.allCases, id: \.self) { tab in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        selectedTab = tab
                    }
                }) {
                    VStack(spacing: 6) {
                        Image(systemName: tab.icon)
                            .font(.system(size: 16, weight: .medium))
                        
                        Text(tab.title)
                            .font(.caption.weight(.medium))
                    }
                    .foregroundStyle(selectedTab == tab ? .white : .secondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(
                        ZStack {
                            if selectedTab == tab {
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(.ultraThinMaterial)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 8)
                                            .stroke(.blue.opacity(0.5), lineWidth: 1)
                                    )
                            }
                        }
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(4)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.regularMaterial)
        )
    }
    
    // MARK: - 内容区域
    
    @ViewBuilder
    private var overviewContent: some View {
        VStack(spacing: 20) {
            // 星际能量统计
            if let overview = energyOverview {
                stellarEnergyStatsCard(overview: overview)
            }
            
            // 习惯成就概览
            habitAchievementOverview
            
            // 社区贡献统计
            communityContributionStats
        }
    }
    
    @ViewBuilder
    private var achievementsContent: some View {
        VStack(spacing: 20) {
            // 成就徽章网格
            achievementBadgesGrid
            
            // 成就进度
            achievementProgressSection
        }
    }
    
    @ViewBuilder
    private var timelineContent: some View {
        VStack(spacing: 20) {
            // 时间轴过滤器
            timelineFilterSection
            
            // 星际成就时间轴
            EAStellarAchievementTimeline(
                user: user,
                filter: timelineFilter,
                energyService: energyService
            )
        }
    }
    
    @ViewBuilder
    private var statisticsContent: some View {
        VStack(spacing: 20) {
            // 详细统计图表
            detailedStatisticsSection
        }
    }
    
    // MARK: - 星际能量统计卡片
    
    private func stellarEnergyStatsCard(overview: EAUserEnergyOverview) -> some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "bolt.circle.fill")
                    .font(.title2)
                    .foregroundStyle(.yellow)
                
                Text("星际能量统计")
                    .font(.headline.bold())
                    .foregroundStyle(.primary)
                
                Spacer()
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                energyStatItem(
                    title: "总能量",
                    value: "\(overview.totalStellarEnergy)",
                    icon: "bolt.fill",
                    color: .yellow
                )
                
                energyStatItem(
                    title: "本周获得",
                    value: "\(realWeeklyEnergyGained)",
                    icon: "calendar.circle.fill",
                    color: .green
                )
                
                energyStatItem(
                    title: "日均获得",
                    value: "\(realDailyAverageEnergy)",
                    icon: "chart.line.uptrend.xyaxis.circle.fill",
                    color: .blue
                )
                
                energyStatItem(
                    title: "升级进度",
                    value: "\(Int(overview.progressToNextLevel * 100))%",
                    icon: "arrow.up.circle.fill",
                    color: .purple
                )
            }
        }
        .padding(20)
        .background(cosmicCardBackground)
    }
    
    private func energyStatItem(title: String, value: String, icon: String, color: Color) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundStyle(color)
            
            Text(value)
                .font(.title3.bold())
                .foregroundStyle(.primary)
            
            Text(title)
                .font(.caption)
                .foregroundStyle(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(color.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    // MARK: - 习惯成就概览
    
    private var habitAchievementOverview: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "target")
                    .font(.title2)
                    .foregroundStyle(.green)
                
                Text("习惯成就概览")
                    .font(.headline.bold())
                    .foregroundStyle(.primary)
                
                Spacer()
            }
            
            HStack(spacing: 16) {
                habitStatCard(
                    title: "完成习惯",
                    value: "\(viewModel.userStats.completedHabits)",
                    subtitle: "个习惯",
                    color: .green
                )
                
                habitStatCard(
                    title: "最长连击",
                    value: "\(viewModel.userStats.longestStreak)",
                    subtitle: "天记录",
                    color: .orange
                )
                
                habitStatCard(
                    title: "当前连击",
                    value: "\(viewModel.userStats.currentStreak)",
                    subtitle: "天连击",
                    color: .red
                )
            }
        }
        .padding(20)
        .background(cosmicCardBackground)
    }
    
    private func habitStatCard(title: String, value: String, subtitle: String, color: Color) -> some View {
        VStack(spacing: 6) {
            Text(value)
                .font(.title2.bold())
                .foregroundStyle(color)
            
            Text(title)
                .font(.caption.weight(.medium))
                .foregroundStyle(.primary)
            
            Text(subtitle)
                .font(.caption2)
                .foregroundStyle(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
    
    // MARK: - 社区贡献统计
    
    private var communityContributionStats: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "person.2.circle.fill")
                    .font(.title2)
                    .foregroundStyle(.cyan)
                
                Text("社区贡献统计")
                    .font(.headline.bold())
                    .foregroundStyle(.primary)
                
                Spacer()
            }
            
            HStack(spacing: 16) {
                communityStatCard(
                    title: "广播数",
                    value: "\(userPostsCount)",
                    icon: "antenna.radiowaves.left.and.right",
                    color: .blue
                )
                
                communityStatCard(
                    title: "获赞数",
                    value: "\(userLikesReceived)",
                    icon: "heart.fill",
                    color: .red
                )
                
                communityStatCard(
                    title: "评论数",
                    value: "\(userCommentsReceived)",
                    icon: "bubble.left.fill",
                    color: .green
                )
            }
        }
        .padding(20)
        .background(cosmicCardBackground)
    }
    
    private func communityStatCard(title: String, value: String, icon: String, color: Color) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundStyle(color)
            
            Text(value)
                .font(.title3.bold())
                .foregroundStyle(.primary)
            
            Text(title)
                .font(.caption)
                .foregroundStyle(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.regularMaterial)
        )
    }
    
    // MARK: - 成就徽章网格
    
    private var achievementBadgesGrid: some View {
        VStack(spacing: 16) {
            HStack {
                Text("成就徽章")
                    .font(.headline.bold())
                    .foregroundStyle(.primary)
                
                Spacer()
                
                Text("\(earnedBadgesCount)/\(totalBadgesCount)")
                    .font(.subheadline)
                    .foregroundStyle(.secondary)
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 12) {
                ForEach(EABadgeType.allCases, id: \.self) { badgeType in
                    badgeCard(badgeType: badgeType, isEarned: hasEarnedBadge(badgeType))
                }
            }
        }
        .padding(20)
        .background(cosmicCardBackground)
    }
    
    private func badgeCard(badgeType: EABadgeType, isEarned: Bool) -> some View {
        VStack(spacing: 8) {
            ZStack {
                Circle()
                    .fill(isEarned ? .yellow.opacity(0.2) : .gray.opacity(0.1))
                    .frame(width: 50, height: 50)
                
                Image(systemName: getBadgeIcon(badgeType))
                    .font(.title2)
                    .foregroundStyle(isEarned ? .yellow : .gray)
            }
            
            Text(badgeType.title)
                .font(.caption2)
                .foregroundStyle(isEarned ? .primary : .secondary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
        }
        .opacity(isEarned ? 1.0 : 0.5)
        .scaleEffect(isEarned ? 1.0 : 0.9)
    }
    
    // MARK: - 成就进度区域
    
    private var achievementProgressSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("成就进度")
                    .font(.headline.bold())
                    .foregroundStyle(.primary)
                
                Spacer()
            }
            
            // 各类型成就进度
            VStack(spacing: 12) {
                achievementProgressItem(
                    title: "连击成就",
                    current: viewModel.userStats.currentStreak,
                    target: getNextStreakTarget(),
                    color: .orange
                )
                
                achievementProgressItem(
                    title: "广播成就",
                    current: userPostsCount,
                    target: 10,
                    color: .blue
                )
                
                achievementProgressItem(
                    title: "社交成就",
                    current: 0, // TODO: 实际互动数
                    target: 50,
                    color: .green
                )
            }
        }
        .padding(20)
        .background(cosmicCardBackground)
    }
    
    private func achievementProgressItem(title: String, current: Int, target: Int, color: Color) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(title)
                    .font(.subheadline.weight(.medium))
                    .foregroundStyle(.primary)
                
                Spacer()
                
                Text("\(current)/\(target)")
                    .font(.caption.bold())
                    .foregroundStyle(color)
            }
            
            ProgressView(value: Double(current), total: Double(target))
                .progressViewStyle(LinearProgressViewStyle(tint: color))
                .scaleEffect(y: 2)
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.regularMaterial)
        )
    }
    
    // MARK: - 时间轴过滤器
    
    private var timelineFilterSection: some View {
        HStack {
            Text("成就时间轴")
                .font(.headline.bold())
                .foregroundStyle(.primary)
            
            Spacer()
            
            Button(action: {
                showTimelineFilter = true
            }) {
                HStack(spacing: 4) {
                    Text(timelineFilter.displayName)
                        .font(.caption)
                    
                    Image(systemName: "chevron.down")
                        .font(.caption2)
                }
                .foregroundStyle(.blue)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    Capsule()
                        .fill(.blue.opacity(0.2))
                )
            }
        }
        .confirmationDialog("筛选成就类型", isPresented: $showTimelineFilter) {
            ForEach(TimelineFilter.allCases, id: \.self) { filter in
                Button(filter.displayName) {
                    timelineFilter = filter
                }
            }
        }
    }
    
    // MARK: - 详细统计区域
    
    private var detailedStatisticsSection: some View {
        VStack(spacing: 20) {
            // 能量获得趋势
            energyTrendChart
            
            // 习惯完成分析
            habitCompletionAnalysis
            
            // 活跃度分析
            activityAnalysis
        }
    }
    
    private var energyTrendChart: some View {
        VStack(spacing: 16) {
            HStack {
                Text("能量获得趋势")
                    .font(.headline.bold())
                    .foregroundStyle(.primary)
                
                Spacer()
                
                Text("最近30天")
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }
            
            // ✅ 修复：使用真实的能量趋势数据
            HStack(alignment: .bottom, spacing: 4) {
                ForEach(Array(realEnergyTrendData.enumerated()), id: \.offset) { index, energy in
                    Rectangle()
                        .fill(.blue.opacity(0.6))
                        .frame(width: 8, height: max(CGFloat(energy) * 2, 2)) // 基于真实能量值计算高度
                }
            }
            .frame(height: 60)
        }
        .padding(20)
        .background(cosmicCardBackground)
    }
    
    private var habitCompletionAnalysis: some View {
        VStack(spacing: 16) {
            HStack {
                Text("习惯完成分析")
                    .font(.headline.bold())
                    .foregroundStyle(.primary)
                
                Spacer()
            }
            
            VStack(spacing: 12) {
                analysisItem(
                    title: "完成率",
                    value: String(format: "%.1f%%", viewModel.userStats.completionRate * 100),
                    trend: .up,
                    color: .green
                )
                
                analysisItem(
                    title: "平均每日习惯",
                    value: String(format: "%.1f", averageDailyHabits),
                    trend: .stable,
                    color: .blue
                )
                
                analysisItem(
                    title: "最佳表现时段",
                    value: realBestPerformanceTime,
                    trend: .up,
                    color: .orange
                )
            }
        }
        .padding(20)
        .background(cosmicCardBackground)
    }
    
    private var activityAnalysis: some View {
        VStack(spacing: 16) {
            HStack {
                Text("活跃度分析")
                    .font(.headline.bold())
                    .foregroundStyle(.primary)
                
                Spacer()
            }
            
            HStack(spacing: 16) {
                activityCard(
                    title: "连续活跃",
                    value: "\(viewModel.userStats.currentStreak)",
                    unit: "天",
                    color: .red
                )
                
                activityCard(
                    title: "本月活跃",
                    value: "\(realMonthlyActiveDays)",
                    unit: "天",
                    color: .green
                )
                
                activityCard(
                    title: "活跃等级",
                    value: activityLevel,
                    unit: "",
                    color: .purple
                )
            }
        }
        .padding(20)
        .background(cosmicCardBackground)
    }
    
    private func analysisItem(title: String, value: String, trend: TrendDirection, color: Color) -> some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundStyle(.primary)
            
            Spacer()
            
            HStack(spacing: 6) {
                Text(value)
                    .font(.subheadline.bold())
                    .foregroundStyle(color)
                
                Image(systemName: trend.icon)
                    .font(.caption)
                    .foregroundStyle(trend.color)
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(.ultraThinMaterial)
        )
    }
    
    private func activityCard(title: String, value: String, unit: String, color: Color) -> some View {
        VStack(spacing: 6) {
            HStack(alignment: .bottom, spacing: 2) {
                Text(value)
                    .font(.title2.bold())
                    .foregroundStyle(color)
                
                if !unit.isEmpty {
                    Text(unit)
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
            }
            
            Text(title)
                .font(.caption)
                .foregroundStyle(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.regularMaterial)
        )
    }
    
    // MARK: - 宇宙卡片背景
    
    private var cosmicCardBackground: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(.ultraThinMaterial)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(
                        LinearGradient(
                            colors: [.blue.opacity(0.5), .purple.opacity(0.3)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
    }
    
    // MARK: - 辅助方法
    
    private func loadProfileData() async {
        isLoading = true
        defer { isLoading = false }

        // 🔑 重构：加载当前用户数据
        currentUser = await sessionManager.safeCurrentUser

        // 加载星际能量数据
        energyOverview = await energyService.getUserEnergyOverview(for: user)
        
        // ✅ 修复：加载真实的社区贡献数据（包含用户发表的评论数）
        await loadCommunityContributionData()
        
        // ✅ 新增：计算真实的星际能量统计
        await calculateRealEnergyStatistics()
        
        // ✅ 新增：计算真实的活跃度和表现分析
        await calculateRealActivityAnalysis()
        
        // 加载用户统计数据
        await viewModel.loadUserStats()
        await viewModel.loadAchievements()
    }
    
    /// ✅ 修复：加载真实的社区贡献数据（包含用户发表的评论数）
    private func loadCommunityContributionData() async {
        do {
            // ✅ 修复：首先同步所有帖子的计数字段，确保数据一致性
            try await repositoryContainer.communityRepository.syncAllPostCounts()

            // 获取用户发布的帖子
            let userPosts = try await repositoryContainer.communityRepository.fetchUserPosts(
                userId: user.id,
                limit: 1000, // 获取所有帖子用于统计
                includeHidden: false
            )

            // 计算帖子数量（广播数）
            userPostsCount = userPosts.count

            // ✅ 修复：计算总获赞数（使用实际的likes关系数组，而不是可能不准确的likeCount字段）
            userLikesReceived = userPosts.reduce(0) { total, post in
                // 统计每个帖子的有效点赞数（isActive为true的点赞）
                let activeLikes = post.likes.filter { $0.isActive }
                return total + activeLikes.count
            }

            // ✅ 修复：计算用户发表的评论数（而不是用户帖子收到的评论数）
            userCommentsReceived = await calculateUserCommentsCount()

        } catch {
            // 加载社区贡献数据失败，使用默认值
            userPostsCount = 0
            userLikesReceived = 0
            userCommentsReceived = 0
        }
    }
    
    /// ✅ 新增：计算用户发表的评论数量
    private func calculateUserCommentsCount() async -> Int {
        do {
            // 获取所有社区帖子
            let allPosts = try await repositoryContainer.communityRepository.fetchPosts(limit: 10000, offset: 0)
            
            var userCommentsCount = 0
            
            // 遍历所有帖子，统计用户发表的评论
            for post in allPosts {
                // 获取帖子的所有评论
                let comments = post.comments.filter { comment in
                    comment.isVisible && comment.author?.id == user.id
                }
                userCommentsCount += comments.count
            }
            
            return userCommentsCount
            
        } catch {
            // 计算用户评论数失败，返回默认值
            return 0
        }
    }
    
    /// ✅ 新增：计算真实的星际能量统计
    private func calculateRealEnergyStatistics() async {
        guard let overview = energyOverview else { return }
        
        // 计算真实的日均能量（基于用户创建时间）
        let days = max(1, explorationDays)
        realDailyAverageEnergy = overview.totalStellarEnergy / days
        
        // 计算本周能量获得（基于星际能量历史记录）
        // 这里可以通过用户的dailyEnergyHistory计算，暂时使用简化算法
        let weeklyEstimate = realDailyAverageEnergy * 7
        realWeeklyEnergyGained = min(weeklyEstimate, overview.totalStellarEnergy)
    }
    
    /// ✅ 新增：计算真实的活跃度和表现分析
    private func calculateRealActivityAnalysis() async {
        do {
            // 获取用户的所有习惯
            let userHabits = try await repositoryContainer.habitRepository.fetchHabits(for: user.id)
            
            // 获取所有习惯的完成记录
            var allCompletions: [EACompletion] = []
            for habit in userHabits {
                let habitCompletions = try await repositoryContainer.completionRepository.fetchCompletions(for: habit.id)
                allCompletions.append(contentsOf: habitCompletions)
            }
            
            // 计算本月活跃天数
            let calendar = Calendar.current
            let now = Date()
            let startOfMonth = calendar.dateInterval(of: .month, for: now)?.start ?? now
            
            let monthlyCompletions = allCompletions.filter { completion in
                completion.date >= startOfMonth
            }
            
            // 按日期分组，计算有完成记录的天数
            let uniqueDays = Set(monthlyCompletions.map { completion in
                calendar.startOfDay(for: completion.date)
            })
            realMonthlyActiveDays = uniqueDays.count
            
            // 分析最佳表现时段
            realBestPerformanceTime = analyzeBestPerformanceTime(completions: allCompletions)
            
            // 生成能量趋势数据（最近30天）
            realEnergyTrendData = generateEnergyTrendData(completions: allCompletions)
            
        } catch {
            // 计算活跃度分析失败，使用默认值
            realMonthlyActiveDays = 0
            realBestPerformanceTime = "暂无数据"
            realEnergyTrendData = Array(repeating: 0, count: 30)
        }
    }
    
    /// 分析最佳表现时段
    private func analyzeBestPerformanceTime(completions: [EACompletion]) -> String {
        guard !completions.isEmpty else { return "暂无数据" }
        
        let calendar = Calendar.current
        var timeSlotCounts: [String: Int] = [
            "早晨": 0,    // 6-12
            "下午": 0,    // 12-18
            "晚上": 0     // 18-24
        ]
        
        for completion in completions {
            let hour = calendar.component(.hour, from: completion.date)
            switch hour {
            case 6..<12:
                timeSlotCounts["早晨", default: 0] += 1
            case 12..<18:
                timeSlotCounts["下午", default: 0] += 1
            case 18..<24, 0..<6:
                timeSlotCounts["晚上", default: 0] += 1
            default:
                break
            }
        }
        
        // 找出完成次数最多的时段
        let bestTimeSlot = timeSlotCounts.max { $0.value < $1.value }
        return bestTimeSlot?.key ?? "暂无数据"
    }
    
    /// 生成能量趋势数据（最近30天）
    private func generateEnergyTrendData(completions: [EACompletion]) -> [Int] {
        let calendar = Calendar.current
        let now = Date()
        var trendData: [Int] = []
        
        // 生成最近30天的数据
        for dayOffset in (0..<30).reversed() {
            guard let targetDate = calendar.date(byAdding: .day, value: -dayOffset, to: now) else {
                trendData.append(0)
                continue
            }
            
            let startOfDay = calendar.startOfDay(for: targetDate)
            let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay) ?? startOfDay
            
            // 计算当天的完成次数作为能量值
            let dayCompletions = completions.filter { completion in
                completion.date >= startOfDay && completion.date < endOfDay
            }
            
            // 每个完成记录贡献的能量值（基于energyLevel）
            let dayEnergy = dayCompletions.reduce(0) { total, completion in
                total + completion.energyLevel
            }
            
            trendData.append(dayEnergy)
        }
        
        return trendData
    }
    
    private func shareProfile() {
        // TODO: 实现档案分享功能
    }
    
    private var stellarLevelColor: Color {
        guard let level = energyOverview?.stellarLevel else { return .blue }
        
        switch level {
        case 1...3:
            return .blue
        case 4...6:
            return .purple
        case 7...10:
            return .yellow
        default:
            return .blue
        }
    }
    
    private var explorationDays: Int {
        guard let user = currentUser else { return 0 }
        let calendar = Calendar.current
        let daysSinceCreation = calendar.dateComponents([.day], from: user.creationDate, to: Date()).day ?? 0
        return max(0, daysSinceCreation)
    }
    
    // ✅ 已移除：weeklyEnergyGained 和 dailyAverageEnergy 的模拟数据计算
    // 现在使用 realWeeklyEnergyGained 和 realDailyAverageEnergy 的真实数据计算
    
    private var earnedBadgesCount: Int {
        EABadgeType.allCases.filter { hasEarnedBadge($0) }.count
    }
    
    private var totalBadgesCount: Int {
        EABadgeType.allCases.count
    }
    
    private func hasEarnedBadge(_ badgeType: EABadgeType) -> Bool {
        // ✅ 修复：基于真实用户数据判断成就徽章获得状态
        switch badgeType {
        // 连续打卡成就
        case .weekStreak:
            return viewModel.userStats.currentStreak >= 7
        case .monthStreak:
            return viewModel.userStats.currentStreak >= 30
        case .hundredDayStreak:
            return viewModel.userStats.currentStreak >= 100
        case .yearStreak:
            return viewModel.userStats.currentStreak >= 365
            
        // 广播成就（基于真实帖子数量）
        case .sharingNovice:
            return userPostsCount >= 1
        case .sharingExpert:
            return userPostsCount >= 10
        case .sharingMaster:
            return userPostsCount >= 50
            
        // 社交成就（基于真实获赞数）
        case .socialNovice:
            return userLikesReceived >= 5
        case .socialExpert:
            return userLikesReceived >= 50
        case .socialMaster:
            return userLikesReceived >= 200
            
        // 探索成就（基于星际能量和等级）
        case .noviceExplorer:
            return (energyOverview?.stellarLevel ?? 0) >= 1
        case .stellarTraveler:
            return (energyOverview?.stellarLevel ?? 0) >= 5
        case .cosmicNavigator:
            return (energyOverview?.stellarLevel ?? 0) >= 10
        }
    }
    
    private func getBadgeIcon(_ badgeType: EABadgeType) -> String {
        switch badgeType {
        case .weekStreak, .monthStreak, .hundredDayStreak, .yearStreak:
            return "flame.fill"
        case .sharingNovice, .sharingExpert, .sharingMaster:
            return "square.and.arrow.up.fill"
        case .socialNovice, .socialExpert, .socialMaster:
            return "person.2.fill"
        case .noviceExplorer, .stellarTraveler, .cosmicNavigator:
            return "star.fill"
        }
    }
    
    private func getNextStreakTarget() -> Int {
        let current = viewModel.userStats.currentStreak
        let targets = [7, 30, 100, 365]
        return targets.first { $0 > current } ?? 365
    }
    
    private var averageDailyHabits: Double {
        let totalDays = max(1, explorationDays)
        return Double(viewModel.userStats.completedHabits) / Double(totalDays)
    }
    
    private var activityLevel: String {
        let current = viewModel.userStats.currentStreak
        switch current {
        case 0...6:
            return "新手"
        case 7...29:
            return "活跃"
        case 30...99:
            return "专家"
        default:
            return "大师"
        }
    }
}

// MARK: - 支持枚举和结构

enum ProfileTab: String, CaseIterable {
    case overview = "overview"
    case achievements = "achievements"
    case timeline = "timeline"
    case statistics = "statistics"
    
    var title: String {
        switch self {
        case .overview: return "概览"
        case .achievements: return "成就"
        case .timeline: return "时间轴"
        case .statistics: return "统计"
        }
    }
    
    var icon: String {
        switch self {
        case .overview: return "person.circle.fill"
        case .achievements: return "trophy.fill"
        case .timeline: return "timeline.selection"
        case .statistics: return "chart.bar.fill"
        }
    }
}

enum TimelineFilter: String, CaseIterable {
    case all = "all"
    case habits = "habits"
    case levels = "levels"
    case badges = "badges"
    case challenges = "challenges"
    
    var displayName: String {
        switch self {
        case .all: return "全部"
        case .habits: return "习惯成就"
        case .levels: return "等级提升"
        case .badges: return "徽章获得"
        case .challenges: return "挑战完成"
        }
    }
}

enum TrendDirection {
    case up, down, stable
    
    var icon: String {
        switch self {
        case .up: return "arrow.up"
        case .down: return "arrow.down"
        case .stable: return "minus"
        }
    }
    
    var color: Color {
        switch self {
        case .up: return .green
        case .down: return .red
        case .stable: return .gray
        }
    }
}

// MARK: - SwiftUI预览

#Preview {
    let container = try! EAAppSchema.createPreviewContainer()
    
    let sampleContainer = EARepositoryContainerImpl(modelContainer: container)
    let sampleSessionManager = EASessionManager(repositoryContainer: sampleContainer)
    let sampleViewModel = EAMeViewModel(sessionManager: sampleSessionManager)
    sampleViewModel.setRepositoryContainer(sampleContainer)
    
    // 创建示例用户
    let sampleUser = EAUser(username: "示例用户", email: "<EMAIL>")
    
    return NavigationView {
        EACosmicExplorerProfileView(
            user: sampleUser,
            repositoryContainer: sampleContainer,
            viewModel: sampleViewModel
        )
        .environmentObject(sampleSessionManager)
    }
} 