import SwiftUI

/// 通用占位符视图，用于显示开发中的页面
/// 包含生态背景效果和光粒子动画，完全按照原型图设计
struct EAPlaceholderView: View {
    let title: String
    let subtitle: String?
    let icon: String
    
    init(title: String, subtitle: String? = nil, icon: String = "gear") {
        self.title = title
        self.subtitle = subtitle
        self.icon = icon
    }
    
    var body: some View {
        ZStack {
            // 生态背景 - 完全按照原型图
            EABackgroundView()
                .ignoresSafeArea(.all)
            
            // 光粒子动画层
            LightParticlesView()
            
            // 主内容
            VStack(spacing: 32) {
                // 顶部间距 - 为状态栏和Dynamic Island提供适当间距
                Spacer(minLength: 60)
                
                // 智慧核心样式的图标
                ZStack {
                    // 发光背景
                    Circle()
                        .fill(
                            RadialGradient(
                                colors: [
                                    Color(red: 0.22, green: 0.94, blue: 0.49).opacity(0.8),
                                    Color(red: 0.0, green: 0.78, blue: 0.78).opacity(0.7),
                                    Color(red: 0.0, green: 0.5, blue: 0.5).opacity(0.5)
                                ],
                                center: .center,
                                startRadius: 0,
                                endRadius: 40
                            )
                        )
                        .frame(width: 80, height: 80)
                        .shadow(
                            color: Color(red: 0.22, green: 0.94, blue: 0.49).opacity(0.5),
                            radius: 15,
                            x: 0,
                            y: 0
                        )
                        .shadow(
                            color: Color(red: 0.0, green: 0.78, blue: 0.78).opacity(0.3),
                            radius: 25,
                            x: 0,
                            y: 0
                        )
                    
                    // 图标
                    Image(systemName: icon)
                        .font(.system(size: 32, weight: .medium))
                        .foregroundColor(.white)
                }
                .scaleEffect(1.0)
                .animation(
                    Animation.easeInOut(duration: 4.0)
                        .repeatForever(autoreverses: true),
                    value: UUID()
                )
                
                VStack(spacing: 16) {
                    // 主标题
                    Text(title)
                        .font(.system(size: 28, weight: .semibold, design: .rounded))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [
                                    Color(red: 0.36, green: 0.85, blue: 0.85),
                                    Color(red: 0.22, green: 0.94, blue: 0.49)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .multilineTextAlignment(.center)
                    
                    // 副标题
                    if let subtitle = subtitle {
                        Text(subtitle)
                            .font(.system(size: 16, weight: .regular))
                            .foregroundColor(Color(red: 0.69, green: 0.93, blue: 0.93))
                            .multilineTextAlignment(.center)
                            .lineLimit(3)
                    }
                }
                
                Spacer()
                
                // 开发中提示 - 生态风格
                VStack(spacing: 12) {
                    HStack(spacing: 8) {
                        Image(systemName: "leaf.fill")
                            .font(.system(size: 16))
                            .foregroundColor(Color(red: 0.22, green: 0.94, blue: 0.49))
                        
                        Text("生态系统构建中")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(Color(red: 0.25, green: 0.88, blue: 0.82))
                    }
                    
                    Text("即将为您呈现完整的计划生态体验")
                        .font(.system(size: 12))
                        .foregroundColor(Color(red: 0.69, green: 0.93, blue: 0.93).opacity(0.8))
                        .multilineTextAlignment(.center)
                }
                .padding(.horizontal, 24)
                .padding(.vertical, 16)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.black.opacity(0.2))
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(.ultraThinMaterial)
                                .environment(\.colorScheme, .dark)
                        )
                )
                
                // 底部间距 - 为Tab Bar留出空间
                Spacer(minLength: 120)
            }
            .padding(.horizontal, 32)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

/// 光粒子动画视图
struct LightParticlesView: View {
    @State private var animationOffset1 = CGSize.zero
    @State private var animationOffset2 = CGSize.zero
    @State private var animationOffset3 = CGSize.zero
    @State private var animationOffset4 = CGSize.zero
    
    var body: some View {
        ZStack {
            // 光粒子1
            Circle()
                .fill(Color(red: 0.68, green: 0.85, blue: 0.90).opacity(0.3))
                .frame(width: 5, height: 5)
                .offset(animationOffset1)
                .position(x: 60, y: 120)
                .onAppear {
                    withAnimation(
                        Animation.easeInOut(duration: 12.0)
                            .repeatForever(autoreverses: true)
                    ) {
                        animationOffset1 = CGSize(width: 10, height: -20)
                    }
                }
            
            // 光粒子2
            Circle()
                .fill(Color(red: 0.68, green: 0.85, blue: 0.90).opacity(0.4))
                .frame(width: 8, height: 8)
                .offset(animationOffset2)
                .position(x: 320, y: 200)
                .onAppear {
                    withAnimation(
                        Animation.easeInOut(duration: 15.0)
                            .repeatForever(autoreverses: true)
                            .delay(2.0)
                    ) {
                        animationOffset2 = CGSize(width: -15, height: 25)
                    }
                }
            
            // 光粒子3
            Circle()
                .fill(Color(red: 0.68, green: 0.85, blue: 0.90).opacity(0.25))
                .frame(width: 6, height: 6)
                .offset(animationOffset3)
                .position(x: 40, y: 400)
                .onAppear {
                    withAnimation(
                        Animation.easeInOut(duration: 10.0)
                            .repeatForever(autoreverses: true)
                            .delay(1.0)
                    ) {
                        animationOffset3 = CGSize(width: 20, height: -15)
                    }
                }
            
            // 光粒子4
            Circle()
                .fill(Color(red: 0.68, green: 0.85, blue: 0.90).opacity(0.35))
                .frame(width: 7, height: 7)
                .offset(animationOffset4)
                .position(x: 280, y: 500)
                .onAppear {
                    withAnimation(
                        Animation.easeInOut(duration: 13.0)
                            .repeatForever(autoreverses: true)
                            .delay(3.0)
                    ) {
                        animationOffset4 = CGSize(width: -10, height: 30)
                    }
                }
        }
    }
}

#Preview {
    EAPlaceholderView(
        title: "今日",
                        subtitle: "查看今日计划进度和能量状态",
        icon: "sun.max.fill"
    )
    .preferredColorScheme(.dark)
}

#Preview("Atlas") {
    EAPlaceholderView(
        title: "图鉴",
                        subtitle: "管理你的计划生态系统",
        icon: "book.fill"
    )
    .preferredColorScheme(.dark)
}

#Preview("AuraSpace") {
    EAPlaceholderView(
        title: "灵境",
        subtitle: "与AI智慧核心深度交流",
        icon: "brain.head.profile"
    )
    .preferredColorScheme(.dark)
}

#Preview("Me") {
    EAPlaceholderView(
        title: "我的",
        subtitle: "个人中心与设置",
        icon: "person.fill"
    )
    .preferredColorScheme(.dark)
} 