import SwiftUI
import Photos

/// 🔑 图片源类型枚举 - 支持本地文件和网络URL
public enum ImageSource: Hashable {
    case local(path: String)
    case remote(url: URL)
    
    /// 获取显示用的URL
    var displayURL: URL? {
        switch self {
        case .local(let path):
            return getLocalImageURL(from: path)
        case .remote(let url):
            return url
        }
    }
    
    /// 获取用于保存的UIImage
    func loadUIImage() async -> UIImage? {
        switch self {
        case .local(let path):
            let fullPath = getFullLocalPath(from: path)
            return UIImage(contentsOfFile: fullPath)
        case .remote(let url):
            do {
                let (data, _) = try await URLSession.shared.data(from: url)
                return UIImage(data: data)
            } catch {
                return nil
            }
        }
    }
}

/// 🔑 修复：获取本地图片的完整路径 - 增强好友聊天图片支持
public func getFullLocalPath(from path: String) -> String {
    // 如果已经是完整路径，先验证是否存在
    if path.hasPrefix("/") {
        if FileManager.default.fileExists(atPath: path) {
            return path
        }
    }

    // 获取基础目录路径
    let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first?.path ?? ""
    let cachesPath = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first?.path ?? ""

    // 🔑 修复好友聊天图片支持：按优先级检查多个可能的路径位置
    let possiblePaths = [
        // 1. Documents目录下的直接路径（好友聊天图片：Images/xxx.jpg）
        "\(documentsPath)/\(path)",

        // 2. 如果是纯文件名，尝试在Images子目录中查找（好友聊天常用）
        path.contains("/") ? nil : "\(documentsPath)/Images/\(path)",

        // 3. 🔑 新增：专门支持好友聊天的ChatImages目录
        path.contains("/") ? nil : "\(documentsPath)/ChatImages/\(path)",

        // 4. 🔑 新增：支持媒体服务处理的图片路径
        path.contains("/") ? nil : "\(documentsPath)/ProcessedImages/\(path)",

        // 5. 🔑 新增：支持用户上传的图片路径
        path.contains("/") ? nil : "\(documentsPath)/UserImages/\(path)",

        // 6. 临时目录
        "\(NSTemporaryDirectory())\(path)",

        // 7. 🔑 新增：临时目录下的Images子目录
        path.contains("/") ? nil : "\(NSTemporaryDirectory())Images/\(path)",

        // 8. 缓存目录
        "\(cachesPath)/\(path)",

        // 9. 如果是纯文件名，尝试在缓存的Images子目录中查找
        path.contains("/") ? nil : "\(cachesPath)/Images/\(path)",

        // 10. 🔑 新增：缓存目录下的ChatImages子目录
        path.contains("/") ? nil : "\(cachesPath)/ChatImages/\(path)",

        // 11. 🔑 新增：Bundle资源路径（用于测试图片）
        Bundle.main.path(forResource: path.replacingOccurrences(of: ".jpg", with: "").replacingOccurrences(of: ".png", with: ""), ofType: nil)
    ].compactMap { $0 }

    // 按优先级检查每个路径
    for possiblePath in possiblePaths {
        if FileManager.default.fileExists(atPath: possiblePath) {
            return possiblePath
        }
    }

    // 🔑 修复：如果文件不存在，在调试模式下创建测试图片
    #if DEBUG
    if ProcessInfo.processInfo.environment["SIMULATOR_DEVICE_NAME"] != nil {
        return createTestImageIfNeeded(for: path)
    }
    #endif

    // 如果都找不到，返回Documents目录下的路径作为默认值
    let defaultPath = "\(documentsPath)/\(path)"
    return defaultPath
}

/// 🔑 修复：获取本地图片URL - 增强路径处理和调试信息
public func getLocalImageURL(from path: String) -> URL? {
    let fullPath = getFullLocalPath(from: path)

    if FileManager.default.fileExists(atPath: fullPath) {
        return URL(fileURLWithPath: fullPath)
    }

    // 🔑 修复：增强调试信息，帮助诊断好友聊天图片问题
    #if DEBUG
    if ProcessInfo.processInfo.environment["SIMULATOR_DEVICE_NAME"] != nil {
        // 模拟器环境下提供详细的路径诊断信息
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first?.path ?? ""
        
        // 检查常见的图片存储位置（用于调试信息）
        _ = [
            "\(documentsPath)/\(path)",
            "\(documentsPath)/Images/\(path.contains("/") ? String(path.split(separator: "/").last ?? "") : path)",
            "\(documentsPath)/ChatImages/\(path.contains("/") ? String(path.split(separator: "/").last ?? "") : path)"
        ]
        
        // 尝试创建测试图片以确保功能正常
        let testPath = createTestImageIfNeeded(for: path)
        if FileManager.default.fileExists(atPath: testPath) {
            return URL(fileURLWithPath: testPath)
        }
    }
    #endif

    // 🔑 修复：增强错误处理 - 返回nil而不是创建无效URL
    return nil
}

/// 🔑 为模拟器创建测试图片（仅调试模式）
#if DEBUG
private func createTestImageIfNeeded(for path: String) -> String {
    let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
    let fullURL = documentsPath.appendingPathComponent(path)
    let fullPath = fullURL.path
    
    // 如果文件已存在，直接返回
    if FileManager.default.fileExists(atPath: fullPath) {
        return fullPath
    }
    
    // 创建测试图片
    let size = CGSize(width: 400, height: 300)
    let renderer = UIGraphicsImageRenderer(size: size)
    
    let testImage = renderer.image { context in
        // 背景色
        UIColor.systemBlue.setFill()
        context.fill(CGRect(origin: .zero, size: size))
        
        // 添加文字说明
        let text = "测试图片\n\(path)" as NSString
        let attributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 16),
            .foregroundColor: UIColor.white
        ]
        
        let textRect = CGRect(x: 20, y: size.height/2 - 20, width: size.width - 40, height: 40)
        text.draw(in: textRect, withAttributes: attributes)
    }
    
    // 确保目录存在
    try? FileManager.default.createDirectory(at: fullURL.deletingLastPathComponent(), withIntermediateDirectories: true, attributes: nil)
    
    // 保存测试图片
    if let imageData = testImage.jpegData(compressionQuality: 0.8) {
        try? imageData.write(to: fullURL)
    }
    
    return fullPath
}

/// 🔑 新增：专门处理好友聊天图片路径的辅助函数
/// 为好友聊天图片提供增强的路径兼容性支持
private func resolveFriendChatImagePath(_ originalPath: String) -> String? {
    let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
    
    // 如果是形如 "Images/image_xxx.jpg" 的路径，直接处理
    if originalPath.hasPrefix("Images/") {
        let fullURL = documentsPath.appendingPathComponent(originalPath)
        if FileManager.default.fileExists(atPath: fullURL.path) {
            return fullURL.path
        }
    }
    
    // 如果是纯文件名，尝试在好友聊天常用的目录中查找
    if !originalPath.contains("/") {
        let possibleDirectories = ["Images", "ChatImages", "TempImages"]
        for directory in possibleDirectories {
            let fullURL = documentsPath.appendingPathComponent(directory).appendingPathComponent(originalPath)
            if FileManager.default.fileExists(atPath: fullURL.path) {
                return fullURL.path
            }
        }
    }
    
    return nil
}
#endif

/// 🔑 星域风格的图片查看器 - 支持画廊模式、缩放、长按保存
struct EAZoomableImageView: View {
    let imageSources: [ImageSource]
    let startIndex: Int
    
    @State private var currentIndex: Int
    @State private var scale: CGFloat = 1.0
    @State private var offset: CGSize = .zero
    @State private var lastScaleValue: CGFloat = 1.0
    @State private var lastOffset: CGSize = .zero
    
    // 🔑 新增：长按保存相关状态
    @State private var showingSaveAlert = false
    @State private var saveAlertMessage = ""
    @State private var currentImageForSave: UIImage?
    @State private var showingSaveConfirmation = false
    
    @Environment(\.dismiss) private var dismiss
    
    init(imageSources: [ImageSource], startIndex: Int = 0) {
        self.imageSources = imageSources
        self.startIndex = max(0, min(startIndex, imageSources.count - 1))
        self._currentIndex = State(initialValue: max(0, min(startIndex, imageSources.count - 1)))
    }
    
    var body: some View {
        ZStack {
            // 🌌 星域背景
            Color.black
                .ignoresSafeArea()
            
            if imageSources.isEmpty {
                emptyStateView
            } else {
                imageGalleryView
            }
            
            // 关闭按钮
            VStack {
                HStack {
                    Spacer()
                    Button(action: { dismiss() }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title2)
                            .foregroundColor(.white)
                            .background(Color.black.opacity(0.6))
                            .clipShape(Circle())
                    }
                    .padding()
                }
                Spacer()
            }
            
            // 图片指示器
            if imageSources.count > 1 {
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        Text("\(currentIndex + 1) / \(imageSources.count)")
                            .font(.caption)
                            .foregroundColor(.white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(Color.black.opacity(0.6))
                            .cornerRadius(15)
                            .padding()
                        Spacer()
                    }
                }
            }
        }
        // 🔑 修复：长按保存确认对话框
        .confirmationDialog("保存图片", isPresented: $showingSaveConfirmation) {
            Button("保存到相册") {
                saveCurrentImageToPhotos()
            }
            Button("取消", role: .cancel) { }
        } message: {
            Text("将这张图片保存到您的相册中")
        }
        .alert("保存结果", isPresented: $showingSaveAlert) {
            Button("确定") { }
        } message: {
            Text(saveAlertMessage)
        }
    }
    
    // MARK: - 图片画廊视图
    private var imageGalleryView: some View {
        TabView(selection: $currentIndex) {
            ForEach(imageSources.indices, id: \.self) { index in
                zoomableImageView(for: imageSources[index])
                    .tag(index)
                    .onAppear {
                        // 重置当前图片的缩放状态
                        if index == currentIndex {
                            resetZoom()
                        }
                    }
            }
        }
        .tabViewStyle(.page(indexDisplayMode: .never))
        .onChange(of: currentIndex) { _, newIndex in
            resetZoom()
        }
    }
    
    // MARK: - 可缩放图片视图
    private func zoomableImageView(for source: ImageSource) -> some View {
        GeometryReader { geometry in
            // 本地文件用UIImage加载，远程URL仍用AsyncImage
            if case .local = source {
                if let url = source.displayURL,
                   let uiImage = UIImage(contentsOfFile: url.path) {
                    Image(uiImage: uiImage)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .scaleEffect(scale)
                        .offset(offset)
                        .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                        .onTapGesture(count: 2) {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                if scale > 1.0 { resetZoom() } else { scale = 2.0 }
                            }
                        }
                        .onLongPressGesture(minimumDuration: 0.8, maximumDistance: 30) {
                            requestSaveConfirmation(for: source)
                        }
                        .gesture(magnificationGesture())
                        .gesture(scale > 1.0 ? dragGesture() : nil)
                } else {
                    // 🔑 修复：本地图片加载失败时显示详细错误信息和重试功能
                    VStack(spacing: 16) {
                        Image(systemName: "photo.badge.exclamationmark")
                            .font(.system(size: 48))
                            .foregroundColor(.white.opacity(0.8))

                        Text("图片加载失败")
                            .font(.headline)
                            .foregroundColor(.white)

                        Text("无法找到或加载图片文件")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.7))
                            .multilineTextAlignment(.center)

                        // 🔑 新增：重试按钮
                        Button("重新加载") {
                            // 强制刷新当前视图
                            resetZoom()
                        }
                        .font(.caption)
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(Color.blue.opacity(0.6))
                        .cornerRadius(8)

                        #if DEBUG
                        if case .local(let path) = source {
                            VStack(spacing: 4) {
                                Text("调试信息:")
                                    .font(.caption2)
                                    .foregroundColor(.white.opacity(0.5))

                                Text("原始路径: \(path)")
                                    .font(.caption2)
                                    .foregroundColor(.white.opacity(0.5))
                                    .multilineTextAlignment(.center)
                                    .padding(.horizontal)

                                if let url = source.displayURL {
                                    Text("解析路径: \(url.path)")
                                        .font(.caption2)
                                        .foregroundColor(.white.opacity(0.5))
                                        .multilineTextAlignment(.center)
                                        .padding(.horizontal)

                                    Text("文件存在: \(FileManager.default.fileExists(atPath: url.path) ? "是" : "否")")
                                        .font(.caption2)
                                        .foregroundColor(.white.opacity(0.5))
                                } else {
                                    Text("URL解析失败")
                                        .font(.caption2)
                                        .foregroundColor(.red.opacity(0.7))
                                }
                            }
                            .padding(.top, 8)
                        }
                        #endif
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.black.opacity(0.8))
                }
            } else {
                AsyncImage(url: source.displayURL) { phase in
                    switch phase {
                    case .success(let image):
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .scaleEffect(scale)
                            .offset(offset)
                            .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                            // 🔑 核心修复：重新设计智能手势架构，确保TabView滑动和图片功能完美共存
                            // 双击缩放手势 - 始终可用，但使用更精确的实现
                            .onTapGesture(count: 2) {
                                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                    if scale > 1.0 { resetZoom() } else { scale = 2.0 }
                                }
                            }
                            // 🔑 长按保存 - 使用高级配置避免干扰滑动
                            .onLongPressGesture(minimumDuration: 0.8, maximumDistance: 30) {
                                requestSaveConfirmation(for: source)
                            }
                            // 🔑 缩放手势 - 使用高优先级，只在多指时激活
                            .gesture(magnificationGesture())
                            // 🔑 关键修复：条件拖拽手势 - 只在图片缩放时才添加，避免与TabView冲突
                            .gesture(scale > 1.0 ? dragGesture() : nil)
                            .clipped()
                        
                    case .failure(_):
                        // 🔑 修复：加载失败状态 - 添加重试功能
                        VStack(spacing: 16) {
                            Image(systemName: "photo.badge.exclamationmark")
                                .font(.system(size: 48))
                                .foregroundColor(.gray)

                            Text("图片加载失败")
                                .font(.caption)
                                .foregroundColor(.gray)

                            // 🔑 新增：重试按钮
                            Button("点击重试") {
                                // 强制刷新AsyncImage
                                if let url = source.displayURL {
                                    // 通过添加时间戳参数强制重新加载
                                    _ = url.appendingPathComponent("?refresh=\(Date().timeIntervalSince1970)")
                                    // 这里可以触发重新加载逻辑
                                }
                            }
                            .font(.caption)
                            .foregroundColor(.blue)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(8)

                            #if DEBUG
                            if case .local(let path) = source {
                                Text("路径: \(path)")
                                    .font(.caption2)
                                    .foregroundColor(.gray.opacity(0.7))
                                    .multilineTextAlignment(.center)
                                    .padding(.horizontal)
                            } else if case .remote(let url) = source {
                                Text("URL: \(url.absoluteString)")
                                    .font(.caption2)
                                    .foregroundColor(.gray.opacity(0.7))
                                    .multilineTextAlignment(.center)
                                    .padding(.horizontal)
                            }
                            #endif
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        
                    case .empty:
                        loadingStateView
                        
                    @unknown default:
                        loadingStateView
                    }
                }
            }
        }
    }
    
    // MARK: - 长按保存确认
    private func requestSaveConfirmation(for source: ImageSource) {
        Task {
            if let image = await source.loadUIImage() {
                await MainActor.run {
                    currentImageForSave = image
                    showingSaveConfirmation = true
                }
            } else {
                await MainActor.run {
                    saveAlertMessage = "图片加载失败，无法保存"
                    showingSaveAlert = true
                }
            }
        }
    }
    
    // MARK: - 保存图片到相册
    private func saveCurrentImageToPhotos() {
        guard let image = currentImageForSave else {
            saveAlertMessage = "图片加载失败"
            showingSaveAlert = true
            return
        }
        
        // 检查相册权限
        PHPhotoLibrary.requestAuthorization(for: .addOnly) { status in
            DispatchQueue.main.async {
                switch status {
                case .authorized, .limited:
                    UIImageWriteToSavedPhotosAlbum(image, nil, nil, nil)
                    saveAlertMessage = "图片已成功保存到相册"
                    showingSaveAlert = true
                case .denied, .restricted:
                    saveAlertMessage = "无法保存图片，请在设置中允许访问相册"
                    showingSaveAlert = true
                case .notDetermined:
                    saveAlertMessage = "无法确定相册权限"
                    showingSaveAlert = true
                @unknown default:
                    saveAlertMessage = "保存失败"
                    showingSaveAlert = true
                }
            }
        }
    }
    
    // MARK: - 重置缩放
    private func resetZoom() {
        withAnimation(.spring()) {
            scale = 1.0
            offset = .zero
            lastScaleValue = 1.0
            lastOffset = .zero
        }
    }
    
    // MARK: - 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "photo")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("暂无图片")
                .font(.title2)
                .foregroundColor(.gray)
        }
    }
    
    // MARK: - 加载状态视图
    private var loadingStateView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
                .tint(.white)
            
            Text("加载中...")
                .font(.caption)
                .foregroundColor(.gray)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - 手势
    private func magnificationGesture() -> some Gesture {
        MagnificationGesture()
            .onChanged { value in
                let delta = value / lastScaleValue
                lastScaleValue = value
                scale *= delta
                scale = max(0.5, min(scale, 5.0))
            }
            .onEnded { _ in
                lastScaleValue = 1.0
                if scale < 1.0 {
                    withAnimation(.spring()) {
                        scale = 1.0
                        offset = .zero
                    }
                }
            }
    }
    
    private func dragGesture() -> some Gesture {
        // 🔑 关键修复：只在图片缩放时才启用拖拽手势，避免与TabView滑动冲突
        DragGesture(minimumDistance: scale > 1.0 ? 5 : 50)
            .onChanged { value in
                // 只有在图片缩放时才允许拖拽
                if scale > 1.0 {
                    offset = CGSize(
                        width: lastOffset.width + value.translation.width,
                        height: lastOffset.height + value.translation.height
                    )
                }
            }
            .onEnded { _ in
                // 只有在图片缩放时才保存拖拽位置
                if scale > 1.0 {
                    lastOffset = offset
                }
            }
    }
}

// MARK: - 预览

#Preview("单张图片") {
    EAZoomableImageView(imageSources: [.local(path: "test-image.jpg")], startIndex: 0)
}

#Preview("多张图片") {
    EAZoomableImageView(imageSources: [.local(path: "image1.jpg"), .local(path: "image2.jpg"), .local(path: "image3.jpg")], startIndex: 0)
}

#Preview("混合图片源") {
    EAZoomableImageView(imageSources: [.local(path: "local-image.jpg"), .remote(url: URL(string: "https://example.com/image.jpg")!)], startIndex: 0)
} 