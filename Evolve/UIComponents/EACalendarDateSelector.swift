import SwiftUI
import Foundation

/// 日历式日期选择组件
/// 提供更直观的日历表形式，顶部显示周期（周一到周日）
struct EACalendarDateSelector: View {
    @Binding var selectedDates: Set<Int>
    let title: String
    let subtitle: String?
    
    // 日历相关属性
    @State private var currentMonth: Date = Date()
    private let calendar = Calendar.current
    
    // 周期名称（周一到周日）
    private let weekdayHeaders = ["一", "二", "三", "四", "五", "六", "日"]
    
    init(
        selectedDates: Binding<Set<Int>>,
        title: String = "选择执行日期",
        subtitle: String? = "点击日期进行选择"
    ) {
        self._selectedDates = selectedDates
        self.title = title
        self.subtitle = subtitle
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题区域
            headerSection
            
            // 月份导航
            monthNavigationSection
            
            // 日历表
            calendarGridSection
            
            // 选择状态提示
            selectionStatusSection
        }
        .padding(16)
        .background(calendarBackground)
        .onChange(of: currentMonth) { _, _ in
            // 当月份改变时，清理无效的日期选择
            cleanupInvalidSelectedDates()
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text(title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                
                Spacer()
                
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        selectedDates.removeAll()
                    }
                }) {
                    Text("清空")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(Color.hexColor("40E0D0"))
                }
                .buttonStyle(PlainButtonStyle())
            }
            
            if let subtitle = subtitle {
                Text(subtitle)
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(Color.white.opacity(0.6))
            }
        }
    }
    
    // MARK: - Month Navigation Section
    private var monthNavigationSection: some View {
        HStack {
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    currentMonth = calendar.date(byAdding: .month, value: -1, to: currentMonth) ?? currentMonth
                }
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color.hexColor("40E0D0"))
                    .frame(width: 32, height: 32)
                    .background(
                        Circle()
                            .fill(Color.white.opacity(0.05))
                            .overlay(
                                Circle()
                                    .stroke(Color.white.opacity(0.1), lineWidth: 1)
                            )
                    )
            }
            .buttonStyle(PlainButtonStyle())
            
            Spacer()
            
            VStack(spacing: 2) {
                Text(monthYearString)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                
                Text("点击日期选择执行时间")
                    .font(.system(size: 11, weight: .regular))
                    .foregroundColor(Color.white.opacity(0.5))
            }
            
            Spacer()
            
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    currentMonth = calendar.date(byAdding: .month, value: 1, to: currentMonth) ?? currentMonth
                }
            }) {
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color.hexColor("40E0D0"))
                    .frame(width: 32, height: 32)
                    .background(
                        Circle()
                            .fill(Color.white.opacity(0.05))
                            .overlay(
                                Circle()
                                    .stroke(Color.white.opacity(0.1), lineWidth: 1)
                            )
                    )
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    // MARK: - Calendar Grid Section
    private var calendarGridSection: some View {
        VStack(spacing: 8) {
            // 周期标题行（周一到周日）
            weekdayHeaderRow
            
            // 日期网格
            calendarDatesGrid
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.03))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(0.08), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Weekday Header Row
    private var weekdayHeaderRow: some View {
        HStack(spacing: 0) {
            ForEach(weekdayHeaders, id: \.self) { weekday in
                Text(weekday)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(Color.hexColor("40E0D0"))
                    .frame(maxWidth: .infinity)
                    .frame(height: 32)
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.hexColor("40E0D0").opacity(0.1))
        )
    }
    
    // MARK: - Calendar Dates Grid
    private var calendarDatesGrid: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 6) {
            ForEach(Array(calendarDays.enumerated()), id: \.offset) { index, day in
                calendarDayCell(day: day)
            }
        }
    }
    
    // MARK: - Calendar Day Cell
    private func calendarDayCell(day: Int) -> some View {
        Button(action: {
            if day > 0 {
                withAnimation(.easeInOut(duration: 0.2)) {
                    if selectedDates.contains(day) {
                        selectedDates.remove(day)
                    } else {
                        selectedDates.insert(day)
                    }
                }
            }
        }) {
            ZStack {
                // 背景
                RoundedRectangle(cornerRadius: 8)
                    .fill(dayBackgroundColor(for: day))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(dayBorderColor(for: day), lineWidth: 1)
                    )
                
                // 日期数字
                if day > 0 {
                    Text("\(day)")
                        .font(.system(size: 14, weight: selectedDates.contains(day) ? .semibold : .medium))
                        .foregroundColor(dayTextColor(for: day))
                } else {
                    // 空白占位
                    Text("")
                }
            }
            .frame(height: 36)
            .scaleEffect(selectedDates.contains(day) && day > 0 ? 1.05 : 1.0)
            .animation(.easeInOut(duration: 0.15), value: selectedDates.contains(day))
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(day <= 0)
    }
    
    // MARK: - Selection Status Section
    private var selectionStatusSection: some View {
        HStack {
            if !selectedDates.isEmpty {
                HStack(spacing: 8) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color.hexColor("40E0D0"))
                    
                    Text("已选择 \(selectedDates.count) 个日期")
                        .font(.system(size: 13, weight: .medium))
                        .foregroundColor(.white)
                }
                
                Spacer()
                
                // 显示选中的日期（最多显示前5个）
                HStack(spacing: 4) {
                    let sortedDates = Array(selectedDates.sorted().prefix(5))
                    ForEach(sortedDates, id: \.self) { date in
                        Text("\(date)")
                            .font(.system(size: 11, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(Color.hexColor("40E0D0").opacity(0.3))
                            )
                    }
                    
                    if selectedDates.count > 5 {
                        Text("...")
                            .font(.system(size: 11, weight: .medium))
                            .foregroundColor(Color.white.opacity(0.6))
                    }
                }
            } else {
                HStack(spacing: 8) {
                    Image(systemName: "calendar")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color.white.opacity(0.5))
                    
                    Text("未选择日期")
                        .font(.system(size: 13, weight: .medium))
                        .foregroundColor(Color.white.opacity(0.6))
                }
                
                Spacer()
            }
        }
        .padding(.top, 8)
    }
    
    // MARK: - Computed Properties
    
    private var monthYearString: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年M月"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: currentMonth)
    }
    
    private var calendarDays: [Int] {
        guard let firstOfMonth = calendar.dateInterval(of: .month, for: currentMonth)?.start else {
            return []
        }
        
        let firstWeekday = calendar.component(.weekday, from: firstOfMonth)
        // 转换为我们的格式（1=周一，7=周日）
        let adjustedFirstWeekday = firstWeekday == 1 ? 7 : firstWeekday - 1
        
        let daysInMonth = calendar.range(of: .day, in: .month, for: currentMonth)?.count ?? 31
        
        var days: [Int] = []
        
        // 添加前面的空白天数
        for _ in 1..<adjustedFirstWeekday {
            days.append(0) // 0 表示空白
        }
        
        // 添加当月的实际天数（根据真实日历显示）
        for day in 1...daysInMonth {
            days.append(day)
        }
        
        // 补齐到完整的周（42个格子 = 6周）
        while days.count < 42 {
            days.append(0)
        }
        
        return days
    }
    
    // MARK: - Style Methods
    
    private func dayBackgroundColor(for day: Int) -> Color {
        if day <= 0 {
            return Color.clear
        } else if selectedDates.contains(day) {
            return Color.hexColor("40E0D0")
        } else {
            return Color.white.opacity(0.05)
        }
    }
    
    private func dayBorderColor(for day: Int) -> Color {
        if day <= 0 {
            return Color.clear
        } else if selectedDates.contains(day) {
            return Color.hexColor("40E0D0")
        } else {
            return Color.white.opacity(0.1)
        }
    }
    
    private func dayTextColor(for day: Int) -> Color {
        if day <= 0 {
            return Color.clear
        } else if selectedDates.contains(day) {
            return .white
        } else {
            return Color.white.opacity(0.8)
        }
    }
    
    // MARK: - Helper Methods
    
    private func cleanupInvalidSelectedDates() {
        let daysInMonth = calendar.range(of: .day, in: .month, for: currentMonth)?.count ?? 31
        let validDates = selectedDates.filter { $0 <= daysInMonth }
        
        if validDates.count != selectedDates.count {
            withAnimation(.easeInOut(duration: 0.2)) {
                selectedDates = Set(validDates)
            }
        }
    }
    
    private var calendarBackground: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(Color.white.opacity(0.05))
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.white.opacity(0.1), lineWidth: 1)
            )
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.black.ignoresSafeArea()
        
        VStack {
            EACalendarDateSelector(
                selectedDates: .constant(Set([1, 15, 28])),
                title: "选择执行日期",
                subtitle: "点击日期进行选择"
            )
            
            Spacer()
        }
        .padding()
    }
} 