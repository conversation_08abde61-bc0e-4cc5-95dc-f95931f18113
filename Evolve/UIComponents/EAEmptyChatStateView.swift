import SwiftUI

/// 聊天空状态视图组件
/// 用于显示聊天页面的空状态，包含开始对话提示和快速开场白功能
struct EAEmptyChatStateView: View {
    let friendName: String
    let onQuickStartTap: (String) -> Void
    
    var body: some View {
        VStack(spacing: 24) {
            // 空状态图标
            iconSection
            
            // 文本提示
            textSection
            
            // 快速开场白按钮
            quickStartButtons
        }
        .padding(.horizontal, 32)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - 子视图组件
    
    /// 空状态图标
    private var iconSection: some View {
        ZStack {
            Circle()
                .fill(Color.chatAccentPrimary.opacity(0.2))
                .frame(width: 80, height: 80)

            Image(systemName: "message")
                .font(.system(size: 32, weight: .medium))
                .foregroundColor(Color.chatAccentPrimary)
        }
    }
    
    /// 文本提示区域
    private var textSection: some View {
        VStack(spacing: 12) {
            Text("开始对话")
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(.white)
            
            Text("发送第一条消息开始你们的星际对话")
                .font(.system(size: 16))
                .foregroundColor(.white.opacity(0.7))
                .multilineTextAlignment(.center)
        }
    }
    
    /// 快速开场白按钮区域
    private var quickStartButtons: some View {
        VStack(spacing: 8) {
            Text("快速开场白：")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white.opacity(0.8))
            
            // 🔧 使用EAQuickReplyButtonGroup简化代码
            EAQuickReplyButtonGroup(
                replies: ["你好！", "最近怎么样？", "一起加油！"]
            ) { reply in
                onQuickStartTap(reply)
            }
        }
    }
}

// MARK: - 预览
#Preview {
    ZStack {
        // 使用聊天页面的背景色
        EABackgroundView(style: .auraSpace, showParticles: true)
            .ignoresSafeArea()
        
        EAEmptyChatStateView(
            friendName: "张三"
        ) { message in
            print("快速开场白: \(message)")
        }
    }
    .preferredColorScheme(.dark)
}

#Preview("不同好友名称") {
    ZStack {
        EABackgroundView(style: .auraSpace, showParticles: true)
            .ignoresSafeArea()
        
        EAEmptyChatStateView(
            friendName: "李四"
        ) { message in
            print("快速开场白: \(message)")
        }
    }
    .preferredColorScheme(.dark)
}
