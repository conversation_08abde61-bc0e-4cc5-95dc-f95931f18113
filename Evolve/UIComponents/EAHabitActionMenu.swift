import SwiftUI
import SwiftData

// MARK: - 习惯功能菜单弹窗组件 - 简化版本
struct EAHabitActionMenu: View {
    let habit: EAHabit
    let onEdit: () -> Void
    let onDelete: () -> Void
    
    @Environment(\.dismiss) private var dismiss
    @State private var showReminderSettings = false
    
    var body: some View {
        VStack(spacing: 0) {
            // 拖拽指示器
            RoundedRectangle(cornerRadius: 2.5)
                .fill(Color.secondary.opacity(0.4))
                .frame(width: 36, height: 5)
                .padding(.top, 12)
                .padding(.bottom, 24)
            
            // 菜单选项 - 简化版本
            VStack(spacing: 1) {
                // 编辑
                ActionMenuItem(
                    icon: "pencil",
                    title: "编辑",
                    iconColor: Color.blue,
                    isFirst: true
                ) {
                    onEdit()
                    dismiss()
                }
                .accessibilityLabel("编辑习惯")
                .accessibilityHint("点击编辑习惯的名称、图标和频率设置")
                
                // 提醒设置 - 暂时注释掉
                /*
                ActionMenuItem(
                    icon: "bell",
                    title: "提醒设置",
                    iconColor: Color.orange
                ) {
                    showReminderSettings = true
                }
                .accessibilityLabel("设置提醒")
                .accessibilityHint("配置习惯的提醒时间和频率")
                */
                
                // 删除习惯
                ActionMenuItem(
                    icon: "trash",
                    title: "删除习惯",
                    iconColor: Color.red,
                    isLast: true
                ) {
                    onDelete()
                    dismiss()
                }
                .accessibilityLabel("删除习惯")
                .accessibilityHint("永久删除此习惯及其所有记录")
            }
            .background(
                RoundedRectangle(cornerRadius: 14)
                    .fill(.regularMaterial)
            )
            .padding(.horizontal, 20)
            
            Spacer(minLength: 40)
        }
        .background(Color.clear)
        // 暂时注释掉提醒设置页面
        /*
        .sheet(isPresented: $showReminderSettings) {
            EAHabitReminderSettingsView(habit: habit)
        }
        */
    }
}

// MARK: - 菜单项组件 - 简化版本
struct ActionMenuItem: View {
    let icon: String
    let title: String
    let iconColor: Color
    let isFirst: Bool
    let isLast: Bool
    let action: () -> Void
    
    init(
        icon: String,
        title: String,
        iconColor: Color = .primary,
        isFirst: Bool = false,
        isLast: Bool = false,
        action: @escaping () -> Void
    ) {
        self.icon = icon
        self.title = title
        self.iconColor = iconColor
        self.isFirst = isFirst
        self.isLast = isLast
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // 图标
                Image(systemName: icon)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(iconColor)
                    .frame(width: 24, height: 24)
                
                // 标题
                Text(title)
                    .font(.system(size: 17, weight: .regular))
                    .foregroundColor(.primary)
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
        .background(
            Rectangle()
                .fill(Color.clear)
        )
        .overlay(
            // 分割线 - 除了最后一个项目
            Group {
                if !isLast {
                    VStack {
                        Spacer()
                        Rectangle()
                            .fill(Color.gray.opacity(0.3))
                            .frame(height: 0.5)
                            .padding(.leading, 60)
                    }
                }
            }
        )
    }
}

// MARK: - 预览 - 简化版本
#Preview("习惯功能菜单") {
    // 创建一个简单的预览习惯
    let previewHabit = EAHabit(
        name: "预览习惯",
        iconName: "figure.walk",
        targetFrequency: 7
    )
    
    EAHabitActionMenu(
        habit: previewHabit,
                    onEdit: { 
                                  #if DEBUG
                          print("编辑习惯")
                  #endif
            },
            onDelete: { 
                #if DEBUG
                        print("删除习惯")
                #endif
            }
    )
}