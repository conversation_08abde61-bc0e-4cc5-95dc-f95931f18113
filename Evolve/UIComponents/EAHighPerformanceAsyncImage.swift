import SwiftUI
import UIKit

/// 🚀 高性能异步图片组件 - 微信朋友圈级别优化
/// 核心功能：分层加载 + 渐进式显示 + 智能预加载 + 内存优化
/// 专为社区页面List滚动性能优化设计
@MainActor
struct EAHighPerformanceAsyncImage<Content: View, Placeholder: View>: View {
    
    // MARK: - Properties
    
    let url: URL?
    let targetSize: CGSize
    let tier: EAImageCacheService.ImageTier
    let enableProgressive: Bool
    let enablePreload: Bool
    let content: (Image) -> Content
    let placeholder: () -> Placeholder
    
    // MARK: - State
    
    @State private var currentImage: UIImage?
    @State private var isLoading = false
    @State private var loadingTask: Task<Void, Never>?
    @State private var progressiveTask: Task<Void, Never>?
    
    // 🚀 新增：渐进式加载状态
    @State private var thumbnailLoaded = false
    @State private var standardLoaded = false
    
    // ✅ 修复：通过Environment注入图片缓存服务
    @Environment(\.imageCacheService) private var imageCacheService
    
    // MARK: - Initialization
    
    init(
        url: URL?,
        targetSize: CGSize,
        tier: EAImageCacheService.ImageTier = .standard,
        enableProgressive: Bool = false,
        enablePreload: Bool = false,
        @ViewBuilder content: @escaping (Image) -> Content,
        @ViewBuilder placeholder: @escaping () -> Placeholder
    ) {
        self.url = url
        self.targetSize = targetSize
        self.tier = tier
        self.enableProgressive = enableProgressive
        self.enablePreload = enablePreload
        self.content = content
        self.placeholder = placeholder
    }
    
    // MARK: - Body
    
    var body: some View {
        Group {
            if let currentImage = currentImage {
                content(Image(uiImage: currentImage))
                    .transition(.opacity.combined(with: .scale(scale: 0.95)))
            } else {
                placeholder()
                    .onAppear {
                        startImageLoading()
                    }
            }
        }
        .onDisappear {
            cancelAllLoading()
        }
        .onReceive(NotificationCenter.default.publisher(for: UIApplication.didReceiveMemoryWarningNotification)) { _ in
            handleMemoryWarning()
        }
    }
    
    // MARK: - 🚀 微信级别图片加载
    
    /// 开始图片加载
    private func startImageLoading() {
        guard let url = url, !isLoading else { return }
        
        isLoading = true
        
        if enableProgressive {
            startProgressiveLoading(from: url)
        } else {
            startSingleTierLoading(from: url)
        }
    }
    
    /// 🚀 渐进式加载（微信朋友圈模式）
    private func startProgressiveLoading(from url: URL) {
        progressiveTask = Task {
            guard let cacheService = imageCacheService else {
                await fallbackDirectLoading(from: url)
                return
            }
            
            // 1. 先加载缩略图（快速显示）
            if let thumbnail = await cacheService.getCachedImage(for: url, tier: .thumbnail) {
                let optimizedThumbnail = await prepareImageForDisplay(thumbnail, targetSize: targetSize)
                await MainActor.run {
                    self.currentImage = optimizedThumbnail
                    self.thumbnailLoaded = true
                }
                
                // 🚀 微信级别的平滑过渡动画
                withAnimation(.easeInOut(duration: 0.2)) {
                    // 动画已在transition中定义
                }
            }
            
            // 2. 再加载标准图（高质量显示）
            if tier == .standard || tier == .original {
                let targetTier: EAImageCacheService.ImageTier = tier == .original ? .original : .standard
                if let standardImage = await cacheService.getCachedImage(for: url, tier: targetTier) {
                    let optimizedStandard = await prepareImageForDisplay(standardImage, targetSize: targetSize)
                    await MainActor.run {
                        self.currentImage = optimizedStandard
                        self.standardLoaded = true
                        self.isLoading = false
                    }
                    
                    // 🚀 更平滑的质量提升动画
                    withAnimation(.easeInOut(duration: 0.3)) {
                        // 动画已在transition中定义
                    }
                }
            } else {
                await MainActor.run {
                    self.isLoading = false
                }
            }
        }
    }
    
    /// 🚀 单层级加载
    private func startSingleTierLoading(from url: URL) {
        loadingTask = Task {
            guard let cacheService = imageCacheService else {
                await fallbackDirectLoading(from: url)
                return
            }
            
            if let cachedImage = await cacheService.getCachedImage(for: url, tier: tier) {
                let optimizedImage = await prepareImageForDisplay(cachedImage, targetSize: targetSize)
                await MainActor.run {
                    self.currentImage = optimizedImage
                    self.isLoading = false
                }
            } else {
                await MainActor.run {
                    self.isLoading = false
                }
            }
        }
    }
    
    /// 备用直接加载方案
    private func fallbackDirectLoading(from url: URL) async {
        do {
            let originalImage = try await loadOriginalImage(from: url)
            let optimizedImage = await prepareImageForDisplay(originalImage, targetSize: targetSize)
            
            await MainActor.run {
                self.currentImage = optimizedImage
                self.isLoading = false
            }
        } catch {
            await MainActor.run {
                self.isLoading = false
            }
            #if DEBUG
            // 调试环境下可以记录错误，但不使用print
            #endif
        }
    }
    
    /// 🚀 iOS 15+图片预处理优化（支持自定义尺寸）
    private func prepareImageForDisplay(_ image: UIImage, targetSize: CGSize) async -> UIImage {
        return await withCheckedContinuation { continuation in
            // 🚀 智能尺寸选择：如果目标尺寸很小，使用prepareThumbnail
            if targetSize.width <= 200 && targetSize.height <= 200 {
                image.prepareThumbnail(of: targetSize) { thumbnailImage in
                    if let thumbnail = thumbnailImage {
                        continuation.resume(returning: thumbnail)
                    } else {
                        // 降级到prepareForDisplay
                        image.prepareForDisplay { preparedImage in
                            continuation.resume(returning: preparedImage ?? image)
                        }
                    }
                }
            } else {
                // 🚀 对于较大尺寸，使用prepareForDisplay保持质量
                image.prepareForDisplay { preparedImage in
                    if let prepared = preparedImage {
                        continuation.resume(returning: prepared)
                    } else {
                        // 最后降级到原图
                        continuation.resume(returning: image)
                    }
                }
            }
        }
    }
    
    /// 加载原始图片
    private func loadOriginalImage(from url: URL) async throws -> UIImage {
        if url.isFileURL {
            // 本地文件
            guard let image = UIImage(contentsOfFile: url.path) else {
                throw EAImageLoadingError.invalidLocalFile
            }
            return image
        } else {
            // 网络图片
            let (data, _) = try await URLSession.shared.data(from: url)
            guard let image = UIImage(data: data) else {
                throw EAImageLoadingError.invalidImageData
            }
            return image
        }
    }
    
    /// 取消所有加载
    private func cancelAllLoading() {
        loadingTask?.cancel()
        progressiveTask?.cancel()
        loadingTask = nil
        progressiveTask = nil
        isLoading = false
    }
    
    /// 处理内存警告
    private func handleMemoryWarning() {
        if !isLoading {
            // 🚀 智能内存管理：保留缩略图，清理高质量图片
            if standardLoaded && thumbnailLoaded {
                // 如果有标准图，降级到缩略图
                if let url = url, let cacheService = imageCacheService {
                    Task {
                        if let thumbnail = await cacheService.getCachedImage(for: url, tier: .thumbnail) {
                            await MainActor.run {
                                self.currentImage = thumbnail
                                self.standardLoaded = false
                            }
                        }
                    }
                }
            } else if !isLoading {
                // 如果没有在加载，清理图片
                currentImage = nil
                thumbnailLoaded = false
                standardLoaded = false
            }
        }
    }
}

// MARK: - Error Types

enum EAImageLoadingError: Error {
    case invalidLocalFile
    case invalidImageData
    case networkError
    case cacheServiceUnavailable
    
    var localizedDescription: String {
        switch self {
        case .invalidLocalFile:
            return "无法加载本地图片文件"
        case .invalidImageData:
            return "图片数据格式无效"
        case .networkError:
            return "网络加载失败"
        case .cacheServiceUnavailable:
            return "图片缓存服务不可用"
        }
    }
}

// MARK: - 🚀 便捷初始化器

extension EAHighPerformanceAsyncImage {
    
    /// 社区帖子缩略图专用初始化器
    static func forCommunityThumbnail(
        url: URL?,
        @ViewBuilder content: @escaping (Image) -> Content,
        @ViewBuilder placeholder: @escaping () -> Placeholder
    ) -> EAHighPerformanceAsyncImage {
        return EAHighPerformanceAsyncImage(
            url: url,
            targetSize: CGSize(width: 80, height: 80),
            tier: .thumbnail,
            enableProgressive: false,
            enablePreload: true,
            content: content,
            placeholder: placeholder
        )
    }
    
    /// 社区帖子标准图专用初始化器（支持渐进式加载）
    static func forCommunityStandard(
        url: URL?,
        targetSize: CGSize = CGSize(width: 300, height: 300),
        @ViewBuilder content: @escaping (Image) -> Content,
        @ViewBuilder placeholder: @escaping () -> Placeholder
    ) -> EAHighPerformanceAsyncImage {
        return EAHighPerformanceAsyncImage(
            url: url,
            targetSize: targetSize,
            tier: .standard,
            enableProgressive: true,
            enablePreload: false,
            content: content,
            placeholder: placeholder
        )
    }
    
    /// 图片查看器原图专用初始化器
    static func forImageViewer(
        url: URL?,
        targetSize: CGSize,
        @ViewBuilder content: @escaping (Image) -> Content,
        @ViewBuilder placeholder: @escaping () -> Placeholder
    ) -> EAHighPerformanceAsyncImage {
        return EAHighPerformanceAsyncImage(
            url: url,
            targetSize: targetSize,
            tier: .original,
            enableProgressive: true,
            enablePreload: false,
            content: content,
            placeholder: placeholder
        )
    }
}

// MARK: - 🚀 性能监控扩展

extension EAHighPerformanceAsyncImage {
    
    /// 获取当前加载状态
    var loadingState: LoadingState {
        if isLoading {
            if thumbnailLoaded && !standardLoaded {
                return .loadingStandard
            } else {
                return .loading
            }
        } else if currentImage != nil {
            return .loaded
        } else {
            return .idle
        }
    }
    
    enum LoadingState {
        case idle
        case loading
        case loadingStandard
        case loaded
    }
} 