import SwiftUI

// MARK: - 时间选择器组件
struct EATimeSelector: View {
    @Binding var selectedTime: Date
    @Binding var isReminderEnabled: Bool
    @Binding var isPushNotificationEnabled: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            // 执行时间标题和开关
            HStack {
                Text("执行时间")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                
                Spacer()
                
                Toggle("", isOn: $isReminderEnabled)
                    .toggleStyle(SwitchToggleStyle(tint: Color.hexColor("40E0D0")))
            }
            
            // 时间选择器卡片（提醒开启时显示）
            if isReminderEnabled {
                timePickerCard
                    .transition(.opacity.combined(with: .scale(scale: 0.95)))
                
                // 推送通知开关
                pushNotificationToggle
                    .transition(.opacity.combined(with: .scale(scale: 0.95)))
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
        .animation(.easeInOut(duration: 0.3), value: isReminderEnabled)
    }
    
    // MARK: - 时间选择器卡片
    private var timePickerCard: some View {
        VStack(spacing: 16) {
            // 时间显示
            HStack {
                Image(systemName: "clock")
                    .foregroundColor(Color.hexColor("40E0D0"))
                    .font(.title2)
                
                Text("每天")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color.white.opacity(0.8))
                
                Text(timeFormatter.string(from: selectedTime))
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            // 时间选择器
            DatePicker(
                "选择时间",
                selection: $selectedTime,
                displayedComponents: .hourAndMinute
            )
            .datePickerStyle(.wheel)
            .labelsHidden()
            .environment(\.locale, Locale(identifier: "zh_CN"))
            .colorScheme(.dark) // 确保时间选择器在深色背景下可见
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.black.opacity(0.3))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.hexColor("40E0D0").opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    // MARK: - 推送通知开关
    private var pushNotificationToggle: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("推送通知")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white)
                    
                    Text(isPushNotificationEnabled ? "系统推送提醒" : "应用内提醒")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(Color.white.opacity(0.7))
                }
                
                Spacer()
                
                Toggle("", isOn: $isPushNotificationEnabled)
                    .toggleStyle(SwitchToggleStyle(tint: Color.hexColor("FFD700")))
            }
            
            // 说明文字
            Text(isPushNotificationEnabled ? 
                 "开启后将通过系统通知提醒您完成计划" : 
                 "关闭时将在应用内的今日页面和灵境对话中提醒您")
                .font(.system(size: 11, weight: .regular))
                .foregroundColor(Color.white.opacity(0.6))
                .lineLimit(nil)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color.black.opacity(0.2))
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
    
    // MARK: - 时间格式化器
    private var timeFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }
}

// MARK: - 预览
#Preview {
    struct PreviewWrapper: View {
        @State private var selectedTime = Date()
        @State private var isReminderEnabled = true
        @State private var isPushNotificationEnabled = false
        
        var body: some View {
            VStack(spacing: 20) {
                EATimeSelector(
                    selectedTime: $selectedTime,
                    isReminderEnabled: $isReminderEnabled,
                    isPushNotificationEnabled: $isPushNotificationEnabled
                )
                
                Spacer()
            }
            .padding()
        }
    }
    
    return PreviewWrapper()
} 