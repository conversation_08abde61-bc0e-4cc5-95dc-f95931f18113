import SwiftUI

/// 消息操作菜单组件 - iOS风格长按菜单
/// 符合iOS 13+ contextMenu设计规范，提供删除和撤销功能
struct EAMessageActionMenu: View {
    let message: EAFriendMessage
    let isFromCurrentUser: Bool
    let onDelete: () -> Void
    let onRevoke: () -> Void
    
    var body: some View {
        Group {
            // 删除选项（所有消息都可删除）
            But<PERSON>(role: .destructive) {
                // 添加触觉反馈
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()
                onDelete()
            } label: {
                Label("删除", systemImage: "trash")
            }
            
            // 撤销选项（仅自己的消息且在时限内）
            if isFromCurrentUser && message.canBeRevoked() {
                Button(role: .destructive) {
                    // 添加触觉反馈
                    let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
                    impactFeedback.impactOccurred()
                    onRevoke()
                } label: {
                    Label("撤回", systemImage: "arrow.uturn.backward")
                }
            }
        }
    }
}

/// 撤销消息占位符组件
/// 显示撤销通知的特殊样式
struct EARevokedMessagePlaceholder: View {
    let message: EAFriendMessage
    
    var body: some View {
        HStack {
            Image(systemName: "arrow.uturn.backward.circle")
                .foregroundColor(.secondary)
                .font(.system(size: 14))
            
            Text(message.content)
                .font(.system(size: 14))
                .foregroundColor(.secondary)
                .italic()
            
            Spacer()
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.secondary.opacity(0.1))
        )
    }
}

#if DEBUG
#Preview {
    VStack(spacing: 20) {
        // 预览撤销消息占位符
        EARevokedMessagePlaceholder(
            message: EAFriendMessage(content: "你撤回了一条消息")
        )
        .padding()
        
        // 预览长按菜单（需要在实际使用中通过contextMenu显示）
        Text("长按消息可显示操作菜单")
            .padding()
            .background(Color.blue.opacity(0.1))
            .cornerRadius(12)
            .contextMenu {
                EAMessageActionMenu(
                    message: EAFriendMessage(content: "测试消息"),
                    isFromCurrentUser: true,
                    onDelete: {},
                    onRevoke: {}
                )
            }
    }
    .padding()
}
#endif 