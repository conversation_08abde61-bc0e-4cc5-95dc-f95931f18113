import SwiftUI

/// 增强的频率选择组件
/// 支持每周、全天、每月等多种打卡模式，提供灵活的日期选择
struct EAFrequencySelector: View {
    @Binding var selectedFrequencyType: FrequencyType
    @Binding var selectedWeekdays: Set<Int> // 1-7 代表周一到周日
    @Binding var dailyTarget: Int // 每日目标次数
    @Binding var monthlyTarget: Int // 每月目标次数
    @Binding var selectedMonthlyDates: Set<Int> // 每月自定义日期
    @Binding var monthlyMode: MonthlyMode // 每月执行模式
    @Binding var selectedOneTimeDates: [String]  // ✅ 新增：一次性计划日期绑定

    // ✅ 关键修复：将 viewModel 标记为 @ObservedObject，使视图随 @Published 状态变化自动刷新
    @ObservedObject var viewModel: EAHabitCreationViewModel
    
    // 预设的每日目标选项
    private let dailyOptions = [1, 2, 3, 5, 8, 10]
    // 预设的每月目标选项
    private let monthlyOptions = [5, 10, 15, 20, 25, 30]
    // 星期名称
    private let weekdayNames = ["一", "二", "三", "四", "五", "六", "日"]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            // 标题
            Text("打卡频率")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
            
            // 频率类型选择器
            frequencyTypeSelector
            
            // 根据选择的频率类型显示不同的配置选项
            switch selectedFrequencyType {
            case .weekly:
                weeklySelector
            case .daily:
                dailySelector
            case .monthly:
                monthlySelector
            case .oneTime:  // ✅ 新增分支
                oneTimeSelector
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
    
    // MARK: - 频率类型选择器
    private var frequencyTypeSelector: some View {
        HStack(spacing: 12) {
            ForEach(FrequencyType.allCases, id: \.self) { type in
                Button(action: {
                    // 先立即更新 UI 选择状态
                    withAnimation(.easeInOut(duration: 0.3)) {
                        selectedFrequencyType = type
                    }
                    // 异步执行频率切换后的重置逻辑，确保不会因为视图重建而被取消
                    Task { @MainActor in
                        await resetSettingsForType(type)
                    }
                }) {
                    Text(type.description)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(selectedFrequencyType == type ? .white : .white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(selectedFrequencyType == type ? 
                                      Color.hexColor("40E0D0") : 
                                      Color.white.opacity(0.05))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 20)
                                        .stroke(
                                            selectedFrequencyType == type ? 
                                            Color.hexColor("40E0D0") : 
                                            Color.white.opacity(0.1),
                                            lineWidth: 1
                                        )
                                )
                        )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
    
    // MARK: - 每周选择器
    private var weeklySelector: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("选择执行日期")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.white.opacity(0.8))
                
                Spacer()
                
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        if selectedWeekdays.count == 7 {
                            selectedWeekdays.removeAll()
                        } else {
                            selectedWeekdays = Set(1...7)
                        }
                    }
                }) {
                    Text(selectedWeekdays.count == 7 ? "取消全选" : "全选")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(Color.hexColor("40E0D0"))
                }
                .buttonStyle(PlainButtonStyle())
            }
            
            HStack(spacing: 8) {
                ForEach(1...7, id: \.self) { weekday in
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            if selectedWeekdays.contains(weekday) {
                                selectedWeekdays.remove(weekday)
                            } else {
                                selectedWeekdays.insert(weekday)
                            }
                        }
                    }) {
                        VStack(spacing: 4) {
                            Text("周\(weekdayNames[weekday - 1])")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(selectedWeekdays.contains(weekday) ? .white : .white)
                            
                            Circle()
                                .fill(selectedWeekdays.contains(weekday) ? 
                                      Color.hexColor("40E0D0") : 
                                      Color.white.opacity(0.3))
                                .frame(width: 8, height: 8)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(selectedWeekdays.contains(weekday) ? 
                                      Color.hexColor("40E0D0").opacity(0.15) : 
                                      Color.white.opacity(0.05))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(
                                            selectedWeekdays.contains(weekday) ? 
                                            Color.hexColor("40E0D0").opacity(0.5) : 
                                            Color.white.opacity(0.1),
                                            lineWidth: 1
                                        )
                                )
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            
            if !selectedWeekdays.isEmpty {
                Text("已选择 \(selectedWeekdays.count) 天")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(Color.white.opacity(0.6))
            }
        }
    }
    
    // MARK: - 每日选择器
    private var dailySelector: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("每日目标次数")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(Color.white.opacity(0.8))
            
            // 🔑 优化：显示选择提示
            if dailyTarget == 0 {
                Text("请选择每日目标次数")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(Color.white.opacity(0.6))
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
                ForEach(dailyOptions, id: \.self) { option in
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            dailyTarget = option
                        }
                    }) {
                        VStack(spacing: 4) {
                            Text("\(option)")
                                .font(.system(size: 20, weight: .bold))
                                .foregroundColor(dailyTarget == option ? .white : .white)
                            
                            Text("次/日")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(dailyTarget == option ? .white : Color.white.opacity(0.7))
                        }
                        .frame(height: 60)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(dailyTarget == option ? 
                                      Color.hexColor("40E0D0") : 
                                      Color.white.opacity(0.05))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(
                                            dailyTarget == option ? 
                                            Color.hexColor("40E0D0") : 
                                            Color.white.opacity(0.1),
                                            lineWidth: 1
                                        )
                                )
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            
            // 🔑 优化：显示已选择状态
            if dailyTarget > 0 {
                Text("已选择每日 \(dailyTarget) 次")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(Color.hexColor("40E0D0"))
            }
        }
    }
    
    // MARK: - 每月选择器（重新设计，明确区分两种模式）
    private var monthlySelector: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 每月模式选择
            VStack(alignment: .leading, spacing: 12) {
                Text("执行方式")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.white.opacity(0.8))
                
                HStack(spacing: 12) {
                    // 按次数模式
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            monthlyMode = .target
                            // 切换到按次数模式时，清空自定义日期
                            selectedMonthlyDates.removeAll()
                        }
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: monthlyMode == .target ? "checkmark.circle.fill" : "circle")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(monthlyMode == .target ? Color.hexColor("40E0D0") : Color.white.opacity(0.6))
                            
                            Text("按次数目标")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(monthlyMode == .target ? .white : Color.white.opacity(0.7))
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(monthlyMode == .target ? 
                                      Color.hexColor("40E0D0").opacity(0.15) : 
                                      Color.white.opacity(0.05))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(
                                            monthlyMode == .target ? 
                                            Color.hexColor("40E0D0").opacity(0.5) : 
                                            Color.white.opacity(0.1),
                                            lineWidth: 1
                                        )
                                )
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    // 按日期模式
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            monthlyMode = .dates
                            // 🔑 优化：不自动设置默认日期，让用户自己选择
                        }
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: monthlyMode == .dates ? "checkmark.circle.fill" : "circle")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(monthlyMode == .dates ? Color.hexColor("40E0D0") : Color.white.opacity(0.6))
                            
                            Text("按指定日期")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(monthlyMode == .dates ? .white : Color.white.opacity(0.7))
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(monthlyMode == .dates ? 
                                      Color.hexColor("40E0D0").opacity(0.15) : 
                                      Color.white.opacity(0.05))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(
                                            monthlyMode == .dates ? 
                                            Color.hexColor("40E0D0").opacity(0.5) : 
                                            Color.white.opacity(0.1),
                                            lineWidth: 1
                                        )
                                )
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            
            // 根据选择的模式显示对应的配置
            if monthlyMode == .target {
                monthlyTargetSelector
            } else {
                monthlyDatesSelector
            }
        }
    }
    
    // MARK: - 每月目标次数选择器
    private var monthlyTargetSelector: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("每月目标次数")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(Color.white.opacity(0.8))
            
            Text("可以在任意时间完成，达到目标次数即可")
                .font(.system(size: 12, weight: .regular))
                .foregroundColor(Color.white.opacity(0.6))
            
            // 🔑 优化：显示选择提示
            if monthlyTarget == 0 {
                Text("请选择每月目标次数")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(Color.white.opacity(0.6))
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
                ForEach(monthlyOptions, id: \.self) { option in
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            monthlyTarget = option
                        }
                    }) {
                        VStack(spacing: 4) {
                            Text("\(option)")
                                .font(.system(size: 18, weight: .bold))
                                .foregroundColor(monthlyTarget == option ? .white : .white)
                            
                            Text("次/月")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(monthlyTarget == option ? .white : Color.white.opacity(0.7))
                        }
                        .frame(height: 50)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(monthlyTarget == option ? 
                                      Color.hexColor("40E0D0") : 
                                      Color.white.opacity(0.05))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(
                                            monthlyTarget == option ? 
                                            Color.hexColor("40E0D0") : 
                                            Color.white.opacity(0.1),
                                            lineWidth: 1
                                        )
                                )
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            
            // 🔑 优化：显示已选择状态
            if monthlyTarget > 0 {
                Text("已选择每月 \(monthlyTarget) 次")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(Color.hexColor("40E0D0"))
            }
        }
    }
    
    // MARK: - 每月自定义日期选择器
    private var monthlyDatesSelector: some View {
        EACalendarDateSelector(
            selectedDates: $selectedMonthlyDates,
            title: "选择执行日期",
            subtitle: "只在选定的日期提醒和执行"
        )
    }
    
    // MARK: - 一次性日期选择器（✅ 重新设计，简洁美观）
    private var oneTimeSelector: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 简化的说明文本
            Text("选择所有需要执行的日期，完成后计划自动结束")
                .font(.system(size: 12, weight: .regular))
                .foregroundColor(Color.white.opacity(0.6))
            
            // 🔥 决定性修复：使用实时状态监听，确保UI能响应ViewModel变化
            VStack(spacing: 8) {
                // 月份导航
                monthNavigationBar

                // 日历内容 - 直接监听ViewModel状态
                calendarContent
            }
            
            // 选择状态显示
            if !selectedOneTimeDates.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    Text("已选择 \(selectedOneTimeDates.count) 个日期")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(Color.hexColor("40E0D0"))
                    
                    // ✅ iPad优化：简化日期显示，减少复杂计算
                    if selectedOneTimeDates.count > 0 {
                        HStack {
                            Text("包含 \(selectedOneTimeDates.count) 个日期")
                                .font(.system(size: 11, weight: .regular))
                                .foregroundColor(Color.white.opacity(0.5))

                            Spacer()

                            // 日期上限提醒 - 简化版本
                            if selectedOneTimeDates.count >= 15 {
                                Text("最多20个")
                                    .font(.system(size: 10, weight: .medium))
                                    .foregroundColor(Color.orange.opacity(0.8))
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 2)
                                    .background(
                                        RoundedRectangle(cornerRadius: 4)
                                            .fill(Color.orange.opacity(0.1))
                                    )
                            }
                        }
                    }
                }
            }
        }
    }

    // MARK: - 内联日历组件
    /// 🔥 决定性修复：内联实现日历组件，直接监听ViewModel状态

    // 月份导航栏
    private var monthNavigationBar: some View {
        HStack {
            // 上一月按钮
            Button(action: {
                let previousMonth = Calendar.current.date(byAdding: .month, value: -1, to: viewModel.currentMonth) ?? viewModel.currentMonth
                Task {
                    await viewModel.updateCurrentMonth(previousMonth)
                }
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color.hexColor("40E0D0"))
                    .frame(width: 28, height: 28)
                    .background(
                        Circle()
                            .fill(Color.white.opacity(0.05))
                            .overlay(
                                Circle()
                                    .stroke(Color.white.opacity(0.1), lineWidth: 1)
                            )
                    )
            }

            Spacer()

            // 当前月份显示
            VStack(spacing: 2) {
                Text(monthFormatter.string(from: viewModel.currentMonth))
                    .font(.system(size: 15, weight: .semibold))
                    .foregroundColor(.white)

                Text("可跨月选择")
                    .font(.system(size: 10, weight: .regular))
                    .foregroundColor(Color.white.opacity(0.5))
            }

            Spacer()

            // 下一月按钮
            Button(action: {
                let nextMonth = Calendar.current.date(byAdding: .month, value: 1, to: viewModel.currentMonth) ?? viewModel.currentMonth
                Task {
                    await viewModel.updateCurrentMonth(nextMonth)
                }
            }) {
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color.hexColor("40E0D0"))
                    .frame(width: 28, height: 28)
                    .background(
                        Circle()
                            .fill(Color.white.opacity(0.05))
                            .overlay(
                                Circle()
                                    .stroke(Color.white.opacity(0.1), lineWidth: 1)
                            )
                    )
            }
        }
    }

    // 日历内容
    private var calendarContent: some View {
        VStack(spacing: 8) {
            // MARK: 日历区域（固定高度，避免窗口高度变化）
            ZStack {
                VStack(spacing: 6) {
                    weekdayHeaders
                    datesGrid
                }
                // 加载覆盖层：半透明背景 + 转圈
                if viewModel.isCalendarLoading {
                    Color.black.opacity(0.3)
                        .cornerRadius(12)
                    loadingStateView
                }
            }
            // 固定高度 = 星期头(24)+垂直内边距(4+4)+日期行(32*6)+行间距(4*5) = 24+8+192+20 = 244
            .frame(height: 244)
            .animation(.easeInOut(duration: 0.25), value: viewModel.isCalendarLoading)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.03))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(0.08), lineWidth: 1)
                )
        )
    }

    // 周期标题行
    private var weekdayHeaders: some View {
        // 使用与日期网格一致的列定义，确保宽度对齐
        let gridColumns = Array(repeating: GridItem(.flexible(), spacing: 4), count: 7)

        return LazyVGrid(columns: gridColumns, spacing: 4) {
            ForEach(["一", "二", "三", "四", "五", "六", "日"], id: \.self) { weekday in
                Text(weekday)
                    .font(.system(size: 11, weight: .medium))
                    .foregroundColor(Color.hexColor("40E0D0"))
                    .frame(maxWidth: .infinity, minHeight: 24)
            }
        }
        .padding(.vertical, 4)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(Color.hexColor("40E0D0").opacity(0.1))
        )
    }

    // 加载状态视图
    private var loadingStateView: some View {
        VStack(spacing: 12) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(0.8)

            Text("加载日历...")
                .font(.system(size: 12, weight: .regular))
                .foregroundColor(.white.opacity(0.6))
        }
        .frame(minHeight: 120)
        .frame(maxWidth: .infinity)
    }

    // 日期网格
    private var datesGrid: some View {
        let gridColumns = Array(repeating: GridItem(.flexible(), spacing: 4), count: 7)

        return LazyVGrid(columns: gridColumns, spacing: 4) {
            ForEach(viewModel.calendarDays, id: \.id) { day in
                dayCell(day: day)
            }
        }
        .padding(.vertical, 8)
    }

    // 日期单元格
    private func dayCell(day: CalendarDayCreation) -> some View {
        Button(action: {
            if day.isSelectable && !day.dayNumber.isEmpty {
                Task {
                    await viewModel.toggleDateSelection(day.date)
                }
            }
        }) {
            Text(day.dayNumber.isEmpty ? " " : day.dayNumber)
                .font(.system(size: 13, weight: .medium))
                .foregroundColor(cellTextColor(for: day))
                .frame(width: 32, height: 32)
                .background(cellBackground(for: day))
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(!day.isSelectable || day.dayNumber.isEmpty)
    }

    // 单元格文字颜色
    private func cellTextColor(for day: CalendarDayCreation) -> Color {
        if day.dayNumber.isEmpty || !day.isSelectable {
            return .clear
        }
        return day.isSelected ? .white : .white.opacity(0.8)
    }

    // 单元格背景
    private func cellBackground(for day: CalendarDayCreation) -> some View {
        RoundedRectangle(cornerRadius: 6)
            .fill(day.isSelected ? Color.hexColor("40E0D0").opacity(0.3) : Color.clear)
            .overlay(
                RoundedRectangle(cornerRadius: 6)
                    .stroke(
                        day.isSelected ? Color.hexColor("40E0D0").opacity(0.6) : Color.clear,
                        lineWidth: 1
                    )
            )
    }

    // 月份格式化器
    private var monthFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年M月"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }

}

// MARK: - 辅助方法
extension EAFrequencySelector {
    
    // 切换星期选择
    private func toggleWeekday(_ weekday: Int) {
        if selectedWeekdays.contains(weekday) {
            selectedWeekdays.remove(weekday)
        } else {
            selectedWeekdays.insert(weekday)
        }
    }
    
    // 全选/取消全选星期
    private func toggleAllWeekdays() {
        if selectedWeekdays.count == 7 {
            selectedWeekdays.removeAll()
        } else {
            selectedWeekdays = Set(1...7)
        }
    }
    
    // 🔑 优化：切换频率类型时重置设置（异步），确保一次性日历能够正确初始化
    @MainActor
    private func resetSettingsForType(_ type: FrequencyType) async {
        switch type {
        case .weekly:
            // 🔑 优化：不自动设置默认选择，保持空状态让用户自己选择
            break
        case .daily:
            // 🔑 优化：不自动设置默认选择，保持空状态让用户自己选择
            break
        case .monthly:
            // 🔑 优化：不自动设置默认选择，保持空状态让用户自己选择
            break
        case .oneTime:
            // ✅ 关键修复：移除手动状态设置，让ensureCalendarDataInitialized完全管理状态
            // 直接调用初始化方法，避免状态冲突
            await viewModel.ensureCalendarDataInitialized()

            #if DEBUG
            print("🔧 [频率切换] 一次性频率日历初始化完成，日历数据数量: \(viewModel.calendarDays.count)")
            #endif
        }
    }
}

// MARK: - 频率类型枚举
enum FrequencyType: String, CaseIterable {
    case weekly = "weekly"
    case daily = "daily"
    case monthly = "monthly"
    case oneTime = "oneTime"  // ✅ 新增一次性类型
    
    var description: String {
        switch self {
        case .weekly:
            return "每周"
        case .daily:
            return "全天"
        case .monthly:
            return "每月"
        case .oneTime:
            return "一次性"  // ✅ 新增
        }
    }
}

// MARK: - 每月模式枚举
enum MonthlyMode: String, CaseIterable {
    case target = "target" // 按次数目标
    case dates = "dates"   // 按指定日期
    
    var description: String {
        switch self {
        case .target:
            return "按次数目标"
        case .dates:
            return "按指定日期"
        }
    }
}

// MARK: - 预览
#Preview {
    ZStack {
        Color.black.ignoresSafeArea()

        let sessionManager = EASessionManager()
        let repositoryContainer = EARepositoryContainerImpl.preview()
        let viewModel = EAHabitCreationViewModel(
            sessionManager: sessionManager,
            repositoryContainer: repositoryContainer,
            editingHabit: nil
        )

        EAFrequencySelector(
            selectedFrequencyType: .constant(.weekly),
            selectedWeekdays: .constant(Set([1, 3, 5])),
            dailyTarget: .constant(1),
            monthlyTarget: .constant(15),
            selectedMonthlyDates: .constant(Set([1, 15])),
            monthlyMode: .constant(.target),
            selectedOneTimeDates: .constant([]),
            viewModel: viewModel
        )
        .padding()
    }
}