//
//  EAUniverseGuideView.swift
//  Evolve
//
//  Created by AI Assistant on 2025-06-06.
//  Phase 2 Day 5: AI宇宙向导集成 - 宇宙向导UI组件
//

import SwiftUI
import SwiftData

/// 宇宙向导主视图
/// 提供与AI宇宙向导的完整对话体验和智能引导功能
/// 遵循开发规范文档的"数字宇宙视觉元素"和"交互设计原则"
struct EAUniverseGuideView: View {
    
    // MARK: - 环境和状态
    
    @Environment(\.repositoryContainer) private var repositoryContainer
    @Environment(\.communityAIDataBridge) private var communityAIDataBridge
    @Environment(\.presentationMode) private var presentationMode
    @StateObject private var guideService: EAUniverseGuideService
    
    // MARK: - 用户输入
    
    @State private var messageText: String = ""
    @State private var isInputFocused: Bool = false
    
    // MARK: - 视觉效果
    
    @State private var showParticleEffect: Bool = false
    @State private var guideAppearAnimation: Bool = false
    
    // MARK: - 配置
    
    let user: EAUser
    let triggerReason: EAGuideTriggerReason
    
    // MARK: - 初始化
    
    /// ✅ 修复：通过依赖注入获取AI数据桥接，而不是直接创建
    init(user: EAUser, triggerReason: EAGuideTriggerReason = .userInitiated) {
        self.user = user
        self.triggerReason = triggerReason
        
        // ✅ 修复：延迟初始化guideService，在onAppear中设置依赖
        self._guideService = StateObject(wrappedValue: EAUniverseGuideService())
    }
    
    /// @deprecated 使用接收完整用户对象的初始化方法替代
    @available(*, deprecated, message: "Use init(user:triggerReason:) instead")
    init(userId: UUID, triggerReason: EAGuideTriggerReason = .userInitiated) {
        fatalError("Deprecated initializer used. Please use init(user:triggerReason:) instead.")
    }
    
    // MARK: - 主视图
    
    var body: some View {
        NavigationView {
            ZStack {
                // 宇宙背景
                universeBacground
                
                VStack(spacing: 0) {
                    // 向导头部
                    guideHeader
                    
                    // 对话区域
                    conversationArea
                    
                    // 输入区域
                    inputArea
                }
            }
            .navigationBarHidden(true)
            .onAppear {
                setupGuideView()
            }
        }
        .navigationViewStyle(StackNavigationViewStyle())
    }
    
    // MARK: - 宇宙背景
    
    private var universeBacground: some View {
        ZStack {
            // 深邃宇宙背景
            LinearGradient(
                colors: [
                    Color(red: 0.04, green: 0.05, blue: 0.15), // 深宇宙蓝
                    Color(red: 0.08, green: 0.12, blue: 0.25), // 星云紫
                    Color(red: 0.02, green: 0.08, blue: 0.20)  // 深邃蓝
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            // 星光粒子效果
            if showParticleEffect {
                ForEach(0..<50, id: \.self) { _ in
                    Circle()
                        .fill(Color.white.opacity(0.3))
                        .frame(width: CGFloat.random(in: 1...3))
                        .position(
                            x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                            y: CGFloat.random(in: 0...UIScreen.main.bounds.height)
                        )
                        .animation(
                            Animation.easeInOut(duration: Double.random(in: 2...4))
                                .repeatForever(autoreverses: true),
                            value: showParticleEffect
                        )
                }
            }
        }
    }
    
    // MARK: - 向导头部
    
    private var guideHeader: some View {
        VStack(spacing: 12) {
            // 返回按钮
            HStack {
                Button(action: { presentationMode.wrappedValue.dismiss() }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.white.opacity(0.7))
                }
                
                Spacer()
                
                // 向导个性选择
                Menu {
                    ForEach(EAGuidePersonality.allCases, id: \.self) { personality in
                        Button(personality.displayName) {
                            guideService.guidePersonality = personality
                        }
                    }
                } label: {
                    Image(systemName: "slider.horizontal.3")
                        .font(.title2)
                        .foregroundColor(.white.opacity(0.7))
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
            
            // 向导形象
            VStack(spacing: 8) {
                ZStack {
                    // 向导光环
                    Circle()
                        .stroke(
                            guideService.guidePersonality.primaryColor.opacity(0.4),
                            lineWidth: 3
                        )
                        .frame(width: 100, height: 100)
                        .scaleEffect(guideAppearAnimation ? 1.1 : 1.0)
                        .animation(
                            Animation.easeInOut(duration: 2.0)
                                .repeatForever(autoreverses: true),
                            value: guideAppearAnimation
                        )
                    
                    // 向导图标
                    Image(systemName: guideService.guidePersonality.icon)
                        .font(.system(size: 40))
                        .foregroundColor(guideService.guidePersonality.primaryColor)
                        .scaleEffect(guideAppearAnimation ? 1.0 : 0.8)
                }
                
                // 向导名称
                VStack(spacing: 4) {
                    Text(guideService.guidePersonality.displayName)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text("数字宇宙向导")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                }
            }
        }
        .padding(.bottom, 20)
    }
    
    // MARK: - 对话区域
    
    private var conversationArea: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 16) {
                    ForEach(guideService.conversationHistory) { message in
                        EAGuideMessageBubble(message: message)
                            .id(message.id)
                    }
                    
                    // 加载指示器
                    if guideService.isProcessingResponse {
                        EAGuideTypingIndicator()
                    }
                }
                .padding(.horizontal, 16)
                .padding(.bottom, 20)
            }
            .onChange(of: guideService.conversationHistory.count) {
                // 自动滚动到最新消息
                if let lastMessage = guideService.conversationHistory.last {
                    withAnimation(.easeOut(duration: 0.5)) {
                        proxy.scrollTo(lastMessage.id, anchor: .bottom)
                    }
                }
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.black.opacity(0.2))
                .blur(radius: 10)
        )
        .padding(.horizontal, 16)
    }
    
    // MARK: - 输入区域
    
    private var inputArea: some View {
        VStack(spacing: 16) {
            // 快捷操作按钮
            if !isInputFocused {
                quickActionButtons
                    .transition(.opacity.combined(with: .move(edge: .bottom)))
            }
            
            // 文本输入框
            HStack(spacing: 12) {
                // 输入框
                TextField("与宇宙向导对话...", text: $messageText, axis: .vertical)
                    .textFieldStyle(PlainTextFieldStyle())
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 25)
                            .fill(Color.white.opacity(0.1))
                            .overlay(
                                RoundedRectangle(cornerRadius: 25)
                                    .stroke(Color.white.opacity(0.3), lineWidth: 1)
                            )
                    )
                    .foregroundColor(.white)
                    .lineLimit(1...4)
                    .onTapGesture {
                        isInputFocused = true
                    }
                
                // 发送按钮
                Button(action: sendMessage) {
                    Image(systemName: "paperplane.fill")
                        .font(.title2)
                        .foregroundColor(.white)
                        .frame(width: 44, height: 44)
                        .background(
                            Circle()
                                .fill(guideService.guidePersonality.primaryColor)
                        )
                }
                .disabled(messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                .opacity(messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? 0.5 : 1.0)
            }
            .padding(.horizontal, 16)
        }
        .padding(.bottom, 20)
        .animation(.easeInOut(duration: 0.3), value: isInputFocused)
    }
    
    // MARK: - 快捷操作按钮
    
    private var quickActionButtons: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                quickActionButton("需要帮助", icon: "questionmark.circle") {
                    messageText = "我需要一些帮助和指导"
                    sendMessage()
                }
                
                quickActionButton("分享成就", icon: "star.circle") {
                    messageText = "我想分享我的习惯成就"
                    sendMessage()
                }
                
                quickActionButton("遇到困难", icon: "exclamationmark.triangle") {
                    messageText = "我在习惯养成中遇到了困难"
                    sendMessage()
                }
                
                quickActionButton("探索功能", icon: "sparkles") {
                    messageText = "我想了解更多有趣的功能"
                    sendMessage()
                }
            }
            .padding(.horizontal, 16)
        }
    }
    
    private func quickActionButton(_ title: String, icon: String, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.caption)
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.white.opacity(0.3), lineWidth: 1)
                    )
            )
            .foregroundColor(.white)
        }
    }
    
    // MARK: - 私有方法
    
    private func setupGuideView() {
        // ✅ 修复：在onAppear中设置AI数据桥接依赖
        if let aiDataBridge = communityAIDataBridge,
           let repoContainer = repositoryContainer {
            guideService.setupDependencies(
                repositoryContainer: repoContainer,
                aiDataBridge: aiDataBridge
            )
        }
        
        // 启动向导对话
        Task {
            await guideService.startConversation(userId: user.id, triggerReason: triggerReason)
        }
        
        // 启动视觉效果
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            withAnimation(.easeIn(duration: 1.0)) {
                showParticleEffect = true
                guideAppearAnimation = true
            }
        }
    }
    
    private func sendMessage() {
        let message = messageText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !message.isEmpty else { return }
        
        messageText = ""
        isInputFocused = false
        
        // 发送消息给向导
        Task {
            await guideService.sendMessage(message, userId: user.id)
        }
        
        // 隐藏键盘
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}

// MARK: - 消息气泡组件

struct EAGuideMessageBubble: View {
    let message: EAGuideMessage
    
    var body: some View {
        HStack {
            if message.role == .user {
                Spacer()
            }
            
            VStack(alignment: message.role == .user ? .trailing : .leading, spacing: 4) {
                // 消息内容
                Text(message.content)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 18)
                            .fill(messageBackgroundColor)
                    )
                    .foregroundColor(messageTextColor)
                    .font(.body)
                
                // 时间戳
                Text(formatTime(message.timestamp))
                    .font(.caption2)
                    .foregroundColor(.white.opacity(0.5))
                    .padding(.horizontal, 4)
            }
            .frame(maxWidth: UIScreen.main.bounds.width * 0.7, alignment: message.role == .user ? .trailing : .leading)
            
            if message.role != .user {
                Spacer()
            }
        }
    }
    
    private var messageBackgroundColor: Color {
        switch message.role {
        case .user:
            return Color.blue.opacity(0.8)
        case .guide:
            return message.guidePersonality?.primaryColor.opacity(0.2) ?? Color.purple.opacity(0.2)
        case .system:
            return Color.gray.opacity(0.3)
        }
    }
    
    private var messageTextColor: Color {
        switch message.role {
        case .user:
            return .white
        case .guide, .system:
            return .white
        }
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

// MARK: - 打字指示器

struct EAGuideTypingIndicator: View {
    @State private var animating = false
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                HStack(spacing: 8) {
                    Image(systemName: "sparkles.rectangle.stack.fill")
                        .font(.caption)
                        .foregroundColor(.purple)
                    
                    Text("宇宙向导正在思考...")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                }
                
                // 动画点点点
                HStack(spacing: 4) {
                    ForEach(0..<3) { index in
                        Circle()
                            .fill(Color.white.opacity(0.5))
                            .frame(width: 6, height: 6)
                            .scaleEffect(animating ? 1.0 : 0.5)
                            .animation(
                                Animation.easeInOut(duration: 0.6)
                                    .repeatForever()
                                    .delay(Double(index) * 0.2),
                                value: animating
                            )
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.purple.opacity(0.2))
                )
            }
            
            Spacer()
        }
        .onAppear {
            animating = true
        }
    }
}

// MARK: - 预览

#Preview {
    @Previewable @State var sampleContainer = EARepositoryContainerImpl(modelContainer: try! EAAppSchema.createPreviewContainer())
    
    // 创建示例用户
    let sampleUser = EAUser(username: "示例用户", email: "<EMAIL>")
    
    EAUniverseGuideView(
        user: sampleUser,
        triggerReason: .userInitiated
    )
} 