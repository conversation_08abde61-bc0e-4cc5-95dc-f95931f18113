import SwiftUI
import Foundation

/// 一次性计划简洁日历选择器
/// ✅ iOS 18.5真机性能优化：纯渲染视图，接收预计算的日历数据
struct EAOneTimeCalendarAdapter: View {
    // ✅ 性能优化：接收预计算的日历数据，避免视图层计算
    let calendarDays: [CalendarDayCreation]
    let currentMonth: Date
    let isLoading: Bool
    let onDateTap: (Date) async -> Void
    let onMonthChange: (Date) async -> Void

    // ✅ 性能优化：静态DateFormatter，避免重复创建
    private static let monthFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年M月"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }()

    var body: some View {
        VStack(spacing: 12) {
            // 简洁的月份导航
            monthNavigationBar

            // 轻量级日历网格
            compactCalendarGrid
        }
    }
    
    // MARK: - 简洁月份导航栏
    private var monthNavigationBar: some View {
        HStack {
            // 上一月按钮
            Button(action: {
                // ✅ 关键修复：立即响应用户操作，避免延迟感
                let previousMonth = Calendar.current.date(byAdding: .month, value: -1, to: currentMonth) ?? currentMonth

                #if DEBUG
                print("🔧 [月份切换] 用户点击上一月按钮")
                #endif

                // ✅ 修复：直接调用异步方法，确保立即执行
                Task {
                    await onMonthChange(previousMonth)
                }
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color.hexColor("40E0D0"))
                    .frame(width: 28, height: 28)
                    .background(
                        Circle()
                            .fill(Color.white.opacity(0.05))
                            .overlay(
                                Circle()
                                    .stroke(Color.white.opacity(0.1), lineWidth: 1)
                            )
                    )
            }

            Spacer()

            // 当前月份显示
            VStack(spacing: 2) {
                Text(Self.monthFormatter.string(from: currentMonth))
                    .font(.system(size: 15, weight: .semibold))
                    .foregroundColor(.white)

                Text("可跨月选择")
                    .font(.system(size: 10, weight: .regular))
                    .foregroundColor(Color.white.opacity(0.5))
            }

            Spacer()

            // 下一月按钮
            Button(action: {
                // ✅ 关键修复：立即响应用户操作，避免延迟感
                let nextMonth = Calendar.current.date(byAdding: .month, value: 1, to: currentMonth) ?? currentMonth

                #if DEBUG
                print("🔧 [月份切换] 用户点击下一月按钮")
                #endif

                // ✅ 修复：直接调用异步方法，确保立即执行
                Task {
                    await onMonthChange(nextMonth)
                }
            }) {
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color.hexColor("40E0D0"))
                    .frame(width: 28, height: 28)
                    .background(
                        Circle()
                            .fill(Color.white.opacity(0.05))
                            .overlay(
                                Circle()
                                    .stroke(Color.white.opacity(0.1), lineWidth: 1)
                            )
                    )
            }
        }
    }
    
    // MARK: - 紧凑日历网格
    /// ✅ 修复约束冲突：简化布局结构，移除固定高度约束
    private var compactCalendarGrid: some View {
        VStack(spacing: 8) {
            // 周期标题（一到日）
            weekdayHeaders

            // ✅ 关键修复：移除固定高度，让内容自然撑开
            if isLoading {
                loadingStateView
            } else if calendarDays.isEmpty {
                emptyStateView
            } else {
                // ✅ 性能优化：纯渲染日期网格，无计算逻辑
                datesGrid
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.03))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(0.08), lineWidth: 1)
                )
        )
    }

    // MARK: - 加载状态视图
    /// ✅ 修复约束冲突：分离加载状态，使用最小高度而非固定高度
    private var loadingStateView: some View {
        VStack(spacing: 12) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(0.8)

            Text("加载日历...")
                .font(.system(size: 12, weight: .regular))
                .foregroundColor(.white.opacity(0.6))
        }
        .frame(minHeight: 120) // 使用最小高度而非固定高度
        .frame(maxWidth: .infinity)
    }

    // MARK: - 空状态视图
    /// ✅ 修复约束冲突：分离空状态，使用最小高度而非固定高度
    private var emptyStateView: some View {
        VStack(spacing: 12) {
            Image(systemName: "calendar.badge.exclamationmark")
                .font(.system(size: 24, weight: .medium))
                .foregroundColor(.white.opacity(0.4))

            Text("日历数据加载中...")
                .font(.system(size: 12, weight: .regular))
                .foregroundColor(.white.opacity(0.6))

            Text("如果持续显示此信息，请切换到其他频率后再切换回来")
                .font(.system(size: 10, weight: .regular))
                .foregroundColor(.white.opacity(0.4))
                .multilineTextAlignment(.center)
        }
        .frame(minHeight: 120) // 使用最小高度而非固定高度
        .frame(maxWidth: .infinity)
        .padding(.horizontal, 16)
    }
    
    // MARK: - 周期标题行
    /// ✅ 修复约束冲突：优化标题行布局，确保与网格对齐
    private var weekdayHeaders: some View {
        HStack(spacing: 4) { // 与网格间距保持一致
            ForEach(["一", "二", "三", "四", "五", "六", "日"], id: \.self) { weekday in
                Text(weekday)
                    .font(.system(size: 11, weight: .medium))
                    .foregroundColor(Color.hexColor("40E0D0"))
                    .frame(width: 32, height: 24) // 与日期单元格宽度保持一致
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(Color.hexColor("40E0D0").opacity(0.1))
        )
    }
    
    // MARK: - 日期网格
    /// ✅ 修复约束冲突：优化网格布局，确保7列布局稳定性
    private var datesGrid: some View {
        // ✅ 关键修复：使用标准7列网格，不过滤数据，确保布局一致性
        let gridColumns = Array(repeating: GridItem(.flexible(), spacing: 4), count: 7)

        return LazyVGrid(columns: gridColumns, spacing: 4) {
            // ✅ 修复：直接使用所有日历数据，在单元格内处理显示逻辑
            ForEach(calendarDays, id: \.id) { day in
                dayCell(day: day)
            }
        }
        .padding(.vertical, 8) // 添加垂直间距，避免与其他元素冲突
    }
    
    // MARK: - 日期单元格
    /// ✅ 修复约束冲突：优化单元格布局，确保尺寸一致性
    private func dayCell(day: CalendarDayCreation) -> some View {
        Button(action: {
            // ✅ 双重检查：确保只有有效且可选择的日期才能被点击
            if day.isSelectable && !day.dayNumber.isEmpty {
                #if DEBUG
                print("📅 [日期选择] 用户点击日期：\(day.dayNumber)")
                #endif
                Task {
                    await onDateTap(day.date)
                }
            }
        }) {
            // ✅ 关键修复：简化文本显示逻辑，避免复杂条件判断
            Text(day.dayNumber.isEmpty ? " " : day.dayNumber)
                .font(.system(size: 13, weight: .medium))
                .foregroundColor(cellTextColor(for: day))
                .frame(width: 32, height: 32) // 保持固定尺寸确保网格对齐
                .background(cellBackground(for: day))
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(!day.isSelectable || day.dayNumber.isEmpty)
    }

    // MARK: - 单元格样式辅助方法
    /// ✅ 修复约束冲突：分离样式逻辑，简化视图构建
    private func cellTextColor(for day: CalendarDayCreation) -> Color {
        if day.dayNumber.isEmpty || !day.isSelectable {
            return .clear
        }
        return day.isSelected ? .white : .white.opacity(0.8)
    }

    /// ✅ 修复约束冲突：分离背景样式，避免复杂嵌套
    private func cellBackground(for day: CalendarDayCreation) -> some View {
        RoundedRectangle(cornerRadius: 6)
            .fill(day.isSelected ? Color.hexColor("40E0D0").opacity(0.3) : Color.clear)
            .overlay(
                RoundedRectangle(cornerRadius: 6)
                    .stroke(
                        day.isSelected ? Color.hexColor("40E0D0").opacity(0.6) : Color.clear,
                        lineWidth: 1
                    )
            )
    }
}

// MARK: - 预览
#Preview {
    ZStack {
        Color.black.ignoresSafeArea()

        VStack {
            Text("一次性计划日期选择")
                .font(.headline)
                .foregroundColor(.white)
                .padding()

            EAOneTimeCalendarAdapter(
                calendarDays: [],
                currentMonth: Date(),
                isLoading: false,
                onDateTap: { _ in },
                onMonthChange: { _ in }
            )
            .padding()
        }
    }
}