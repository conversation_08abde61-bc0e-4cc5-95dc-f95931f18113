//
//  EAChatInputStyles.swift
//  Evolve
//
//  Created by AI Assistant on 2025-07-09.
//  Copyright © 2025 Evolve. All rights reserved.
//

import SwiftUI
import UIKit

/// 聊天输入框统一样式配置
/// 遵循微信/iMessage设计标准，保持星域风格的视觉语言
struct EAChatInputStyles {
    
    // MARK: - 尺寸标准
    
    /// 输入框高度标准 - 遵循WeChat/iMessage规范
    struct Heights {
        /// 最小高度 - 单行文本标准（优化为36pt，提供更好的触摸体验）
        static let minimum: CGFloat = 36

        /// 最大高度 - 多行文本限制（约4-5行，优化为100pt）
        static let maximum: CGFloat = 100

        /// 按钮标准高度
        static let button: CGFloat = 36
    }
    
    // MARK: - 间距标准
    
    /// 间距配置 - 符合微信/iMessage标准
    struct Spacing {
        /// 组件间水平间距（微信标准8pt）
        static let horizontal: CGFloat = 8

        /// 输入框内边距（微信标准：左右12pt，上下由textContainerInset控制）
        static let inputPadding = UIEdgeInsets(top: 0, left: 12, bottom: 0, right: 12)

        /// 外部容器边距
        static let containerPadding: CGFloat = 16

        /// 垂直间距（微信标准8pt）
        static let vertical: CGFloat = 8
    }
    
    // MARK: - 圆角标准
    
    /// 圆角半径配置
    struct CornerRadius {
        /// 输入框圆角 - 微信风格
        static let input: CGFloat = 16
        
        /// 按钮圆角
        static let button: CGFloat = 16
        
        /// 小按钮圆角
        static let smallButton: CGFloat = 12
    }
    
    // MARK: - 颜色配置
    
    /// 星域风格颜色配置
    struct Colors {
        /// 输入框背景色（优化透明度，提供更好的视觉层次）
        static let inputBackground = Color.hexColor("2D3748").opacity(0.8)

        /// 输入框边框色（降低透明度，减少视觉干扰）
        static let inputBorder = Color.hexColor("40E0D0").opacity(0.2)

        /// 主题色 - 星域青色
        static let primary = Color.hexColor("40E0D0")

        /// 按钮背景色
        static let buttonBackground = Color.hexColor("40E0D0").opacity(0.15)

        /// 按钮边框色
        static let buttonBorder = Color.hexColor("40E0D0").opacity(0.3)

        /// 发送按钮激活色
        static let sendActive = Color.chatAccentPrimary

        /// 发送按钮禁用色
        static let sendDisabled = Color.white.opacity(0.4)

        /// 文本颜色
        static let text = UIColor.white

        /// 文本颜色（SwiftUI）
        static let textSwiftUI = Color.white

        /// 占位符颜色
        static let placeholder = UIColor.white.withAlphaComponent(0.6)
    }
    
    // MARK: - 动画配置
    
    /// 动画效果配置
    struct Animations {
        /// 高度变化动画
        static let heightChange = Animation.easeInOut(duration: 0.2)
        
        /// 按钮状态切换动画
        static let buttonToggle = Animation.easeInOut(duration: 0.15)
        
        /// 面板展开动画
        static let panelExpand = Animation.easeInOut(duration: 0.25)
    }
    
    // MARK: - 字体配置
    
    /// 字体配置
    struct Fonts {
        /// 输入框字体大小
        static let input: CGFloat = 16
        
        /// 按钮图标大小
        static let buttonIcon: CGFloat = 18
        
        /// 小按钮图标大小
        static let smallButtonIcon: CGFloat = 16
    }
}

// MARK: - 输入框背景样式组件

/// 统一的输入框背景样式
struct EAChatInputBackground: View {
    let cornerRadius: CGFloat
    
    init(cornerRadius: CGFloat = EAChatInputStyles.CornerRadius.input) {
        self.cornerRadius = cornerRadius
    }
    
    var body: some View {
        RoundedRectangle(cornerRadius: cornerRadius)
            .fill(EAChatInputStyles.Colors.inputBackground)
            .overlay(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .stroke(EAChatInputStyles.Colors.inputBorder, lineWidth: 0.5)
            )
    }
}

// MARK: - 按钮样式组件

/// 统一的圆形按钮样式
struct EAChatCircleButton: View {
    let systemName: String
    let isActive: Bool
    let action: () -> Void
    
    init(systemName: String, isActive: Bool = true, action: @escaping () -> Void) {
        self.systemName = systemName
        self.isActive = isActive
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            Image(systemName: systemName)
                .font(.system(size: EAChatInputStyles.Fonts.buttonIcon, weight: .medium))
                .foregroundColor(isActive ? EAChatInputStyles.Colors.primary : EAChatInputStyles.Colors.sendDisabled)
                .frame(width: EAChatInputStyles.Heights.button, height: EAChatInputStyles.Heights.button)
                .background(
                    Circle()
                        .fill(EAChatInputStyles.Colors.buttonBackground)
                        .overlay(
                            Circle()
                                .stroke(EAChatInputStyles.Colors.buttonBorder, lineWidth: 1)
                        )
                )
        }
        .disabled(!isActive)
    }
}

// MARK: - 发送按钮样式组件

/// 统一的发送按钮样式
struct EAChatSendButton: View {
    let isEnabled: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Image(systemName: "paperplane.fill")
                .font(.system(size: EAChatInputStyles.Fonts.buttonIcon, weight: .medium))
                .foregroundColor(isEnabled ? EAChatInputStyles.Colors.sendActive : EAChatInputStyles.Colors.sendDisabled)
        }
        .disabled(!isEnabled)
    }
}
