import SwiftUI

/// 发光呼吸智慧核心组件
/// 参考原型文件aura_coach_home.html设计，实现真正的发光呼吸动态特效
struct EABreathingWisdomCore: View {
    // 呼吸动画状态
    @State private var isBreathing = false
    // 发光强度状态
    @State private var glowIntensity = false
    // 缩放状态
    @State private var scale = false
    
    // 组件尺寸 - 默认改为60pt，符合原型设计
    let size: CGFloat
    
    // 初始化
    init(size: CGFloat = 60) {
        self.size = size
    }
    
    var body: some View {
        ZStack {
            // 最外层羽化光晕 - 增强羽化效果
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color("PrimaryTurquoise").opacity(0.15),
                            Color("PrimaryTurquoise").opacity(0.08),
                            Color("PrimaryTurquoise").opacity(0.03),
                            Color.clear
                        ]),
                        center: .center,
                        startRadius: 0,
                        endRadius: size * 1.2
                    )
                )
                .frame(width: size * 2.4, height: size * 2.4)
                .scaleEffect(isBreathing ? 1.4 : 1.0)
                .opacity(glowIntensity ? 0.8 : 0.4)
                .blur(radius: 20)
            
            // 第二层羽化光晕
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color("PrimaryTurquoise").opacity(0.25),
                            Color("PrimaryTurquoise").opacity(0.15),
                            Color("PrimaryTurquoise").opacity(0.05),
                            Color.clear
                        ]),
                        center: .center,
                        startRadius: 0,
                        endRadius: size * 1.0
                    )
                )
                .frame(width: size * 2.0, height: size * 2.0)
                .scaleEffect(isBreathing ? 1.3 : 0.9)
                .opacity(glowIntensity ? 0.7 : 0.3)
                .blur(radius: 15)
            
            // 外层光晕效果 - 增强发光
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color("PrimaryTurquoise").opacity(0.4),
                            Color("PrimaryTurquoise").opacity(0.25),
                            Color("PrimaryTurquoise").opacity(0.1),
                            Color.clear
                        ]),
                        center: .center,
                        startRadius: 0,
                        endRadius: size * 0.8
                    )
                )
                .frame(width: size * 1.6, height: size * 1.6)
                .scaleEffect(isBreathing ? 1.2 : 0.8)
                .opacity(glowIntensity ? 0.9 : 0.5)
                .blur(radius: 10)
            
            // 中层发光环
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color("PrimaryTurquoise").opacity(0.6),
                            Color("PrimaryTurquoise").opacity(0.4),
                            Color("PrimaryTurquoise").opacity(0.2),
                            Color.clear
                        ]),
                        center: .center,
                        startRadius: 0,
                        endRadius: size * 0.6
                    )
                )
                .frame(width: size * 1.2, height: size * 1.2)
                .scaleEffect(isBreathing ? 1.15 : 0.85)
                .opacity(glowIntensity ? 1.0 : 0.6)
                .blur(radius: 6)
            
            // 核心发光体 - 增强白色内核融合
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.95), // 中心更亮的白色
                            Color.white.opacity(0.7),  // 第一层过渡
                            Color("PrimaryTurquoise").opacity(0.9), // 青色过渡
                            Color("PrimaryTurquoise").opacity(0.7),
                            Color("PrimaryTurquoise").opacity(0.5),
                            Color("PrimaryTurquoise").opacity(0.3)
                        ]),
                        center: .center,
                        startRadius: 0,
                        endRadius: size * 0.5
                    )
                )
                .frame(width: size, height: size)
                .scaleEffect(isBreathing ? 1.1 : 0.9)
                .opacity(glowIntensity ? 1.0 : 0.85)
            
            // 内核亮点 - 更自然的融合
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.9),
                            Color.white.opacity(0.6),
                            Color.white.opacity(0.3),
                            Color("PrimaryTurquoise").opacity(0.2),
                            Color.clear
                        ]),
                        center: .center,
                        startRadius: 0,
                        endRadius: size * 0.2
                    )
                )
                .frame(width: size * 0.4, height: size * 0.4)
                .scaleEffect(isBreathing ? 1.3 : 0.7)
                .opacity(glowIntensity ? 0.8 : 0.5)
                .blur(radius: 1)
        }
        .shadow(
            color: Color("PrimaryTurquoise").opacity(0.4),
            radius: isBreathing ? 25 : 15,
            x: 0,
            y: 0
        )
        .shadow(
            color: Color("PrimaryTurquoise").opacity(0.2),
            radius: isBreathing ? 40 : 25,
            x: 0,
            y: 0
        )
        .shadow(
            color: Color("PrimaryTurquoise").opacity(0.1),
            radius: isBreathing ? 60 : 35,
            x: 0,
            y: 0
        )
        .onAppear {
            startBreathingAnimation()
        }
        .onTapGesture {
            // 智慧核心点击处理 - 实际使用时会有具体逻辑
        }
    }
    
    private func startBreathingAnimation() {
        // 呼吸动画 - 加快节奏，增强效果
        withAnimation(.easeInOut(duration: 2.5).repeatForever(autoreverses: true)) {
            isBreathing.toggle()
        }
        
        // 发光强度动画 - 与呼吸同步但稍有延迟
        withAnimation(.easeInOut(duration: 2.5).delay(0.3).repeatForever(autoreverses: true)) {
            glowIntensity.toggle()
        }
    }
}

/// 可交互的发光呼吸智慧核心组件
struct EAInteractiveBreathingWisdomCore: View {
    let size: CGFloat
    let action: () -> Void
    
    init(size: CGFloat = 60, action: @escaping () -> Void) {
        self.size = size
        self.action = action
    }
    
    var body: some View {
        Button(action: {
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
            
            action()
        }) {
            EABreathingWisdomCore(size: size)
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(1.0)
        .animation(.easeInOut(duration: 0.1), value: false)
    }
}

// MARK: - 预览
#Preview("发光呼吸智慧核心") {
    ZStack {
        Color.black
        VStack(spacing: 40) {
            EABreathingWisdomCore(size: 60)
            EABreathingWisdomCore(size: 80)
            EAInteractiveBreathingWisdomCore(size: 60) {
                // 智慧核心点击处理 - 实际使用时会有具体逻辑
            }
        }
    }
} 