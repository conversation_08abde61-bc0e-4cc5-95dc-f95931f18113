import SwiftUI
import SwiftData

/// 好友行视图组件 - 星际伙伴卡片
/// 遵循iOS设计规范，展示好友信息和互动状态
/// 🔑 关键修复：完全避免UI组件中的异步数据加载，接收预处理数据
struct FriendRowView: View {
    // 🔑 根本修复：纯展示组件，只接收显示数据
    let friendshipData: FriendshipDisplayData

    // 🔑 批次三新增：屏蔽状态显示
    let isBlocked: Bool

    // MARK: - 设计常量
    private enum Design {
        static let statusIndicatorFontSize: CGFloat = 10
        static let statusIndicatorSpacing: CGFloat = 4
        static let statusIndicatorPaddingH: CGFloat = 6
        static let statusIndicatorPaddingV: CGFloat = 2
    }

    // 🔑 移除所有异步状态，改为直接使用传入的数据

    init(friendshipData: FriendshipDisplayData, isBlocked: Bool = false) {
        self.friendshipData = friendshipData
        self.isBlocked = isBlocked
    }
    
    var body: some View {
        HStack(spacing: 16) {
            // 头像
            friendAvatar

            // 好友信息
            VStack(alignment: .leading, spacing: 4) {
                // 用户名和在线状态
                HStack(spacing: 8) {
                    Text(friendshipData.friendDisplayName)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(isBlocked ? .white.opacity(0.5) : .white)

                    // 🔑 批次三新增：屏蔽状态指示器
                    if isBlocked {
                        blockedStatusIndicator
                    } else {
                        // 在线状态指示器
                        onlineStatusIndicator
                    }

                    Spacer()

                    // 星际能量等级
                    if !isBlocked {
                        energyLevelBadge
                    }
                }

                // 最后互动时间
                Text(friendshipData.lastInteractionText)
                    .font(.system(size: 14))
                    .foregroundColor(.white.opacity(0.6))

                // 友谊等级进度条
                friendshipLevelProgress
            }

            // 🔑 修复：移除自定义箭头，避免与NavigationLink系统箭头重复
            // NavigationLink会自动提供系统标准的导航指示器
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.hexColor("2D3748").opacity(0.8), // 深蓝灰
                            Color.hexColor("1A365D").opacity(0.6), // 深青蓝
                            Color.hexColor("2C3E50").opacity(0.7)  // 温暖灰蓝
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.hexColor("40E0D0").opacity(0.6), // 主青色
                                    Color.blue.opacity(0.4), // 蓝色过渡
                                    Color.hexColor("40E0D0").opacity(0.3)  // 青色渐淡
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1.5
                        )
                )
                .shadow(
                    color: Color.hexColor("40E0D0").opacity(0.2),
                    radius: 8,
                    x: 0,
                    y: 4
                )
        )
        // 🔑 根本修复：移除所有可能干扰NavigationLink的交互修饰符
        // EAFriendRowView现在是纯展示组件，不处理任何手势
        // 🔑 根本修复：EAFriendRowView现在是完全纯净的展示组件
    }

    // MARK: - 子组件

    /// 好友头像
    private var friendAvatar: some View {
        ZStack {
            // 🔑 修复：使用统一的头像显示组件
            EAAvatarView(
                avatarData: friendshipData.friendAvatarData,
                size: 50,
                showShadow: true
            )
            
            // 在线状态光环（如果在线）
            if friendshipData.isOnline {
                Circle()
                    .stroke(Color.green.opacity(0.8), lineWidth: 2)
                    .frame(width: 54, height: 54)
            }
        }
    }
    
    /// 在线状态指示器
    private var onlineStatusIndicator: some View {
        Circle()
            .fill(friendshipData.isOnline ? Color.green : Color.gray.opacity(0.4))
            .frame(width: 8, height: 8)
            .overlay(
                Circle()
                    .fill(friendshipData.isOnline ? Color.green.opacity(0.3) : Color.clear)
                    .scaleEffect(friendshipData.isOnline ? 2.0 : 1.0)
                    .animation(
                        friendshipData.isOnline ? 
                        Animation.easeInOut(duration: 1.0).repeatForever(autoreverses: true) : 
                        .default, 
                        value: friendshipData.isOnline
                    )
            )
            .allowsHitTesting(false)
    }
    
    /// 星际能量等级徽章
    private var energyLevelBadge: some View {
        HStack(spacing: 4) {
            Image(systemName: "star.fill")
                .font(.system(size: 10))
                .foregroundColor(Color.hexColor("40E0D0"))
            
            Text("Lv.\(friendshipData.friendshipLevel)")
                .font(.system(size: 12, weight: .bold))
                .foregroundColor(Color.hexColor("40E0D0"))
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            Capsule()
                .fill(Color.hexColor("40E0D0").opacity(0.2))
                .overlay(
                    Capsule()
                        .stroke(Color.hexColor("40E0D0").opacity(0.4), lineWidth: 1)
                )
        )
    }

    /// 🔑 批次三新增：屏蔽状态指示器
    private var blockedStatusIndicator: some View {
        HStack(spacing: Design.statusIndicatorSpacing) {
            Image(systemName: "eye.slash.fill")
                .font(.system(size: Design.statusIndicatorFontSize))
                .foregroundColor(.red)

            Text("已屏蔽") // TODO: 使用本地化字符串 NSLocalizedString("blocked", comment: "")
                .font(.system(size: Design.statusIndicatorFontSize, weight: .medium))
                .foregroundColor(.red)
        }
        .padding(.horizontal, Design.statusIndicatorPaddingH)
        .padding(.vertical, Design.statusIndicatorPaddingV)
        .background(
            Capsule()
                .fill(Color.red.opacity(0.2))
                .overlay(
                    Capsule()
                        .stroke(Color.red.opacity(0.4), lineWidth: 1)
                )
        )
    }

    /// 友谊等级进度条
    private var friendshipLevelProgress: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text("友谊等级")
                    .font(.system(size: 12))
                    .foregroundColor(.white.opacity(0.5))
                
                Spacer()
                
                Text("\(friendshipData.sharedStellarEnergy)/\(friendshipData.nextLevelRequirement)")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(Color.hexColor("40E0D0"))
            }
            
            // 进度条
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // 背景
                    RoundedRectangle(cornerRadius: 2)
                        .fill(Color.white.opacity(0.2))
                        .frame(height: 4)
                    
                    // 进度
                    RoundedRectangle(cornerRadius: 2)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.hexColor("40E0D0"),
                                    Color.blue
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(width: geometry.size.width * friendshipData.progressPercentage, height: 4)
                        .animation(.easeInOut(duration: 0.3), value: friendshipData.progressPercentage)
                }
            }
            .frame(height: 4)
        }
    }

    // 🔑 根本修复：移除所有交互方法，EAFriendRowView现在是纯展示组件
}

// MARK: - 好友请求行视图

/// 好友请求行视图 - 处理好友申请
struct FriendRequestRowView: View {
    let request: EAFriendRequest
    let friendshipService: EAFriendshipService
    
    @State private var isProcessing = false
    
    var body: some View {
        HStack(spacing: 16) {
            // 请求头像
            requestAvatar
            
            // 请求信息
            VStack(alignment: .leading, spacing: 8) {
                // 发送者信息
                HStack {
                    Text(senderDisplayName)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    Text(requestTimeText)
                        .font(.system(size: 12))
                        .foregroundColor(.white.opacity(0.6))
                }
                
                // 请求消息
                if let message = request.requestMessage, !message.isEmpty {
                    Text(message)
                        .font(.system(size: 14))
                        .foregroundColor(.white.opacity(0.8))
                        .lineLimit(2)
                }
                
                // 操作按钮
                actionButtons
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.hexColor("2D3748").opacity(0.8),
                            Color.hexColor("1A365D").opacity(0.6)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.orange.opacity(0.4), lineWidth: 1.5)
                )
        )
        .overlay(
            // 新请求指示器
            Circle()
                .fill(Color.orange)
                .frame(width: 8, height: 8)
                .offset(x: -8, y: -8),
            alignment: .topTrailing
        )
    }
    
    // MARK: - 子组件
    
    /// 请求头像
    private var requestAvatar: some View {
        ZStack {
            // 🔑 修复：使用统一的头像显示组件
            EAAvatarView(
                avatarData: request.senderProfile?.user?.avatarData,
                size: 50,
                showShadow: true
            )
            
            // 请求光环效果
            Circle()
                .stroke(Color.orange.opacity(0.6), lineWidth: 2)
                .frame(width: 54, height: 54)
        }
    }
    
    /// 操作按钮
    private var actionButtons: some View {
        HStack(spacing: 12) {
            // 接受按钮
            Button(action: acceptRequest) {
                HStack(spacing: 4) {
                    Image(systemName: "checkmark")
                        .font(.system(size: 12, weight: .semibold))
                    Text("接受")
                        .font(.system(size: 14, weight: .medium))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color.green)
                )
            }
            .disabled(isProcessing)
            
            // 拒绝按钮
            Button(action: rejectRequest) {
                HStack(spacing: 4) {
                    Image(systemName: "xmark")
                        .font(.system(size: 12, weight: .semibold))
                    Text("拒绝")
                        .font(.system(size: 14, weight: .medium))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color.red)
                )
            }
            .disabled(isProcessing)
            
            Spacer()
        }
    }
    
    // MARK: - 计算属性
    
    /// 发送者显示名称
    private var senderDisplayName: String {
        return request.senderProfile?.user?.username ?? "未知用户"
    }
    
    /// 请求时间文本
    private var requestTimeText: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.dateTimeStyle = .named
        return formatter.localizedString(for: request.creationDate, relativeTo: Date())
    }
    
    // MARK: - 操作方法
    
    /// 接受好友请求
    private func acceptRequest() {
        guard !isProcessing else { return }
        
        isProcessing = true
        
        Task {
            do {
                _ = try await friendshipService.acceptFriendRequest(requestId: request.id)
                
                await MainActor.run {
                    isProcessing = false
                    // 发送通知告知列表刷新
                    NotificationCenter.default.post(name: NSNotification.Name("FriendRequestAccepted"), object: nil)
                }
            } catch {
                await MainActor.run {
                    isProcessing = false
                    // 处理错误 - 可以添加错误提示
                }
            }
        }
    }
    
    /// 拒绝好友请求
    private func rejectRequest() {
        guard !isProcessing else { return }
        
        isProcessing = true
        
        Task {
            do {
                _ = try await friendshipService.rejectFriendRequest(requestId: request.id, reason: nil)
                
                await MainActor.run {
                    isProcessing = false
                    // 发送通知告知列表刷新
                    NotificationCenter.default.post(name: NSNotification.Name("FriendRequestProcessed"), object: nil)
                }
            } catch {
                await MainActor.run {
                    isProcessing = false
                    // 处理错误 - 可以添加错误提示
                }
            }
        }
    }
}

// MARK: - 预览

#Preview("好友行视图") {
    let sampleData = FriendshipDisplayData(
        friendDisplayName: "张小明",
        friendshipLevel: 3,
        isOnline: true,
        lastInteractionText: "2小时前",
        progressPercentage: 0.65,
        nextLevelRequirement: 400,
        sharedStellarEnergy: 260,
        friendAvatarData: nil,
        isValidFriendship: true
    )
    
    VStack(spacing: 16) {
        FriendRowView(friendshipData: sampleData, isBlocked: false)
        FriendRowView(friendshipData: sampleData, isBlocked: true)
    }
    .padding()
    .background(Color.black)
}

#Preview("好友请求行视图") {
    let request = EAFriendRequest(requestMessage: "想和您成为星际伙伴！")
    let repositoryContainer = EARepositoryContainerImpl(modelContainer: try! EAAppSchema.createPreviewContainer())
    let sessionManager = EASessionManager()
    let integrityGuard = EAUserIntegrityGuard(repositoryContainer: repositoryContainer)  // 🔑 修复：创建完整性守护服务
    let friendshipService = EAFriendshipService(repositoryContainer: repositoryContainer, sessionManager: sessionManager, integrityGuard: integrityGuard)

    VStack(spacing: 16) {
        FriendRequestRowView(request: request, friendshipService: friendshipService)
    }
    .padding()
    .background(Color.black)
}

/// 🔑 核心修复：好友显示数据结构，避免直接使用SwiftData对象
struct FriendshipDisplayData {
    let friendDisplayName: String
    let friendshipLevel: Int
    let isOnline: Bool
    let lastInteractionText: String
    let progressPercentage: Double
    let nextLevelRequirement: Int
    let sharedStellarEnergy: Int
    let friendAvatarData: EAAvatarData?
    let isValidFriendship: Bool
}

// 🔑 移除不再需要的扩展和方法
