import SwiftUI
import UIKit

/// 🔗 社区分享服务
/// 提供帖子分享功能，支持系统分享、复制链接、保存图片等
/// 遵循iOS原生分享体验，集成星域数字宇宙主题
@MainActor
class EACommunityShareService: ObservableObject {
    
    // MARK: - Properties
    
    /// Repository容器
    private let repositoryContainer: EARepositoryContainer
    
    /// 分享状态
    @Published var isSharing: Bool = false
    @Published var shareError: String?
    
    // MARK: - Initialization
    
    init(repositoryContainer: EARepositoryContainer) {
        self.repositoryContainer = repositoryContainer
    }
    
    // MARK: - Public Methods
    
    /// 分享帖子
    /// - Parameters:
    ///   - post: 要分享的帖子
    ///   - sourceView: 分享按钮的视图（用于iPad定位）
    func sharePost(_ post: EACommunityPost, from sourceView: UIView? = nil) {
        guard !isSharing else { return }
        
        isSharing = true
        shareError = nil
        
        Task {
            do {
                let shareContent = try await generateShareContent(for: post)
                await presentShareSheet(with: shareContent, from: sourceView)
                
                // 🔑 更新分享计数
                await updateShareCount(for: post)
                
            } catch {
                await MainActor.run {
                    shareError = "分享失败：\(error.localizedDescription)"
                }
            }
            
            await MainActor.run {
                isSharing = false
            }
        }
    }
    
    /// 复制帖子链接
    /// - Parameter post: 要复制的帖子
    func copyPostLink(_ post: EACommunityPost) {
        let shareText = generateShareText(for: post)
        UIPasteboard.general.string = shareText
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        // 可以在这里显示复制成功的提示
    }
    
    /// 保存帖子图片到相册
    /// - Parameter post: 包含图片的帖子
    func savePostImages(_ post: EACommunityPost) {
        guard !post.imageURLs.isEmpty else { return }
        
        Task {
            for imagePath in post.imageURLs {
                await saveImageToPhotos(imagePath: imagePath)
            }
        }
    }
    
    // MARK: - Private Methods
    
    /// 生成分享内容
    private func generateShareContent(for post: EACommunityPost) async throws -> [Any] {
        var shareItems: [Any] = []
        
        // 添加文本内容
        let shareText = generateShareText(for: post)
        shareItems.append(shareText)
        
        // 添加图片（如果有）
        if !post.imageURLs.isEmpty {
            let images = try await loadPostImages(post.imageURLs)
            shareItems.append(contentsOf: images)
        }
        
        return shareItems
    }
    
    /// 生成分享文本
    private func generateShareText(for post: EACommunityPost) -> String {
        var text = "🌟 来自星域探索者的分享\n\n"
        
        // 添加作者信息
        text += "👤 \(post.getAuthorUsername())\n"
        
        // 添加帖子内容
        text += "\(post.content)\n"
        
        // 添加习惯标签（如果有）
        if let habitName = post.habitName, !habitName.isEmpty {
            text += "\n🎯 习惯：\(habitName)"
        }
        
        // 添加能量等级
        if post.energyLevel > 0 {
            text += "\n⚡ 能量等级：Lv.\(post.energyLevel)"
        }
        
        // 添加应用推广
        text += "\n\n📱 来自 Evolve - 星域探索者"
        
        return text
    }
    
    /// 加载帖子图片
    private func loadPostImages(_ imagePaths: [String]) async throws -> [UIImage] {
        var images: [UIImage] = []
        
        for imagePath in imagePaths {
            let fullPath = getFullImagePath(imagePath)
            if let image = UIImage(contentsOfFile: fullPath) {
                images.append(image)
            }
        }
        
        return images
    }
    
    /// 展示系统分享面板
    private func presentShareSheet(with items: [Any], from sourceView: UIView?) async {
        await MainActor.run {
            guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                  let window = windowScene.windows.first,
                  let rootViewController = window.rootViewController else {
                return
            }
            
            let activityViewController = UIActivityViewController(
                activityItems: items,
                applicationActivities: nil
            )
            
            // 排除不需要的分享选项
            activityViewController.excludedActivityTypes = [
                .assignToContact,
                .addToReadingList,
                .openInIBooks
            ]
            
            // iPad适配
            if let popover = activityViewController.popoverPresentationController {
                if let sourceView = sourceView {
                    popover.sourceView = sourceView
                    popover.sourceRect = sourceView.bounds
                } else {
                    popover.sourceView = window
                    popover.sourceRect = CGRect(x: window.bounds.midX, y: window.bounds.midY, width: 0, height: 0)
                }
                popover.permittedArrowDirections = []
            }
            
            // 分享完成回调
            activityViewController.completionWithItemsHandler = { activityType, completed, returnedItems, error in
                if completed {
                    // 分享成功的处理
                    let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                    impactFeedback.impactOccurred()
                }
            }
            
            rootViewController.present(activityViewController, animated: true)
        }
    }
    
    /// 更新分享计数
    private func updateShareCount(for post: EACommunityPost) async {
        do {
            // 🔑 修复：直接更新帖子的分享计数，然后保存
            if let targetPost = try await repositoryContainer.communityRepository.fetchPost(by: post.id) {
                targetPost.shareCount += 1
                targetPost.updateUniversalImpact() // 更新宇宙影响力

                // 保存更改
                let _ = try await repositoryContainer.communityRepository.updatePost(targetPost)
            }
        } catch {
            // 静默处理错误，不影响用户体验
            #if DEBUG
            // 静默处理更新分享计数失败
            #endif
        }
    }
    
    /// 保存图片到相册
    private func saveImageToPhotos(imagePath: String) async {
        let fullPath = getFullImagePath(imagePath)
        guard let image = UIImage(contentsOfFile: fullPath) else { return }
        
        await MainActor.run {
            UIImageWriteToSavedPhotosAlbum(image, nil, nil, nil)
        }
    }
    
    /// 获取完整图片路径
    private func getFullImagePath(_ relativePath: String) -> String {
        let documentsPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)[0]
        return (documentsPath as NSString).appendingPathComponent(relativePath)
    }
}

// MARK: - Environment Extension

extension EnvironmentValues {
    @Entry var communityShareService: EACommunityShareService?
}

// MARK: - Share Options

/// 分享选项枚举
enum EACommunityShareOption: String, CaseIterable {
    case systemShare = "系统分享"
    case copyLink = "复制链接"
    case saveImages = "保存图片"
    
    var icon: String {
        switch self {
        case .systemShare:
            return "square.and.arrow.up"
        case .copyLink:
            return "link"
        case .saveImages:
            return "square.and.arrow.down"
        }
    }
    
    var description: String {
        switch self {
        case .systemShare:
            return "通过系统分享到其他应用"
        case .copyLink:
            return "复制帖子内容到剪贴板"
        case .saveImages:
            return "保存帖子图片到相册"
        }
    }
}

// MARK: - Custom Share Activity

/// 自定义分享活动 - 复制链接
class EACopyLinkActivity: UIActivity {
    private var shareText: String?
    
    override var activityType: UIActivity.ActivityType? {
        return UIActivity.ActivityType("com.evolve.copylink")
    }
    
    override var activityTitle: String? {
        return "复制链接"
    }
    
    override var activityImage: UIImage? {
        return UIImage(systemName: "link")
    }
    
    override func canPerform(withActivityItems activityItems: [Any]) -> Bool {
        return activityItems.contains { $0 is String }
    }
    
    override func prepare(withActivityItems activityItems: [Any]) {
        shareText = activityItems.compactMap { $0 as? String }.first
    }
    
    override func perform() {
        if let text = shareText {
            UIPasteboard.general.string = text
        }
        activityDidFinish(true)
    }
}
