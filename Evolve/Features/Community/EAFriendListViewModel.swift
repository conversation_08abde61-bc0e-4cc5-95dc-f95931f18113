import Foundation
import SwiftUI
import SwiftData

/// 好友列表页面ViewModel
/// 遵循项目MVVM架构标准，管理好友列表的状态和业务逻辑
/// 严格遵循《Evolve项目AI开发审查规则.md》和iOS开发规范
@MainActor
final class EAFriendListViewModel: ObservableObject {
    
    // MARK: - Published Properties

    /// 🔧 性能优化：统一的加载状态
    @Published var isLoading: Bool = false

    /// 🔑 批次三新增：初始化状态（幂等性保护）
    @Published var isInitialized: Bool = false

    /// 搜索文本（高频变化，必须保持@Published）
    @Published var searchText: String = ""

    /// 选中的标签页 (0: 好友列表, 1: 好友请求)
    @Published var selectedTab: Int = 0

    /// 好友显示数据列表（核心数据）
    @Published var friendDisplayDataList: [FriendDisplayItem] = []

    /// 🔧 性能优化：统一的警告状态
    @Published var showAlert: Bool = false
    @Published var alertMessage: String = ""

    /// 🔑 批次三新增：屏蔽状态缓存
    /// 🔧 内存管理：限制缓存大小，避免无限增长
    @Published var blockedUserIds: Set<UUID> = [] {
        didSet {
            // 🔑 内存保护：限制缓存大小为1000个用户ID
            if blockedUserIds.count > 1000 {
                let excess = blockedUserIds.count - 1000
                let toRemove = Array(blockedUserIds.prefix(excess))
                blockedUserIds.subtract(toRemove)
            }
        }
    }

    // 🔑 最终修复：移除冗余的导航状态，使用值驱动导航
    // showChatView 和 selectedFriendshipId 已不再需要

    // MARK: - 🔧 性能优化：非频繁变化的属性，使用普通属性

    /// 错误消息（与alertMessage合并使用）
    private var _errorMessage: String? = nil

    /// 错误消息的计算属性（保持API兼容性）
    var errorMessage: String? {
        get { _errorMessage }
        set {
            _errorMessage = newValue
            if let message = newValue {
                alertMessage = message
                showAlert = true
            }
        }
    }
    
    // MARK: - Dependencies (Environment-based)
    
    /// 会话管理器（通过Environment注入）
    private var sessionManager: EASessionManager?
    
    /// Repository容器（通过Environment注入）
    private var repositoryContainer: EARepositoryContainer?
    
    /// 好友服务（通过Environment注入）
    private var friendshipService: EAFriendshipService?
    
    /// 通知服务（通过Environment注入）
    private var notificationService: EAFriendNotificationService?
    
    // MARK: - Initialization
    
    /// 初始化ViewModel
    /// 遵循项目标准，使用Environment-based依赖注入
    init() {
        // 空初始化，依赖通过Environment注入
        // 这符合项目的依赖注入架构模式
        setupBlockingStatusObserver()
    }

    /// 🔑 系统性修复：设置完整的状态变化监听
    private func setupBlockingStatusObserver() {
        // 监听屏蔽状态变化
        NotificationCenter.default.addObserver(
            forName: .blockingStatusChanged,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            guard let userInfo = notification.userInfo,
                  let _ = userInfo["userId"] as? UUID,
                  let action = userInfo["action"] as? String else { return }

            if action == "unblocked" {
                // 取消屏蔽后重新加载好友列表
                Task { @MainActor in
                    await self?.refreshData()
                }
            }
        }

        // 🔑 修复：监听账号切换事件
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("EASessionLogoutCompleted"),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.clearAllData()
            }
        }

        // 🔑 修复：监听新用户登录事件
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("EASessionLoginCompleted"),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                await self?.initializeData()
            }
        }
    }

    /// 🔑 内存管理：移除通知观察者
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - Dependency Injection Methods
    
    /// 设置依赖项（由View层调用）
    /// - Parameters:
    ///   - sessionManager: 会话管理器
    ///   - repositoryContainer: Repository容器
    ///   - friendshipService: 好友服务
    ///   - notificationService: 通知服务
    func setDependencies(
        sessionManager: EASessionManager,
        repositoryContainer: EARepositoryContainer,
        friendshipService: EAFriendshipService,
        notificationService: EAFriendNotificationService
    ) {
        self.sessionManager = sessionManager
        self.repositoryContainer = repositoryContainer
        self.friendshipService = friendshipService
        self.notificationService = notificationService
    }
    
    // MARK: - Computed Properties
    
    /// 过滤后的好友显示数据
    var filteredFriendDisplayData: [FriendDisplayItem] {
        if searchText.isEmpty {
            return friendDisplayDataList
        } else {
            return friendDisplayDataList.filter { item in
                item.displayData.friendDisplayName.lowercased().contains(searchText.lowercased())
            }
        }
    }
    
    // MARK: - Public Methods

    /// 🔑 MVVM架构修复：加载好友请求数据
    func loadPendingRequests() async {
        await friendshipService?.loadPendingRequests()
    }

    /// 🔑 批次三修复：初始化数据加载（幂等性设计）
    /// 在View的onAppear中调用，确保多次调用安全
    func initializeData() async {
        // 🔑 幂等性保护：防止重复初始化
        guard !isInitialized else {
            #if DEBUG
            print("✅ [FriendListViewModel] 数据已初始化，跳过重复初始化")
            #endif
            return
        }

        // 🔑 防止并发初始化
        guard !isLoading else {
            #if DEBUG
            print("⚠️ [FriendListViewModel] 正在初始化中，跳过重复调用")
            #endif
            return
        }

        // 🔑 依赖检查：确保必要依赖已注入
        guard let sessionManager = sessionManager,
              let _ = repositoryContainer,
              let friendshipService = friendshipService else {
            #if DEBUG
            print("⚠️ [FriendListViewModel] 依赖未完全注入，等待依赖就绪")
            #endif
            // 🔑 修复：不使用递归重试，而是标记为未初始化状态
            return
        }

        // 🔑 验证用户会话状态
        guard sessionManager.isLoggedIn, sessionManager.currentUserID != nil else {
            #if DEBUG
            print("⚠️ [FriendListViewModel] 用户未登录，跳过数据初始化")
            #endif
            return
        }

        #if DEBUG
        print("🚀 [FriendListViewModel] 开始初始化数据...")
        #endif

        // 🔑 设置加载状态
        await MainActor.run {
            isLoading = true
        }

        // 🔑 第一步：加载好友显示数据
        await loadFriendDisplayData()

        // 🔑 第二步：并行加载好友请求数据
        await friendshipService.loadPendingRequests()

        // 🔑 第三步：刷新屏蔽状态（确保数据完整性）
        refreshBlockingStatus(forceRefresh: true)

        // 🔑 标记初始化完成
        await MainActor.run {
            isInitialized = true
            isLoading = false
        }

        #if DEBUG
        print("✅ [FriendListViewModel] 数据初始化完成")
        #endif
    }

    /// 🔑 批次三修复：刷新数据（优化版）
    /// 用于下拉刷新功能，确保状态管理正确
    func refreshData() async {
        // 🔑 防止并发刷新
        guard !isLoading else {
            #if DEBUG
            print("⚠️ [FriendListViewModel] 正在加载中，跳过重复刷新")
            #endif
            return
        }

        await MainActor.run {
            isLoading = true
        }

        // 🔑 刷新好友显示数据
        await loadFriendDisplayData()

        // 🔑 同时刷新好友请求数据
        await friendshipService?.loadPendingRequests()

        // 🔑 刷新屏蔽状态（强制从数据库重新加载）
        refreshBlockingStatus(forceRefresh: true)

        await MainActor.run {
            isLoading = false
        }
    }
    
    /// 删除好友
    /// - Parameter friendshipId: 好友关系ID
    func deleteFriend(friendshipId: UUID) {
        // 🔑 第二阶段：从View层迁移的删除逻辑
        Task {
            do {
                _ = try await friendshipService?.deleteFriendship(friendshipId: friendshipId)

                await MainActor.run {
                    // 从显示列表中移除
                    friendDisplayDataList.removeAll { $0.friendshipId == friendshipId }

                    showAlert = true
                    alertMessage = "好友已删除"
                }
            } catch {
                await MainActor.run {
                    showError("删除好友失败：\(error.localizedDescription)")
                }
            }
        }
    }

    /// 🔑 系统性修复：屏蔽好友（遵循MVVM单向数据流）
    /// - Parameter friendshipId: 好友关系ID（EAFriendship.id，用于标识具体的好友关系）
    /// - Important: 此方法使用好友关系ID，内部会自动解析出需要屏蔽的用户ID
    func blockFriend(friendshipId: UUID) {
        // 🔑 安全检查：确保服务可用
        guard let friendshipService = friendshipService else {
            showError("好友服务不可用")
            return
        }

        Task {
            do {
                // 🔑 安全检查：确保好友关系ID有效
                guard friendshipId != UUID() else {
                    await MainActor.run {
                        showError("无效的好友关系")
                    }
                    return
                }

                // 🔑 修复1：先获取用户ID，用于后续状态管理
                guard let friendItem = friendDisplayDataList.first(where: { $0.friendshipId == friendshipId }),
                      let targetUserId = friendItem.userId, targetUserId != UUID() else {
                    await MainActor.run {
                        showError("无法获取用户信息")
                    }
                    return
                }

                // 🔑 修复2：执行屏蔽操作（数据库层面）
                _ = try await friendshipService.blockFriendship(friendshipId: friendshipId)

                await MainActor.run {
                    // 🔑 系统性修复：立即更新内存中的UI状态（实现瞬间响应）
                    if let index = friendDisplayDataList.firstIndex(where: { $0.friendshipId == friendshipId }) {
                        // 创建新的显示项，标记为已屏蔽
                        var updatedItem = friendDisplayDataList[index]
                        updatedItem.isBlocked = true
                        friendDisplayDataList[index] = updatedItem
                    }

                    // 🔑 更新屏蔽状态缓存
                    blockedUserIds.insert(targetUserId)

                    // 🔑 后台异步同步数据（确保最终一致性）
                    Task {
                        await loadFriendDisplayData()
                    }

                    showAlert = true
                    alertMessage = "用户已屏蔽"
                }
            } catch {
                await MainActor.run {
                    showError("屏蔽用户失败：\(error.localizedDescription)")
                }
            }
        }
    }

    /// 🔑 批次三新增：取消屏蔽好友
    /// - Parameter userId: 用户ID（EAUser.id，用于标识被屏蔽的用户）
    /// - Important: 此方法使用用户ID，直接操作屏蔽关系
    func unblockUser(userId: UUID) {
        // 🔑 安全检查：确保用户ID有效
        guard userId != UUID() else {
            showError("无效的用户ID")
            return
        }

        Task {
                // 获取屏蔽服务
                guard let repositoryContainer = repositoryContainer else {
                    await MainActor.run {
                        showError("服务不可用")
                    }
                    return
                }

                let blockingService = repositoryContainer.blockingService

                // 获取当前用户ID
                guard let sessionManager = sessionManager,
                      let currentUser = await sessionManager.safeCurrentUser else {
                    await MainActor.run {
                        showError("当前用户信息不可用")
                    }
                    return
                }

                // 执行取消屏蔽
                await blockingService.unblockUser(currentUserID: currentUser.id, userID: userId)

                await MainActor.run {
                    // 🔑 系统性修复：立即更新内存中的UI状态（实现瞬间响应）
                    if let index = friendDisplayDataList.firstIndex(where: { $0.userId == userId }) {
                        // 创建新的显示项，标记为未屏蔽
                        var updatedItem = friendDisplayDataList[index]
                        updatedItem.isBlocked = false
                        friendDisplayDataList[index] = updatedItem
                    }

                    // 🔑 更新屏蔽状态缓存
                    blockedUserIds.remove(userId)

                    // 🔑 后台异步同步数据（确保最终一致性）
                    Task {
                        await loadFriendDisplayData()
                    }

                    showAlert = true
                    alertMessage = "已取消屏蔽"
                }

        }
    }

    /// 🔑 批次三新增：检查用户是否被屏蔽
    /// - Parameter userId: 用户ID
    /// - Returns: 是否被屏蔽
    func isUserBlocked(userId: UUID) -> Bool {
        // 🔑 安全检查：过滤无效的UUID
        guard userId != UUID() else {
            return false
        }

        return blockedUserIds.contains(userId)
    }

    /// 🔑 系统性修复：完整的数据清理方法
    /// 在用户登出或切换账户时调用
    func clearAllData() {
        // 清理所有缓存数据
        blockedUserIds.removeAll()
        friendDisplayDataList.removeAll()
        searchText = ""
        selectedTab = 0
        isLoading = false
        isInitialized = false  // 🔑 批次三新增：重置初始化状态
        showAlert = false
        alertMessage = ""
        _errorMessage = nil

        #if DEBUG
        print("✅ [FriendListViewModel] 所有数据已清理")
        #endif
    }

    /// 🔑 向后兼容：保留原有方法名
    func clearBlockingCache() {
        clearAllData()
    }

    /// 🔑 系统性修复：智能屏蔽状态刷新（支持增量更新）
    /// - Parameter specificUserIds: 可选的特定用户ID列表，如果为nil则检查所有好友
    /// - Parameter forceRefresh: 是否强制从数据库重新加载状态
    func refreshBlockingStatus(specificUserIds: [UUID]? = nil, forceRefresh: Bool = false) {
        Task {
                // 🔑 安全检查：确保所有依赖都已注入
                guard let repositoryContainer = repositoryContainer,
                      let sessionManager = sessionManager,
                      let currentUser = await sessionManager.safeCurrentUser else {
                    #if DEBUG
                    print("⚠️ refreshBlockingStatus: 依赖未完全注入，跳过屏蔽状态检查")
                    #endif
                    return
                }

                // 🔑 安全检查：确保有好友数据
                guard !friendDisplayDataList.isEmpty else {
                    #if DEBUG
                    print("⚠️ refreshBlockingStatus: 好友列表为空，跳过屏蔽状态检查")
                    #endif
                    return
                }

                let blockingService = repositoryContainer.blockingService

                // 🔑 修复：支持增量更新
                let userIds: [UUID]
                if let specificIds = specificUserIds {
                    userIds = specificIds.filter { $0 != UUID() }
                } else {
                    userIds = friendDisplayDataList.compactMap { $0.userId }.filter { $0 != UUID() }
                }

                // 🔑 安全检查：确保有有效的用户ID
                guard !userIds.isEmpty else {
                    if specificUserIds == nil || forceRefresh {
                        await MainActor.run {
                            self.blockedUserIds.removeAll()
                        }
                    }
                    #if DEBUG
                    print("⚠️ refreshBlockingStatus: 没有有效的用户ID，跳过屏蔽状态检查")
                    #endif
                    return
                }

                #if DEBUG
                print("🔍 refreshBlockingStatus: 开始检查 \(userIds.count) 个用户的屏蔽状态，强制刷新：\(forceRefresh)")
                #endif

                // 批量检查屏蔽状态
                let blockingStatus = await blockingService.checkBlockingStatus(
                    currentUserID: currentUser.id,
                    userIDs: userIds
                )

                await MainActor.run {
                    if specificUserIds != nil && !forceRefresh {
                        // 🔑 增量更新：只更新特定用户的状态
                        for (userId, isBlocked) in blockingStatus {
                            if isBlocked {
                                self.blockedUserIds.insert(userId)
                            } else {
                                self.blockedUserIds.remove(userId)
                            }
                        }
                    } else {
                        // 🔑 全量更新：替换整个缓存
                        let blockedIds = Set(blockingStatus.compactMap { key, value in
                            value ? key : nil
                        })
                        self.blockedUserIds = blockedIds
                    }

                    #if DEBUG
                    print("✅ refreshBlockingStatus: 屏蔽用户数: \(self.blockedUserIds.count)")
                    #endif
                    
                    // 🔑 关键修复：更新好友显示项的屏蔽状态
                    for index in self.friendDisplayDataList.indices {
                        if let userId = self.friendDisplayDataList[index].userId {
                            self.friendDisplayDataList[index].isBlocked = self.blockedUserIds.contains(userId)
                        }
                    }
                }

        }
    }
    
    /// 接受好友请求
    /// - Parameter requestId: 请求ID
    func acceptFriendRequest(requestId: UUID) async {
        // 🔑 第二阶段：实现好友请求接受逻辑
        do {
            _ = try await friendshipService?.acceptFriendRequest(requestId: requestId)

            await MainActor.run {
                showAlert = true
                alertMessage = "好友请求已接受"
            }

            // 重新加载数据
            await refreshData()
        } catch {
            await MainActor.run {
                showError("接受好友请求失败：\(error.localizedDescription)")
            }
        }
    }

    /// 拒绝好友请求
    /// - Parameter requestId: 请求ID
    func rejectFriendRequest(requestId: UUID) async {
        // 🔑 第二阶段：实现好友请求拒绝逻辑
        do {
            _ = try await friendshipService?.rejectFriendRequest(requestId: requestId, reason: nil)

            await MainActor.run {
                showAlert = true
                alertMessage = "好友请求已拒绝"
            }
        } catch {
            await MainActor.run {
                showError("拒绝好友请求失败：\(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - Private Methods

    /// 🔑 核心修复：统一的好友显示数据加载方法（从View层迁移）
    private func loadFriendDisplayData() async {
        // 🔑 批次三修复：避免重复设置isLoading，由调用方管理

        // 先加载基础好友关系数据
        await friendshipService?.loadFriendships()

        guard let currentUser = await sessionManager?.safeCurrentUser,
              let currentProfile = currentUser.socialProfile else {
            await MainActor.run {
                showError("无法获取当前用户信息")
            }
            return
        }

        // 🔑 预处理所有好友数据，转换为显示格式
        var displayItems: [FriendDisplayItem] = []
        let baseTime = Date()

        guard let friendships = friendshipService?.friendships else {
            await MainActor.run {
                showError("无法获取好友关系数据")
            }
            return
        }

        for friendship in friendships {
            // 🔑 安全获取好友档案
            guard let friendProfile = friendship.getOtherProfile(currentProfile: currentProfile),
                  let friendUser = friendProfile.user else {
                continue
            }

            // 🔑 一次性获取所有数据，避免重复访问
            let displayName = friendUser.username
            let level = friendship.friendshipLevel
            let sharedEnergy = friendship.sharedStellarEnergy
            let lastInteractionDate = friendship.lastInteractionDate

            // 计算显示文本
            let interactionText: String
            if lastInteractionDate != Date.distantPast {
                let formatter = RelativeDateTimeFormatter()
                formatter.locale = Locale(identifier: "zh_CN")
                formatter.dateTimeStyle = .named
                interactionText = formatter.localizedString(for: lastInteractionDate, relativeTo: baseTime)
            } else {
                interactionText = "无互动记录"
            }

            // 计算进度
            let nextLevelReq = (level + 1) * 100
            let progressPercentage = Double(sharedEnergy) / Double(nextLevelReq)

            // 创建显示数据
            let displayData = FriendshipDisplayData(
                friendDisplayName: displayName,
                friendshipLevel: level,
                isOnline: false, // 暂时使用默认值
                lastInteractionText: interactionText,
                progressPercentage: progressPercentage,
                nextLevelRequirement: nextLevelReq,
                sharedStellarEnergy: sharedEnergy,
                friendAvatarData: friendUser.avatarData,
                isValidFriendship: true
            )

            // 🔑 系统性修复：检查屏蔽状态并创建显示项
            let isBlocked = blockedUserIds.contains(friendUser.id)

            displayItems.append(FriendDisplayItem(
                friendshipId: friendship.id,
                userId: friendUser.id,  // 🔑 用户ID，符合ID使用规范
                displayData: displayData,
                isBlocked: isBlocked    // 🔑 设置屏蔽状态
            ))
        }

        // 🔑 排序：按最近互动时间排序
        displayItems.sort { item1, item2 in
            // 这里可以添加排序逻辑，比如按最近互动时间
            item1.displayData.friendDisplayName < item2.displayData.friendDisplayName
        }

        await MainActor.run {
            friendDisplayDataList = displayItems
        }
    }

    /// 🔧 性能优化：显示错误消息（统一处理）
    /// - Parameter message: 错误消息
    private func showError(_ message: String) {
        errorMessage = message // 这会自动设置alertMessage和showAlert
    }

    /// 🔧 性能优化：清除错误状态（统一处理）
    private func clearError() {
        errorMessage = nil
        showAlert = false
        alertMessage = ""
    }
}

// MARK: - Supporting Types
// 注意：FriendDisplayItem和FriendshipDisplayData已在EAFriendListView.swift中定义
