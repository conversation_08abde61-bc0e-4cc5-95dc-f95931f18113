import Foundation
import SwiftData
import SwiftUI
import Combine

/// 🔑 性能优化：简化的帖子交互数据结构
struct EAPostInteractionData {
    let likeCount: Int
    let commentCount: Int
    let shareCount: Int
    let lastUpdated: Date
    
    init(likeCount: Int = 0, commentCount: Int = 0, shareCount: Int = 0) {
        self.likeCount = likeCount
        self.commentCount = commentCount
        self.shareCount = shareCount
        self.lastUpdated = Date()
    }
}

/// 🔑 性能优化：轻量级帖子状态管理
struct PostUIState {
    let postId: UUID
    var isLiked: Bool = false
    var likeCount: Int = 0
    var commentCount: Int = 0
    var isVisible: Bool = false

    init(post: EACommunityPost) {
        self.postId = post.id
        self.likeCount = post.likeCount
        self.commentCount = post.commentCount
        self.isVisible = false
    }
}

/// 🔑 性能优化：合并筛选状态结构体
struct FilterState {
    var selectedCategory: String? = nil
    var selectedTags: [String] = []
    var availableCategories: [String] = ["habit", "challenge", "share"]
    var popularTags: [String] = ["运动", "学习", "健康", "阅读"]
    var isFilterActive: Bool = false
}

/// 社区ViewModel错误类型
enum EACommunityViewModelError: LocalizedError {
    case repositoryNotAvailable
    case userNotLoggedIn
    case postNotFound
    case operationFailed(String)

    var errorDescription: String? {
        switch self {
        case .repositoryNotAvailable:
            return "数据访问服务不可用"
        case .userNotLoggedIn:
            return "用户未登录"
        case .postNotFound:
            return "帖子不存在"
        case .operationFailed(let message):
            return "操作失败：\(message)"
        }
    }
}

/// 社区页面ViewModel - 性能优化版
/// 🚀 关键优化：简化状态管理、减少异步操作、优化数据加载
/// ✅ Phase 2优化：增强与服务层集成、改进缓存策略、优化用户体验
@MainActor
final class EACommunityViewModel: ObservableObject {
    
    // MARK: - 核心状态属性
    
    /// 帖子列表
    @Published var posts: [EACommunityPost] = []
    
    /// 加载状态
    @Published var isLoading: Bool = false
    
    /// 错误消息
    @Published var errorMessage: String?
    
    /// 是否还有下一页
    @Published var hasNextPage: Bool = true
    
    /// 🔑 性能优化：合并筛选状态到单一结构体
    @Published var filterState = FilterState()
    
    /// 当前页码
    @Published var currentPage: Int = 0

    // MARK: - 🔑 新增：用户Profile预加载状态

    /// 用户Profile是否就绪（用于好友功能访问控制）
    @Published var isUserProfileReady: Bool = false

    /// 用户Profile加载错误信息
    @Published var userProfileLoadingError: String? = nil

    // MARK: - 🔑 性能优化：简化的依赖注入
    
    /// ✅ 优化：使用社区服务替代直接Repository访问
    private var communityService: EACommunityService?

    /// Repository容器（保留用于其他服务）
    private var repositoryContainer: EARepositoryContainer?

    /// SessionManager
    private var sessionManager: EASessionManager?

    /// 🔑 新增：好友服务（用于用户Profile预加载）
    private var friendshipService: EAFriendshipService?
    
    /// 🔒 修复：性能监控器依赖注入
    private var performanceMonitor: EAPerformanceMonitor
    
    /// 分享服务
    private var shareService: EACommunityShareService?
    
    /// 🔑 新增：通知监听取消令牌，用于清理通知监听器
    private var notificationCancellables: Set<AnyCancellable> = []

    // MARK: - 🔑 性能优化：简化的私有属性

    /// ✅ 优化：调整页面大小，与服务层保持一致
    private let pageSize: Int = 20 // 🚀 微信朋友圈级别：优化分页大小，平衡性能和用户体验
    
    /// 上次刷新时间，用于防抖
    private var lastRefreshTime: Date = Date.distantPast
    
    /// 🔑 性能优化：优化缓存机制
    private var cachedPosts: [EACommunityPost] = []
    private var cacheTimestamp: Date = Date.distantPast
    private let cacheValidDuration: TimeInterval = 300 // 🚀 微信朋友圈级别：5分钟缓存，平衡性能和实时性

    /// 页面状态管理
    @Published var isViewActive: Bool = false
    
    /// 🔑 性能优化：轻量级帖子状态管理
    @Published private var postUIStates: [UUID: PostUIState] = [:]
    
    /// ✅ 新增：操作防抖机制
    private var lastOperationTime: [String: Date] = [:]
    private let operationDebounceInterval: TimeInterval = 0.5
    
    /// 🚀 微信朋友圈级别优化：智能预加载
    private var preloadThreshold: Int = 5 // 距离底部5个帖子时开始预加载
    
    /// 🚀 内存管理：最大缓存帖子数量
    private let maxCachedPosts: Int = 200 // 限制内存中的帖子数量，防止内存爆炸
    
    /// 🚀 性能监控：滚动性能指标
    private var scrollPerformanceMetrics: [String: Any] = [:]
    
    // MARK: - 初始化
    
    /// 🔑 新增：初始化方法，设置通知监听
    init() {
        self.performanceMonitor = EAPerformanceMonitor()
        setupNotificationListeners()
        setupNotificationObservers()
    }
    
    /// 🔑 新增：清理资源
    deinit {
        notificationCancellables.removeAll()
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - 🔑 性能优化：Repository容器管理（优化版）
    
    /// 检查是否有Repository容器
    var hasRepositoryContainer: Bool {
        return repositoryContainer != nil && communityService != nil
    }
    
    /// ✅ 优化：设置Repository容器并初始化服务
    func setRepositoryContainer(_ container: EARepositoryContainer) {
        self.repositoryContainer = container
        self.shareService = EACommunityShareService(repositoryContainer: container)
        
        // ✅ 关键：初始化社区服务
        if let sessionManager = self.sessionManager {
            self.communityService = EACommunityService(
                repository: container.communityRepository,
                sessionManager: sessionManager
            )
            
            // ✅ 修复：初始化并设置星际能量服务（解决能量奖励不生效问题）
            let stellarEnergyService = EAStellarEnergyService(repositoryContainer: container)
            self.communityService?.setStellarEnergyService(stellarEnergyService)
        }
    }
    
    /// ✅ 优化：设置SessionManager并初始化服务
    func setSessionManager(_ sessionManager: EASessionManager) {
        self.sessionManager = sessionManager

        // ✅ 关键：如果Repository容器已设置，初始化社区服务
        if let container = self.repositoryContainer {
            self.communityService = EACommunityService(
                repository: container.communityRepository,
                sessionManager: sessionManager
            )

            // 🔑 新增：初始化好友服务用于用户Profile预加载
            let integrityGuard = EAUserIntegrityGuard(repositoryContainer: container)  // 🔑 修复：创建完整性守护服务
            self.friendshipService = EAFriendshipService(
                repositoryContainer: container,
                sessionManager: sessionManager,
                integrityGuard: integrityGuard
            )
            
            // ✅ 修复：初始化并设置星际能量服务（解决能量奖励不生效问题）
            let stellarEnergyService = EAStellarEnergyService(repositoryContainer: container)
            self.communityService?.setStellarEnergyService(stellarEnergyService)
        }
    }
    
    // MARK: - 🔑 新增：通知监听和状态同步
    
    /// 设置通知监听器
    private func setupNotificationListeners() {
        // 🔑 关键：监听点赞状态变化通知
        NotificationCenter.default.publisher(for: .postLikeStatusDidChange)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] notification in
                self?.handlePostLikeStatusChange(notification)
            }
            .store(in: &notificationCancellables)
        
        // 🔑 关键：监听评论数量变化通知
        NotificationCenter.default.publisher(for: .commentCountDidChange)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] notification in
                self?.handleCommentCountChange(notification)
            }
            .store(in: &notificationCancellables)
        
        // 🔑 关键：监听社区数据更新通知
        NotificationCenter.default.publisher(for: .communityDataDidUpdate)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] notification in
                self?.handleCommunityDataUpdate(notification)
            }
            .store(in: &notificationCancellables)
    }
    
    /// 🔑 核心方法：处理点赞状态变化通知
    private func handlePostLikeStatusChange(_ notification: Notification) {
        guard let postId = notification.object as? UUID,
              let newIsLiked = notification.userInfo?["isLiked"] as? Bool,
              let newCount = notification.userInfo?["newCount"] as? Int else {
            return
        }
        
        // 🔑 关键：更新本地UI状态
        if var state = postUIStates[postId] {
            let stateChanged = state.isLiked != newIsLiked || state.likeCount != newCount
            
            if stateChanged {
                state.isLiked = newIsLiked
                state.likeCount = newCount
                postUIStates[postId] = state
                
                // 🔑 关键：同步更新帖子列表中的数据
                if let postIndex = posts.firstIndex(where: { $0.id == postId }) {
                    posts[postIndex].likeCount = newCount
                }
                
                // 🔑 关键：同步更新缓存中的数据
                if let cachedIndex = cachedPosts.firstIndex(where: { $0.id == postId }) {
                    cachedPosts[cachedIndex].likeCount = newCount
                }
                
                #if DEBUG
                #if DEBUG
        // 同步点赞状态
        _ = (postId, newIsLiked, newCount)
        #endif
                #endif
            }
        } else {
            // 🔑 备用方案：创建新的UI状态
            if let post = posts.first(where: { $0.id == postId }) {
                var newState = PostUIState(post: post)
                newState.isLiked = newIsLiked
                newState.likeCount = newCount
                postUIStates[postId] = newState
                
                #if DEBUG
                #if DEBUG
                // 创建新点赞状态
                _ = (postId, newIsLiked, newCount)
                #endif
                #endif
            }
        }
    }
    
    /// 🔑 核心方法：处理评论数量变化通知
    private func handleCommentCountChange(_ notification: Notification) {
        guard let postId = notification.object as? UUID,
              let newCount = notification.userInfo?["newCount"] as? Int else {
            return
        }
        
        // 🔑 关键：更新本地UI状态
        if var state = postUIStates[postId] {
            if state.commentCount != newCount {
                state.commentCount = newCount
                postUIStates[postId] = state
                
                // 🔑 关键：同步更新帖子列表中的数据
                if let postIndex = posts.firstIndex(where: { $0.id == postId }) {
                    posts[postIndex].commentCount = newCount
                }
                
                // 🔑 关键：同步更新缓存中的数据
                if let cachedIndex = cachedPosts.firstIndex(where: { $0.id == postId }) {
                    cachedPosts[cachedIndex].commentCount = newCount
                }
                
                #if DEBUG
                print("✅ EACommunityViewModel: 同步评论数量 - 帖子ID: \(postId), 数量: \(newCount)")
                #endif
            }
        }
    }
    
    /// 🔑 核心方法：处理社区数据更新通知
    private func handleCommunityDataUpdate(_ notification: Notification) {
        guard let postId = notification.object as? UUID,
              let updateType = notification.userInfo?["type"] as? String,
              let newCount = notification.userInfo?["newCount"] as? Int else {
            return
        }
        
        switch updateType {
        case "commentCount":
            handleCommentCountChange(notification)
        case "likeCount":
            // 构造点赞状态变化通知格式
            let likeNotification = Notification(
                name: .postLikeStatusDidChange,
                object: postId,
                userInfo: [
                    "isLiked": getPostLikeStatus(postId: postId),
                    "newCount": newCount
                ]
            )
            handlePostLikeStatusChange(likeNotification)
        default:
            break
        }
    }
    
    // MARK: - 帖子状态管理（优化版）
    
    /// ✅ 优化：获取帖子点赞状态，增强性能和准确性
    func getPostLikeStatus(postId: UUID) -> Bool {
        // 🔑 修复：优先从UI状态获取，确保状态准确性
        if let uiState = postUIStates[postId] {
            return uiState.isLiked
        }
        
        // 🔑 修复：从帖子列表中查找并同步初始化真实状态
        if let post = posts.first(where: { $0.id == postId }) {
            // 🔑 关键修复：先同步检查已有的数据库状态，避免异步带来的显示延迟
            var newState = PostUIState(post: post)
            
            // 🔑 重构：检查当前用户是否在点赞列表中（使用ID检查）
            if let sessionManager = sessionManager,
               let currentUserId = sessionManager.currentUserID {
                // 🔑 修复：检查帖子的点赞关系中是否包含当前用户
                let isLikedByCurrentUser = post.likes.contains { like in
                    guard let likeUserId = like.user?.id else { return false }
                    return likeUserId == currentUserId && like.isActive
                }
                newState.isLiked = isLikedByCurrentUser
            } else {
                newState.isLiked = false
            }
            
            postUIStates[postId] = newState
            
            // 🔑 优化：异步验证和同步数据库状态（确保数据最新）
            Task { @MainActor in
                let realStatus = await getUserLikeStatus(for: post)
                if var state = postUIStates[postId] {
                    // 🔑 修复：只有当状态确实不同时才更新，避免不必要的UI刷新
                    if state.isLiked != realStatus {
                        state.isLiked = realStatus
                        postUIStates[postId] = state
                        
                        // 🔑 修复：发送通知更新UI，确保卡片显示正确状态
                        NotificationCenter.default.post(
                            name: .postLikeStatusDidChange,
                            object: postId,
                            userInfo: ["isLiked": realStatus, "newCount": state.likeCount]
                        )
                    }
                }
            }
            
            return newState.isLiked // 🔑 修复：返回同步检查的状态，而非默认false
        }
        
        return false
    }
    
    /// ✅ 优化：获取帖子点赞数量，增强数据一致性
    func getPostLikeCount(postId: UUID) -> Int {
        // 🔑 修复：优先从UI状态获取，确保数据一致性
        if let uiState = postUIStates[postId] {
            return uiState.likeCount
        }
        
        // 🔑 备用方案：从帖子列表中获取
        if let post = posts.first(where: { $0.id == postId }) {
            return post.likeCount
        }
        
        // 🔑 最后备用：从缓存中获取
        if let cachedPost = cachedPosts.first(where: { $0.id == postId }) {
            return cachedPost.likeCount
        }
        
        return 0
    }
    
    // MARK: - 数据加载方法（优化版）
    
    /// ✅ 优化：加载帖子列表，使用服务层
    func loadPosts(refresh: Bool = false) async {
        // ✅ 优化：防抖机制
        let now = Date()
        if !refresh && now.timeIntervalSince(lastRefreshTime) < 1.0 {
            return
        }
        lastRefreshTime = now
        
        // ✅ 优化：检查缓存有效性
        if !refresh && isCacheValid() {
            posts = cachedPosts
            return
        }
        
        guard let service = communityService else {
            errorMessage = "社区服务不可用"
            return
        }
        
        isLoading = true
        errorMessage = nil
        
        do {
            if refresh {
                currentPage = 0
                hasNextPage = true
            }
            
            // ✅ 使用优化的服务层方法
            let newPosts = try await service.fetchPosts(page: currentPage, pageSize: pageSize)
            
            if refresh {
                posts = newPosts
                cachedPosts = newPosts
                cacheTimestamp = Date()
            } else {
                posts.append(contentsOf: newPosts)
                cachedPosts.append(contentsOf: newPosts)
            }
            
            // ✅ 优化：更新UI状态
            updatePostUIStates(for: newPosts)
            
            hasNextPage = newPosts.count == pageSize
            if !refresh {
                currentPage += 1
            }
            
        } catch {
            errorMessage = "加载帖子失败：\(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    /// ✅ 新增：更新帖子UI状态
    private func updatePostUIStates(for posts: [EACommunityPost]) {
        for post in posts {
            if postUIStates[post.id] == nil {
                var newState = PostUIState(post: post)
                
                // 🔑 重构：在初始化时同步检查点赞状态（使用ID检查）
                if let sessionManager = sessionManager,
                   let currentUserId = sessionManager.currentUserID {
                    // 🔑 修复：检查帖子的点赞关系中是否包含当前用户
                    let isLikedByCurrentUser = post.likes.contains { like in
                        guard let likeUserId = like.user?.id else { return false }
                        return likeUserId == currentUserId && like.isActive
                    }
                    newState.isLiked = isLikedByCurrentUser
                } else {
                    newState.isLiked = false
                }
                
                postUIStates[post.id] = newState
            }
        }
    }
    
    /// 🔑 新增：批量初始化帖子点赞状态（用于页面重新加载）
    func initializePostLikeStates() async {
        guard let sessionManager = sessionManager,
              let _ = sessionManager.currentUserID else {
            return
        }
        
        for post in posts {
            if var state = postUIStates[post.id] {
                // 🔑 修复：异步获取准确的点赞状态
                let realStatus = await getUserLikeStatus(for: post)
                let realLikeCount = await getRealLikeCount(for: post)
                
                state.isLiked = realStatus
                state.likeCount = realLikeCount
                postUIStates[post.id] = state
            }
        }
        
        // 🔑 修复：发送全局状态更新通知
        NotificationCenter.default.post(
            name: .communityDataDidUpdate,
            object: nil,
            userInfo: ["type": "allPostStates"]
        )
    }
    
    /// ✅ 优化：检查缓存有效性
    private func isCacheValid() -> Bool {
        return !cachedPosts.isEmpty && 
               Date().timeIntervalSince(cacheTimestamp) < cacheValidDuration
    }
    
    /// 🚀 优化的缓存清理
    func clearCache() {
        cachedPosts.removeAll()
        cacheTimestamp = Date.distantPast
        // 保留关键的UI状态，只清理缓存数据
    }
    
    // MARK: - Initialization (移除重复定义)
    
    /// 🔑 修复：设置通知监听器，避免在View.body中处理
    private func setupNotificationObservers() {
        // 监听评论数量变化通知
        NotificationCenter.default.addObserver(
            forName: .commentCountDidChange,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            guard let self = self else { return }

            // 🔑 修复：添加防护机制和防抖，避免无限循环和频繁触发
            if let postId = notification.object as? UUID,
               let newCount = notification.userInfo?["newCount"] as? Int {

                // 🔑 Swift 6修复：在MainActor上下文中处理防抖逻辑
                Task { @MainActor in
                    // 防抖机制：避免频繁更新
                    let operationKey = "commentUpdate_\(postId)"
                    let now = Date()
                    if let lastTime = self.lastOperationTime[operationKey],
                       now.timeIntervalSince(lastTime) < 0.1 { // 100ms防抖
                        return
                    }
                    self.lastOperationTime[operationKey] = now

                    self.updatePostCommentCountSafely(postId: postId, newCount: newCount)
                }
            }
        }
    }
    
    /// 🔑 修复：安全的评论数量更新方法，添加防护机制
    private func updatePostCommentCountSafely(postId: UUID, newCount: Int) {
        // 防护：检查是否正在加载，避免在数据不稳定时更新
        guard !isLoading else {
            return
        }
        
        // 防护：检查Repository容器
        guard repositoryContainer != nil else {
            return
        }
        
        // 修复：更新UI状态
        if var state = postUIStates[postId] {
            state.commentCount = newCount
            postUIStates[postId] = state
        }
        
        // 修复：更新帖子列表中的数据
        if let index = posts.firstIndex(where: { $0.id == postId }) {
            posts[index].commentCount = newCount
        }
        
        // 修复：更新缓存数据
        if let index = cachedPosts.firstIndex(where: { $0.id == postId }) {
            cachedPosts[index].commentCount = newCount
        }
        
        // 🔑 关键修复：只触发UI更新，不再发送通知避免循环
        // objectWillChange.send() 已经通过@Published属性自动触发
    }
    
    // 移除重复的deinit定义
    
    // MARK: - 🚨 iPad真机修复：安全的数据加载方法
    
    /// 🔑 新增：初始数据加载方法 - 用于启动时的轻量级初始化
    func loadInitialData() async {
        // 🔑 轻量级初始化：只在必要时加载数据
        guard posts.isEmpty else { return }
        
        // 🔑 确保Repository容器可用
        guard repositoryContainer != nil else {
            await MainActor.run {
                errorMessage = "数据服务未初始化"
            }
            return
        }
        
        // 🔑 优化：执行轻量级数据加载
        await MainActor.run {
            loadPosts(forceRefresh: false)
        }
    }
    
    /// 🚨 iPad真机修复：安全的数据加载，添加错误处理和降级策略
    func loadPosts(forceRefresh: Bool = false) {
        // 🚨 关键修复：防止重复加载
        guard !isLoading else { 
            return 
        }
        
        // 🚨 关键修复：Repository容器检查
        guard repositoryContainer != nil else {
            errorMessage = "数据服务未初始化，请重试"
            return
        }
        
        // 🔑 性能优化：检查缓存有效性
        if !forceRefresh && isCacheValid() && !cachedPosts.isEmpty {
            posts = cachedPosts
            return
        }
        
        isLoading = true
        errorMessage = nil
        
        Task {
            do {
                let loadedPosts = try await fetchPostsFromRepository()
                
                await MainActor.run {
                    if forceRefresh || currentPage == 0 {
                        posts = loadedPosts
                    } else {
                        posts.append(contentsOf: loadedPosts)
                    }
                    
                    // 更新缓存
                    cachedPosts = posts
                    cacheTimestamp = Date()
                    
                    // 更新分页状态
                    hasNextPage = loadedPosts.count >= pageSize
                    if hasNextPage {
                        currentPage += 1
                    }
                    
                    isLoading = false
                }
                
                // 🔑 性能优化：异步初始化帖子状态
                await initializePostStates(for: loadedPosts)
                
            } catch {
                await MainActor.run {
                    errorMessage = "数据加载失败：\(error.localizedDescription)"
                    isLoading = false
                    
                    // 🚨 iPad真机修复：提供降级策略
                    if posts.isEmpty && !cachedPosts.isEmpty {
                        posts = cachedPosts
                    }
                }
            }
        }
    }
    
    /// 🚨 iPad真机修复：安全的帖子状态初始化
    private func initializePostStates(for posts: [EACommunityPost]) async {
        
        await withTaskGroup(of: Void.self) { group in
            for post in posts {
                group.addTask { [weak self] in
                    guard let self = self else { return }
                    
                    // 🔑 修复：检查是否已有状态，避免重复初始化
                    await MainActor.run {
                        if self.postUIStates[post.id] == nil {
                            var state = PostUIState(post: post)
                            state.likeCount = post.likeCount
                            state.commentCount = post.commentCount
                            self.postUIStates[post.id] = state
                        }
                    }
                    
                    // 🚨 iPad真机修复：移除不必要的do-catch，getUserLikeStatus不是throwing函数
                    // 🔑 修复：异步获取真实点赞状态
                    let isLiked = await self.getUserLikeStatus(for: post)
                    
                    await MainActor.run {
                        if var state = self.postUIStates[post.id] {
                            state.isLiked = isLiked
                            self.postUIStates[post.id] = state
                        }
                    }
                }
            }
        }
    }
    
    /// 🚨 iPad真机修复：安全的Repository数据获取
    private func fetchPostsFromRepository() async throws -> [EACommunityPost] {
        guard let container = repositoryContainer else {
            throw EACommunityViewModelError.repositoryNotAvailable
        }
        
        let repository = container.communityRepository
        
        // 🚨 iPad真机修复：添加超时机制
        return try await withThrowingTaskGroup(of: [EACommunityPost].self) { group in
            // 🔑 修复：在async闭包外部获取filterState的值
            let selectedCategory = filterState.selectedCategory
            let selectedTags = filterState.selectedTags
            let currentPageSize = pageSize
            let currentPageOffset = currentPage * pageSize
            
            group.addTask {
                // 构建查询参数
                var filters: [String: Any] = [:]
                if let category = selectedCategory {
                    filters["category"] = category
                }
                if !selectedTags.isEmpty {
                    filters["tags"] = selectedTags
                }
                
                return try await repository.fetchPosts(limit: currentPageSize, offset: currentPageOffset)
            }
            
            // 🚨 iPad真机修复：添加5秒超时
            group.addTask {
                try await Task.sleep(nanoseconds: 5_000_000_000) // 5秒
                throw EACommunityViewModelError.operationFailed("请求超时")
            }
            
            // 返回第一个完成的任务结果
            for try await result in group {
                group.cancelAll()
                return result
            }
            
            throw EACommunityViewModelError.operationFailed("请求失败")
        }
    }
    
    /// 🚀 微信朋友圈级别：智能帖子出现处理
    func handlePostAppearLightweight(post: EACommunityPost) {
        // 记录可见状态
        postUIStates[post.id]?.isVisible = true
        
        // 🚀 微信朋友圈级别优化：智能预加载逻辑
        if let postIndex = posts.firstIndex(where: { $0.id == post.id }) {
            let remainingPosts = posts.count - postIndex - 1
            
            // 当接近底部时触发预加载
            if remainingPosts <= preloadThreshold && hasNextPage && !isLoading {
                Task {
                    // 添加延迟，避免频繁触发
                    try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒延迟
                    await MainActor.run {
                        loadMorePostsIfNeeded()
                    }
                }
            }
            
            // 🚀 性能监控：记录滚动位置
            scrollPerformanceMetrics["currentPosition"] = postIndex
            scrollPerformanceMetrics["totalPosts"] = posts.count
            scrollPerformanceMetrics["scrollProgress"] = Double(postIndex) / Double(posts.count)
        }
        
        // 🚀 内存管理：当缓存超过限制时清理旧数据
        if cachedPosts.count > maxCachedPosts {
            cleanupOldCachedPosts()
        }
    }
    
    /// 🚀 内存优化：清理旧的缓存帖子
    private func cleanupOldCachedPosts() {
        // 保留最近的帖子，清理较旧的
        let keepCount = maxCachedPosts * 3 / 4 // 保留75%
        if cachedPosts.count > keepCount {
            cachedPosts = Array(cachedPosts.suffix(keepCount))
        }
        
        // 清理不可见的UI状态
        let visiblePostIds = Set(posts.map { $0.id })
        postUIStates = postUIStates.filter { visiblePostIds.contains($0.key) }
    }
    
    /// 加载更多帖子
    func loadMorePostsIfNeeded() {
        guard hasNextPage && !isLoading else { return }
        Task {
            await loadPosts()
        }
    }
    
    // MARK: - 🔑 性能优化：简化的交互方法
    
    /// 🔑 点赞切换（处理点赞和取消点赞）
    func toggleLike(for post: EACommunityPost) {
        // 🔑 重构：操作防护检查（使用ID检查）
        guard let sessionManager = sessionManager,
              sessionManager.currentUserID != nil,
              let service = communityService else {
            errorMessage = "用户未登录或服务不可用"
            return
        }
        
        // 🔑 修复：防抖机制，避免快速重复点击
        let operationKey = "toggleLike_\(post.id)"
        let now = Date()
        if let lastTime = lastOperationTime[operationKey],
           now.timeIntervalSince(lastTime) < operationDebounceInterval {
            return
        }
        lastOperationTime[operationKey] = now
        
        // 🔑 修复：获取当前准确的点赞状态
        let currentIsLiked = getPostLikeStatus(postId: post.id)
        let currentLikeCount = getPostLikeCount(postId: post.id)
        
        // 🔑 修复：计算新状态
        let newIsLiked = !currentIsLiked
        let newLikeCount = newIsLiked ? currentLikeCount + 1 : max(0, currentLikeCount - 1)
        
        // 🔑 修复：立即更新UI状态（乐观更新）
        updatePostLikeStatus(postId: post.id, isLiked: newIsLiked, newLikeCount: newLikeCount)
        
        // 🔑 修复：异步执行数据库操作
        Task { @MainActor in
            do {
                if newIsLiked {
                    // 执行点赞
                    try await service.likePost(post.id)
                } else {
                    // 执行取消点赞
                    try await service.unlikePost(post.id)
                }
                
                // 🔑 修复：操作成功后，验证最终状态
                let verifyStatus = await getUserLikeStatus(for: post)
                let verifyCount = await getRealLikeCount(for: post)
                
                // 🔑 修复：如果验证状态与预期不符，回滚UI
                if verifyStatus != newIsLiked || verifyCount != newLikeCount {
                    updatePostLikeStatus(postId: post.id, isLiked: verifyStatus, newLikeCount: verifyCount)
                }
                
            } catch {
                // 🔑 修复：操作失败时回滚UI状态
                updatePostLikeStatus(postId: post.id, isLiked: currentIsLiked, newLikeCount: currentLikeCount)
                errorMessage = "点赞操作失败：\(error.localizedDescription)"
            }
        }
    }
    
    /// 删除帖子
    func deletePost(_ post: EACommunityPost) async {
        guard let container = repositoryContainer else { return }
        
        do {
            let repository = container.communityRepository
            try await repository.deletePost(id: post.id)
            
            await MainActor.run {
                posts.removeAll { $0.id == post.id }
                postUIStates.removeValue(forKey: post.id)
                
                // 清理缓存
                cachedPosts.removeAll { $0.id == post.id }
            }
        } catch {
            await MainActor.run {
                errorMessage = "删除失败：\(error.localizedDescription)"
            }
        }
    }
    
    /// 分享帖子
    func sharePost(_ post: EACommunityPost) {
        shareService?.sharePost(post)
    }
    
    /// 创建新帖子
    func createPost(content: String, imagePaths: [String], category: String?, tags: [String]) async throws {
        guard let communityService = communityService else {
            throw CommunityError.userNotLoggedIn
        }
        
        guard !content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw CommunityError.invalidContent
        }
        
        do {
            // 创建新帖子
            let newPost = EACommunityPost(
                title: "我的生活分享", // 默认标题，可以基于内容优化
                content: content,
                habitName: nil,
                category: category ?? "日常分享",
                energyLevel: 5
            )
            
            // 设置图片路径和标签
            newPost.imageURLs = imagePaths
            newPost.tags = tags
            
            // ✅ 修复：使用CommunityService而不是直接调用Repository（包含能量奖励）
            let createdPost = try await communityService.createPost(
                content: content,
                habitName: nil,
                imageURLs: imagePaths
            )
            
            await MainActor.run {
                // 将新帖子添加到列表顶部
                posts.insert(createdPost, at: 0)
                
                // 初始化UI状态
                postUIStates[createdPost.id] = PostUIState(post: createdPost)
                
                // 清理缓存，确保数据一致性
                clearCache()
            }
            
        } catch CommunityError.userNotLoggedIn {
            await MainActor.run {
                errorMessage = "请先登录后再发布帖子"
            }
            throw CommunityError.userNotLoggedIn
        } catch {
            await MainActor.run {
                errorMessage = "发布失败：\(error.localizedDescription)"
            }
            throw error
        }
    }
    
    /// 搜索帖子
    func searchPosts(with query: String) {
        guard !query.isEmpty else {
            Task {
                loadPosts(forceRefresh: true)
            }
            return
        }
        
        // 🔑 性能优化：简化搜索逻辑
        Task {
            do {
                guard let container = repositoryContainer else { return }
                
                let repository = container.communityRepository
                let searchResults = try await repository.searchPosts(query: query, limit: 50)
                
                await MainActor.run {
                    posts = searchResults
                    hasNextPage = false
                    currentPage = 0
                }
                
            } catch {
                await MainActor.run {
                    errorMessage = "搜索失败：\(error.localizedDescription)"
                }
            }
        }
    }
    
    // MARK: - 🔑 性能优化：简化的筛选方法
    
    /// 应用筛选
    func applyFilters(category: String?, tags: [String]) {
        filterState.selectedCategory = category
        filterState.selectedTags = tags
        filterState.isFilterActive = category != nil || !tags.isEmpty
        
        // 重置分页并重新加载
        currentPage = 0
        hasNextPage = true
        clearCache()
        Task {
            loadPosts(forceRefresh: true)
        }
    }
    
    /// 清除筛选
    func clearFilters() {
        filterState.selectedCategory = nil
        filterState.selectedTags = []
        filterState.isFilterActive = false
        
        // 重置分页并重新加载
        currentPage = 0
        hasNextPage = true
        clearCache()
        Task {
            loadPosts(forceRefresh: true)
        }
    }
    
    // MARK: - 🔑 性能优化：简化的生命周期管理
    
    /// 优化的Tab切换到活跃状态
    func handleTabSwitchToActiveOptimized() async {
        isViewActive = true
        
        // 🔑 性能优化：只在必要时刷新数据
        if posts.isEmpty || !isCacheValid() {
            Task {
                loadPosts(forceRefresh: true)
            }
        }
    }
    
    /// 优化的Tab切换离开状态
    func handleTabSwitchAwayOptimized() async {
        isViewActive = false
        // 🔑 性能优化：保留数据和状态，不清理缓存和UI状态
        // 保持postUIStates不被清理，确保状态持久化
    }
    
    /// 🔑 根本性修复：页面重新激活时的状态恢复
    func handleTabSwitchBackOptimized() async {
        isViewActive = true
        

        
        // 🔑 修复：重新获取所有帖子的真实状态（点赞状态 + 评论数量）
        await withTaskGroup(of: Void.self) { group in
            for post in posts {
                group.addTask { [weak self] in
                    guard let self = self else { return }
                    
                    // 🔑 修复：并发获取真实的点赞状态和评论数量
                    async let realLikeStatus = self.getUserLikeStatus(for: post)
                    async let realCommentCount = self.getRealCommentCount(for: post)
                    
                    let (likeStatus, commentCount) = await (realLikeStatus, realCommentCount)
                    
                    await MainActor.run {
                        // 更新UI状态
                        if var state = self.postUIStates[post.id] {
                            let statusChanged = state.isLiked != likeStatus
                            let likeCountChanged = state.likeCount != post.likeCount
                            let commentCountChanged = state.commentCount != commentCount
                            
                            if statusChanged || likeCountChanged || commentCountChanged {
                                state.isLiked = likeStatus
                                state.likeCount = post.likeCount
                                state.commentCount = commentCount  // 🔑 修复：使用真实的评论数量
                                self.postUIStates[post.id] = state
                                
                                // 🔑 修复：同步更新帖子数据
                                if let index = self.posts.firstIndex(where: { $0.id == post.id }) {
                                    self.posts[index].commentCount = commentCount
                                }
                                
                                // 🔑 修复：同步更新缓存数据
                                if let index = self.cachedPosts.firstIndex(where: { $0.id == post.id }) {
                                    self.cachedPosts[index].commentCount = commentCount
                                }
                                
                                // 🔑 修复：发送通知更新UI
                                if statusChanged {
                                    NotificationCenter.default.post(
                                        name: .postLikeStatusDidChange,
                                        object: post.id,
                                        userInfo: ["isLiked": likeStatus, "newCount": state.likeCount]
                                    )
                                }
                                
                                if commentCountChanged {
                                    NotificationCenter.default.post(
                                        name: .commentCountDidChange,
                                        object: post.id,
                                        userInfo: ["newCount": commentCount]
                                    )
                                }
                                
                                
                            }
                        } else {
                            // 🔑 修复：如果没有UI状态，创建新的状态
                            var newState = PostUIState(post: post)
                            newState.isLiked = likeStatus
                            newState.commentCount = commentCount  // 🔑 修复：使用真实的评论数量
                            self.postUIStates[post.id] = newState
                            
                            // 🔑 修复：同步更新帖子数据
                            if let index = self.posts.firstIndex(where: { $0.id == post.id }) {
                                self.posts[index].commentCount = commentCount
                            }
                            
                            
                        }
                    }
                }
            }
        }
        

        
        // 🔑 新增：发送状态恢复完成通知，让UI刷新显示
        NotificationCenter.default.post(
            name: .communityStateRestoreCompleted,
            object: nil,
            userInfo: ["restoredPostsCount": posts.count]
        )
    }
    
    /// 处理帖子删除通知
    func handlePostDeleted(_ postId: UUID) async {
        await MainActor.run {
            posts.removeAll { $0.id == postId }
            postUIStates.removeValue(forKey: postId)
            cachedPosts.removeAll { $0.id == postId }
        }
    }
    
    // MARK: - 🔑 性能优化：辅助方法
    
    /// 获取当前用户ID
    private func getCurrentUserId() throws -> UUID {
        guard let sessionManager = sessionManager else {
            throw CommunityError.userNotLoggedIn
        }

        guard let currentUserID = sessionManager.currentUserID else {
            throw CommunityError.userNotLoggedIn
        }

        return currentUserID
    }
    
    /// 获取用户点赞状态 - 🔑 根本性修复：从数据库获取真实状态
    private func getUserLikeStatus(for post: EACommunityPost) async -> Bool {
        do {
            // 🔑 重构：使用Repository获取真实的点赞状态（使用ID检查）
            guard let container = repositoryContainer,
                  let sessionManager = sessionManager,
                  let currentUserId = sessionManager.currentUserID else {
                return false
            }
            
            let repository = container.communityRepository
            let likes = try await repository.fetchLikes(for: post.id)
            
            // 🔑 修复：检查当前用户是否真的点赞了这个帖子
            let isLiked = likes.contains { like in
                guard let likeUserId = like.user?.id else { return false }
                return likeUserId == currentUserId && like.isActive
            }
            

            
            return isLiked
        } catch {
            return false
        }
    }
    
    /// 🔑 新增：获取真实点赞数量 - 从数据库获取真实数据
    private func getRealLikeCount(for post: EACommunityPost) async -> Int {
        do {
            // 🔑 修复：使用Repository获取真实的点赞数量
            guard let container = repositoryContainer else {
                return 0
            }
            
            let repository = container.communityRepository
            let likes = try await repository.fetchLikes(for: post.id)
            
            // 🔑 修复：只计算isActive为true的点赞
            let realCount = likes.filter { $0.isActive }.count
            
            return realCount
        } catch {
            return 0
        }
    }
    
    /// 🔑 新增：获取真实评论数量 - 从数据库获取真实数据
    private func getRealCommentCount(for post: EACommunityPost) async -> Int {
        do {
            // 🔑 修复：使用Repository获取真实的评论数量
            guard let container = repositoryContainer else {
                return 0
            }
            
            let repository = container.communityRepository
            let comments = try await repository.fetchComments(for: post.id)
            
            // 🔑 修复：返回真实的评论数量
            let realCount = comments.count
            

            
            return realCount
        } catch {
            return 0
        }
    }
    
    // 🔑 修复：原updatePostCommentCount方法已删除，避免无限循环
    // 现在使用updatePostCommentCountSafely方法，该方法不会发送通知，避免循环
    
    /// 🔑 修复：更新帖子点赞状态和数量
    func updatePostLikeStatus(postId: UUID, isLiked: Bool, newLikeCount: Int) {
        // 更新UI状态
        if var uiState = postUIStates[postId] {
            uiState.isLiked = isLiked
            uiState.likeCount = newLikeCount
            postUIStates[postId] = uiState
        }
        
        // 更新帖子列表中的数据
        if let index = posts.firstIndex(where: { $0.id == postId }) {
            posts[index].likeCount = newLikeCount
        }
        
        // 更新缓存
        if let index = cachedPosts.firstIndex(where: { $0.id == postId }) {
            cachedPosts[index].likeCount = newLikeCount
        }
        
        // 发送通知，确保其他页面也能收到更新
        NotificationCenter.default.post(
            name: .postLikeStatusDidChange,
            object: postId,
            userInfo: ["isLiked": isLiked, "newCount": newLikeCount]
        )
    }
    
    /// 🔑 修复：获取帖子评论数量
    func getPostCommentCount(postId: UUID) -> Int {
        // 🔑 修复：优先从UI状态获取，确保数据一致性
        if let uiState = postUIStates[postId] {
            return uiState.commentCount
        }
        
        // 🔑 备用方案：从帖子列表中获取
        if let post = posts.first(where: { $0.id == postId }) {
            return post.commentCount
        }
        
        // 🔑 最后备用：从缓存中获取
        if let cachedPost = cachedPosts.first(where: { $0.id == postId }) {
            return cachedPost.commentCount
        }
        
        return 0
    }
    
    // MARK: - 🚀 微信朋友圈级别的内存管理优化
    
    /// 🚀 智能内存警告处理
    func handleMemoryWarning() {
        // 🚀 优先级1：清理图片缓存
        NotificationCenter.default.post(
            name: NSNotification.Name("EAMemoryPressureWarning"),
            object: nil
        )
        
        // 🚀 优先级2：清理帖子缓存，但保留当前可见的
        intelligentCacheCleaning()
        
        // 🚀 优先级3：清理非关键UI状态
        cleanupNonCriticalUIStates()
        
        // 🚀 优先级4：清理性能监控数据
        scrollPerformanceMetrics.removeAll()
        lastOperationTime.removeAll()
    }
    
    /// 🚀 智能缓存清理
    private func intelligentCacheCleaning() {
        // 保留当前页面的帖子和前后各一页的帖子
        let currentPageStart = currentPage * pageSize
        let keepStart = max(0, currentPageStart - pageSize)
        let keepEnd = min(posts.count, currentPageStart + pageSize * 2)
        
        if keepEnd > keepStart {
            let postsToKeep = Array(posts[keepStart..<keepEnd])
            cachedPosts = postsToKeep
        } else {
            cachedPosts.removeAll()
        }
        
        cacheTimestamp = Date.distantPast // 强制下次重新加载
    }
    
    /// 🚀 清理非关键UI状态
    private func cleanupNonCriticalUIStates() {
        // 只保留当前可见帖子的UI状态
        let visiblePostIds = Set(posts.prefix(pageSize * 2).map { $0.id })
        postUIStates = postUIStates.filter { visiblePostIds.contains($0.key) && $0.value.isVisible }
    }
    
    // MARK: - 🔑 新增：页面生命周期管理
    
    /// 🔑 核心方法：页面即将出现时调用，确保状态同步
    func viewWillAppear() {
        isViewActive = true
        
        // 🔑 关键：页面返回时刷新所有帖子的状态
        Task { @MainActor in
            await refreshAllPostStates()
        }
    }
    
    /// 🔑 核心方法：页面即将消失时调用
    func viewWillDisappear() {
        isViewActive = false
    }
    
    /// 🔑 核心方法：刷新所有帖子的状态，确保数据同步
    func refreshAllPostStates() async {
        guard let sessionManager = sessionManager,
              let _ = sessionManager.currentUserID else {
            return
        }
        
        #if DEBUG
        print("🔄 EACommunityViewModel: 开始刷新所有帖子状态...")
        #endif
        
        var stateChanges: [(UUID, Bool, Int)] = []
        
        // 🔑 关键：批量检查所有帖子的真实状态
        for post in posts {
            let realIsLiked = await getUserLikeStatus(for: post)
            let realLikeCount = await getRealLikeCount(for: post)
            
            // 检查状态是否需要更新
            let currentIsLiked = getPostLikeStatus(postId: post.id)
            let currentLikeCount = getPostLikeCount(postId: post.id)
            
            if realIsLiked != currentIsLiked || realLikeCount != currentLikeCount {
                stateChanges.append((post.id, realIsLiked, realLikeCount))
            }
        }
        
        // 🔑 关键：批量更新所有变化的状态
        await MainActor.run {
            for (postId, isLiked, likeCount) in stateChanges {
                // 更新UI状态
                if var state = postUIStates[postId] {
                    state.isLiked = isLiked
                    state.likeCount = likeCount
                    postUIStates[postId] = state
                } else {
                    if let post = posts.first(where: { $0.id == postId }) {
                        var newState = PostUIState(post: post)
                        newState.isLiked = isLiked
                        newState.likeCount = likeCount
                        postUIStates[postId] = newState
                    }
                }
                
                // 更新帖子列表中的数据
                if let postIndex = posts.firstIndex(where: { $0.id == postId }) {
                    posts[postIndex].likeCount = likeCount
                }
                
                // 更新缓存中的数据
                if let cachedIndex = cachedPosts.firstIndex(where: { $0.id == postId }) {
                    cachedPosts[cachedIndex].likeCount = likeCount
                }
                
                // 🔑 关键：发送通知确保UI组件收到更新
                NotificationCenter.default.post(
                    name: .postLikeStatusDidChange,
                    object: postId,
                    userInfo: [
                        "isLiked": isLiked,
                        "newCount": likeCount,
                        "source": "communityViewRefresh"
                    ]
                )
            }
            
            #if DEBUG
            if !stateChanges.isEmpty {
                print("✅ EACommunityViewModel: 刷新完成，更新了 \(stateChanges.count) 个帖子的状态")
            } else {
                print("✅ EACommunityViewModel: 刷新完成，所有状态都是最新的")
            }
            #endif
        }
    }
    
    /// 🔑 新增：强制刷新特定帖子的状态（用于详情页返回时调用）
    func refreshPostState(postId: UUID) async {
        guard let post = posts.first(where: { $0.id == postId }) else {
            return
        }
        
        let realIsLiked = await getUserLikeStatus(for: post)
        let realLikeCount = await getRealLikeCount(for: post)
        
        await MainActor.run {
            // 更新UI状态
            if var state = postUIStates[postId] {
                state.isLiked = realIsLiked
                state.likeCount = realLikeCount
                postUIStates[postId] = state
            } else {
                var newState = PostUIState(post: post)
                newState.isLiked = realIsLiked
                newState.likeCount = realLikeCount
                postUIStates[postId] = newState
            }
            
            // 更新帖子列表中的数据
            if let postIndex = posts.firstIndex(where: { $0.id == postId }) {
                posts[postIndex].likeCount = realLikeCount
            }
            
            // 发送通知确保UI组件收到更新
            NotificationCenter.default.post(
                name: .postLikeStatusDidChange,
                object: postId,
                userInfo: [
                    "isLiked": realIsLiked,
                    "newCount": realLikeCount,
                    "source": "singlePostRefresh"
                ]
            )
            
            #if DEBUG
            print("✅ EACommunityViewModel: 单个帖子状态刷新完成 - 帖子ID: \(postId), 点赞: \(realIsLiked), 数量: \(realLikeCount)")
            #endif
        }
    }
    
    /// 更新帖子评论数量（公开方法）
    func updatePostCommentCount(postId: UUID, newCount: Int) {
        updatePostCommentCountSafely(postId: postId, newCount: newCount)
    }

    // MARK: - 🔑 新增：用户Profile预加载方法

    /// 🔑 核心方法：确保用户Profile就绪（幂等操作）
    /// 在用户访问好友功能前调用，确保基础数据完整性
    func ensureUserProfileReady() async {
        // 如果已经就绪，直接返回
        if isUserProfileReady {
            return
        }

        // 检查用户是否已登录
        guard let sessionManager = self.sessionManager,
              sessionManager.isLoggedIn,
              sessionManager.currentUserID != nil else {
            await MainActor.run {
                userProfileLoadingError = "用户未登录"
            }
            return
        }

        // 检查好友服务是否可用
        guard let friendshipService = self.friendshipService else {
            await MainActor.run {
                userProfileLoadingError = "好友服务不可用"
            }
            return
        }

        // 执行用户数据验证
        let isValid = await friendshipService.validateAndRepairUserData()

        await MainActor.run {
            if isValid {
                isUserProfileReady = true
                userProfileLoadingError = nil
            } else {
                userProfileLoadingError = "用户数据验证失败"
            }
        }
    }

    /// 🔑 重置用户Profile状态（用于登录状态变化时）
    func resetUserProfileState() {
        isUserProfileReady = false
        userProfileLoadingError = nil
    }

}

// MARK: - 错误类型定义

enum CommunityError: LocalizedError {
    case userNotLoggedIn
    case invalidContent
    case networkError
    case dataCorruption
    case dataNotFound
    case insufficientPermissions

    var errorDescription: String? {
        switch self {
        case .userNotLoggedIn:
            return "用户未登录"
        case .invalidContent:
            return "内容格式无效"
        case .networkError:
            return "网络连接错误"
        case .dataCorruption:
            return "数据损坏"
        case .dataNotFound:
            return "数据未找到"
        case .insufficientPermissions:
            return "权限不足"
        }
    }
}

// MARK: - 通知扩展

extension Notification.Name {
    /// 评论数量变化通知
    static let commentCountDidChange = Notification.Name("commentCountDidChange")

    /// 帖子点赞状态变化通知
    static let postLikeStatusDidChange = Notification.Name("postLikeStatusDidChange")

    /// 帖子分享成功通知
    static let postShareDidSuccess = Notification.Name("postShareDidSuccess")

    /// 社区状态恢复完成通知
    static let communityStateRestoreCompleted = Notification.Name("communityStateRestoreCompleted")

    /// 社区数据更新通知
    static let communityDataDidUpdate = Notification.Name("communityDataDidUpdate")

    /// 好友关系状态变化通知
    static let friendshipStatusChanged = Notification.Name("friendshipStatusChanged")

    /// 屏蔽状态变化通知
    static let blockingStatusChanged = Notification.Name("blockingStatusChanged")
}
