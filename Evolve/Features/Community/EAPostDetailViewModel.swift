import SwiftUI
import SwiftData

/// 帖子详情页面ViewModel
/// 处理评论加载、点赞交互、评论提交和帖子操作
@MainActor
class EAPostDetailViewModel: ObservableObject {
    
    // MARK: - Properties
    
    /// 帖子数据
    let post: EACommunityPost
    
    /// Repository容器（强制依赖注入）
    private let repositoryContainer: EARepositoryContainer

    /// ✅ 修复：添加sessionManager依赖注入（可变，支持运行时设置）
    private var sessionManager: EASessionManager
    
    /// ✅ 新增：社区服务（包含能量奖励功能）
    private var communityService: EACommunityService?
    
    /// 评论列表
    @Published var comments: [EACommunityComment] = []
    
    /// 评论输入文本
    @Published var commentText: String = ""
    
    /// 回复目标评论
    @Published var replyTargetComment: EACommunityComment?
    
    /// UI状态
    @Published var isLoadingComments: Bool = false
    @Published var isSubmittingComment: Bool = false
    @Published var showAlert: Bool = false
    @Published var alertMessage: String?
    @Published var showDeleteDialog: Bool = false
    @Published var shouldDismiss: Bool = false  // 🔑 新增：控制页面关闭
    
    /// 错误恢复状态
    @Published var loadingError: String?
    @Published var canRetry: Bool = false
    
    /// 评论排序方式
    @Published var commentSortOrder: CommentSortOrder = .newest
    
    /// 当前用户缓存
    private var currentUser: EAUser?
    
    // MARK: - 🚨 崩溃修复：异步任务和资源管理
    
    /// 🔑 新增：异步任务管理
    private var activeTasks: Set<Task<Void, Never>> = []
    
    /// 🔑 新增：页面是否已释放的标记
    private var isViewReleased: Bool = false
    
    /// 🔑 新增：通知观察者管理
    private var notificationObservers: [NSObjectProtocol] = []
    
    // MARK: - Initialization
    
    /// 删除成功回调
    var onPostDeleted: (() -> Void)?

    /// ✅ 修复：强制Repository模式，移除ModelContext依赖
    init(post: EACommunityPost, sessionManager: EASessionManager, repositoryContainer: EARepositoryContainer, onPostDeleted: (() -> Void)? = nil) {
        self.post = post
        self.sessionManager = sessionManager
        self.repositoryContainer = repositoryContainer
        self.onPostDeleted = onPostDeleted
        
        // ✅ 新增：初始化社区服务（包含能量奖励功能）
        self.communityService = EACommunityService(
            repository: repositoryContainer.communityRepository,
            sessionManager: sessionManager
        )
        
        // ✅ 修复：初始化并设置星际能量服务（解决能量奖励不生效问题）
        let stellarEnergyService = EAStellarEnergyService(repositoryContainer: repositoryContainer)
        self.communityService?.setStellarEnergyService(stellarEnergyService)

    }
    
    /// 🔑 崩溃修复：完善的析构函数，确保资源正确释放
    deinit {
        // 🔑 第一步：标记页面已释放
        isViewReleased = true
        
        // 🔑 第二步：取消所有活跃的异步任务
        for task in activeTasks {
            task.cancel()
        }
        activeTasks.removeAll()
        
        // 🔑 第三步：移除所有通知观察者
        for observer in notificationObservers {
            NotificationCenter.default.removeObserver(observer)
        }
        notificationObservers.removeAll()

    }
    
    // MARK: - 🔑 崩溃修复：安全的异步任务管理
    
    /// 创建并管理异步任务
    private func createManagedTask(_ operation: @escaping () async -> Void) {
        let task = Task { @MainActor in
            // 检查页面是否仍然活跃
            guard !isViewReleased else { return }
            await operation()
        }
        activeTasks.insert(task)
        
        // 任务完成后自动清理
        Task { @MainActor in
            await task.value
            activeTasks.remove(task)
        }
    }
    
    /// 🔑 新增：页面即将关闭时的清理方法
    func prepareForDismissal() {
        shouldDismiss = true
        
        // 取消所有进行中的异步任务
        for task in activeTasks {
            task.cancel()
        }
        
        // 清理状态
        isSubmittingComment = false
        isLoadingComments = false

    }
    
    // MARK: - Public Methods
    
    /// 加载评论列表 - 🔑 崩溃修复：使用安全的异步任务管理
    func loadComments() {
        guard !isViewReleased else { return }
        
        isLoadingComments = true
        
        // 🔑 使用安全的异步任务管理
        createManagedTask {
            await self.loadCommentsWithRepository(self.repositoryContainer)
        }

        // 🔑 新增：同时刷新用户状态，确保UI正确显示
        refreshUserState()
    }

    /// 🔑 新增：刷新用户状态
    func refreshUserState() {
        guard !isViewReleased else { return }
        
        createManagedTask {
            do {
                // 尝试获取当前用户，这会触发会话恢复
                let _ = try await self.getCurrentUser()

                // 触发UI更新
                await MainActor.run {
                    self.objectWillChange.send()
                }

                #if DEBUG
                #endif
            } catch {
                #if DEBUG
                #endif
            }
        }
    }

    /// 🔑 新增：重试加载评论
    func retryLoadComments() {
        guard !isViewReleased else { return }
        
        loadingError = nil
        canRetry = false
        loadComments()
    }
    
    /// 使用Repository加载评论 - 🔑 崩溃修复：添加页面状态检查
    private func loadCommentsWithRepository(_ container: EARepositoryContainer) async {
        // 🔑 检查页面是否仍然活跃
        guard !isViewReleased, !shouldDismiss else {
            return
        }
        
        do {
            let fetchedComments = try await container.communityRepository.fetchComments(for: post.id)
            
            // 🔑 再次检查页面状态
            guard !isViewReleased, !shouldDismiss else {
                #if DEBUG
#if DEBUG
                print("⚠️ [评论加载] 页面状态已变更，取消UI更新")
                #endif
                #endif
                return
            }
            
            await MainActor.run {
                self.comments = fetchedComments
                self.isLoadingComments = false
                self.sortComments()
            }
        } catch {
            await MainActor.run {
                self.handleLoadingError("加载评论失败：\(error.localizedDescription)")
            }
        }
    }
    
    /// 提交评论 - 🔑 崩溃修复：使用安全的异步任务管理
    func submitComment() async {
        guard canSubmitComment, !isViewReleased else { return }

        // 🔑 修复：设置提交状态，防止重复提交
        await MainActor.run {
            isSubmittingComment = true
        }

        // 🔑 使用安全的异步任务管理
        createManagedTask {
            await self.performCommentSubmission()
        }
    }
    
    /// 执行评论提交的核心逻辑（增强版）
    private func performCommentSubmission() async {
        do {
            // 🔑 第一步：获取当前用户（使用增强版认证检查）
            _ = try await getCurrentUser()

            // 🔑 第二步：验证评论内容
            let trimmedContent = commentText.trimmingCharacters(in: .whitespacesAndNewlines)
            guard !trimmedContent.isEmpty else {
                throw NSError(domain: "CommunityError", code: 2, userInfo: [NSLocalizedDescriptionKey: "评论内容不能为空"])
            }

            guard trimmedContent.count <= 500 else {
                throw NSError(domain: "CommunityError", code: 3, userInfo: [NSLocalizedDescriptionKey: "评论内容不能超过500字"])
            }

            // 🔑 第三步：验证帖子状态
            guard post.isVisible else {
                throw NSError(domain: "CommunityError", code: 4, userInfo: [NSLocalizedDescriptionKey: "该帖子已不可用"])
            }

            #if DEBUG
            #endif

            // ✅ 修复：使用CommunityService而不是直接调用Repository（包含能量奖励）
            guard let communityService = self.communityService else {
                throw NSError(domain: "CommunityError", code: 5, userInfo: [NSLocalizedDescriptionKey: "社区服务不可用"])
            }
            
            let newComment = try await communityService.addComment(
                to: post,
                content: trimmedContent,
                replyToUsername: replyTargetComment?.getAuthorUsername()
            )

            // 🔑 第五步：更新UI状态
            await updateUIAfterSubmission(comment: newComment)

            #if DEBUG
            #endif

        } catch {
            await MainActor.run {
                self.handleSubmissionError(error)
            }
        }
    }
    
    // 🔑 修复：移除不再需要的辅助方法，简化代码结构
    
    /// 更新UI状态 - 🔑 根本性修复：添加页面状态检查，避免崩溃
    private func updateUIAfterSubmission(comment: EACommunityComment) async {
        // 🔑 修复：检查页面是否仍然活跃，避免在页面关闭时操作UI
        guard !shouldDismiss else {
            return
        }
        
        await MainActor.run {
            // 🔑 修复：再次检查页面状态，确保安全
            guard !self.shouldDismiss else {
                return
            }
            
            // 添加到本地列表顶部
            self.comments.insert(comment, at: 0)

            // 🔑 关键修复：使用当前评论列表的真实数量，避免重复计数
            // Repository已经调用了syncCommentCount()更新了帖子的commentCount字段
            // 所以我们直接使用当前评论列表的数量作为真实数量
            let newCommentCount = self.comments.count
            
            // 🔑 修复：使用安全的通知发送机制
            self.sendCommentCountUpdateNotification(postId: self.post.id, newCount: newCommentCount)

            // 清空输入框和回复状态
            self.commentText = ""
            self.replyTargetComment = nil
            self.isSubmittingComment = false

        }
    }
    
    /// 完全重写安全的通知发送机制
    private func sendCommentCountUpdateNotification(postId: UUID, newCount: Int) {
        // 第一步：立即检查页面状态
        guard !isViewReleased, !shouldDismiss else {
            return
        }
        
        // 第二步：使用弱引用避免循环引用
        weak var weakSelf = self
        
        // 第三步：创建可取消的延迟任务
        let notificationTask = Task { @MainActor in
            // 延迟0.1秒，确保UI更新完成
            try? await Task.sleep(nanoseconds: 100_000_000)
            
            // 再次检查页面状态和弱引用
            guard let strongSelf = weakSelf,
                  !strongSelf.isViewReleased,
                  !strongSelf.shouldDismiss else {
                return
            }
            
            // 第四步：发送主要通知
            NotificationCenter.default.post(
                name: .commentCountDidChange,
                object: postId,
                userInfo: [
                    "newCount": newCount,
                    "timestamp": Date().timeIntervalSince1970
                ]
            )
            
            // 第五步：发送确认通知，确保社区页面收到
            NotificationCenter.default.post(
                name: .communityDataDidUpdate,
                object: postId,
                userInfo: [
                    "type": "commentCount",
                    "newCount": newCount,
                    "source": "postDetail",
                    "timestamp": Date().timeIntervalSince1970
                ]
            )
        }
        
        // 第六步：将任务添加到管理集合中
        activeTasks.insert(notificationTask)
        
        // 第七步：任务完成后自动清理
        Task { @MainActor in
            await notificationTask.value
            activeTasks.remove(notificationTask)
        }
    }
    
    /// 处理提交错误（增强版错误处理）
    private func handleSubmissionError(_ error: Error) {
        isSubmittingComment = false

        // 🔑 改进：根据错误类型提供更详细的提示
        if let nsError = error as NSError? {
            if nsError.domain == "CommunityError" && nsError.code == 1 {
                showError("登录状态已过期，请重新登录后再试")
            } else if nsError.localizedDescription.contains("网络") {
                showError("网络连接异常，请检查网络后重试")
            } else if nsError.localizedDescription.contains("内容") {
                showError("评论内容不符合要求，请修改后重试")
            } else if error is CommunityError {
                showError("评论提交失败：\(error.localizedDescription)")
            } else {
                showError("评论提交失败，请稍后重试")
            }
        } else {
            showError("评论提交失败，请稍后重试")
        }

        #if DEBUG
        #endif
    }
    
    /// 切换帖子点赞状态
    func togglePostLike() {
        // ✅ 修复：实现完整的帖子点赞功能
        guard !isViewReleased else { return }
        
        createManagedTask {
            await self.performPostLikeToggle()
        }
    }
    
    /// 执行帖子点赞切换操作
    private func performPostLikeToggle() async {
        do {
            let currentUser = try await getCurrentUser()
            
            // ✅ 修复：使用Repository的toggleLike方法
            let newLikeCount = try await repositoryContainer.communityRepository.toggleLike(
                postId: post.id,
                userId: currentUser.id
            )
            
            // 发送点赞状态变化通知，确保UI同步
            await MainActor.run {
                // 重新计算点赞状态
                let updatedIsLiked = self.checkPostLikedStatus(userId: currentUser.id)
                
                NotificationCenter.default.post(
                    name: .postLikeStatusDidChange,
                    object: self.post.id,
                    userInfo: [
                        "isLiked": updatedIsLiked,
                        "newCount": newLikeCount
                    ]
                )
                
                // 触发UI更新
                self.objectWillChange.send()
            }
            
        } catch {
            showError("点赞操作失败：\(error.localizedDescription)")
        }
    }
    

    
    /// 切换评论点赞状态 - 优化性能
    func toggleCommentLike(_ comment: EACommunityComment) {
        Task { @MainActor in
            await performCommentLikeToggle(comment)
        }
    }
    
    /// 执行评论点赞切换操作 - 简化实现，直接使用Repository
    private func performCommentLikeToggle(_ comment: EACommunityComment) async {
        do {
            let currentUser = try await getCurrentUser()
            
            // ✅ 简化：直接使用Repository的toggleLike方法处理评论点赞
            // 注意：Repository中需要实现评论的toggleLike方法
            let existingLike = try await repositoryContainer.communityRepository.findExistingLike(
                commentId: comment.id,
                userId: currentUser.id
            )
            
            if let existingLike = existingLike {
                // 切换现有点赞状态
                existingLike.isActive.toggle()
                existingLike.creationDate = Date()
            } else {
                // 创建新点赞
                _ = try await repositoryContainer.communityRepository.createLike(
                    targetType: "comment",
                    targetCommentId: comment.id,
                    userId: currentUser.id,
                    userEnergyLevel: 5
                )
            }
            
        } catch {
            showError("点赞操作失败：\(error.localizedDescription)")
        }
    }
    
    /// 准备回复评论
    func prepareReplyToComment(_ comment: EACommunityComment) {
        replyTargetComment = comment
    }
    
    /// 取消回复
    func cancelReply() {
        replyTargetComment = nil
    }
    
    /// 切换评论排序
    func toggleCommentSort() {
        switch commentSortOrder {
        case .newest:
            commentSortOrder = .oldest
        case .oldest:
            commentSortOrder = .mostLiked
        case .mostLiked:
            commentSortOrder = .newest
        }
        sortComments()
    }
    
    /// 分享帖子
    func sharePost() {
        // TODO: 实现分享功能
        showAlert(message: "分享功能开发中...")
    }
    
    /// 举报帖子
    func reportPost() {
        // TODO: 实现举报功能
        showAlert(message: "举报功能开发中...")
    }
    
    /// 显示用户资料
    func showUserProfile(for user: EAUser? = nil) {
        // TODO: 实现用户资料页面
        let username = user?.username ?? post.getAuthorUsername()
        showAlert(message: "查看 \(username) 的个人资料")
    }
    
    /// 显示删除确认
    func showDeleteConfirmation() {
        showDeleteDialog = true
    }
    
    /// 删除帖子 - 通过Repository（增强版错误处理）
    func deletePost() {
        Task {
            do {
                // 🔑 第一步：获取当前用户（使用增强版认证检查）
                let currentUser = try await getCurrentUser()

                // 🔑 第二步：详细权限检查
                guard let postAuthor = self.post.getAuthor() else {
                    await MainActor.run {
                        self.showError("无法确定帖子作者，删除失败")
                    }
                    return
                }

                guard postAuthor.id == currentUser.id else {
                    await MainActor.run {
                        self.showError("只能删除自己发布的帖子")
                    }
                    return
                }

                // 🔑 第三步：立即关闭页面，避免显示"未知用户"状态
                await MainActor.run {
                    self.shouldDismiss = true
                    self.onPostDeleted?()
                }

                // 🔑 第四步：执行删除操作（在页面关闭后）
                try await repositoryContainer.communityRepository.deletePost(id: self.post.id)

                // 🔑 第五步：发送删除通知，更新社区列表
                await MainActor.run {
                    // 发送帖子删除通知
                    NotificationCenter.default.post(
                        name: NSNotification.Name("EACommunityPostDeleted"),
                        object: self.post.id
                    )

                    #if DEBUG
                    #endif
                }

            } catch let error as NSError {
                await MainActor.run {
                    // 🔑 改进：根据错误类型提供更友好的提示
                    if error.domain == "CommunityError" && error.code == 1 {
                        self.showError("登录状态已过期，请重新登录后再试")
                    } else if error.localizedDescription.contains("权限") {
                        self.showError("权限不足，无法删除此帖子")
                    } else if error.localizedDescription.contains("网络") {
                        self.showError("网络连接异常，请检查网络后重试")
                    } else {
                        self.showError("删除失败，请稍后重试")
                    }

                    #if DEBUG
                    #endif
                }
            }
        }
    }
    
    // MARK: - Computed Properties
    
    /// 是否可以提交评论
    var canSubmitComment: Bool {
        !commentText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty && !isSubmittingComment
    }
    
    /// 是否可以删除帖子（实时权限检查）
    var canDeletePost: Bool {
        // 🔑 重构：使用ID检查权限，避免在computed property中使用async
        guard let currentUserID = sessionManager.currentUserID else {
            #if DEBUG
            #endif
            return false
        }

        // 检查是否为帖子作者
        guard let postAuthor = post.getAuthor() else {
            #if DEBUG
            #endif
            return false
        }

        let canDelete = postAuthor.id == currentUserID

        #if DEBUG
        #endif

        return canDelete
    }
    
    /// 是否已点赞帖子
    var isPostLiked: Bool {
        // 🔑 重构：实现帖子点赞状态检查（使用ID检查）
        guard let currentUserID = sessionManager.currentUserID else {
            return false
        }

        // 通过Repository检查点赞状态（同步版本，用于UI绑定）
        return checkPostLikedStatus(userId: currentUserID)
    }
    
    /// 检查帖子点赞状态（同步版本，用于UI）
    private func checkPostLikedStatus(userId: UUID) -> Bool {
        // ✅ 修复：实现真正的点赞状态检查
        // 通过帖子的点赞关系检查当前用户是否已点赞
        let postLikes = post.likes
        
        // 检查是否存在当前用户的活跃点赞记录
        return postLikes.contains { like in
            like.user?.id == userId && like.isActive && like.targetType == "post"
        }
    }
    
    /// 获取帖子点赞数量 - 通过Repository
    func getPostLikesCount() -> Int {
        // ✅ 修复：实现帖子点赞数量获取
        // 返回帖子的当前点赞数量
        return post.likeCount
    }
    
    /// 获取评论点赞数量 - 通过Repository
    func getCommentLikesCount(_ comment: EACommunityComment) -> Int {
        // ✅ 修复：实现评论点赞数量获取
        // 返回评论的当前点赞数量
        return comment.likeCount
    }
    
    /// 检查评论是否已点赞
    func isCommentLiked(_ comment: EACommunityComment) -> Bool {
        // 🔑 重构：实现评论点赞状态检查（使用ID检查）
        guard let currentUserID = sessionManager.currentUserID else {
            return false
        }

        // 通过Repository检查评论点赞状态（同步版本，用于UI绑定）
        return checkCommentLikedStatus(commentId: comment.id, userId: currentUserID)
    }
    
    /// 检查评论点赞状态（同步版本，用于UI）
    private func checkCommentLikedStatus(commentId: UUID, userId: UUID) -> Bool {
        // 这里可以实现本地缓存检查
        // 实际的异步检查会在后台更新状态
        return false // 默认返回false，实际状态通过通知更新
    }
    
    /// 评论排序文字
    var commentSortText: String {
        switch commentSortOrder {
        case .newest: return "最新"
        case .oldest: return "最早"
        case .mostLiked: return "最热"
        }
    }
    
    // MARK: - Private Methods
    
    /// 处理加载错误 - 新增错误恢复机制
    private func handleLoadingError(_ message: String) {
        isLoadingComments = false
        
        // 🔑 修复：根据错误类型决定是否可重试
        if message.contains("数据上下文未初始化") {
            loadingError = "数据环境正在准备中，请稍后..."
            canRetry = true
        } else {
            loadingError = message
            canRetry = true
        }
        
        showError(message)
    }
    
    /// 排序评论
    private func sortComments() {
        switch commentSortOrder {
        case .newest:
            comments.sort { $0.creationDate > $1.creationDate }
        case .oldest:
            comments.sort { $0.creationDate < $1.creationDate }
        case .mostLiked:
            comments.sort { getCommentLikesCount($0) > getCommentLikesCount($1) }
        }
    }
    
    /// 显示错误消息
    private func showError(_ message: String) {
        alertMessage = message
        showAlert = true
    }
    
    /// 显示提示消息
    private func showAlert(message: String) {
        alertMessage = message
        showAlert = true
    }
    
    /// 获取当前用户（增强版用户认证检查）
    private func getCurrentUser() async throws -> EAUser {
        // 🔑 重构：优先使用新的安全异步接口
        if let currentUser = await sessionManager.safeCurrentUser {
            // 验证用户数据的有效性
            if await isUserDataValid(currentUser) {
                return currentUser
            }
        }

        // 🔑 第二步：如果safeCurrentUser失败，尝试会话恢复
        // 🚨 架构重构修复：不再使用Repository直接获取，保持架构一致性

        // 🔑 第三步：尝试触发会话恢复
        await MainActor.run {
            sessionManager.restoreSession()
        }

        // 延迟重试一次
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒

        if let retryUser = await sessionManager.safeCurrentUser {
            return retryUser
        }

        // 🔑 最终：所有尝试都失败，抛出用户友好的错误
        throw NSError(
            domain: "CommunityError",
            code: 1,
            userInfo: [
                NSLocalizedDescriptionKey: "登录状态已过期，请重新登录",
                NSLocalizedRecoverySuggestionErrorKey: "请返回登录页面重新登录"
            ]
        )
    }

    /// 🔑 新增：验证用户数据有效性
    private func isUserDataValid(_ user: EAUser) async -> Bool {
        // 检查用户基本信息
        guard !user.username.isEmpty else { return false }

        // 检查用户是否在数据库中存在
        do {
            let existingUser = try await repositoryContainer.userRepository.fetchUser(id: user.id)
            return existingUser != nil
        } catch {
            #if DEBUG
            #endif
            return false
        }
    }
}

// MARK: - CommentSortOrder

/// 评论排序方式
enum CommentSortOrder {
    case newest    // 最新
    case oldest    // 最早
    case mostLiked // 最热
}

// MARK: - Helper Types

/// 评论提交数据结构
private struct CommentSubmissionData {
    let content: String
    let user: EAUser
    let post: EACommunityPost
    let parentComment: EACommunityComment?
}