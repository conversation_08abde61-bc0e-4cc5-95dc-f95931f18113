import SwiftUI
import SwiftData
import Combine

/// 添加好友页面的ViewModel - MVVM架构
/// 集中管理所有状态和业务逻辑，简化视图层复杂度
@MainActor
final class EAAddFriendViewModel: ObservableObject {
    
    // MARK: - Published状态属性
    
    /// 搜索文本
    @Published var searchText = ""
    
    /// 搜索结果列表
    @Published var searchResults: [EAUserSocialProfile] = []
    
    /// 搜索加载状态
    @Published var isSearching = false
    
    /// 弹窗显示状态
    @Published var showAlert = false
    
    /// 弹窗消息内容
    @Published var alertMessage = ""
    
    /// 是否为成功提示弹窗
    @Published var isSuccessAlert = false

    /// 🔑 批次三新增：自动dismiss触发器
    @Published var shouldDismiss = false

    /// 当前选中的用户（用于发送好友请求）
    @Published var selectedUser: EAUserSocialProfile?
    
    /// 好友请求消息输入框显示状态
    @Published var showMessageInput = false
    
    /// 好友请求附加消息
    @Published var friendRequestMessage = ""

    // MARK: - 私有状态管理
    
    /// 当前搜索任务（用于防抖和取消）
    private var searchTask: Task<Void, Never>?
    
    /// 当前分页页码
    private var currentPage = 0
    
    /// 是否还有更多搜索结果
    private var hasMoreResults = true
    
    /// 是否正在加载
    @Published var isLoading = false
    
    /// 成功消息
    @Published var successMessage: String?
    
    /// 错误消息
    @Published var errorMessage: String?
    
    // MARK: - 服务依赖

    /// 好友关系服务 - 标准化：通过依赖注入设置
    var friendshipService: EAFriendshipService?

    /// Repository容器 - 标准化：通过依赖注入设置
    private var repositoryContainer: EARepositoryContainer?

    // MARK: - 初始化

    init() {
        // 标准化：空初始化，支持SwiftUI依赖注入模式
    }

    // MARK: - 依赖注入方法

    /// 标准化：设置依赖服务（由View在onAppear中调用）
    func setupDependencies(friendshipService: EAFriendshipService, repositoryContainer: EARepositoryContainer?) {
        self.friendshipService = friendshipService
        self.repositoryContainer = repositoryContainer

        // 监听搜索文本变化，实现防抖搜索
        setupSearchTextObserver()
    }
    
    // MARK: - 好友状态管理方法
    
    /// 获取指定用户的好友关系状态
    func friendshipStatus(for profile: EAUserSocialProfile) -> FriendshipStatus {
        // 从缓存的搜索结果中获取状态，或返回默认值
        guard friendshipService != nil else {
            return .notFriend
        }
        
        // 这里可以添加缓存逻辑，避免重复查询
        // 临时返回默认值，实际项目中应该从服务获取
        return .notFriend
    }
    
    /// 异步获取指定用户的好友关系状态
    func getFriendshipStatusAsync(for profile: EAUserSocialProfile) async -> FriendshipStatus {
        guard let friendshipService = friendshipService else {
            return .notFriend
        }
        
        do {
            return try await friendshipService.getFriendshipStatus(with: profile.id)
        } catch {
            return .notFriend
        }
    }
    
    // MARK: - 搜索相关方法
    
    /// 设置搜索文本监听器，实现防抖搜索
    private func setupSearchTextObserver() {
        // 使用Combine来监听searchText变化并实现防抖
        $searchText
            .debounce(for: .milliseconds(500), scheduler: RunLoop.main)
            .removeDuplicates()
            .sink { [weak self] newValue in
                Task { @MainActor in
                    await self?.handleSearchTextChange(newValue)
                }
            }
            .store(in: &cancellables)
    }
    
    /// Combine订阅存储
    private var cancellables = Set<AnyCancellable>()
    
    /// 处理搜索文本变化
    private func handleSearchTextChange(_ newValue: String) async {
        if newValue.count >= 2 {
            await debounceSearch()
        } else {
            await clearSearchResults()
        }
    }
    
    /// 防抖搜索
    func debounceSearch() async {
        // 取消之前的搜索任务
        searchTask?.cancel()
        
        // 创建新的搜索任务
        searchTask = Task {
            // 延迟300ms，实现防抖效果
            try? await Task.sleep(for: .milliseconds(300))
            
            // 检查是否被取消
            if !Task.isCancelled {
                await performSearch()
            }
        }
        
        await searchTask?.value
    }
    
    /// 执行搜索
    func performSearch() async {
        guard !searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            await clearSearchResults()
            return
        }
        
        isSearching = true
        currentPage = 0
        hasMoreResults = true
        
        do {
            // 通过CommunityRepository执行搜索，然后转换为社交档案
            let users = try await repositoryContainer?.communityRepository.searchUsers(
                query: searchText,
                limit: 20
            ) ?? []
            
            // 转换为社交档案
            let profiles = convertUsersToProfiles(users)
            
            searchResults = profiles
            hasMoreResults = profiles.count == 20 // 如果返回满页，说明可能还有更多
            currentPage = 1 // 下一页从1开始
            
        } catch {
            alertMessage = "搜索失败：\(error.localizedDescription)"
            showAlert = true
            searchResults = []
        }
        
        isSearching = false
    }
    
    /// 加载更多搜索结果
    func loadMoreResults() async {
        guard !isSearching && hasMoreResults else { return }
        
        isSearching = true
        
        do {
            // 搜索更多用户（模拟分页）
            let allUsers = try await repositoryContainer?.communityRepository.searchUsers(
                query: searchText,
                limit: (currentPage + 1) * 20
            ) ?? []
            
            // 手动分页：跳过已加载的用户
            let startIndex = currentPage * 20
            let users = Array(allUsers.dropFirst(startIndex))
            
            // 转换为社交档案
            let newProfiles = convertUsersToProfiles(users)
            
            // 合并结果，避免重复
            let uniqueResults = newProfiles.filter { newResult in
                !searchResults.contains { existing in
                    existing.id == newResult.id
                }
            }
            
            searchResults.append(contentsOf: uniqueResults)
            hasMoreResults = newProfiles.count == 20
            currentPage += 1
            
        } catch {
            alertMessage = "加载更多失败：\(error.localizedDescription)"
            showAlert = true
        }
        
        isSearching = false
    }
    
    /// 清空搜索结果
    func clearSearchResults() async {
        searchResults.removeAll()
        currentPage = 0
        hasMoreResults = true
    }
    
    // MARK: - 好友请求相关方法
    
    /// 显示好友请求消息输入框
    func showFriendRequestInput(for user: EAUserSocialProfile) {
        selectedUser = user
        friendRequestMessage = ""
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            showMessageInput = true
        }
    }
    
    /// 隐藏好友请求消息输入框
    func hideFriendRequestInput() {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            showMessageInput = false
            selectedUser = nil
            friendRequestMessage = ""
        }
    }
    
    /// 发送好友请求
    func sendFriendRequest() async {
        // 🔑 关键修复：防止重复提交
        guard !isLoading else {
            return
        }

        guard let targetUser = selectedUser else {
            await MainActor.run {
                alertMessage = "请选择要添加的用户"
                showAlert = true
            }
            return
        }

        // 🔑 防御性编程：验证friendshipService存在
        guard let friendshipService = friendshipService else {
            await MainActor.run {
                alertMessage = "服务未初始化，请稍后重试"
                showAlert = true
            }
            return
        }

        // 🔑 关键修复：保存目标用户信息和消息，避免在hideFriendRequestInput中被清空
        // 🚨 重大修复：传递用户ID而不是社交档案ID！
        guard let targetUserId = targetUser.user?.id else {
            await MainActor.run {
                alertMessage = "目标用户信息异常，请重新搜索"
                showAlert = true
            }
            return
        }
        let requestMessage = friendRequestMessage.isEmpty ? nil : friendRequestMessage

        #if DEBUG
        print("🔧 [AddFriendVM] 修复后的ID传递:")
        print("   - 社交档案ID: \(targetUser.id)")
        print("   - 用户ID: \(targetUserId) ← 正确传递给好友服务")
        #endif

        // 🔑 关键修复：设置加载状态，防止重复提交
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }

        // 隐藏输入框（这会清空selectedUser和friendRequestMessage）
        await MainActor.run {
            hideFriendRequestInput()
        }

        do {
            #if DEBUG
            print("🔍 [AddFriendVM] 调用friendshipService.sendFriendRequest - targetUserId: \(targetUserId)")
            #endif

            // 通过FriendshipService发送请求（使用保存的值）
            try await friendshipService.sendFriendRequest(
                to: targetUserId,
                message: requestMessage
            )

            #if DEBUG
            print("✅ [AddFriendVM] sendFriendRequest调用成功")
            #endif

            // 🔑 关键修复：好友申请发送成功后，立即刷新好友列表数据
            await friendshipService.loadFriendships()
            await friendshipService.loadPendingRequests()

            // 🔑 批次三修复：显示成功提示并准备自动dismiss
            await MainActor.run {
                alertMessage = "好友请求已发送！"
                isSuccessAlert = true
                showAlert = true
                isLoading = false
            }

            // 🔑 批次三新增：延迟自动dismiss（给用户足够时间看到成功提示）
            try? await Task.sleep(for: .seconds(1.5))

            await MainActor.run {
                // 🔑 触发自动dismiss
                shouldDismiss = true
            }

        } catch {
            await MainActor.run {
                // 🔑 统一错误处理：根据错误类型提供用户友好的提示
                let errorMessage = handleFriendshipError(error)
                alertMessage = errorMessage
                showAlert = true
                isLoading = false
            }
        }
    }

    // MARK: - 🔑 统一错误处理

    /// 统一的好友功能错误处理
    private func handleFriendshipError(_ error: Error) -> String {
        if let friendshipError = error as? FriendshipServiceError {
            switch friendshipError {
            case .currentUserNotFound:
                return "无法获取您的用户信息，请重新登录后再试"
            case .profileInitializationFailed:
                return "用户档案初始化失败，请稍后重试或重新登录"
            case .userNotFound:
                return "目标用户信息无效，请刷新页面后重试"
            case .alreadyFriends:
                return "您已经是好友了，无需重复添加"
            case .requestAlreadyExists:
                return "好友请求已存在或请求发送过于频繁，请稍后再试"
            case .requestCannotBeProcessed:
                return "无法发送请求，可能被对方拉黑或达到请求上限"
            case .unknownError(let underlyingError):
                return "发送失败：\(underlyingError.localizedDescription)"
            case .requestNotFound:
                return "好友请求不存在，可能已被处理"
            case .friendshipNotFound:
                return "好友关系不存在"
            case .invalidRequestProfiles:
                return "用户信息异常，请稍后重试"
            case .blockingFailed(let message):
                return "屏蔽操作失败：\(message)"
            }
        } else {
            // 通用错误处理，提供兼容性支持
            if error.localizedDescription.contains("上下文不匹配") ||
               error.localizedDescription.contains("兼容性") {
                return "系统兼容性问题，请稍后重试"
            } else {
                return "发送失败：\(error.localizedDescription)"
            }
        }
    }

    // MARK: - 弹窗管理
    
    /// 🔑 批次三修复：处理弹窗确认（移除手动dismiss逻辑）
    func handleAlertConfirmation() {
        if isSuccessAlert {
            // 🔑 成功提示确认后，立即触发自动dismiss
            shouldDismiss = true
        }
        // 重置状态
        isSuccessAlert = false
    }
    
    // MARK: - 好友申请处理方法
    
    /// 接受好友申请
    /// 🔑 修复：强化错误处理和UI保护，解决Context冲突导致的崩溃
    func acceptFriendRequest(_ request: EAFriendRequest) async {
        // 🔑 防重复操作保护
        guard !isLoading else {
            errorMessage = "操作正在进行中，请稍候..."
            return
        }
        
        // 🔑 修复：验证依赖服务存在
        guard let friendshipService = friendshipService else {
            errorMessage = "服务未初始化，请稍后重试"
            return
        }
        
        await MainActor.run {
            isLoading = true
            errorMessage = nil
            successMessage = nil
        }
        
        defer {
            Task { @MainActor in
                isLoading = false
            }
        }
        
        // 🔑 关键修复：使用FriendshipService的新方法，确保Context一致性
        // 注意：acceptFriendRequest方法内部处理所有错误，不会抛出异常
        await friendshipService.acceptFriendRequest(request)

        await MainActor.run {
            // 检查FriendshipService的错误状态
            if let errorMessage = friendshipService.errorMessage {
                self.errorMessage = errorMessage
            } else if let successMessage = friendshipService.successMessage {
                self.successMessage = successMessage
                // 🔑 修复：使用正确的方法名
                Task {
                    await friendshipService.loadFriendships()
                }
            }
        }

        #if DEBUG
        // 调试环境下记录好友申请接受完成
        #endif
    }
    
    // MARK: - 私有辅助方法

    /// 🔑 关键修复：将用户转换为社交档案（增强版数据验证）
    /// 确保转换后的社交档案对象包含完整且有效的用户关系
    private func convertUsersToProfiles(_ users: [EAUser]) -> [EAUserSocialProfile] {
        var profiles: [EAUserSocialProfile] = []

        for user in users {
            // 🔑 第一层验证：检查用户基础数据完整性
            guard !user.username.isEmpty,
                  user.id != UUID(uuidString: "00000000-0000-0000-0000-000000000000") else {
                #if DEBUG
                print("⚠️ [AddFriendVM] 跳过无效用户 - ID: \(user.id), 用户名: '\(user.username)'")
                #endif
                continue
            }

            // 🔑 第二层验证：检查社交档案存在性
            guard let socialProfile = user.socialProfile else {
                #if DEBUG
                print("⚠️ [AddFriendVM] 跳过无社交档案的用户 - 用户名: \(user.username)")
                #endif
                continue
            }

            // 🔑 第三层验证：检查社交档案的反向关系完整性
            guard let profileUser = socialProfile.user,
                  profileUser.id == user.id,
                  profileUser.username == user.username else {
                #if DEBUG
                print("⚠️ [AddFriendVM] 跳过关系不一致的用户档案 - 用户名: \(user.username)")
                print("   - socialProfile.user: \(socialProfile.user?.username ?? "nil")")
                print("   - 用户ID匹配: \(socialProfile.user?.id == user.id)")
                #endif
                continue
            }

            // 🔑 第四层验证：检查社交档案数据完整性
            guard socialProfile.id != UUID(uuidString: "00000000-0000-0000-0000-000000000000") else {
                #if DEBUG
                print("⚠️ [AddFriendVM] 跳过无效的社交档案 - 用户名: \(user.username)")
                #endif
                continue
            }

            // 🔑 第五层验证：确保数字宇宙数据已初始化（防止后续业务逻辑失败）
            if socialProfile.stellarLevel == nil || socialProfile.totalStellarEnergy == nil {
                #if DEBUG
                print("🔧 [AddFriendVM] 为用户初始化数字宇宙数据 - 用户名: \(user.username)")
                #endif
                // 安全初始化数字宇宙数据（有防重复机制）
                socialProfile.initializeDigitalUniverseData()
            }

            #if DEBUG
            print("✅ [AddFriendVM] 成功验证用户档案 - 用户名: \(user.username)")
            print("🔍 [AddFriendVM] 用户ID: \(user.id)")
            print("🔍 [AddFriendVM] 社交档案ID: \(socialProfile.id)")
            print("🚨 [AddFriendVM] 注意：将传递社交档案ID(\(socialProfile.id))给好友服务")
            #endif

            profiles.append(socialProfile)
        }

        #if DEBUG
        print("📊 [AddFriendVM] 用户转换统计 - 输入: \(users.count), 输出: \(profiles.count)")
        #endif

        return profiles
    }
}

// MARK: - Combine导入
import Combine 