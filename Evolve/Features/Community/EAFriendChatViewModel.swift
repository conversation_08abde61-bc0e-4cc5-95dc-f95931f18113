import Foundation
import SwiftUI
import SwiftData

/// 好友聊天页面ViewModel - 单一真理之源
/// 遵循项目MVVM架构标准，管理聊天界面的所有状态和业务逻辑
/// 严格遵循《Evolve项目AI开发审查规则.md》和《Evolve项目ID使用规范文档.md》
@MainActor
final class EAFriendChatViewModel: ObservableObject {
    
    // MARK: - Published Properties - 驱动UI的所有状态
    
    /// 聊天消息列表 - UI的核心数据源
    @Published var messages: [EAFriendMessage] = []
    
    /// 当前输入的消息文本
    @Published var messageText: String = ""
    
    /// 加载状态
    @Published var isLoading: Bool = false
    
    /// 发送消息状态
    @Published var isSendingMessage: Bool = false
    
    /// 错误消息
    @Published var errorMessage: String?
    
    /// 显示错误警告
    @Published var showAlert: Bool = false
    
    /// 好友信息（用于UI显示）
    @Published var friendDisplayName: String = ""
    @Published var friendAvatarUrl: String?
    
    // MARK: - Private Properties - 内部状态管理
    
    /// 好友关系ID - 核心标识符
    private let friendshipId: UUID
    
    /// 当前用户ID - 用于权限验证
    private let currentUserID: UUID
    
    /// 依赖注入的服务
    private let chatService: EAFriendChatService
    private let repositoryContainer: EARepositoryContainer
    private let sessionManager: EASessionManager
    
    /// 当前好友关系对象（内部缓存）
    private var currentFriendship: EAFriendship?
    
    /// 初始化状态标记（防止重复初始化）
    private var isInitialized: Bool = false
    
    // MARK: - Initialization
    
    /// 初始化ViewModel
    /// - Parameters:
    ///   - friendshipId: 好友关系ID（遵循ID使用规范）
    ///   - currentUserID: 当前用户ID
    ///   - chatService: 聊天服务
    ///   - repositoryContainer: Repository容器
    ///   - sessionManager: 会话管理器
    init(
        friendshipId: UUID,
        currentUserID: UUID,
        chatService: EAFriendChatService,
        repositoryContainer: EARepositoryContainer,
        sessionManager: EASessionManager
    ) {
        self.friendshipId = friendshipId
        self.currentUserID = currentUserID
        self.chatService = chatService
        self.repositoryContainer = repositoryContainer
        self.sessionManager = sessionManager
        
        #if DEBUG
        print("🔧 [EAFriendChatViewModel] 初始化 - 好友关系ID: \(friendshipId.uuidString)")
        #endif
    }
    
    // MARK: - Public Methods - 供View调用的方法
    
    /// 初始化数据加载（幂等性保护）
    func initializeData() async {
        // 🔑 幂等性保护：防止重复初始化
        guard !isInitialized else { return }
        
        // 🔑 依赖检查：确保必要的依赖都存在
        guard sessionManager.isLoggedIn,
              sessionManager.currentUserID == currentUserID else {
            await showError("用户会话异常，请重新登录")
            return
        }
        
        isInitialized = true
        
        // 开始加载数据
        await loadChatHistory()
        await loadFriendInfo()
    }
    
    /// 加载聊天历史记录
    func loadChatHistory() async {
        isLoading = true
        defer { isLoading = false }
        
        do {
            // 🔑 关键修复：通过Repository获取好友关系
            guard let friendship = try await repositoryContainer.friendshipRepository.fetchFriendship(by: friendshipId) else {
                await showError("好友关系不存在")
                return
            }
            
            // 缓存好友关系
            currentFriendship = friendship
            
            // 🔑 核心修复：调用修复后的fetchChatHistory方法
            let fetchedMessages = try await repositoryContainer.friendMessageRepository.fetchChatHistory(
                friendship: friendship,
                limit: 100,
                offset: 0
            )
            
            // 🔑 确保消息按时间正确排序（先按sequenceId，再按创建时间）
            let sortedMessages = fetchedMessages.sorted { message1, message2 in
                if message1.sequenceId != message2.sequenceId {
                    return message1.sequenceId < message2.sequenceId
                }
                return message1.creationDate < message2.creationDate
            }
            
            // 更新UI
            messages = sortedMessages
            
            #if DEBUG
            print("✅ [EAFriendChatViewModel] 聊天历史加载成功 - 消息数量: \(messages.count)")
            #endif
            
        } catch {
            await showError("加载聊天历史失败: \(error.localizedDescription)")
        }
    }
    
    /// 发送消息（实现乐观更新）
    func sendMessage() async {
        // 输入验证
        let trimmedText = messageText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedText.isEmpty else { return }

        // 检查好友关系
        guard let friendship = currentFriendship else {
            await showError("好友关系异常，请重新进入聊天")
            return
        }

        // 检查用户会话
        guard let currentUser = await sessionManager.safeCurrentUser,
              currentUser.id == currentUserID else {
            await showError("用户会话异常，请重新登录")
            return
        }

        isSendingMessage = true

        // 🔑 乐观更新：立即创建临时消息显示在UI中
        let tempMessage = createTempMessage(content: trimmedText, friendship: friendship)
        messages.append(tempMessage)

        // 清空输入框
        let messageToSend = trimmedText
        messageText = ""

        do {
            // 🔑 网络就绪架构：创建完整的EAFriendMessage对象
            let messageToSend = createMessageForSending(
                content: messageToSend,
                messageType: .text,
                friendship: friendship
            )

            // 🔑 后台持久化：调用新的Service方法发送完整消息对象
            try await chatService.sendMessage(messageToSend)

            // 🔑 关键修复：发送成功后重新加载完整历史记录，确保数据一致性
            await loadChatHistory()

            #if DEBUG
            print("✅ [EAFriendChatViewModel] 消息发送成功")
            #endif

        } catch {
            // 🔑 错误处理：移除临时消息，显示错误
            if let tempIndex = messages.firstIndex(where: { $0.id == tempMessage.id }) {
                messages.remove(at: tempIndex)
            }
            await showError("发送消息失败: \(error.localizedDescription)")
        }

        isSendingMessage = false
    }
    
    // MARK: - Private Methods - 内部辅助方法
    
    /// 加载好友信息（用于UI显示）
    private func loadFriendInfo() async {
        guard let friendship = currentFriendship else { return }
        
        // 🔑 根据ID使用规范：获取对方的社交档案信息
        let otherProfile = friendship.initiatorProfile?.id == currentUserID 
            ? friendship.friendProfile 
            : friendship.initiatorProfile
        
        if let profile = otherProfile {
            friendDisplayName = profile.user?.username ?? "未知用户"
            friendAvatarUrl = profile.avatarUrl
        }
    }
    
    /// 创建临时消息（用于乐观更新）
    private func createTempMessage(content: String, friendship: EAFriendship) -> EAFriendMessage {
        let tempMessage = EAFriendMessage()
        tempMessage.content = content
        tempMessage.messageType = .text
        tempMessage.creationDate = Date()
        tempMessage.sequenceId = Int64(Date().timeIntervalSince1970 * 1000) // 临时序列ID
        tempMessage.friendship = friendship

        // 设置发送者信息（临时）
        if let currentProfile = friendship.initiatorProfile?.id == currentUserID
            ? friendship.initiatorProfile
            : friendship.friendProfile {
            tempMessage.senderProfile = currentProfile
        }

        return tempMessage
    }

    /// 🔑 新增：创建用于发送的完整消息对象（网络就绪架构）
    private func createMessageForSending(
        content: String,
        messageType: EAFriendMessage.MessageType,
        friendship: EAFriendship
    ) -> EAFriendMessage {
        let message = EAFriendMessage()
        message.content = content
        message.messageType = messageType
        message.creationDate = Date()
        message.sequenceId = getNextSequenceId()
        message.friendship = friendship

        // 设置发送者和接收者信息
        let currentProfile = friendship.initiatorProfile?.id == currentUserID
            ? friendship.initiatorProfile
            : friendship.friendProfile
        let friendProfile = friendship.initiatorProfile?.id == currentUserID
            ? friendship.friendProfile
            : friendship.initiatorProfile

        message.senderProfile = currentProfile
        message.receiverProfile = friendProfile

        return message
    }

    /// 获取下一个序列ID
    private func getNextSequenceId() -> Int64 {
        // 简单的序列ID生成：使用时间戳 + 随机数确保唯一性
        return Int64(Date().timeIntervalSince1970 * 1000) + Int64.random(in: 0...999)
    }
    
    /// 显示错误信息
    private func showError(_ message: String) async {
        errorMessage = message
        showAlert = true
        
        #if DEBUG
        print("❌ [EAFriendChatViewModel] 错误: \(message)")
        #endif
    }
    
    /// 清除错误状态
    func clearError() {
        errorMessage = nil
        showAlert = false
    }
}

// MARK: - 扩展：便利方法

extension EAFriendChatViewModel {
    
    /// 检查是否可以发送消息
    var canSendMessage: Bool {
        !messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty && 
        !isSendingMessage && 
        currentFriendship != nil
    }
    
    /// 获取消息数量
    var messageCount: Int {
        messages.count
    }
    
    /// 检查是否有错误
    var hasError: Bool {
        errorMessage != nil
    }


}
