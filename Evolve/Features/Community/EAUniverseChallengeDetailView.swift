import SwiftUI

// MARK: - 宇宙挑战详情视图
struct EAUniverseChallengeDetailView: View {
    
    // MARK: - 属性
    
    let challenge: EAUniverseChallenge
    
    // MARK: - 状态
    
    @Environment(\.dismiss) private var dismiss
    @Environment(\.sessionManager) private var sessionManager
    @Environment(\.repositoryContainer) private var repositoryContainer
    
    @State private var isParticipating = false
    @State private var participation: EAUniverseChallengeParticipation?
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var showingJoinConfirmation = false
    
    // MARK: - 视图主体
    
    var body: some View {
        NavigationView {
            ZStack {
                // 宇宙背景
                cosmicBackground
                
                ScrollView {
                    VStack(spacing: 24) {
                        // 挑战头部
                        challengeHeader
                        
                        // 挑战信息
                        challengeInfo
                        
                        // 挑战进度
                        if isParticipating, let participation = participation {
                            challengeProgress(participation)
                        }
                        
                        // 挑战规则
                        challengeRules
                        
                        // 奖励信息
                        rewardInfo
                        
                        // 操作按钮
                        actionButtons
                        
                        Spacer(minLength: 100)
                    }
                    .padding()
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                    .foregroundColor(.white)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    shareButton
                }
            }
            .task {
                await loadParticipationInfo()
            }
            .alert("参与挑战", isPresented: $showingJoinConfirmation) {
                Button("取消", role: .cancel) {}
                Button("参与") {
                    Task {
                        await joinChallenge()
                    }
                }
            } message: {
                Text("确定要参与「\(challenge.title)」吗？")
            }
            .alert("错误", isPresented: .constant(errorMessage != nil)) {
                Button("确定") {
                    errorMessage = nil
                }
            } message: {
                if let error = errorMessage {
                    Text(error)
                }
            }
        }
    }
    
    // MARK: - 宇宙背景
    
    private var cosmicBackground: some View {
        LinearGradient(
            colors: [
                difficultyColor.opacity(0.3),
                Color.black.opacity(0.8),
                Color.purple.opacity(0.2)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .overlay {
            // 星星效果
            ForEach(0..<30, id: \.self) { _ in
                Circle()
                    .fill(Color.white.opacity(0.4))
                    .frame(width: CGFloat.random(in: 1...3), height: CGFloat.random(in: 1...3))
                    .position(
                        x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                        y: CGFloat.random(in: 0...UIScreen.main.bounds.height)
                    )
            }
        }
        .ignoresSafeArea()
    }
    
    // MARK: - 挑战头部
    
    private var challengeHeader: some View {
        VStack(spacing: 16) {
            // 挑战徽章
            Image(systemName: challenge.badgeIcon)
                .font(.system(size: 80))
                .foregroundColor(difficultyColor)
                .shadow(color: difficultyColor.opacity(0.5), radius: 10, x: 0, y: 0)
            
            // 挑战标题
            Text(challenge.title)
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
            
            // 宇宙区域
            Text(challenge.universeRegionDescription)
                .font(.headline)
                .foregroundColor(difficultyColor)
            
            // 难度标签
            HStack(spacing: 8) {
                ForEach(0..<challenge.cosmicDifficulty, id: \.self) { _ in
                    Image(systemName: "star.fill")
                        .foregroundColor(difficultyColor)
                }
                
                Text(challenge.difficultyDescription)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white.opacity(0.8))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                Capsule()
                    .fill(difficultyColor.opacity(0.2))
                    .overlay(
                        Capsule()
                            .stroke(difficultyColor.opacity(0.4), lineWidth: 1)
                    )
            )
        }
    }
    
    // MARK: - 挑战信息
    
    private var challengeInfo: some View {
        VStack(spacing: 16) {
            // 挑战描述
            Text(challenge.challengeDescription)
                .font(.body)
                .foregroundColor(.white.opacity(0.9))
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            // 信息卡片
            HStack(spacing: 16) {
                infoCard(
                    icon: "person.2.fill",
                    title: "参与者",
                    value: "\(challenge.participantCount)/\(challenge.maxParticipants)"
                )
                
                infoCard(
                    icon: "clock.fill",
                    title: "持续时间",
                    value: "\(challenge.durationInDays)天"
                )
                
                infoCard(
                    icon: "target",
                    title: "目标",
                    value: "\(challenge.targetValue)次"
                )
            }
        }
    }
    
    // MARK: - 信息卡片
    
    private func infoCard(icon: String, title: String, value: String) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(difficultyColor)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.white.opacity(0.7))
            
            Text(value)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.white)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.black.opacity(0.3))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(difficultyColor.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    // MARK: - 挑战进度
    
    private func challengeProgress(_ participation: EAUniverseChallengeParticipation) -> some View {
        VStack(spacing: 16) {
            HStack {
                Text("我的进度")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Spacer()
                
                Text("\(participation.currentProgress)/\(challenge.targetValue)")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(difficultyColor)
            }
            
            // 进度条
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // 背景
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.white.opacity(0.2))
                        .frame(height: 12)
                    
                    // 进度
                    RoundedRectangle(cornerRadius: 8)
                        .fill(
                            LinearGradient(
                                colors: [difficultyColor, difficultyColor.opacity(0.7)],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(
                            width: geometry.size.width * progressPercentage(participation),
                            height: 12
                        )
                        .animation(.easeInOut(duration: 0.5), value: progressPercentage(participation))
                }
            }
            .frame(height: 12)
            
            // 进度状态
            HStack {
                if participation.isCompleted {
                    Label("已完成", systemImage: "checkmark.circle.fill")
                        .foregroundColor(.green)
                } else {
                    Label("进行中", systemImage: "play.circle.fill")
                        .foregroundColor(difficultyColor)
                }
                
                Spacer()
                
                Text("\(Int(progressPercentage(participation) * 100))%")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white.opacity(0.8))
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.black.opacity(0.4))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(difficultyColor.opacity(0.4), lineWidth: 1)
                )
        )
    }
    
    // MARK: - 挑战规则
    
    private var challengeRules: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("挑战规则")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            VStack(alignment: .leading, spacing: 8) {
                ruleItem("🎯", "目标：\(challenge.challengeTypeDescription)")
                ruleItem("📅", "时间：\(formatDate(challenge.startDate)) - \(formatDate(challenge.endDate))")
                ruleItem("🏆", "完成标准：达到 \(challenge.targetValue) 次目标")
                ruleItem("⚡", "奖励倍数：\(String(format: "%.1f", challenge.energyMultiplier))x")
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.black.opacity(0.3))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    // MARK: - 规则项
    
    private func ruleItem(_ icon: String, _ text: String) -> some View {
        HStack(spacing: 12) {
            Text(icon)
                .font(.title3)
            
            Text(text)
                .font(.body)
                .foregroundColor(.white.opacity(0.9))
        }
    }
    
    // MARK: - 奖励信息
    
    private var rewardInfo: some View {
        VStack(spacing: 16) {
            Text("奖励详情")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            HStack(spacing: 20) {
                // 星际能量奖励
                VStack(spacing: 8) {
                    Image(systemName: "sparkles")
                        .font(.title)
                        .foregroundColor(.yellow)
                    
                    Text("\(challenge.calculateBaseStellarReward())")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.yellow)
                    
                    Text("星际能量")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                }
                
                Divider()
                    .background(Color.white.opacity(0.3))
                    .frame(height: 60)
                
                // 成就徽章
                VStack(spacing: 8) {
                    Image(systemName: challenge.badgeIcon)
                        .font(.title)
                        .foregroundColor(difficultyColor)
                    
                    Text("专属徽章")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(difficultyColor)
                    
                    Text("永久收藏")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                }
                
                if !challenge.specialRewards.isEmpty {
                    Divider()
                        .background(Color.white.opacity(0.3))
                        .frame(height: 60)
                    
                    // 特殊奖励
                    VStack(spacing: 8) {
                        Image(systemName: "gift.fill")
                            .font(.title)
                            .foregroundColor(.purple)
                        
                        Text("特殊奖励")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.purple)
                        
                        Text("\(challenge.specialRewards.count)项")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.7))
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.yellow.opacity(0.1),
                            Color.purple.opacity(0.05)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    Color.yellow.opacity(0.4),
                                    Color.purple.opacity(0.3)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
        )
    }
    
    // MARK: - 操作按钮
    
    private var actionButtons: some View {
        VStack(spacing: 16) {
            if !isParticipating && challenge.canParticipate() {
                // 参与按钮
                Button(action: {
                    showingJoinConfirmation = true
                }) {
                    HStack {
                        Image(systemName: "plus.circle.fill")
                            .font(.title3)
                        
                        Text("参与挑战")
                            .font(.headline)
                            .fontWeight(.bold)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(difficultyColor)
                    )
                    .foregroundColor(.white)
                }
                .disabled(isLoading)
                
            } else if isParticipating {
                // 已参与状态
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title3)
                        .foregroundColor(.green)
                    
                    Text("已参与挑战")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.green.opacity(0.2))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.green.opacity(0.4), lineWidth: 1)
                        )
                )
            } else {
                // 不可参与状态
                HStack {
                    Image(systemName: "exclamationmark.circle")
                        .font(.title3)
                        .foregroundColor(.orange)
                    
                    Text("名额已满")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.orange)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.orange.opacity(0.2))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.orange.opacity(0.4), lineWidth: 1)
                        )
                )
            }
        }
    }
    
    // MARK: - 分享按钮
    
    private var shareButton: some View {
        Button(action: {
            // 分享功能实现
        }) {
            Image(systemName: "square.and.arrow.up")
                .foregroundColor(.white)
        }
    }
    
    // MARK: - 数据加载
    
    private func loadParticipationInfo() async {
        guard let currentUser = await sessionManager.safeCurrentUser,
              let container = repositoryContainer else { return }

        do {
            participation = try await container.challengeRepository.fetchParticipation(
                challengeId: challenge.id,
                userId: currentUser.id
            )
            isParticipating = participation != nil
        } catch {
            errorMessage = "加载参与信息失败：\(error.localizedDescription)"
        }
    }
    
    // MARK: - 参与挑战
    
    private func joinChallenge() async {
        guard let currentUser = await sessionManager.safeCurrentUser,
              let container = repositoryContainer else {
            errorMessage = "用户未登录或服务不可用"
            return
        }

        isLoading = true

        do {
            let newParticipation = try await container.challengeRepository.joinChallenge(challenge, user: currentUser)
            participation = newParticipation
            isParticipating = true
        } catch {
            errorMessage = "参与挑战失败：\(error.localizedDescription)"
        }

        isLoading = false
    }
    
    // MARK: - 计算属性
    
    private var difficultyColor: Color {
        switch challenge.difficulty {
        case "easy":
            return .green
        case "normal":
            return .blue
        case "hard":
            return .purple
        case "extreme":
            return .red
        default:
            return .gray
        }
    }
    
    private func progressPercentage(_ participation: EAUniverseChallengeParticipation) -> Double {
        guard challenge.targetValue > 0 else { return 0 }
        return min(Double(participation.currentProgress) / Double(challenge.targetValue), 1.0)
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM/dd"
        return formatter.string(from: date)
    }
}

// MARK: - 预览
#Preview {
    EAUniverseChallengeDetailView(challenge: sampleChallenge)
        .preferredColorScheme(.dark)
}

// MARK: - 预览数据
private let sampleChallenge: EAUniverseChallenge = {
    let challenge = EAUniverseChallenge(
        title: "银河系早起探索者",
        challengeDescription: "连续7天早起完成晨间习惯，探索银河系的奥秘，开启全新的宇宙探索之旅",
        targetHabitCategory: "morning_routine",
        startDate: Date(),
        endDate: Calendar.current.date(byAdding: .day, value: 7, to: Date()) ?? Date(),
        stellarReward: 500
    )
    challenge.difficulty = "normal"
    challenge.cosmicDifficulty = 3
    challenge.participantCount = 42
    challenge.energyMultiplier = 1.5
    challenge.specialRewards = ["早鸟徽章", "晨光能量包"]
    return challenge
}() 