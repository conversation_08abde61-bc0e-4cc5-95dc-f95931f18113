import SwiftUI
import SwiftData

/// 🔑 根本修复：统一导航目标定义
/// 社区模块的所有导航目标，确保类型安全的导航
enum EACommunityNavigationDestination: Hashable {
    /// 好友列表页面
    case friendList
    /// 好友聊天页面
    /// - Parameter UUID: 好友关系ID
    case friendChat(UUID)
    /// 屏蔽用户管理界面
    /// 🔑 批次三新增：提供屏蔽用户列表查看和管理功能
    case blockedUserList
}

/// Sheet类型枚举 - 统一管理所有Sheet状态
enum EACommunitySheetType: Identifiable {
    case createPost
    case postDetail(EACommunityPost)
    case imagePicker
    case challengeList  // ✅ 新增：挑战列表入口
    case friendList     // ✅ 新增：好友列表入口

    var id: String {
        switch self {
        case .createPost: return "createPost"
        case .postDetail(let post): return "postDetail_\(post.id)"
        case .imagePicker: return "imagePicker"
        case .challengeList: return "challengeList"  // ✅ 新增
        case .friendList: return "friendList"        // ✅ 新增
        }
    }
}

/// 社区主页面 - 展示用户分享的习惯成果和互动
/// 遵循项目MVVM架构规范，使用Repository模式进行数据访问
/// ✅ 修复：实现统一Sheet状态管理，符合.cursorrules规范
struct EACommunityView: View {
    
    // MARK: - Properties
    
    @EnvironmentObject var sessionManager: EASessionManager
    @Environment(\.repositoryContainer) private var repositoryContainer
    @StateObject private var viewModel: EACommunityViewModel
    
    /// ✅ 修复：通过Environment注入图片缓存服务
    @Environment(\.imageCacheService) private var imageCacheService
    
    // MARK: - 🔑 性能优化：状态管理
    
    /// 新帖子内容
    @State private var postContent = ""
    
    /// ✅ 修复：统一Sheet状态管理
    @State private var activeSheet: EACommunitySheetType?
    
    /// 搜索文本
    @State private var searchText: String = ""
    
    /// 选中的图片数据
    @State private var selectedImages: [Data] = []

    /// 图片处理器
    @State private var imageProcessor = EAImageProcessor()

    /// 错误提示
    @State private var showImageError = false
    @State private var imageErrorMessage = ""

    /// 定位相关
    @State private var showLocationPicker = false
    @State private var selectedLocation: String = ""
    @State private var isLocationEnabled = false

    /// 发布状态
    @State private var isPublishing = false

    /// 创建帖子相关
    @State private var showCreatePost = false
    // 🔑 性能优化：筛选状态已移至ViewModel的filterState
    @State private var showAlert = false
    @State private var alertMessage = ""

    // MARK: - Initialization
    
    /// ✅ 阶段1修复：移除ViewModel的独立初始化，改为在body中动态初始化
    /// 这样可以确保Repository容器在ViewModel使用前就已可用
    init() {
        // 注意：StateObject的初始化需要在这里进行，但Repository设置将在body中处理
        _viewModel = StateObject(wrappedValue: EACommunityViewModel())
    }
    
    // MARK: - 主视图
    
    var body: some View {
        NavigationStack {
            ZStack {
                // 🌟 星域数字宇宙背景
                stellarUniverseBackground

                VStack(spacing: 0) {
                    // 🔑 新增：自定义导航栏标题
                    customNavigationHeader

                    // 搜索栏
                    searchBar

                    // 🔑 新增：筛选栏（优化版 - 紧凑设计）
                    filterBar

                    // 内容区域 - 🚨 关键修复：添加错误处理和降级UI
                    contentAreaWithErrorHandling
                }
            }
            .navigationBarHidden(true) // 隐藏系统导航栏，使用自定义设计
            // 🔑 根本修复：统一导航架构，处理所有导航目标
            .navigationDestination(for: EACommunityNavigationDestination.self) { destination in
                switch destination {
                case .friendList:
                    EAFriendListView()
                        .environmentObject(sessionManager)
                        .environmentObject(createFriendshipService())
                        .environmentObject(createFriendNotificationService())
                        .environment(\.repositoryContainer, repositoryContainer)
                case .friendChat(let friendshipId):
                    EAFriendChatView(friendshipId: friendshipId)
                        .environmentObject(sessionManager)
                        .environmentObject(createFriendChatService())
                        .environment(\.repositoryContainer, repositoryContainer)
                case .blockedUserList:
                    // 🔑 屏蔽功能修复：完整的环境对象注入
                    EABlockedUserListView()
                        .environmentObject(sessionManager)
                        .environment(\.repositoryContainer, repositoryContainer)
                }
            }
        }
        .onAppear {
            // ✅ 修复：避免在视图更新周期中发布状态变化
            Task { @MainActor in
                // 🔑 修复：渐进式初始化，避免启动时过度加载
                viewModel.isViewActive = true

                // 🔑 关键优化：轻量级初始化 - 只设置基本状态
                viewModel.viewWillAppear()

                // 🔑 修复：确保Repository容器已注入
                if let repositoryContainer = repositoryContainer {
                    // 设置ViewModel的依赖
                    viewModel.setRepositoryContainer(repositoryContainer)
                    viewModel.setSessionManager(sessionManager)

                    // 🔑 优化：延迟执行重量级操作，避免启动时闪烁
                    Task {
                        // 延迟200ms执行重量级初始化，确保UI已稳定
                        try? await Task.sleep(nanoseconds: 200_000_000)

                        // 重量级初始化 - 在后台执行
                        await viewModel.loadInitialData()

                        // 🔑 关键修复：在页面初始化时主动验证用户Profile状态
                        // 确保好友按钮在页面加载完成后立即可用，避免持续加载状态
                        if sessionManager.isLoggedIn && sessionManager.currentUserID != nil {
                            await viewModel.ensureUserProfileReady()
                        }
                    }
                } else {
                    // 🔑 修复：降级模式，显示空状态
                    Task { @MainActor in
                        viewModel.errorMessage = "数据服务暂时不可用"
                    }
                }

                // 🔑 修复：检查登录状态
                if !sessionManager.isLoggedIn {
                    // 用户未登录，显示登录引导
                }
            }
        }
        .onDisappear {
            // 🔑 新增：页面生命周期管理
            viewModel.viewWillDisappear()
            
            // 🔑 性能优化：简化离开逻辑，避免不必要的异步操作
            Task { @MainActor in
                await viewModel.handleTabSwitchAwayOptimized()
                
                // 🔑 内存优化：清理图片缓存
                imageCacheService?.clearMemoryCache()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("EACommunityPostDeleted"))) { notification in
            // 监听帖子删除通知，实时更新列表
            if let deletedPostId = notification.object as? UUID {
                Task {
                    await viewModel.handlePostDeleted(deletedPostId)
                }
            }
        }
        // 🔑 修复：移除View中的重复通知监听器，避免与ViewModel中的监听器冲突
        // 所有通知处理已移至ViewModel中，确保单一数据源和更好的性能
        .onReceive(NotificationCenter.default.publisher(for: UIApplication.didReceiveMemoryWarningNotification)) { _ in
            // 🔑 内存优化：收到内存警告时清理缓存
            Task { @MainActor in
                imageCacheService?.clearMemoryCache()
                viewModel.clearCache()
            }
        }
        // 🔑 修复：通知处理已移至ViewModel，避免View.body中的无限循环
        .refreshable {
            // 下拉刷新优化
            Task {
                await refreshPostsOptimized()
            }
        }
        .sheet(item: $activeSheet) { sheetType in
            // ✅ 修复：使用统一Sheet管理
            sheetContent(for: sheetType)
        }
        .background(
            // 🔑 性能优化：简化背景效果
            Color.black.ignoresSafeArea()
        )
        .navigationBarTitleDisplayMode(.large)
    }
    
    // MARK: - 🚨 关键修复：安全的ViewModel设置
    
    /// 🚨 iPad真机修复：安全设置ViewModel，添加错误处理
    private func setupViewModelSafely() {
        
        // 🚨 关键修复：添加Repository容器检查
        guard let container = repositoryContainer else {
            showAlert = true
            alertMessage = "数据服务初始化中，请稍后重试"
            return
        }
        
        // 🚨 关键修复：添加SessionManager检查
        guard sessionManager.isLoggedIn else {
            return
        }
        
        // 🔒 性能优化：避免重复设置Repository容器
        if !viewModel.hasRepositoryContainer {
            viewModel.setRepositoryContainer(container)
        }
        
        // 🔑 关键修复：设置SessionManager，确保ViewModel能够访问当前用户信息
        viewModel.setSessionManager(sessionManager)
        
        // 🔑 性能优化：使用优化的Tab切换逻辑
        Task { @MainActor in
            await viewModel.handleTabSwitchBackOptimized() // 🔑 修复：页面重新激活时恢复状态
            await viewModel.handleTabSwitchToActiveOptimized()
            
            // 🔑 关键优化：如果已有数据，立即初始化状态
            if !viewModel.posts.isEmpty {
                // 简化状态初始化，避免复杂的批量预载
                // 数据已存在，无需额外处理
            }
            
            // 页面加载完成
        }
    }
    
    // MARK: - 🚨 关键修复：带错误处理的内容区域
    
    /// 🚨 iPad真机修复：带错误处理的内容区域
    private var contentAreaWithErrorHandling: some View {
        Group {
            // 🚨 关键修复：Repository容器检查
            if repositoryContainer == nil {
                repositoryErrorView
            }
            // 🔑 新增：登录状态检查
            else if !sessionManager.isLoggedIn || sessionManager.currentUserID == nil {
                loginRequiredView
            } else if viewModel.isLoading && viewModel.posts.isEmpty {
                loadingView
            } else if viewModel.posts.isEmpty {
                emptyStateView
            } else {
                optimizedPostsList
            }
        }
        .alert("提示", isPresented: $showAlert) {
            Button("确定") { }
            Button("重试") {
                setupViewModelSafely()
            }
        } message: {
            Text(alertMessage)
        }
    }
    
    // MARK: - 🚨 新增：Repository错误视图
    
    /// 🚨 iPad真机修复：Repository服务错误视图
    private var repositoryErrorView: some View {
        VStack(spacing: 24) {
            // 错误图标
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.red.opacity(0.2),
                                Color.orange.opacity(0.1)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 80, height: 80)
                
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 32, weight: .medium))
                    .foregroundColor(.orange)
            }
            
            VStack(spacing: 12) {
                Text("数据服务初始化中")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.white)
                
                Text("正在连接星际网络，请稍后重试")
                    .font(.system(size: 16))
                    .foregroundColor(.white.opacity(0.7))
                    .multilineTextAlignment(.center)
            }
            
            // 重试按钮
            Button(action: {
                setupViewModelSafely()
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "arrow.clockwise")
                        .font(.system(size: 16, weight: .medium))
                    Text("重新连接")
                        .font(.system(size: 16, weight: .medium))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.hexColor("40E0D0"),
                                    Color.blue
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                )
            }
        }
        .padding(.horizontal, 32)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.black.opacity(0.3))
    }
    
    // MARK: - 🔑 性能优化：简化的刷新方法
    
    /// 🔑 性能优化：简化的刷新方法
    private func refreshPostsOptimized() async {
        // 重置状态
        await MainActor.run {
            viewModel.currentPage = 0
            viewModel.hasNextPage = true
        }

        // 清理旧状态
        viewModel.clearCache()

        // 加载新数据
        await viewModel.loadPosts(refresh: true)
    }
    
    // MARK: - Sheet内容管理
    
    /// Sheet内容视图
    @ViewBuilder
    private func sheetContent(for sheetType: EACommunitySheetType) -> some View {
        switch sheetType {
        case .createPost:
            createPostSheet
        case .postDetail(let post):
            NavigationView {
                if let container = repositoryContainer {
                    EAPostDetailView(
                        post: post,
                        sessionManager: sessionManager,
                        repositoryContainer: container,
                        onPostDeleted: {
                            // 删除成功后关闭详情页并清除缓存
                            activeSheet = nil
                            viewModel.clearCache()
                        }
                    )
                    .environmentObject(sessionManager)
                    .environment(\.repositoryContainer, repositoryContainer)
                } else {
                    Text("Repository容器未初始化")
                        .foregroundColor(.red)
                }
            }
        case .imagePicker:
            // 实现图片选择器
            Text("Image Picker")
        case .challengeList:
            // 集成挑战列表页面
            NavigationView {
                EAUniverseChallengeListView()
                    .environmentObject(sessionManager)
            }
        case .friendList:
            // 好友列表现在通过NavigationLink导航，不再使用Sheet
            EmptyView()
        }
    }
    
    // MARK: - 子视图组件

    /// 🔑 新增：自定义导航栏标题 - 符合iOS设计规范
    private var customNavigationHeader: some View {
        VStack(spacing: 0) {
            // 顶部安全区域适配
            Color.clear
                .frame(height: 0)
                .safeAreaInset(edge: .top) {
                    Color.clear.frame(height: 0)
                }

            // 导航栏内容
            HStack {
                // 左侧：挑战按钮
                challengeButton
                    .frame(width: 44, height: 44) // 标准触控区域

                Spacer()

                // 中央：品牌标题
                VStack(spacing: 2) {
                    Text("星域")
                        .font(.system(size: 24, weight: .bold, design: .rounded))
                        .foregroundStyle(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.hexColor("40E0D0"), // 荧光青色
                                    Color.hexColor("00CED1")  // 深青色
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )

                    Text("STELLAR NETWORK")
                        .font(.system(size: 10, weight: .medium, design: .monospaced))
                        .foregroundColor(.white.opacity(0.6))
                        .tracking(1.2) // 字母间距
                }

                Spacer()

                // 右侧按钮组
                HStack(spacing: 8) {
                    // 好友列表按钮
                    friendListButton
                        .frame(width: 44, height: 44) // 标准触控区域

                    // 创建帖子按钮
                    createPostButton
                        .frame(width: 44, height: 44) // 标准触控区域
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                // 渐变背景，与宇宙主题保持一致
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.black.opacity(0.9),
                        Color.black.opacity(0.7),
                        Color.clear
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
            )

            // 分隔线
            Rectangle()
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.clear,
                            Color.hexColor("40E0D0").opacity(0.3),
                            Color.clear
                        ]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .frame(height: 1)
        }
    }

    /// 星际信标搜索栏 - 优化设计
    private var searchBar: some View {
        HStack(spacing: 12) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(Color.hexColor("40E0D0"))

            TextField("搜索星际信标...", text: $searchText)
                .font(.system(size: 16))
                .foregroundColor(.white)
                .onSubmit {
                    Task {
                        viewModel.searchPosts(with: searchText)
                    }
                }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.black.opacity(0.4))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.hexColor("40E0D0").opacity(0.3),
                                    Color.blue.opacity(0.2)
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            ),
                            lineWidth: 1
                        )
                )
        )
        .padding(.horizontal, 16)
        .padding(.top, 12)
        .padding(.bottom, 8)
    }
    
    /// 🔑 新增：筛选栏
    private var filterBar: some View {
        EACommunityFilterBar(
            selectedCategory: $viewModel.filterState.selectedCategory,
            selectedTags: $viewModel.filterState.selectedTags,
            availableCategories: viewModel.filterState.availableCategories,
            popularTags: viewModel.filterState.popularTags,
            onFilterChanged: {
                viewModel.applyFilters(
                    category: viewModel.filterState.selectedCategory,
                    tags: viewModel.filterState.selectedTags
                )
            },
            onClearFilters: {
                viewModel.clearFilters()
            }
        )
        .padding(.horizontal, 16)
        .padding(.bottom, 8)
    }
    
    /// 内容区域
    private var contentArea: some View {
        Group {
            // 🔑 新增：登录状态检查
            if !sessionManager.isLoggedIn || sessionManager.currentUserID == nil {
                loginRequiredView
            } else if viewModel.isLoading && viewModel.posts.isEmpty {
                loadingView
            } else if viewModel.posts.isEmpty {
                emptyStateView
            } else {
                optimizedPostsList
            }
        }
    }
    
    /// 🔑 新增：登录引导视图
    private var loginRequiredView: some View {
        VStack(spacing: 24) {
            // 宇宙探索图标
            ZStack {
                Circle()
                    .fill(
                        RadialGradient(
                            gradient: Gradient(colors: [
                                Color.cyan.opacity(0.3),
                                Color.blue.opacity(0.1),
                                Color.clear
                            ]),
                            center: .center,
                            startRadius: 20,
                            endRadius: 80
                        )
                    )
                    .frame(width: 120, height: 120)
                
                Image(systemName: "person.crop.circle.badge.plus")
                    .font(.system(size: 48, weight: .light))
                    .foregroundStyle(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.hexColor("40E0D0"),
                                Color.cyan
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            }
            
            VStack(spacing: 12) {
                Text("加入星际探索者社区")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.white)
                
                Text("登录后即可分享您的习惯成果\n与其他探索者互动交流")
                    .font(.system(size: 16))
                    .foregroundColor(.white.opacity(0.7))
                    .multilineTextAlignment(.center)
                    .lineSpacing(4)
            }
            
            VStack(spacing: 16) {
                // 登录按钮
                Button(action: {
                    // TODO: 导航到登录页面
                    // 这里需要根据项目的导航结构来实现
                }) {
                    HStack {
                        Image(systemName: "person.circle.fill")
                            .font(.system(size: 18))
                        Text("登录账号")
                            .font(.system(size: 16, weight: .semibold))
                    }
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.hexColor("40E0D0"),
                                Color.cyan
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(12)
                }
                
                // 注册按钮
                Button(action: {
                    // TODO: 导航到注册页面
                    // 这里需要根据项目的导航结构来实现
                }) {
                    HStack {
                        Image(systemName: "plus.circle")
                            .font(.system(size: 18))
                        Text("创建账号")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color.hexColor("40E0D0"),
                                        Color.cyan
                                    ]),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                ),
                                lineWidth: 2
                            )
                    )
                }
            }
            .padding(.horizontal, 32)
            
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.top, 60)
    }
    
    /// 加载视图 - 数字宇宙主题
    private var loadingView: some View {
        VStack {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .cyan))
                .scaleEffect(1.5)
            Text("正在连接星际网络...")
                .foregroundColor(.cyan.opacity(0.8))
                .padding(.top, 12)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    /// 空状态视图 - 优化版（移除初始化按钮）
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            // 宇宙探索图标
            ZStack {
                Circle()
                    .fill(
                        RadialGradient(
                            gradient: Gradient(colors: [
                                Color.cyan.opacity(0.3),
                                Color.blue.opacity(0.1),
                                Color.clear
                            ]),
                            center: .center,
                            startRadius: 20,
                            endRadius: 80
                        )
                    )
                    .frame(width: 120, height: 120)
                
                Image(systemName: "sparkles")
                    .font(.system(size: 40, weight: .light))
                    .foregroundColor(.cyan)
            }
            
            Text("开始您的星际探索之旅")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Text("分享您的习惯成果，与其他探索者一起成长！")
                .foregroundColor(.cyan.opacity(0.8))
                .multilineTextAlignment(.center)
            
            // 🔑 新增：鼓励创建第一条帖子的按钮
            Button("创建我的第一条星际信标") {
                activeSheet = .createPost
            }
            .buttonStyle(.borderedProminent)
            .tint(.cyan)
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    /// 🚀 微信朋友圈级别的帖子列表优化
    private var optimizedPostsList: some View {
        List {
            postsListSection
        }
        .listStyle(.plain)
        .scrollContentBackground(.hidden)
        .scrollBounceBehavior(.basedOnSize)
        // 🚀 关键优化：启用List的性能优化特性
        .scrollIndicators(.hidden)
        .environment(\.defaultMinListRowHeight, 0)
        // 🚀 新增：更激进的内存优化
        .onReceive(NotificationCenter.default.publisher(for: UIApplication.didReceiveMemoryWarningNotification)) { _ in
            // 立即触发内存清理
            viewModel.handleMemoryWarning()
            imageCacheService?.clearMemoryCache()
            
            // 强制垃圾回收（仅在内存警告时）
            DispatchQueue.global(qos: .utility).async {
                // 清理图片缓存
                Task { @MainActor in
                    imageCacheService?.clearMemoryCache()
                }
            }
        }
        // 🚀 性能优化：原生下拉刷新
        .refreshable {
            Task {
                await refreshPostsOptimized()
            }
        }
        // 🚀 滚动性能优化：减少重绘
        .clipped()
        // 🚀 新增：滚动性能监控
        .background(
            GeometryReader { geometry in
                Color.clear
                    .onAppear {
                        // 记录可见区域，用于优化图片加载
                        // viewModel.updateVisibleArea(geometry.frame(in: .global))
                    }
                    .onChange(of: geometry.frame(in: .global)) { _, newFrame in
                        // 滚动时更新可见区域
                        // viewModel.updateVisibleArea(newFrame)
                    }
            }
        )
    }
    
    /// 帖子列表 - 🚀 性能优化：使用优化版帖子卡片
    private var postsListSection: some View {
        ForEach(viewModel.posts, id: \.id) { post in
            // 🚀 关键优化：使用EAOptimizedCommunityPostCard获得微信朋友圈级别流畅度
            EAOptimizedCommunityPostCard(
                post: post,
                isLiked: viewModel.getPostLikeStatus(postId: post.id),
                currentLikeCount: viewModel.getPostLikeCount(postId: post.id),
                currentCommentCount: viewModel.getPostCommentCount(postId: post.id),
                onLike: { (post: EACommunityPost) async throws in
                    viewModel.toggleLike(for: post)
                },
                onComment: { (post: EACommunityPost) in
                    activeSheet = .postDetail(post)
                },
                onShare: { (post: EACommunityPost) in
                    viewModel.sharePost(post)
                },
                onPostTap: { (post: EACommunityPost) in
                    activeSheet = .postDetail(post)
                },
                onUserProfileTap: {
                    // TODO: 处理用户档案点击，导航到用户详情页
                }
            )
            .listRowBackground(Color.clear)
            .listRowSeparator(.hidden)
            .listRowInsets(EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 16))
            .onAppear {
                // 🔑 性能优化：简化的无限滚动检测
                viewModel.handlePostAppearLightweight(post: post)
            }
        }
    }
    
    /// 帖子上下文菜单
    private func postContextMenu(for post: EACommunityPost) -> some View {
        Group {
            Button("分享") {
                // TODO: 实现分享功能
            }
            
            // 只有帖子作者才能删除
            Button("删除", role: .destructive) {
                Task {
                    await viewModel.deletePost(post)
                }
            }
        }
    }
    
    /// 创建帖子按钮 - 重新设计
    private var createPostButton: some View {
        Button {
            // 🔑 新增：检查登录状态
            if sessionManager.isLoggedIn && sessionManager.currentUserID != nil {
                activeSheet = .createPost
            } else {
                // 显示登录提示
                alertMessage = "请先登录后再发布帖子"
                showAlert = true
            }
        } label: {
            ZStack {
                // 背景圆形
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                // 🔑 新增：根据登录状态调整颜色
                                (sessionManager.isLoggedIn && sessionManager.currentUserID != nil)
                                    ? Color.hexColor("40E0D0").opacity(0.2)
                                    : Color.gray.opacity(0.2),
                                (sessionManager.isLoggedIn && sessionManager.currentUserID != nil)
                                    ? Color.hexColor("40E0D0").opacity(0.1)
                                    : Color.gray.opacity(0.1)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        Circle()
                            .stroke(
                                (sessionManager.isLoggedIn && sessionManager.currentUserID != nil)
                                    ? Color.hexColor("40E0D0").opacity(0.4)
                                    : Color.gray.opacity(0.4),
                                lineWidth: 1
                            )
                    )

                // 加号图标
                Image(systemName: (sessionManager.isLoggedIn && sessionManager.currentUserID != nil) ? "plus" : "lock")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(
                        (sessionManager.isLoggedIn && sessionManager.currentUserID != nil)
                            ? Color.hexColor("40E0D0")
                            : Color.gray
                    )
            }
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel(
            (sessionManager.isLoggedIn && sessionManager.currentUserID != nil)
                ? "创建新帖子"
                : "需要登录才能创建帖子"
        )
    }

    /// 宇宙挑战功能入口按钮 - 重新设计
    private var challengeButton: some View {
        Button {
            activeSheet = .challengeList
        } label: {
            ZStack {
                // 背景圆形
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.yellow.opacity(0.2),
                                Color.orange.opacity(0.1)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        Circle()
                            .stroke(Color.yellow.opacity(0.4), lineWidth: 1)
                    )

                // 星星图标
                Image(systemName: "star.fill")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.yellow)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel("宇宙挑战")
    }

    /// 好友列表按钮 - 星际伙伴入口
    private var friendListButton: some View {
        Group {
            if sessionManager.isLoggedIn && sessionManager.currentUserID != nil {
                // 🔑 优化：简化状态管理，减少不必要的加载状态
                if let error = viewModel.userProfileLoadingError {
                    // 用户Profile加载失败 - 显示错误提示
                    Button {
                        alertMessage = "用户数据异常：\(error)，请重新登录后再试"
                        showAlert = true
                    } label: {
                        friendListButtonContent
                            .opacity(0.6)
                    }
                    .buttonStyle(PlainButtonStyle())
                } else if viewModel.isUserProfileReady {
                    // 🔑 根本修复：Profile就绪时使用NavigationLink
                    NavigationLink(value: EACommunityNavigationDestination.friendList) {
                        friendListButtonContent
                    }
                    .buttonStyle(PlainButtonStyle())
                } else {
                    // 🔑 优化：Profile未就绪时显示简洁的加载状态，点击可重试
                    Button {
                        Task {
                            await viewModel.ensureUserProfileReady()
                        }
                    } label: {
                        friendListButtonContent
                            .opacity(0.8)
                            .overlay(
                                // 🔑 优化：更小更不显眼的加载指示器
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white.opacity(0.7)))
                                    .scaleEffect(0.5)
                                    .offset(x: 15, y: -8)
                            )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            } else {
                // 未登录用户 - 显示登录提示
                Button {
                    alertMessage = "请先登录后再查看好友列表"
                    showAlert = true
                } label: {
                    friendListButtonContent
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .accessibilityLabel("星际伙伴")
        .accessibilityHint(
            sessionManager.isLoggedIn && sessionManager.currentUserID != nil && viewModel.isUserProfileReady
                ? "查看和管理你的星际伙伴"
                : "需要登录且数据就绪才能查看好友列表"
        )
    }

    /// 好友列表按钮内容
    private var friendListButtonContent: some View {
        ZStack {
            // 背景圆形
            Circle()
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.hexColor("40E0D0").opacity(0.2),
                            Color.blue.opacity(0.1)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    Circle()
                        .stroke(Color.hexColor("40E0D0").opacity(0.4), lineWidth: 1)
                )

            // 图标
            Image(systemName: "person.2.fill")
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(Color.hexColor("40E0D0"))
        }
    }

    /// 星际信标创建界面 - 重新设计优化版
    private var createPostSheet: some View {
        NavigationView {
            ZStack {
                // 🌟 星域数字宇宙背景
                stellarUniverseBackground

                ScrollView {
                    VStack(spacing: 20) {
                        // 🔑 优化：紧凑的内容输入区域
                        VStack(alignment: .leading, spacing: 12) {
                            // 输入提示 - 移除重复标题
                            HStack {
                                Image(systemName: "antenna.radiowaves.left.and.right")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.cyan)
                                Text("分享你的星际探索...")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.white.opacity(0.8))
                                Spacer()
                            }

                            // 🔑 优化：合理大小的文本输入框
                            ZStack(alignment: .topLeading) {
                                if postContent.isEmpty {
                                    Text("记录你的发现、感悟或想法...")
                                        .foregroundColor(.white.opacity(0.5))
                                        .padding(.horizontal, 16)
                                        .padding(.vertical, 12)
                                        .allowsHitTesting(false)
                                }

                                TextEditor(text: $postContent)
                                    .frame(minHeight: 100, maxHeight: 150)
                                    .padding(12)
                                    .background(
                                        RoundedRectangle(cornerRadius: 12)
                                            .fill(Color.black.opacity(0.6))
                                            .overlay(
                                                RoundedRectangle(cornerRadius: 12)
                                                    .stroke(
                                                        LinearGradient(
                                                            gradient: Gradient(colors: [.cyan, .blue, .purple]),
                                                            startPoint: .topLeading,
                                                            endPoint: .bottomTrailing
                                                        ),
                                                        lineWidth: 1
                                                    )
                                            )
                                    )
                                    .foregroundColor(.white)
                                    .scrollContentBackground(.hidden)
                            }
                        }

                        // 🔑 新增：功能选项区域
                        VStack(spacing: 16) {
                            // 🔑 新增：位置分享
                            createPostOptionRow(
                                icon: "location",
                                title: "添加位置",
                                subtitle: selectedLocation.isEmpty ? "分享你的位置" : selectedLocation,
                                hasContent: !selectedLocation.isEmpty,
                                action: {
                                    showLocationPicker = true
                                }
                            )
                        }

                        // 🔑 图片选择器 - 移到功能区域下方
                        EAPhotoSelector(
                            selectedImages: $selectedImages,
                            maxSelectionCount: 9,
                            onError: { errorMessage in
                                imageErrorMessage = errorMessage
                                showImageError = true
                            }
                        )

                        Spacer(minLength: 100) // 为键盘留出空间
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 10)
                }
            }
            .navigationTitle("星际信标广播")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        activeSheet = nil
                        postContent = ""
                        selectedImages = []
                        selectedLocation = ""
                    }
                    .foregroundColor(.cyan)
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(isPublishing ? "发布中..." : "广播") {
                        Task {
                            await publishPostWithImages()
                        }
                    }
                    .disabled(postContent.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isPublishing)
                    .foregroundColor(postContent.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isPublishing ? .gray : .cyan)
                    .fontWeight(.semibold)
                }
            }
            .toolbarBackground(.black.opacity(0.8), for: .navigationBar)
            .alert("图片处理错误", isPresented: $showImageError) {
                Button("确定") { }
            } message: {
                Text(imageErrorMessage)
            }
            .alert("发布结果", isPresented: $showAlert) {
                Button("确定") { 
                    // 🔑 修复：移除成功确认逻辑，只处理错误情况
                }
            } message: {
                Text(alertMessage)
            }
            .sheet(isPresented: $showLocationPicker) {
                locationPickerSheet
            }
        }
    }

    // MARK: - 辅助UI组件

    /// 创建帖子功能选项行
    private func createPostOptionRow(
        icon: String,
        title: String,
        subtitle: String,
        hasContent: Bool = false,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            HStack(spacing: 12) {
                // 图标
                Image(systemName: icon)
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(hasContent ? .cyan : .white.opacity(0.7))
                    .frame(width: 24, height: 24)

                // 文字信息
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                    Text(subtitle)
                        .font(.system(size: 14))
                        .foregroundColor(.white.opacity(0.6))
                }

                Spacer()

                // 指示器
                Image(systemName: hasContent ? "checkmark.circle.fill" : "chevron.right")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(hasContent ? .cyan : .white.opacity(0.4))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color.black.opacity(0.4))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(hasContent ? Color.cyan.opacity(0.5) : Color.white.opacity(0.1), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    /// 位置选择器Sheet
    private var locationPickerSheet: some View {
        NavigationView {
            ZStack {
                stellarUniverseBackground

                VStack(spacing: 20) {
                    // 预设位置选项
                    VStack(spacing: 12) {
                        locationOption("🏠 家", "温馨的港湾")
                        locationOption("🏢 办公室", "工作的地方")
                        locationOption("☕️ 咖啡厅", "思考的角落")
                        locationOption("🏫 学校", "学习的殿堂")
                        locationOption("🌳 公园", "自然的怀抱")
                        locationOption("🏃‍♂️ 健身房", "挥洒汗水")
                    }

                    Spacer()
                }
                .padding(20)
            }
            .navigationTitle("选择位置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        showLocationPicker = false
                    }
                    .foregroundColor(.cyan)
                }
            }
            .toolbarBackground(.black.opacity(0.8), for: .navigationBar)
        }
    }

    /// 位置选项
    private func locationOption(_ title: String, _ subtitle: String) -> some View {
        Button {
            selectedLocation = title
            showLocationPicker = false
        } label: {
            HStack(spacing: 12) {
                Text(title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)

                Text(subtitle)
                    .font(.system(size: 14))
                    .foregroundColor(.white.opacity(0.6))

                Spacer()

                if selectedLocation == title {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.cyan)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color.black.opacity(0.4))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(selectedLocation == title ? Color.cyan.opacity(0.5) : Color.white.opacity(0.1), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - 图片处理方法

    /// 发布帖子（包含图片）
    private func publishPostWithImages() async {
        guard !postContent.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            await MainActor.run {
                showAlert = true
                alertMessage = "请输入帖子内容"
            }
            return
        }
        
        await MainActor.run {
            isPublishing = true
        }
        
        do {
            var imagePaths: [String] = []
            
            // 处理选中的图片
            for imageData in selectedImages {
                let imagePath = try await imageProcessor.processAndSaveImage(imageData)
                imagePaths.append(imagePath)
            }
            
            // 通过ViewModel创建帖子
            try await viewModel.createPost(
                content: postContent,
                imagePaths: imagePaths,
                                    category: viewModel.filterState.selectedCategory,
                    tags: viewModel.filterState.selectedTags
            )
            
            await MainActor.run {
                // 🔑 修复：发布成功后直接关闭Sheet，移除冗余确认弹窗
                postContent = ""
                selectedImages = []
                            viewModel.filterState.selectedCategory = nil
            viewModel.filterState.selectedTags = []
                selectedLocation = ""
                isPublishing = false
                
                // 🔑 修复：直接关闭Sheet，不显示成功弹窗
                activeSheet = nil
                
                // 🔑 可选：使用轻量级的成功反馈（如触觉反馈）
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()
            }
            
        } catch {
            await MainActor.run {
                isPublishing = false
                showAlert = true
                alertMessage = "发布失败：\(error.localizedDescription)"
            }
        }
    }
    
    /// 🌟 星域数字宇宙背景 - 性能优化版
    private var stellarUniverseBackground: some View {
        // 🔑 性能优化：使用简化的静态渐变背景，移除动态星光效果
        LinearGradient(
            colors: [
                Color.hexColor("000B1A"), // 深空蓝
                Color.hexColor("1E3A8A").opacity(0.3), // 深蓝色
                Color.hexColor("0F172A"), // 深空蓝
                Color.hexColor("1E3A8A").opacity(0.2)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }

    // MARK: - 🔑 性能优化：导航和分享方法
    
    /// 导航到帖子详情页
    private func navigateToPostDetail(_ post: EACommunityPost) {
        activeSheet = .postDetail(post)
    }
    
    /// 导航到用户资料页
    private func navigateToUserProfile(_ user: EAUser?) {
        guard user != nil else { return }
        // TODO: 实现用户资料页导航
    }
    
    /// 分享帖子
    private func sharePost(_ post: EACommunityPost) {
        // TODO: 实现分享功能
    }

    // MARK: - 🔑 紧急修复：优化服务创建，避免重复实例化

    /// 缓存的服务实例，避免重复创建
    @State private var cachedFriendshipService: EAFriendshipService?
    @State private var cachedNotificationService: EAFriendNotificationService?
    @State private var cachedChatService: EAFriendChatService?

    /// 获取或创建好友服务实例（单例模式）
    /// 🔑 紧急修复：避免每次导航都创建新实例，减少内存压力和状态不一致
    private func createFriendshipService() -> EAFriendshipService {
        if let cached = cachedFriendshipService {
            return cached
        }

        guard let repositoryContainer = repositoryContainer else {
            // 创建一个临时的空服务，避免崩溃
            let tempContainer = EARepositoryContainerImpl(modelContainer: try! EAAppSchema.createPreviewContainer())
            let tempIntegrityGuard = EAUserIntegrityGuard(repositoryContainer: tempContainer)
            let service = EAFriendshipService(repositoryContainer: tempContainer, sessionManager: sessionManager, integrityGuard: tempIntegrityGuard)
            cachedFriendshipService = service
            return service
        }

        let integrityGuard = EAUserIntegrityGuard(repositoryContainer: repositoryContainer)
        let service = EAFriendshipService(repositoryContainer: repositoryContainer, sessionManager: sessionManager, integrityGuard: integrityGuard)
        cachedFriendshipService = service
        return service
    }

    /// 获取或创建好友通知服务实例（单例模式）
    private func createFriendNotificationService() -> EAFriendNotificationService {
        if let cached = cachedNotificationService {
            return cached
        }

        guard let repositoryContainer = repositoryContainer else {
            // 创建一个临时的空服务，避免崩溃
            let tempContainer = EARepositoryContainerImpl(modelContainer: try! EAAppSchema.createPreviewContainer())
            let service = EAFriendNotificationService(repositoryContainer: tempContainer)
            cachedNotificationService = service
            return service
        }

        let service = EAFriendNotificationService(repositoryContainer: repositoryContainer)
        cachedNotificationService = service
        return service
    }

    /// 获取或创建好友聊天服务实例（单例模式）
    /// 🔑 紧急修复：避免重复创建聊天服务，确保聊天状态一致性
    private func createFriendChatService() -> EAFriendChatService {
        if let cached = cachedChatService {
            return cached
        }

        guard let repositoryContainer = repositoryContainer else {
            // 创建一个临时的空服务，避免崩溃
            let tempContainer = EARepositoryContainerImpl(modelContainer: try! EAAppSchema.createPreviewContainer())
            let tempAIDataBridge = EACommunityAIDataBridge(repositoryContainer: tempContainer)
            let service = EAFriendChatService(
                repositoryContainer: tempContainer,
                sessionManager: sessionManager,
                aiDataBridge: tempAIDataBridge
            )
            cachedChatService = service
            return service
        }

        let aiDataBridge = EACommunityAIDataBridge(repositoryContainer: repositoryContainer)
        let service = EAFriendChatService(
            repositoryContainer: repositoryContainer,
            sessionManager: sessionManager,
            aiDataBridge: aiDataBridge
        )
        cachedChatService = service
        return service
    }
}

// MARK: - 预览

#Preview("星际信标网络") {
    @MainActor
    func createPreview() -> some View {
        // ✅ 修复：使用本地实例替代单例
        let sessionManager = EASessionManager()
        
        return NavigationView {
            EACommunityView()
                .environmentObject(sessionManager)
                .task {
                    #if DEBUG
                    await sessionManager.simulateLogin()
                    #endif
                }
        }
        .modelContainer(PreviewData.container)
        .preferredColorScheme(.dark)
    }
    
    return createPreview()
}