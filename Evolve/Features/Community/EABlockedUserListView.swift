import SwiftUI
import SwiftData

/// 屏蔽用户管理界面
/// 🔑 批次三新增：提供屏蔽用户列表查看和管理功能
struct EABlockedUserListView: View {
    
    // MARK: - 环境依赖
    
    @EnvironmentObject var sessionManager: EASessionManager
    @Environment(\.repositoryContainer) private var repositoryContainer
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - 状态管理
    
    @StateObject private var viewModel = EABlockedUserListViewModel()
    
    // MARK: - UI状态
    
    @State private var showAlert = false
    @State private var alertMessage = ""
    @State private var showUnblockConfirmation = false
    @State private var userToUnblock: BlockedUserDisplayItem? = nil
    @State private var isLoading = true
    @State private var hasError = false
    @State private var errorMessage = ""
    
    var body: some View {
        // 🔑 导航修复：移除多余的NavigationStack，使用父级导航
        ZStack {
            // 背景
            stellarUniverseBackground
                .ignoresSafeArea()

            if isLoading {
                // 加载状态
                loadingView
            } else if hasError {
                // 错误状态
                errorStateView
            } else if viewModel.blockedUsers.isEmpty {
                // 空状态
                emptyStateView
            } else {
                // 屏蔽用户列表
                blockedUsersList
            }
        }
        .navigationTitle("屏蔽管理")
        .navigationBarTitleDisplayMode(.inline)
        .toolbarBackground(.visible, for: .navigationBar)
        .toolbarBackground(Color.chatBackgroundDeep.opacity(0.95), for: .navigationBar)
        .toolbar {
            if !viewModel.blockedUsers.isEmpty {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("全部取消") {
                        showUnblockAllConfirmation()
                    }
                    .foregroundColor(.blue)
                }
            }
        }
        .onAppear {
            setupViewModel()
            loadBlockedUsers()
        }
        .alert("提示", isPresented: $showAlert) {
            Button("确定") { }
        } message: {
            Text(alertMessage)
        }
        .alert("确认取消屏蔽", isPresented: $showUnblockConfirmation) {
            Button("取消", role: .cancel) { }
            Button("确认取消") {
                if let user = userToUnblock {
                    unblockUser(user)
                }
            }
        } message: {
            if let user = userToUnblock {
                Text("确定要取消屏蔽 \(user.displayName) 吗？")
            }
        }
    }
    
    // MARK: - 子视图
    
    /// 加载视图
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
                .tint(.white)
            
            Text("加载中...")
                .font(.system(size: 16))
                .foregroundColor(.white.opacity(0.8))
        }
    }
    
    /// 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Image(systemName: "eye.slash.circle")
                .font(.system(size: 64))
                .foregroundColor(.white.opacity(0.6))
            
            VStack(spacing: 8) {
                Text("暂无屏蔽用户")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.white)
                
                Text("您还没有屏蔽任何用户")
                    .font(.system(size: 14))
                    .foregroundColor(.white.opacity(0.7))
            }
        }
        .padding(.horizontal, 32)
    }

    /// 错误状态视图
    private var errorStateView: some View {
        VStack(spacing: 24) {
            VStack(spacing: 16) {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 48))
                    .foregroundColor(.orange)

                Text("加载失败")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)

                Text(errorMessage.isEmpty ? "网络连接异常，请检查网络设置后重试" : errorMessage)
                    .font(.system(size: 14))
                    .foregroundColor(.white.opacity(0.7))
                    .multilineTextAlignment(.center)
            }

            Button("重新加载") {
                loadBlockedUsers()
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(
                Capsule()
                    .fill(Color.blue.opacity(0.8))
            )
            .foregroundColor(.white)
            .font(.system(size: 16, weight: .medium))
        }
        .padding(.horizontal, 32)
    }

    /// 屏蔽用户列表
    private var blockedUsersList: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(viewModel.blockedUsers) { user in
                    BlockedUserRowView(
                        user: user,
                        onUnblock: {
                            userToUnblock = user
                            showUnblockConfirmation = true
                        }
                    )
                }
            }
            .padding(.horizontal, 16)
            .padding(.top, 16)
        }
    }
    
    // MARK: - 背景组件
    
    /// 星际宇宙背景
    private var stellarUniverseBackground: some View {
        LinearGradient(
            gradient: Gradient(colors: [
                Color.chatBackgroundDeep,
                Color.chatBackgroundMid,
                Color.chatBackgroundWarm
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    // MARK: - 方法
    
    /// 设置ViewModel
    private func setupViewModel() {
        viewModel.sessionManager = sessionManager
        viewModel.repositoryContainer = repositoryContainer
        
        // 监听ViewModel的状态变化
        viewModel.onError = { message in
            alertMessage = message
            showAlert = true
        }
        
        viewModel.onSuccess = { message in
            alertMessage = message
            showAlert = true
        }
    }
    
    /// 加载屏蔽用户列表
    private func loadBlockedUsers() {
        Task {
            await MainActor.run {
                isLoading = true
                hasError = false
                errorMessage = ""
            }

            do {
                try await viewModel.loadBlockedUsers()
                await MainActor.run {
                    isLoading = false
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    hasError = true
                    errorMessage = error.localizedDescription
                }
            }
        }
    }
    
    /// 取消屏蔽用户
    private func unblockUser(_ user: BlockedUserDisplayItem) {
        Task {
            await viewModel.unblockUser(userId: user.userId)
            userToUnblock = nil
        }
    }
    
    /// 显示全部取消屏蔽确认
    private func showUnblockAllConfirmation() {
        let alert = UIAlertController(
            title: "确认取消全部屏蔽",
            message: "确定要取消屏蔽所有用户吗？此操作不可撤销。",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(UIAlertAction(title: "确认", style: .destructive) { _ in
            Task {
                await viewModel.unblockAllUsers()
            }
        })
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first,
           let rootViewController = window.rootViewController {
            rootViewController.present(alert, animated: true)
        }
    }
}

/// 屏蔽用户显示项
struct BlockedUserDisplayItem: Identifiable {
    let id = UUID()
    let userId: UUID
    let displayName: String
    let avatarData: EAAvatarData?
    let blockedDate: Date
}

/// 屏蔽用户行视图
struct BlockedUserRowView: View {
    let user: BlockedUserDisplayItem
    let onUnblock: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // 头像
            EAAvatarView(
                avatarData: user.avatarData,
                size: 48,
                showShadow: true
            )
            
            // 用户信息
            VStack(alignment: .leading, spacing: 4) {
                Text(user.displayName)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                
                Text("屏蔽于 \(formatDate(user.blockedDate))")
                    .font(.system(size: 12))
                    .foregroundColor(.white.opacity(0.6))
            }
            
            Spacer()
            
            // 取消屏蔽按钮
            Button(action: onUnblock) {
                Text("取消屏蔽")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.blue.opacity(0.8))
                    )
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    /// 格式化日期
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
}

#Preview {
    EABlockedUserListView()
        .environmentObject(EASessionManager())
}
