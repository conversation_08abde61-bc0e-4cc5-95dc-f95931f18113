import SwiftUI

// MARK: - 宇宙挑战列表视图
struct EAUniverseChallengeListView: View {
    
    // MARK: - 视图模型

    @StateObject private var viewModel = EAUniverseChallengeViewModel()
    @Environment(\.repositoryContainer) private var repositoryContainer
    @EnvironmentObject var sessionManager: EASessionManager
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - 状态管理
    
    @State private var selectedFilter: ChallengeFilter = .all
    @State private var searchText = ""
    @State private var showingFilterSheet = false
    
    // MARK: - 视图主体
    
    var body: some View {
        // 🔑 修复：移除NavigationView包装，避免双重导航栏
        ZStack {
            // 宇宙背景
            cosmicBackground

            VStack(spacing: 0) {
                // 🔑 新增：自定义导航栏，符合iOS设计规范
                customNavigationBar

                // 顶部工具栏
                challengeToolbar

                // 主要内容
                if viewModel.isLoading && viewModel.activeChallenges.isEmpty {
                    loadingView
                } else if viewModel.activeChallenges.isEmpty {
                    emptyChallengesView
                } else {
                    challengesList
                }
            }
        }
        .task {
            await viewModel.loadActiveChallenges()
        }
        .onAppear {
            // 🔑 修复：正确设置ViewModel的依赖注入
            viewModel.setSessionManager(sessionManager)
            if let container = repositoryContainer {
                viewModel.setRepositoryContainer(container)
            }
        }
        .refreshable {
            await viewModel.refreshData()
        }
        .sheet(isPresented: $showingFilterSheet) {
            challengeFilterSheet
        }
        .sheet(isPresented: $viewModel.showingChallengeDetail) {
            if let challenge = viewModel.selectedChallenge {
                EAUniverseChallengeDetailView(challenge: challenge)
            }
        }
        .alert("错误", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("确定") {
                viewModel.errorMessage = nil
            }
        } message: {
            if let error = viewModel.errorMessage {
                Text(error)
            }
        }
    }
    
    // MARK: - 自定义导航栏

    /// 🔑 新增：符合iOS设计规范的自定义导航栏
    private var customNavigationBar: some View {
        HStack {
            // 关闭按钮 - 左对齐
            Button(action: {
                dismiss()
            }) {
                HStack(spacing: 6) {
                    Image(systemName: "xmark")
                        .font(.system(size: 16, weight: .medium))
                    Text("关闭")
                        .font(.system(size: 17, weight: .regular))
                }
                .foregroundColor(Color.hexColor("40E0D0"))
                .frame(height: 44) // 标准触控区域高度
            }

            Spacer()

            // 标题 - 居中对齐，优化字体和间距
            Text("宇宙挑战")
                .font(.system(size: 20, weight: .semibold, design: .rounded))
                .foregroundColor(.white)
                .lineLimit(1)

            Spacer()

            // 右侧占位空间，保持标题完全居中
            Color.clear
                .frame(width: 60, height: 44)
        }
        .padding(.horizontal, 16)
        .padding(.top, 8)
        .padding(.bottom, 4)
        .background(
            // 渐变背景，与宇宙主题保持一致
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.black.opacity(0.8),
                    Color.clear
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }

    // MARK: - 宇宙背景

    private var cosmicBackground: some View {
        LinearGradient(
            colors: [
                Color.black.opacity(0.9),
                Color.purple.opacity(0.3),
                Color.blue.opacity(0.2)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .overlay {
            // 星星效果
            ForEach(0..<50, id: \.self) { _ in
                Circle()
                    .fill(Color.white.opacity(0.6))
                    .frame(width: 1, height: 1)
                    .position(
                        x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                        y: CGFloat.random(in: 0...UIScreen.main.bounds.height)
                    )
            }
        }
        .ignoresSafeArea()
    }
    
    // MARK: - 顶部工具栏

    private var challengeToolbar: some View {
        VStack(spacing: 8) { // 🔑 减少间距，优化布局
            // 搜索栏
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.white.opacity(0.7))
                
                TextField("搜索挑战...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())
                    .foregroundColor(.white)
                
                Button(action: { showingFilterSheet = true }) {
                    Image(systemName: "line.3.horizontal.decrease.circle")
                        .foregroundColor(.white)
                        .font(.title2)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
            )
            
            // 快速筛选标签
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(ChallengeFilter.allCases, id: \.self) { filter in
                        filterTag(filter)
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding(.horizontal, 16) // 🔑 优化水平间距
        .padding(.top, 8)         // 🔑 减少顶部间距
        .padding(.bottom, 12)     // 🔑 适当的底部间距
    }
    
    // MARK: - 筛选标签
    
    private func filterTag(_ filter: ChallengeFilter) -> some View {
        Button(action: {
            selectedFilter = filter
        }) {
            HStack(spacing: 6) {
                Text(filter.icon)
                    .font(.caption)
                Text(filter.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                Capsule()
                    .fill(selectedFilter == filter ? Color.blue : Color.white.opacity(0.1))
            )
            .foregroundColor(selectedFilter == filter ? .white : .white.opacity(0.8))
        }
    }
    
    // MARK: - 挑战列表
    
    private var challengesList: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(filteredChallenges, id: \.id) { challenge in
                    EAUniverseChallengeCard(
                        challenge: challenge,
                        isParticipating: viewModel.isUserParticipating(in: challenge),
                        participation: viewModel.getUserParticipation(for: challenge),
                        onTap: {
                            viewModel.showChallengeDetail(challenge)
                        },
                        onJoin: {
                            Task {
                                await viewModel.joinChallenge(challenge)
                            }
                        }
                    )
                }
            }
            .padding()
        }
    }
    
    // MARK: - 加载视图
    
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(1.5)
            
            Text("探索宇宙挑战中...")
                .foregroundColor(.white.opacity(0.8))
                .font(.headline)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - 空状态视图
    
    private var emptyChallengesView: some View {
        VStack(spacing: 24) {
            Image(systemName: "sparkles")
                .font(.system(size: 60))
                .foregroundColor(.white.opacity(0.6))
            
            VStack(spacing: 8) {
                Text("暂无活跃挑战")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Text("新的宇宙探索即将开启\n敬请期待更多精彩挑战")
                    .font(.body)
                    .foregroundColor(.white.opacity(0.7))
                    .multilineTextAlignment(.center)
            }
            
            Button(action: {
                Task {
                    await viewModel.refreshData()
                }
            }) {
                HStack {
                    Image(systemName: "arrow.clockwise")
                    Text("刷新")
                }
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(
                    Capsule()
                        .fill(Color.blue.opacity(0.8))
                )
                .foregroundColor(.white)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - 筛选菜单
    
    private var challengeFilterSheet: some View {
        NavigationView {
            List {
                Section("挑战类型") {
                    ForEach(ChallengeFilter.allCases, id: \.self) { filter in
                        HStack {
                            Text(filter.icon)
                            Text(filter.displayName)
                            Spacer()
                            if selectedFilter == filter {
                                Image(systemName: "checkmark")
                                    .foregroundColor(.blue)
                            }
                        }
                        .contentShape(Rectangle())
                        .onTapGesture {
                            selectedFilter = filter
                            showingFilterSheet = false
                        }
                    }
                }
            }
            .navigationTitle("筛选挑战")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                trailing: Button("完成") {
                    showingFilterSheet = false
                }
            )
        }
    }
    
    // MARK: - 计算属性
    
    private var filteredChallenges: [EAUniverseChallenge] {
        var challenges = viewModel.activeChallenges
        
        // 按筛选器筛选
        switch selectedFilter {
        case .all:
            break
        case .recommended:
            challenges = viewModel.recommendedChallenges()
        case .easy:
            challenges = viewModel.challengesByDifficulty("easy")
        case .normal:
            challenges = viewModel.challengesByDifficulty("normal")
        case .hard:
            challenges = viewModel.challengesByDifficulty("hard")
        case .joined:
            challenges = challenges.filter { viewModel.isUserParticipating(in: $0) }
        }
        
        // 按搜索文本筛选
        if !searchText.isEmpty {
            challenges = challenges.filter {
                $0.title.localizedCaseInsensitiveContains(searchText) ||
                $0.challengeDescription.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        return challenges
    }
}

// MARK: - 挑战筛选枚举
enum ChallengeFilter: CaseIterable {
    case all, recommended, easy, normal, hard, joined
    
    var displayName: String {
        switch self {
        case .all: return "全部"
        case .recommended: return "推荐"
        case .easy: return "简单"
        case .normal: return "普通"
        case .hard: return "困难"
        case .joined: return "已参与"
        }
    }
    
    var icon: String {
        switch self {
        case .all: return "🌌"
        case .recommended: return "⭐"
        case .easy: return "🌱"
        case .normal: return "🚀"
        case .hard: return "💎"
        case .joined: return "✅"
        }
    }
}

// MARK: - 预览
#Preview {
    EAUniverseChallengeListView()
        .preferredColorScheme(.dark)
} 