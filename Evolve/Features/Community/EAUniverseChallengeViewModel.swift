import Foundation
import SwiftUI

// MARK: - 宇宙挑战ViewModel
@MainActor
class EAUniverseChallengeViewModel: ObservableObject {
    
    // MARK: - 发布属性
    
    @Published var activeChallenges: [EAUniverseChallenge] = []
    @Published var userParticipations: [EAUniverseChallengeParticipation] = []
    @Published var selectedChallenge: EAUniverseChallenge?
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var showingChallengeDetail = false
    
    // MARK: - 依赖注入
    
    private var sessionManager: EASessionManager?
    private var repositoryContainer: EARepositoryContainer?
    
    // MARK: - 初始化
    
    /// ✅ 修复：无参数初始化，依赖将通过setter方法注入
    init() {
        // 空初始化，等待依赖注入
    }
    
    /// ✅ 修复：设置SessionManager的方法
    func setSessionManager(_ sessionManager: EASessionManager) {
        self.sessionManager = sessionManager
    }
    
    /// ✅ 修复：设置Repository容器的方法，避免Environment注入问题
    func setRepositoryContainer(_ container: EARepositoryContainer) {
        self.repositoryContainer = container
        // 设置后立即加载数据
        Task {
            await loadActiveChallenges()
        }
    }
    
    // MARK: - 数据加载
    
    /// 加载活跃挑战列表
    func loadActiveChallenges() async {
        isLoading = true
        errorMessage = nil
        
        do {
            guard let container = repositoryContainer else {
                throw ViewModelError.repositoryNotAvailable
            }
            
            activeChallenges = try await container.challengeRepository.fetchActiveChallenges()
            
            if let sessionManager = sessionManager,
               let currentUser = await sessionManager.safeCurrentUser {
                userParticipations = try await container.challengeRepository.fetchUserParticipations(for: currentUser.id)
            }
            
        } catch {
            errorMessage = "加载挑战失败：\(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    /// 刷新数据
    func refreshData() async {
        await loadActiveChallenges()
    }
    
    // MARK: - 挑战操作
    
    /// 参与挑战
    func joinChallenge(_ challenge: EAUniverseChallenge) async {
        guard let sessionManager = sessionManager,
              let currentUser = await sessionManager.safeCurrentUser,
              let container = repositoryContainer else {
            errorMessage = "用户未登录或Repository不可用"
            return
        }
        
        isLoading = true
        
        do {
            let participation = try await container.challengeRepository.joinChallenge(challenge, user: currentUser)
            
            // 更新本地数据
            userParticipations.append(participation)
            
            // 更新挑战参与人数
            if let index = activeChallenges.firstIndex(where: { $0.id == challenge.id }) {
                activeChallenges[index].participantCount += 1
            }
            
        } catch {
            errorMessage = "参与挑战失败：\(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    /// 检查用户是否已参与挑战
    func isUserParticipating(in challenge: EAUniverseChallenge) -> Bool {
        return userParticipations.contains { $0.challenge?.id == challenge.id }
    }
    
    /// 获取用户在特定挑战中的参与记录
    func getUserParticipation(for challenge: EAUniverseChallenge) -> EAUniverseChallengeParticipation? {
        return userParticipations.first { $0.challenge?.id == challenge.id }
    }
    
    // MARK: - UI操作
    
    /// 显示挑战详情
    func showChallengeDetail(_ challenge: EAUniverseChallenge) {
        selectedChallenge = challenge
        showingChallengeDetail = true
    }
    
    /// 隐藏挑战详情
    func hideChallengeDetail() {
        showingChallengeDetail = false
        selectedChallenge = nil
    }
    
    // MARK: - 挑战筛选和排序
    
    /// 按难度筛选挑战
    func challengesByDifficulty(_ difficulty: String) -> [EAUniverseChallenge] {
        return activeChallenges.filter { $0.difficulty == difficulty }
    }
    
    /// 按类型筛选挑战
    func challengesByType(_ type: String) -> [EAUniverseChallenge] {
        return activeChallenges.filter { $0.challengeType == type }
    }
    
    /// 推荐挑战（基于用户习惯）
    func recommendedChallenges() -> [EAUniverseChallenge] {
        // 在实际项目中，这里会根据用户习惯数据推荐
        return Array(activeChallenges.prefix(3))
    }
}

// MARK: - ViewModelError
enum ViewModelError: LocalizedError {
    case repositoryNotAvailable
    case userNotLoggedIn
    case invalidData
    
    var errorDescription: String? {
        switch self {
        case .repositoryNotAvailable:
            return "数据服务不可用"
        case .userNotLoggedIn:
            return "用户未登录"
        case .invalidData:
            return "数据无效"
        }
    }
} 