import SwiftUI
import SwiftData

// MARK: - iOS兼容性修饰符

/// iOS 17.0+ 兼容的ScrollContentBackground修饰符
struct ScrollContentBackgroundModifier: ViewModifier {
    func body(content: Content) -> some View {
        if #available(iOS 16.0, *) {
            content.scrollContentBackground(.hidden)
        } else {
            content
        }
    }
}

/// 添加好友界面 - 星际邀请系统
/// 遵循iOS设计规范，支持搜索用户和发送好友请求
struct EAAddFriendView: View {
    
    // MARK: - 环境依赖
    
    @EnvironmentObject var sessionManager: EASessionManager
    @EnvironmentObject var friendshipService: EAFriendshipService  // ✅ 改为环境依赖注入
    @Environment(\.repositoryContainer) private var repositoryContainer
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - ViewModel状态管理
    
    @StateObject private var viewModel: EAAddFriendViewModel

    // MARK: - 🔑 新增：结构化并发管理
    @State private var friendRequestTask: Task<Void, Never>?

    // MARK: - 初始化

    init() {  // ✅ 简化初始化，不接收参数
        // 创建一个临时的ViewModel，稍后在onAppear中用环境依赖初始化
        self._viewModel = StateObject(wrappedValue: EAAddFriendViewModel())
    }
    
    // MARK: - 主视图
    
    var body: some View {
        ZStack {
            // 🌟 星域数字宇宙背景
            stellarUniverseBackground

            VStack(spacing: 0) {
                // 搜索栏
                searchBar

                // 搜索提示
                searchHints

                // 搜索结果
                searchResultsView
            }

            // 好友请求弹窗 - 直接在主视图中显示
            if viewModel.showMessageInput {
                friendRequestMessagePopup
                    .transition(.asymmetric(
                        insertion: .scale(scale: 0.8).combined(with: .opacity),
                        removal: .scale(scale: 0.8).combined(with: .opacity)
                    ))
                    .animation(.spring(response: 0.3, dampingFraction: 0.8), value: viewModel.showMessageInput)
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .navigationTitle("添加好友")
        .onAppear {
            // ✅ 使用环境依赖初始化ViewModel
            viewModel.setupDependencies(
                friendshipService: friendshipService,
                repositoryContainer: repositoryContainer
            )
        }
        .onDisappear {
            // 🔑 关键修复：取消正在进行的异步任务，防止内存泄漏
            friendRequestTask?.cancel()
            friendRequestTask = nil

            // 🔑 关键修复：页面消失时强制刷新好友列表，确保数据一致性
            Task { [weak friendshipService] in
                await friendshipService?.loadFriendships()
            }
        }
        .alert("提示", isPresented: $viewModel.showAlert) {
            Button("确定") {
                // 🔑 批次三修复：移除手动dismiss，改为自动dismiss
                // 通过ViewModel处理弹窗确认
                viewModel.handleAlertConfirmation()
            }
        } message: {
            Text(viewModel.alertMessage)
        }
        // 🔑 批次三新增：监听自动dismiss触发器（iOS 17.0+兼容）
        .onChange(of: viewModel.shouldDismiss) { _, shouldDismiss in
            if shouldDismiss {
                dismiss()
            }
        }
    }

    // MARK: - 子视图组件
    
    /// 星域数字宇宙背景
    private var stellarUniverseBackground: some View {
        ZStack {
            // 深空背景 - 优化为更有层次感的宇宙色调
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.hexColor("0F1B2F"), // 深蓝夜空
                    Color.hexColor("1E2A3A"), // 中层蓝灰
                    Color.hexColor("2A3441"), // 浅层灰蓝
                    Color.hexColor("1A365D")  // 深青蓝
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            // 星云效果层 - 增加探索的神秘感
            RadialGradient(
                gradient: Gradient(colors: [
                    Color.hexColor("40E0D0").opacity(0.12),
                    Color.purple.opacity(0.06),
                    Color.clear
                ]),
                center: .center,
                startRadius: 100,
                endRadius: 400
            )
            .ignoresSafeArea()
            
            // 次要星云效果
            RadialGradient(
                gradient: Gradient(colors: [
                    Color.blue.opacity(0.08),
                    Color.clear
                ]),
                center: .topTrailing,
                startRadius: 50,
                endRadius: 250
            )
            .ignoresSafeArea()
            
            // 星点效果 - 增加青色调和探索元素
            ForEach(0..<35, id: \.self) { index in
                Circle()
                    .fill(
                        index % 4 == 0 
                        ? Color.hexColor("40E0D0").opacity(Double.random(in: 0.4...0.8))
                        : index % 4 == 1
                        ? Color.blue.opacity(Double.random(in: 0.3...0.6))
                        : Color.white.opacity(Double.random(in: 0.2...0.5))
                    )
                    .frame(width: CGFloat.random(in: 1...2.5))
                    .position(
                        x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                        y: CGFloat.random(in: 0...UIScreen.main.bounds.height)
                    )
            }
        }
    }
    

    
    /// 搜索栏
    private var searchBar: some View {
        VStack(spacing: 16) {
            HStack(spacing: 12) {
                Image(systemName: "magnifyingglass")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(Color.hexColor("40E0D0"))
                
                TextField("输入用户名或邮箱搜索", text: $viewModel.searchText)
                    .font(.system(size: 16))
                    .foregroundColor(.white)
                    .textInputAutocapitalization(.never)
                    .autocorrectionDisabled()
                    .onSubmit {
                        Task { [weak viewModel] in
                            await viewModel?.performSearch()
                        }
                    }
                
                if viewModel.isSearching {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: Color.hexColor("40E0D0")))
                        .scaleEffect(0.8)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 14)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.hexColor("2A3441").opacity(0.8),
                                Color.hexColor("1A365D").opacity(0.6)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color.hexColor("40E0D0").opacity(0.3),
                                        Color.purple.opacity(0.2)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1
                            )
                    )
            )
        }
        .padding(.horizontal, 20)
        .padding(.top, 20)
    }
    
    /// 搜索提示
    private var searchHints: some View {
        VStack(spacing: 0) {
            if viewModel.searchText.isEmpty && viewModel.searchResults.isEmpty {
                VStack(spacing: 20) {
                    Image(systemName: "person.2.badge.gearshape")
                        .font(.system(size: 48, weight: .light))
                        .foregroundColor(Color.hexColor("40E0D0").opacity(0.6))
                    
                    VStack(spacing: 8) {
                        Text("发现新朋友")
                            .font(.system(size: 20, weight: .semibold))
                            .foregroundColor(.white)
                        
                        Text("输入用户名或邮箱地址来寻找朋友")
                            .font(.system(size: 16))
                            .foregroundColor(.white.opacity(0.7))
                            .multilineTextAlignment(.center)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .padding(.top, 60)
            }
        }
    }
    
    /// 搜索结果列表
    private var searchResultsView: some View {
        ScrollView {
            if !viewModel.searchResults.isEmpty {
                LazyVStack(spacing: 12) {
                    ForEach(viewModel.searchResults, id: \.id) { userProfile in
                        UserSearchResultRow(
                            userProfile: userProfile,
                            viewModel: viewModel  // ✅ 传递ViewModel替代服务
                        ) {
                            // 添加好友操作
                            viewModel.showFriendRequestInput(for: userProfile)
                        }
                        .onAppear {
                            // 预加载更多结果
                            if userProfile == viewModel.searchResults.last {
                                Task { [weak viewModel] in
                                    await viewModel?.loadMoreResults()
                                }
                            }
                        }
                    }
                    
                    // 加载更多指示器
                    if viewModel.isSearching && !viewModel.searchResults.isEmpty {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: Color.hexColor("40E0D0")))
                            .frame(height: 40)
                    }
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            } else if !viewModel.searchText.isEmpty && !viewModel.isSearching {
                // 无搜索结果
                VStack(spacing: 16) {
                    Image(systemName: "person.crop.circle.badge.xmark")
                        .font(.system(size: 40, weight: .light))
                        .foregroundColor(Color.white.opacity(0.5))
                    
                    Text("未找到相关用户")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                }
                .frame(maxWidth: .infinity)
                .padding(.top, 40)
            }
        }
    }
    
    /// 好友请求消息弹窗
    private var friendRequestMessagePopup: some View {
        ZStack {
            // 背景蒙层
            Color.black.opacity(0.6)
                .ignoresSafeArea()
                .onTapGesture {
                    viewModel.hideFriendRequestInput()
                }
            
            // 弹窗内容
            VStack(spacing: 24) {
                // 弹窗标题
                VStack(spacing: 8) {
                    Text("发送好友请求")
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(.white)
                    
                    if let username = viewModel.selectedUser?.user?.username {
                        Text("给 \(username)")
                            .font(.system(size: 16))
                            .foregroundColor(.white.opacity(0.7))
                    }
                }
                
                // 消息输入框
                VStack(alignment: .leading, spacing: 8) {
                    Text("附加消息（可选）")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                    
                    TextField("说些什么吧...", text: $viewModel.friendRequestMessage, axis: .vertical)
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                        .lineLimit(3...6)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.black.opacity(0.3))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                                )
                        )
                }
                
                // 按钮组
                HStack(spacing: 16) {
                    // 取消按钮
                    Button("取消") {
                        viewModel.hideFriendRequestInput()
                    }
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                    .frame(maxWidth: .infinity, minHeight: 44)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.white.opacity(0.1))
                    )
                    
                    // 发送按钮
                    Button("发送请求") {
                        // 🔑 关键修复：使用结构化并发，确保任务生命周期管理
                        friendRequestTask?.cancel() // 取消之前的任务
                        friendRequestTask = Task { @MainActor [weak viewModel] in
                            await viewModel?.sendFriendRequest()
                        }
                    }
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity, minHeight: 44)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color.hexColor("40E0D0"),
                                        Color.blue
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                    )
                    .disabled(viewModel.isSearching)
                }
            }
            .padding(24)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.hexColor("1E2A3A").opacity(0.95),
                                Color.hexColor("2A3441").opacity(0.95)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color.white.opacity(0.1), lineWidth: 1)
                    )
            )
            .padding(.horizontal, 32)
            .scaleEffect(viewModel.showMessageInput ? 1.0 : 0.8)
            .opacity(viewModel.showMessageInput ? 1.0 : 0.0)
        }
    }
    

}

/// 用户搜索结果行视图
struct UserSearchResultRow: View {
    let userProfile: EAUserSocialProfile
    let viewModel: EAAddFriendViewModel  // ✅ 使用ViewModel替代直接服务注入
    let onAddFriend: () -> Void
    
    @State private var friendshipStatus: FriendshipStatus = .notFriend  // 临时保留，用于异步获取状态
    
    var body: some View {
        HStack(spacing: 16) {
            // 🔑 修复：使用统一的头像显示组件，确保与"我的"页面一致
            EAAvatarView(
                avatarData: userProfile.user?.avatarData,
                size: 50,
                showShadow: true
            )
            
            // 用户信息
            VStack(alignment: .leading, spacing: 4) {
                Text(userProfile.user?.username ?? "未知用户")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                
                if let email = userProfile.user?.email {
                    Text(email)
                        .font(.system(size: 14))
                        .foregroundColor(.white.opacity(0.6))
                }
            }
            
            Spacer()
            
            // 添加按钮
            actionButton
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.black.opacity(0.4))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
        .onAppear {
            loadFriendshipStatus()
        }
    }
    
    /// 操作按钮
    @ViewBuilder
    private var actionButton: some View {
        switch friendshipStatus {
        case .notFriend:
            addFriendButton
        case .friend(_):
            friendStatusIcon
        case .requestSent(_):
            requestSentLabel
        case .requestReceived(_):
            requestReceivedLabel
        }
    }

    private var addFriendButton: some View {
        Button(action: onAddFriend) {
            Image(systemName: "person.badge.plus")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
                .frame(width: 36, height: 36)
                .background(
                    Circle()
                        .fill(Color.hexColor("40E0D0"))
                )
        }
    }

    private var friendStatusIcon: some View {
        Image(systemName: "checkmark.circle.fill")
            .font(.system(size: 20, weight: .medium))
            .foregroundColor(.green)
    }

    private var requestSentLabel: some View {
        Text("已发送")
            .font(.system(size: 12, weight: .medium))
            .foregroundColor(.white.opacity(0.7))
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                Capsule()
                    .fill(Color.gray.opacity(0.3))
            )
    }

    private var requestReceivedLabel: some View {
        Text("待处理")
            .font(.system(size: 12, weight: .medium))
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                Capsule()
                    .fill(Color.orange)
            )
    }
    
    /// 加载好友关系状态
    private func loadFriendshipStatus() {
        Task { [weak viewModel] in
            // ✅ 通过ViewModel获取好友状态
            guard let viewModel = viewModel else { return }
            let status = await viewModel.getFriendshipStatusAsync(for: userProfile)
            await MainActor.run {
                friendshipStatus = status
            }
        }
    }
}

// MARK: - 预览

#Preview("添加好友界面") {
    // 创建预览用的临时container和service
    let container = try! EAAppSchema.createPreviewContainer()
    let repositoryContainer = EARepositoryContainerImpl(modelContainer: container)
    let sessionManager = EASessionManager()
    let integrityGuard = EAUserIntegrityGuard(repositoryContainer: repositoryContainer)  // 🔑 修复：创建完整性守护服务
    let friendshipService = EAFriendshipService(repositoryContainer: repositoryContainer, sessionManager: sessionManager, integrityGuard: integrityGuard)

    NavigationView {
        EAAddFriendView()  // ✅ 使用新的无参数初始化
            .environmentObject(sessionManager)
            .environmentObject(friendshipService)  // ✅ 注入好友服务
            .environment(\.repositoryContainer, repositoryContainer)
    }
}
