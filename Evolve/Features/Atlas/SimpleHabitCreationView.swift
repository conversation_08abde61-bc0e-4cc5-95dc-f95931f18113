//
//  SimpleHabitCreationView.swift
//  Evolve
//
//  Created by AI Assistant on 2025-06-20.
//  ✅ iOS 18.5真机修复：极简版创建计划视图，避免复杂ViewModel导致的卡死问题
//

import SwiftUI

/// ✅ iOS 18.5真机修复：极简版创建计划视图
/// 移除所有复杂功能，只保留最基本的创建计划功能，确保在任何环境下都能正常工作
struct SimpleHabitCreationView: View {
    let repositoryContainer: EARepositoryContainer?
    let sessionManager: EASessionManager
    let onDismiss: () -> Void
    
    @State private var habitName: String = ""
    @State private var selectedIcon: String = "star.fill"
    @State private var isLoading: Bool = false
    @State private var showError: Bool = false
    @State private var errorMessage: String = ""
    
    // 简化的图标选项
    private let availableIcons = [
        "star.fill", "heart.fill", "flame.fill", "bolt.fill",
        "leaf.fill", "drop.fill", "sun.max.fill", "moon.fill"
    ]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // 标题
                Text("创建新计划")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding(.top)
                
                // 计划名称输入
                VStack(alignment: .leading, spacing: 8) {
                    Text("计划名称")
                        .font(.headline)
                    
                    TextField("输入计划名称", text: $habitName)
                        .textFieldStyle(.roundedBorder)
                        .font(.body)
                }
                
                // 图标选择
                VStack(alignment: .leading, spacing: 8) {
                    Text("选择图标")
                        .font(.headline)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 12) {
                        ForEach(availableIcons, id: \.self) { icon in
                            Button(action: {
                                selectedIcon = icon
                            }) {
                                Image(systemName: icon)
                                    .font(.system(size: 24))
                                    .foregroundColor(selectedIcon == icon ? .white : .primary)
                                    .frame(width: 50, height: 50)
                                    .background(selectedIcon == icon ? Color.blue : Color.gray.opacity(0.2))
                                    .cornerRadius(12)
                            }
                        }
                    }
                }
                
                Spacer()
                
                // 创建按钮
                Button(action: {
                    createHabit()
                }) {
                    if isLoading {
                        HStack {
                            ProgressView()
                                .scaleEffect(0.8)
                            Text("创建中...")
                        }
                    } else {
                        Text("创建计划")
                    }
                }
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(habitName.isEmpty ? Color.gray : Color.blue)
                .foregroundColor(.white)
                .cornerRadius(12)
                .disabled(habitName.isEmpty || isLoading)
                
                // 错误提示
                if showError {
                    Text(errorMessage)
                        .foregroundColor(.red)
                        .font(.caption)
                }
            }
            .padding()
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        onDismiss()
                    }
                }
            }
        }
    }
    
    /// ✅ 极简创建计划功能
    private func createHabit() {
        guard let container = repositoryContainer,
              let userId = sessionManager.currentUserID,
              !habitName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            showError(message: "系统未就绪或计划名称为空")
            return
        }
        
        isLoading = true
        showError = false
        
        Task { @MainActor in
            do {
                // ✅ 极简创建：只设置最基本的属性
                let _ = try await container.habitRepository.createHabit(
                    name: habitName.trimmingCharacters(in: .whitespacesAndNewlines),
                    iconName: selectedIcon,
                    targetFrequency: 1,
                    frequencyType: "daily",
                    category: "其他",
                    difficulty: "简单",
                    for: userId
                )
                
                // 发送通知
                NotificationCenter.default.post(
                    name: NSNotification.Name("HabitDataChanged"),
                    object: nil
                )
                
                // 成功后关闭
                isLoading = false
                onDismiss()
            } catch {
                isLoading = false
                showError(message: "创建失败，请重试")
            }
        }
    }
    
    private func showError(message: String) {
        errorMessage = message
        showError = true
    }
}

#Preview {
    SimpleHabitCreationView(
        repositoryContainer: nil,
        sessionManager: EASessionManager(),
        onDismiss: {}
    )
}
