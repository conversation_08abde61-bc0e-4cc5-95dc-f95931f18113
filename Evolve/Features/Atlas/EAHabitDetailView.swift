import SwiftUI
import SwiftData
import Foundation

/// Sheet类型枚举 - 统一管理所有Sheet状态
enum EAHabitDetailSheetType: Identifiable {
    case editView
    
    var id: String {
        switch self {
        case .editView:
            return "editView"
        }
    }
}

/// 计划详情页面 - 严格遵循MVVM架构
/// 🎯 解决用户反馈的问题：去除大框套小框设计，显示详细执行日期，修复图标显示
/// 🔧 架构修复：正确使用EAHabitDetailViewModel，符合项目MVVM规范
@MainActor
struct EAHabitDetailView: View {
    let habit: EAHabit
    
    // MARK: - 环境依赖
    @Environment(\.repositoryContainer) private var repositoryContainer
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var sessionManager: EASessionManager
    
    // MARK: - ViewModel（符合MVVM架构）
    @StateObject private var viewModel = EAHabitDetailViewModel()
    
    // MARK: - UI状态管理（仅UI相关）
    @State private var activeSheet: EAHabitDetailSheetType?
    @State private var showDeleteAlert = false
    @State private var showEditSheet = false
    
    @Query private var completions: [EACompletion]
    
    init(habit: EAHabit) {
        self.habit = habit
        // 初始化查询，只获取当前习惯的完成记录
        let habitId = habit.id
        self._completions = Query(
            filter: #Predicate<EACompletion> { completion in
                completion.habit?.id == habitId
            },
            sort: \EACompletion.date,
            order: .reverse
        )
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 紧凑导航栏
            compactNavigationBar
            
            // 主要内容
            ScrollView {
                VStack(spacing: 20) {
                    // 基本信息卡片
                    basicInfoCard
                    
                    // 执行安排预览卡片（新的直观显示方式）
                    executionSchedulePreview
                    
                    // 能量统计卡片
                    energyStatsCard
                    
                    // AI观察笔记
                    aiInsightCard
                    
                    // 底部间距，为按钮区域留出空间
                    Spacer(minLength: 120)
                }
                .padding(.horizontal, 20)
                .padding(.top, 16)
            }
        }
        .background(
            // 使用项目标准背景渐变系统
            ZStack {
                // 主背景色
                Color.hexColor("002b20")
                
                // 渐变叠加
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.hexColor("0A2F51"),
                        Color.hexColor("005A4B"), 
                        Color.hexColor("002b20")
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
                
                // 微光粒子效果层
                ZStack {
                    // 椭圆光晕效果
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color.hexColor("008080").opacity(0.35),
                            Color.clear
                        ]),
                        center: .topLeading,
                        startRadius: 50,
                        endRadius: 200
                    )
                    
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color.hexColor("38EF7D").opacity(0.18),
                            Color.clear
                        ]),
                        center: .bottomTrailing,
                        startRadius: 30,
                        endRadius: 150
                    )
                    
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color.hexColor("0D47A1").opacity(0.25),
                            Color.clear
                        ]),
                        center: .center,
                        startRadius: 80,
                        endRadius: 250
                    )
                }
            }
            .ignoresSafeArea()
        )
        .overlay(
            // 底部按钮区域
            bottomButtonArea,
            alignment: .bottom
        )
        .alert("确认删除", isPresented: $showDeleteAlert) {
            Button("取消", role: .cancel) { }
            Button("删除", role: .destructive) {
                Task {
                    await deleteHabit()
                }
            }
        } message: {
            Text("删除后无法恢复，确定要删除这个计划吗？")
        }
        .sheet(isPresented: $showEditSheet) {
            // 🔥 决定性修复：使用新的init参数，让View内部管理StateObject
            EAHabitCreationView(
                editingHabit: habit,
                sessionManager: sessionManager,
                repositoryContainer: repositoryContainer
            )
        }
        .onAppear {
            setupViewModel()
        }
    }
    
    // MARK: - 设置ViewModel
    private func setupViewModel() {
        if let container = repositoryContainer {
            viewModel.setRepositoryContainer(container)
        }
    }
    
    // MARK: - 紧凑导航栏
    private var compactNavigationBar: some View {
        HStack {
            // 返回按钮
            Button(action: { dismiss() }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.white)
                    .frame(width: 32, height: 32)
                    .background(Color.white.opacity(0.1))
                    .clipShape(Circle())
            }
            
            Spacer()
            
            // 习惯名称
            Text(habit.name)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)
                .lineLimit(1)
            
            Spacer()
            
            // 占位空间，保持标题居中
            Color.clear
                .frame(width: 32, height: 32)
        }
        .padding(.horizontal, 20)
        .padding(.top, 4)
        .padding(.bottom, 4)
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.black.opacity(0.3),
                    Color.clear
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }
    
    // MARK: - 底部按钮区域
    private var bottomButtonArea: some View {
        VStack(spacing: 12) {
            // 编辑按钮
            EAButton(
                title: "编辑计划",
                style: .secondary,
                size: .medium,
                icon: "pencil",
                action: {
                    showEditSheet = true
                }
            )
            
            // 删除按钮
            EAButton(
                title: "彻底移除此计划",
                style: .destructive,
                size: .medium,
                icon: "trash",
                action: {
                    showDeleteAlert = true
                }
            )
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 34) // 安全区域底部间距
        .padding(.top, 16)
        .background(
            // 渐变背景，与页面背景融合
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.clear,
                    Color.hexColor("002b20").opacity(0.8),
                    Color.hexColor("002b20")
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }

    // MARK: - 基本信息卡片
    private var basicInfoCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack(spacing: 12) {
                // 习惯图标
                EAColorfulIcon(habit.iconName, size: 32)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(habit.name)
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(.white)
                    
                    Text(targetText)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                }
                
                Spacer()
                
                // 活跃状态指示器
                VStack(spacing: 4) {
                    Circle()
                        .fill(habit.isActive ? Color.hexColor("40E0D0") : Color.gray)
                        .frame(width: 8, height: 8)
                    
                    Text(habit.isActive ? "活跃" : "暂停")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(habit.isActive ? Color.hexColor("40E0D0") : Color.gray)
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.08))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
    
    // MARK: - 执行安排预览卡片（新的直观显示方式）
    @ViewBuilder
    private var executionSchedulePreview: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "calendar")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.hexColor("40E0D0"))
                
                Text("执行安排")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            // 根据计划类型显示不同的预览方式
            ExecutionSchedulePreview(habit: habit)
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.08))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
    
    // MARK: - 能量统计卡片
    private var energyStatsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "chart.bar.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.hexColor("40E0D0"))
                
                Text("能量统计")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                StatCard(
                    title: "总完成次数",
                    value: "\(viewModel.totalCompletions)",
                    color: Color.hexColor("40E0D0")
                )
                
                StatCard(
                    title: "活跃天数",
                    value: "\(viewModel.activeDays)",
                    color: Color.hexColor("67e8f9")
                )
                
                StatCard(
                    title: "最长连续",
                    value: "\(viewModel.longestStreak)",
                    color: Color.hexColor("FF7F50")
                )
                
                StatCard(
                    title: "平均完成率",
                    value: "\(Int(viewModel.completionRate * 100))%",
                    color: Color.hexColor("98FB98")
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.08))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
    
    // MARK: - AI观察笔记
    private var aiInsightCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "brain.head.profile")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.hexColor("40E0D0"))
                
                Text("Aura的观察笔记")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            Text(viewModel.aiObservation)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white.opacity(0.8))
                .lineSpacing(4)
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.08))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
    
    // MARK: - 计算属性
    
    private var targetText: String {
        switch habit.frequencyType {
        case "weekly":
            return "每周 \(habit.targetFrequency) 次"
        case "daily":
            return "每天 1 次"
        case "monthly":
            if habit.monthlyMode == "target" {
                return "每月 \(habit.monthlyTarget) 次"
            } else {
                return "每月，指定的日期"
            }
        case "oneTime":
            let selectedDates = habit.selectedOneTimeDates
            if selectedDates.isEmpty {
                return "一次性计划（未设置日期）"
            } else {
                return "一次性计划（\(selectedDates.count)个日期）"
            }
        default:
            return "每周 \(habit.targetFrequency) 次"
        }
    }
    
    private var frequencyText: String {
        switch habit.frequencyType {
        case "weekly":
            return "每周 \(habit.targetFrequency) 次"
        case "daily":
            return "每天 1 次"
        case "monthly":
            if habit.monthlyMode == "target" {
                return "每月 \(habit.monthlyTarget) 次"
            } else {
                return "每月，指定的日期"
            }
        case "oneTime":
            return "一次性计划"
        default:
            return "每周 \(habit.targetFrequency) 次"
        }
    }
    
    private var executionDaysText: String {
        switch habit.frequencyType {
        case "weekly":
            let weekdayNames = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"]
            let selectedDays = habit.selectedWeekdays.compactMap { weekday in
                weekday >= 0 && weekday < weekdayNames.count ? weekdayNames[weekday] : nil
            }
            return selectedDays.joined(separator: ", ")
            
        case "monthly":
            if habit.monthlyMode == "dates" {
                let selectedDates = habit.selectedMonthlyDates.map { "每月\($0)日" }
                return selectedDates.joined(separator: ", ")
            } else {
                return ""
            }
            
        case "daily":
            return "每天"
            
        case "oneTime":
            let selectedDates = habit.selectedOneTimeDates
            if selectedDates.isEmpty {
                return "未设置执行日期"
            } else {
                let formattedDates = selectedDates.compactMap { dateString -> String? in
                    let formatter = DateFormatter()
                    formatter.dateFormat = "yyyy-MM-dd"
                    guard let date = formatter.date(from: dateString) else { return nil }
                    
                    let displayFormatter = DateFormatter()
                    displayFormatter.dateFormat = "yyyy年M月d日"
                    displayFormatter.locale = Locale(identifier: "zh_CN")
                    return displayFormatter.string(from: date)
                }.sorted()
                
                return formattedDates.joined(separator: ", ")
            }
            
        default:
            return ""
        }
    }
    
    // MARK: - 删除习惯操作
    private func deleteHabit() async {
        // ✅ 修复：检查删除结果，只有成功才关闭页面
        let success = await viewModel.deleteHabit(habit)
        
        await MainActor.run {
            if success {
                // 删除成功，关闭页面
                dismiss()
            } else {
                // 删除失败，保持页面打开，错误信息已经在ViewModel中设置
                // 删除失败，错误已通过errorMessage显示
            }
        }
    }
}

// MARK: - 统计卡片组件
struct StatCard: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Text(value)
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(color)
            
            Text(title)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.white.opacity(0.7))
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
        )
    }
}

// MARK: - 紧凑日期卡片组件
struct CompactDateCard: View {
    let dateString: String
    
    private var formattedDate: (month: String, day: String) {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        guard let date = formatter.date(from: dateString) else {
            return ("??", "??")
        }
        
        let monthFormatter = DateFormatter()
        monthFormatter.dateFormat = "M"
        monthFormatter.locale = Locale(identifier: "zh_CN")
        
        let dayFormatter = DateFormatter()
        dayFormatter.dateFormat = "d"
        dayFormatter.locale = Locale(identifier: "zh_CN")
        
        return (monthFormatter.string(from: date), dayFormatter.string(from: date))
    }
    
    var body: some View {
        VStack(spacing: 2) {
            Text("\(formattedDate.month)月")
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(.white.opacity(0.7))
            
            Text(formattedDate.day)
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(.white)
        }
        .frame(height: 32)
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(Color.hexColor("40E0D0").opacity(0.15))
                .overlay(
                    RoundedRectangle(cornerRadius: 6)
                        .stroke(Color.hexColor("40E0D0").opacity(0.3), lineWidth: 1)
                )
        )
    }
}

// MARK: - 信息行组件
struct InfoRow: View {
    let label: String
    let content: AnyView
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(label)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white.opacity(0.7))
            
            content
        }
    }
}

// MARK: - 执行安排预览组件
/// 参考创建页面的频率选择器设计，但改为只读预览模式
/// 支持月份切换查看一次性计划的跨月日期
struct ExecutionSchedulePreview: View {
    let habit: EAHabit
    @State private var currentMonth: Date = Date()
    
    private let calendar = Calendar.current
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter
    }()
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 频率类型标识（只读显示）
            frequencyTypeIndicator
            
            // 根据频率类型显示不同的预览方式
            switch habit.frequencyType {
            case "weekly":
                weeklyPreview
            case "daily":
                dailyPreview
            case "monthly":
                monthlyPreview
            case "oneTime":
                oneTimePreview
            default:
                Text("未知频率类型")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.6))
            }
            
            // 提醒时间显示（如果有）
            if !habit.reminderTimes.isEmpty {
                reminderTimeSection
            }
        }
    }
    
    // MARK: - 频率类型指示器
    private var frequencyTypeIndicator: some View {
        HStack {
            Text(frequencyTypeText)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color.hexColor("40E0D0"))
                        .overlay(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(Color.hexColor("40E0D0"), lineWidth: 1)
                        )
                )
            
            Spacer()
        }
    }
    
    // MARK: - 每周预览
    private var weeklyPreview: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("执行日期")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white.opacity(0.8))
            
            HStack(spacing: 8) {
                let weekdayNames = ["一", "二", "三", "四", "五", "六", "日"]
                ForEach(1...7, id: \.self) { weekday in
                    let isSelected = habit.selectedWeekdays.contains(weekday)
                    
                    VStack(spacing: 4) {
                        Text("周\(weekdayNames[weekday - 1])")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(isSelected ? .white : .white.opacity(0.6))
                        
                        Circle()
                            .fill(isSelected ? Color.hexColor("40E0D0") : Color.white.opacity(0.3))
                            .frame(width: 8, height: 8)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(isSelected ? Color.hexColor("40E0D0").opacity(0.15) : Color.white.opacity(0.05))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(
                                        isSelected ? Color.hexColor("40E0D0").opacity(0.5) : Color.white.opacity(0.1),
                                        lineWidth: 1
                                    )
                            )
                    )
                }
            }
            
            if !habit.selectedWeekdays.isEmpty {
                Text("已选择 \(habit.selectedWeekdays.count) 天")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(Color.white.opacity(0.6))
            }
        }
    }
    
    // MARK: - 每日预览
    private var dailyPreview: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("每日目标")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white.opacity(0.8))
            
            HStack {
                VStack(spacing: 4) {
                    Text("\(habit.dailyTarget)")
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(.white)
                    
                    Text("次/日")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                }
                .frame(height: 60)
                .frame(width: 80)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.hexColor("40E0D0"))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.hexColor("40E0D0"), lineWidth: 1)
                        )
                )
                
                Spacer()
            }
        }
    }
    
    // MARK: - 每月预览
    private var monthlyPreview: some View {
        VStack(alignment: .leading, spacing: 12) {
            if habit.monthlyMode == "dates" {
                Text("每月执行日期")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                
                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 6), count: 7), spacing: 6) {
                    ForEach(1...31, id: \.self) { date in
                        let isSelected = habit.selectedMonthlyDates.contains(date)
                        
                        Text("\(date)")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(isSelected ? .white : .white.opacity(0.4))
                            .frame(height: 28)
                            .frame(maxWidth: .infinity)
                            .background(
                                RoundedRectangle(cornerRadius: 6)
                                    .fill(isSelected ? Color.hexColor("40E0D0").opacity(0.8) : Color.white.opacity(0.05))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 6)
                                            .stroke(
                                                isSelected ? Color.hexColor("40E0D0") : Color.white.opacity(0.1),
                                                lineWidth: isSelected ? 1.5 : 1
                                            )
                                    )
                            )
                    }
                }
            } else {
                Text("每月目标")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                
                HStack {
                    VStack(spacing: 4) {
                        Text("\(habit.monthlyTarget)")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(.white)
                        
                        Text("次/月")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.white.opacity(0.7))
                    }
                    .frame(height: 60)
                    .frame(width: 80)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.hexColor("40E0D0"))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.hexColor("40E0D0"), lineWidth: 1)
                            )
                    )
                    
                    Spacer()
                }
            }
        }
    }
    
    // MARK: - 一次性计划预览（支持月份切换）
    private var oneTimePreview: some View {
        VStack(alignment: .leading, spacing: 12) {
            if habit.selectedOneTimeDates.isEmpty {
                Text("未设置执行日期")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.6))
            } else {
                // 月份导航（如果有跨月份的日期）
                if hasMultipleMonths {
                    monthNavigationBar
                }
                
                Text("执行日期")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                
                // 当前月份的日历预览
                oneTimeCalendarPreview
                
                // 总计信息
                Text("共选择 \(habit.selectedOneTimeDates.count) 个日期")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(Color.hexColor("40E0D0"))
            }
        }
    }
    
    // MARK: - 一次性计划月份导航
    private var monthNavigationBar: some View {
        HStack {
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    currentMonth = calendar.date(byAdding: .month, value: -1, to: currentMonth) ?? currentMonth
                }
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.7))
                    .frame(width: 32, height: 32)
                    .background(Color.white.opacity(0.1))
                    .clipShape(Circle())
            }
            
            Spacer()
            
            Text(monthYearText)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
            
            Spacer()
            
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    currentMonth = calendar.date(byAdding: .month, value: 1, to: currentMonth) ?? currentMonth
                }
            }) {
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.7))
                    .frame(width: 32, height: 32)
                    .background(Color.white.opacity(0.1))
                    .clipShape(Circle())
            }
        }
    }
    
    // MARK: - 一次性计划日历预览
    private var oneTimeCalendarPreview: some View {
        let daysInMonth = calendar.range(of: .day, in: .month, for: currentMonth)?.count ?? 30
        let firstDayOfMonth = calendar.dateInterval(of: .month, for: currentMonth)?.start ?? currentMonth
        let weekdayOffset = (calendar.component(.weekday, from: firstDayOfMonth) + 5) % 7 // 调整为周一开始
        
        return VStack(spacing: 8) {
            // 星期标题
            HStack(spacing: 4) {
                ForEach(["一", "二", "三", "四", "五", "六", "日"], id: \.self) { weekday in
                    Text(weekday)
                        .font(.system(size: 11, weight: .medium))
                        .foregroundColor(.white.opacity(0.6))
                        .frame(maxWidth: .infinity)
                }
            }
            
            // 日历网格
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 4), count: 7), spacing: 4) {
                // 前置空白天数
                ForEach(0..<weekdayOffset, id: \.self) { _ in
                    Rectangle()
                        .fill(Color.clear)
                        .frame(height: 32)
                }
                
                // 当月日期
                ForEach(1...daysInMonth, id: \.self) { day in
                    let dateString = formatDateString(year: calendar.component(.year, from: currentMonth), 
                                                    month: calendar.component(.month, from: currentMonth), 
                                                    day: day)
                    let isSelected = habit.selectedOneTimeDates.contains(dateString)
                    
                    Text("\(day)")
                        .font(.system(size: 12, weight: isSelected ? .semibold : .medium))
                        .foregroundColor(isSelected ? .white : .white.opacity(0.4))
                        .frame(height: 32)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(isSelected ? Color.hexColor("40E0D0").opacity(0.8) : Color.white.opacity(0.05))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 6)
                                        .stroke(
                                            isSelected ? Color.hexColor("40E0D0") : Color.clear,
                                            lineWidth: isSelected ? 1.5 : 0
                                        )
                                )
                        )
                }
            }
        }
    }
    
    // MARK: - 提醒时间区域
    private var reminderTimeSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("提醒时间")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white.opacity(0.8))
            
            HStack {
                ForEach(habit.reminderTimes, id: \.self) { time in
                    Text(time)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color.hexColor("40E0D0"))
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.hexColor("40E0D0").opacity(0.15))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(Color.hexColor("40E0D0").opacity(0.3), lineWidth: 1)
                                )
                        )
                }
                Spacer()
            }
            
            HStack {
                Image(systemName: "info.circle")
                    .font(.system(size: 12))
                    .foregroundColor(.white.opacity(0.6))
                
                Text("仅在执行日当天提醒")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(.white.opacity(0.6))
            }
        }
    }
    
    // MARK: - 辅助计算属性
    private var frequencyTypeText: String {
        switch habit.frequencyType {
        case "weekly": return "每周"
        case "daily": return "每天"
        case "monthly": return "每月"
        case "oneTime": return "一次性"
        default: return "自定义"
        }
    }
    
    private var hasMultipleMonths: Bool {
        let uniqueMonths = Set(habit.selectedOneTimeDates.compactMap { dateString -> String? in
            guard let date = dateFormatter.date(from: dateString) else { return nil }
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM"
            return formatter.string(from: date)
        })
        return uniqueMonths.count > 1
    }
    
    private var monthYearText: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年M月"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: currentMonth)
    }
    
    private func formatDateString(year: Int, month: Int, day: Int) -> String {
        return String(format: "%04d-%02d-%02d", year, month, day)
    }
}

// MARK: - 预览
#Preview {
    // 简化Preview配置，避免崩溃
            let container = try! EAAppSchema.createPreviewContainer()
    
    let sampleHabit = EAHabit(
        name: "晨间正念计划",
        iconName: "leaf.fill",
        targetFrequency: 5
    )
    
    return EAHabitDetailView(habit: sampleHabit)
        .modelContainer(container)
        .preferredColorScheme(.dark)
} 