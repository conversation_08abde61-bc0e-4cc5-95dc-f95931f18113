import SwiftUI
import SwiftData

/// 智慧宝库视图 - <PERSON><PERSON>'s Wisdom Treasury
/// 展示各类内容资源，包括动力包、心理练习、成功故事等
struct EAContentLibraryView: View {
    @StateObject private var viewModel: EAContentLibraryViewModel
    @Environment(\.repositoryContainer) private var repositoryContainer
    @Environment(\.dismiss) private var dismiss
    
    @State private var searchText = ""
    @State private var selectedCategory: ContentCategory? = nil
    @State private var showingProUpgrade = false
    
    init() {
        // 创建EAContentService实例
        let contentService = EAContentService()
        self._viewModel = StateObject(wrappedValue: EAContentLibraryViewModel(contentService: contentService))
    }
    
    var body: some View {
        NavigationView {
            GeometryReader { geometry in
                ZStack {
                    // 背景
                    EABackgroundView(style: .contentLibrary)
                        .ignoresSafeArea()
                    
                    VStack(spacing: 0) {
                        // 顶部导航区域 - 增加安全区域适配
                        headerView
                            .padding(.horizontal, EAAppConstants.ContentLibrary.contentPadding)
                            .padding(.top, max(geometry.safeAreaInsets.top + EAAppConstants.Dimensions.baseSpacing + 2, EAAppConstants.ContentLibrary.contentPadding))
                        
                        // 搜索和筛选区域
                        searchAndFilterView
                            .padding(.horizontal, EAAppConstants.ContentLibrary.contentPadding)
                            .padding(.top, EAAppConstants.Dimensions.standardPadding)
                        
                        // 内容区域
                        contentView
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                    }
                }
            }
        }
        .onAppear {
            Task {
                await viewModel.loadContents()
            }
        }
        .sheet(isPresented: $showingProUpgrade) {
            EAProUpgradeView()
        }
    }
    
    // MARK: - 顶部导航区域
    
    private var headerView: some View {
        HStack {
            // 返回按钮
            Button(action: {
                dismiss()
            }) {
                HStack(spacing: EAAppConstants.Dimensions.baseSpacing / 2) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: EAAppConstants.Dimensions.standardPadding, weight: .medium))
                    Text("返回")
                        .font(.system(size: EAAppConstants.Dimensions.standardPadding, weight: .medium))
                }
                .foregroundColor(Color("PrimaryTurquoise"))
            }
            
            Spacer()
            
            // 标题
            VStack(spacing: EAAppConstants.Dimensions.baseSpacing / 4) {
                Text("智慧宝库")
                    .font(.system(size: EAAppConstants.Dimensions.largeSpacing - 4, weight: .semibold))
                    .foregroundColor(.white)
                
                Text("Aura's Wisdom Treasury")
                    .font(.system(size: EAAppConstants.Dimensions.baseSpacing + 4, weight: .regular))
                    .foregroundColor(Color("TextSecondary").opacity(0.7))
            }
            
            Spacer()
            
            // Pro状态指示
            if viewModel.userProfile?.isPro == true {
                HStack(spacing: EAAppConstants.Dimensions.baseSpacing / 2) {
                    Image(systemName: "crown.fill")
                        .font(.system(size: EAAppConstants.Dimensions.baseSpacing + 4))
                        .foregroundColor(.yellow)
                    
                    Text("PRO")
                        .font(.system(size: EAAppConstants.Dimensions.baseSpacing + 2, weight: .bold))
                        .foregroundColor(.yellow)
                }
                .padding(.horizontal, EAAppConstants.Dimensions.baseSpacing)
                .padding(.vertical, EAAppConstants.Dimensions.baseSpacing / 2)
                .background(
                    RoundedRectangle(cornerRadius: EAAppConstants.Dimensions.baseSpacing)
                        .fill(Color.yellow.opacity(0.2))
                )
            } else {
                Button(action: {
                    showingProUpgrade = true
                }) {
                    Text("升级Pro")
                        .font(.system(size: EAAppConstants.Dimensions.baseSpacing + 4, weight: .medium))
                        .foregroundColor(Color("PrimaryTurquoise"))
                }
            }
        }
    }
    
    // MARK: - 搜索和筛选区域
    
    private var searchAndFilterView: some View {
        VStack(spacing: EAAppConstants.ContentLibrary.categoryButtonSpacing) {
            // 搜索框
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(Color("TextSecondary"))
                
                TextField("搜索内容...", text: $searchText)
                    .font(.system(size: EAAppConstants.Dimensions.standardPadding))
                    .foregroundColor(.white)
                    .onSubmit {
                        Task {
                            await viewModel.searchContents(query: searchText)
                        }
                    }
                
                if !searchText.isEmpty {
                    Button(action: {
                        searchText = ""
                        Task {
                            await viewModel.loadContents()
                        }
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(Color("TextSecondary"))
                    }
                }
            }
            .padding(.horizontal, EAAppConstants.ContentLibrary.searchFieldPadding)
            .padding(.vertical, EAAppConstants.ContentLibrary.searchFieldVerticalPadding)
            .background(
                RoundedRectangle(cornerRadius: EAAppConstants.Dimensions.standardCornerRadius)
                    .fill(Color.white.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: EAAppConstants.Dimensions.standardCornerRadius)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
            )
            
            // 分类筛选
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: EAAppConstants.ContentLibrary.categoryButtonSpacing) {
                    // 全部分类
                    CategoryFilterButton(
                        title: "全部",
                        icon: "square.grid.2x2",
                        isSelected: selectedCategory == nil
                    ) {
                        selectedCategory = nil
                        Task {
                            await viewModel.loadContents()
                        }
                    }
                    
                    // 各个分类
                    ForEach(ContentCategory.allCases, id: \.self) { category in
                        CategoryFilterButton(
                            title: category.displayName,
                            icon: category.icon,
                            isSelected: selectedCategory == category
                        ) {
                            selectedCategory = category
                                                          Task {
                                 await viewModel.loadContents()
                              }
                        }
                    }
                }
                .padding(.horizontal, EAAppConstants.ContentLibrary.contentPadding)
            }
        }
    }
    
    // MARK: - 内容区域
    
    private var contentView: some View {
        ScrollView {
            LazyVStack(spacing: EAAppConstants.ContentLibrary.contentListSpacing) {
                // 推荐内容区域
                if (viewModel.recommendedContents.isEmpty == false) && selectedCategory == nil && searchText.isEmpty {
                    recommendedSection
                }
                
                // 内容列表
                if viewModel.isLoading == true {
                    loadingView
                } else if viewModel.contents.isEmpty == true {
                    emptyStateView
                } else {
                    contentListSection
                }
                
                // 底部间距
                Color.clear
                    .frame(height: EAAppConstants.ContentLibrary.contentPadding)
            }
            .padding(.horizontal, EAAppConstants.ContentLibrary.contentPadding)
            .padding(.top, EAAppConstants.Dimensions.standardPadding)
        }
    }
    
    // MARK: - 推荐内容区域
    
    private var recommendedSection: some View {
        VStack(alignment: .leading, spacing: EAAppConstants.ContentLibrary.categoryButtonSpacing) {
            HStack {
                Text("为你推荐")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: EAAppConstants.ContentLibrary.categoryButtonSpacing) {
                    ForEach(viewModel.recommendedContents, id: \.id) { content in
                        RecommendedContentCard(content: content) {
                            viewModel.selectContent(content)
                        }
                    }
                }
                .padding(.horizontal, EAAppConstants.ContentLibrary.contentPadding)
            }
        }
    }
    
    // MARK: - 内容列表区域
    
    private var contentListSection: some View {
        VStack(alignment: .leading, spacing: EAAppConstants.ContentLibrary.categoryButtonSpacing) {
            // 区域标题
            HStack {
                Text(sectionTitle)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
                
                Text("\(viewModel.contents.count) 项内容")
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(Color("TextSecondary"))
            }
            
            // 内容卡片列表
            ForEach(viewModel.contents, id: \.id) { content in
                EAContentCard(
                    title: content.title,
                    description: getContentDescription(content),
                    contentType: content.category.rawValue,
                    isPro: content.isPro,
                    readingTime: "\(content.readingTime)分钟"
                ) {
                    if content.isPro && (viewModel.userProfile?.isPro ?? false) == false {
                        showingProUpgrade = true
                    } else {
                        viewModel.selectContent(content)
                    }
                }
            }
        }
    }
    
    // MARK: - 加载状态视图
    
    private var loadingView: some View {
        VStack(spacing: EAAppConstants.Dimensions.standardPadding) {
            ProgressView()
                .scaleEffect(1.2)
                .tint(Color("PrimaryTurquoise"))
            
            Text("加载中...")
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(Color("TextSecondary"))
        }
        .frame(maxWidth: .infinity)
        .padding(.top, EAAppConstants.AuraSpace.welcomeMessageTopPadding + EAAppConstants.ContentLibrary.contentPadding)
    }
    
    // MARK: - 空状态视图
    
    private var emptyStateView: some View {
        VStack(spacing: EAAppConstants.ContentLibrary.contentPadding) {
            Image(systemName: "doc.text.magnifyingglass")
                .font(.system(size: EAAppConstants.ContentLibrary.emptyStateIconSize))
                .foregroundColor(Color("TextSecondary").opacity(0.6))
            
            VStack(spacing: EAAppConstants.Dimensions.baseSpacing) {
                Text("暂无内容")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Text(emptyStateDescription)
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(Color("TextSecondary"))
                    .multilineTextAlignment(.center)
            }
            
            if !searchText.isEmpty || selectedCategory != nil {
                Button(action: {
                    searchText = ""
                    selectedCategory = nil
                    Task {
                        await viewModel.loadContents()
                    }
                }) {
                    Text("查看全部内容")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(Color("PrimaryTurquoise"))
                        .padding(.horizontal, EAAppConstants.ContentLibrary.contentPadding)
                        .padding(.vertical, EAAppConstants.Dimensions.baseSpacing + 2)
                        .background(
                            RoundedRectangle(cornerRadius: EAAppConstants.Dimensions.largeCornerRadius)
                                .stroke(Color("PrimaryTurquoise"), lineWidth: 1)
                        )
                }
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.top, EAAppConstants.AuraSpace.welcomeMessageTopPadding + EAAppConstants.ContentLibrary.contentPadding)
    }
    
    // MARK: - 计算属性
    
    private var sectionTitle: String {
        if !searchText.isEmpty {
            return "搜索结果"
        } else if let category = selectedCategory {
            return category.displayName
        } else {
            return "全部内容"
        }
    }
    
    private var emptyStateDescription: String {
        if !searchText.isEmpty {
            return "没有找到与\"\(searchText)\"相关的内容\n试试其他关键词吧"
        } else if selectedCategory != nil {
            return "该分类下暂无内容\n更多精彩内容正在路上"
        } else {
            return "内容库正在建设中\n敬请期待更多精彩内容"
        }
    }
    
    // MARK: - 辅助方法
    
    /// 获取内容描述
    /// 从完整内容中提取适合显示的描述文本，超出长度时自动截断
    private func getContentDescription(_ content: ContentDetail) -> String {
        let fullContent = content.content
        let maxLength = EAAppConstants.ContentLibrary.contentDescriptionMaxLength
        
        // 如果内容长度超过限制，截断并添加省略号
        if fullContent.count > maxLength {
            let index = fullContent.index(fullContent.startIndex, offsetBy: maxLength)
            return String(fullContent[..<index]) + "..."
        } else {
            return fullContent
        }
    }
}

// MARK: - 分类筛选按钮

struct CategoryFilterButton: View {
    let title: String
    let icon: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: EAAppConstants.ContentLibrary.categoryTagPadding) {
                Image(systemName: icon)
                    .font(.system(size: EAAppConstants.Dimensions.baseSpacing + 6))
                
                Text(title)
                    .font(.system(size: EAAppConstants.Dimensions.baseSpacing + 6, weight: .medium))
            }
            .foregroundColor(isSelected ? .white : Color("TextSecondary"))
            .padding(.horizontal, EAAppConstants.ContentLibrary.categoryButtonPadding)
            .padding(.vertical, EAAppConstants.ContentLibrary.categoryButtonVerticalPadding)
            .background(
                RoundedRectangle(cornerRadius: EAAppConstants.ContentLibrary.categoryButtonCornerRadius)
                    .fill(isSelected ? Color("PrimaryTurquoise") : Color.white.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: EAAppConstants.ContentLibrary.categoryButtonCornerRadius)
                            .stroke(
                                isSelected ? Color.clear : Color.white.opacity(0.2),
                                lineWidth: 1
                            )
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 推荐内容卡片

struct RecommendedContentCard: View {
    let content: ContentDetail
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: EAAppConstants.Dimensions.baseSpacing) {
                // Pro标识
                HStack {
                    Spacer()
                    if content.isPro {
                        EAProBadge()
                    }
                }
                
                // 标题
                Text(content.title)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.leading)
                    .lineLimit(2)
                
                // 分类和阅读时间
                HStack {
                    Text(content.category.displayName)
                        .font(.system(size: EAAppConstants.Dimensions.baseSpacing + 4, weight: .medium))
                        .foregroundColor(Color("PrimaryTurquoise"))
                        .padding(.horizontal, EAAppConstants.ContentLibrary.categoryTagPadding)
                        .padding(.vertical, EAAppConstants.ContentLibrary.categoryTagVerticalPadding)
                        .background(
                            RoundedRectangle(cornerRadius: EAAppConstants.ContentLibrary.categoryTagCornerRadius)
                                .fill(Color("PrimaryTurquoise").opacity(0.2))
                        )
                    
                    Spacer()
                    
                    Text("\(content.readingTime)分钟")
                        .font(.system(size: EAAppConstants.Dimensions.baseSpacing + 4, weight: .regular))
                        .foregroundColor(Color("TextSecondary"))
                }
            }
            .padding(EAAppConstants.ContentLibrary.recommendedCardPadding)
            .frame(width: EAAppConstants.ContentLibrary.recommendedCardWidth)
            .background(
                RoundedRectangle(cornerRadius: EAAppConstants.ContentLibrary.recommendedCardCornerRadius)
                    .fill(Color.white.opacity(0.08))
                    .overlay(
                        RoundedRectangle(cornerRadius: EAAppConstants.ContentLibrary.recommendedCardCornerRadius)
                            .stroke(Color.white.opacity(0.1), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Pro升级视图占位符

struct EAProUpgradeView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ZStack {
                Color("BackgroundDeepGreen")
                    .ignoresSafeArea()
                
                VStack(spacing: EAAppConstants.ContentLibrary.contentPadding) {
                    Image(systemName: "crown.fill")
                        .font(.system(size: EAAppConstants.ContentLibrary.proUpgradeIconSize))
                        .foregroundColor(.yellow)
                    
                    Text("升级到 Pro")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.white)
                    
                    Text("解锁所有高级内容和功能")
                        .font(.system(size: 16))
                        .foregroundColor(Color("TextSecondary"))
                    
                    Button(action: {
                        // TODO: 实现Pro升级逻辑
                        dismiss()
                    }) {
                        Text("立即升级")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, EAAppConstants.ContentLibrary.proUpgradeButtonVerticalPadding)
                            .background(
                                LinearGradient(
                                    colors: [Color.yellow, Color.orange],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                ),
                                in: RoundedRectangle(cornerRadius: EAAppConstants.ContentLibrary.proUpgradeButtonCornerRadius)
                            )
                    }
                    .padding(.horizontal, EAAppConstants.ContentLibrary.proUpgradeButtonPadding)
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                    .foregroundColor(.white)
                }
            }
        }
    }
}

#Preview {
    EAContentLibraryView()
        .modelContainer(PreviewData.container)
}