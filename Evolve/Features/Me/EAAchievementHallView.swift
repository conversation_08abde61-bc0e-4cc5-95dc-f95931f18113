import SwiftUI
import SwiftData

/// 成就殿堂页面
/// 展示用户获得的所有徽章和成就，支持查看详情
struct EAAchievementHallView: View {
    @ObservedObject var viewModel: EAMeViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var selectedAchievement: EAAchievement?
    @State private var showAchievementDetail = false
    
    var body: some View {
        ZStack {
            // 背景
            EABackgroundView()
                .ignoresSafeArea(.all)
            
            // 主要内容
            ScrollView {
                VStack(spacing: 20) {
                    // 顶部间距
                    Spacer(minLength: 20)
                    
                    // 成就网格
                    achievementGrid
                    
                    // 底部间距
                    Spacer(minLength: 40)
                }
                .padding(.horizontal, 20)
            }
        }
        .navigationTitle("成就殿堂")
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: {
                    dismiss()
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .medium))
                        Text("返回")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(Color.hexColor("40E0D0"))
                }
            }
        }
        .navigationDestination(isPresented: $showAchievementDetail) {
            if let achievement = selectedAchievement {
                EAAchievementDetailView(achievement: achievement)
            }
        }
        .onAppear {
            Task {
                await viewModel.loadAchievements()
            }
        }
    }
    
    // MARK: - Achievement Grid
    private var achievementGrid: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 16), count: 2), spacing: 20) {
            ForEach(viewModel.achievements) { achievement in
                EAAchievementCard(achievement: achievement) {
                    selectedAchievement = achievement
                    showAchievementDetail = true
                }
            }
        }
    }
}

// MARK: - Preview
#Preview("成就殿堂") {
    @MainActor
    func createPreview() -> some View {
        let container = PreviewData.container
        let repositoryContainer = EARepositoryContainerImpl(modelContainer: container)
        let sessionManager = EASessionManager(repositoryContainer: repositoryContainer)
        let viewModel = EAMeViewModel(sessionManager: sessionManager)
        
        return NavigationView {
            EAAchievementHallView(viewModel: viewModel)
                .environmentObject(sessionManager)
        }
        .modelContainer(container)
        .preferredColorScheme(.dark)
    }
    
    return createPreview()
} 