import SwiftUI

/// Sheet类型枚举 - 统一管理所有Sheet状态
enum EAAchievementDetailSheetType: Identifiable {
    case share
    
    var id: String {
        switch self {
        case .share: return "share"
        }
    }
}

/// 成就详情页面
/// 展示单个成就的详细信息、获得故事、进度状态等
/// ✅ 修复：实现统一Sheet状态管理，符合审查规范
struct EAAchievementDetailView: View {
    let achievement: EAAchievement
    @Environment(\.dismiss) private var dismiss
    
    // ✅ 修复：统一Sheet状态管理
    @State private var activeSheet: EAAchievementDetailSheetType?
    
    var body: some View {
        ZStack {
            // 背景
            EABackgroundView()
                .ignoresSafeArea(.all)
            
            // 主要内容
            ScrollView {
                VStack(spacing: 32) {
                    // 顶部间距
                    Spacer(minLength: 20)
                    
                    // 成就徽章展示
                    achievementBadgeSection
                    
                    // 成就信息
                    achievementInfoSection
                    
                    // 进度展示（如果未完成）
                    if !achievement.isCompleted && achievement.isUnlocked {
                        progressSection
                    }
                    
                    // 成就故事
                    storySection
                    
                    // 获得时间（如果已获得）
                    if let unlockedDate = achievement.unlockedDate {
                        unlockedDateSection(date: unlockedDate)
                    }
                    
                    // 分享按钮（如果已获得）
                    if achievement.isUnlocked {
                        shareSection
                    }
                    
                    // 底部间距
                    Spacer(minLength: 40)
                }
                .padding(.horizontal, 20)
            }
        }
        .navigationTitle("成就详情")
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: {
                    dismiss()
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .medium))
                        Text("返回")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(Color.hexColor("40E0D0"))
                }
            }
            
            ToolbarItem(placement: .navigationBarTrailing) {
                if achievement.isUnlocked {
                    Button(action: {
                        activeSheet = .share
                    }) {
                        Image(systemName: "square.and.arrow.up")
                            .foregroundColor(Color.hexColor("40E0D0"))
                    }
                }
            }
        }
        .sheet(item: $activeSheet) { sheet in
            switch sheet {
            case .share:
                EAShareSheet(items: [generateShareText()])
            }
        }
    }
    
    // MARK: - Achievement Badge Section
    private var achievementBadgeSection: some View {
        VStack(spacing: 20) {
            // 大徽章图标
            ZStack {
                // 背景光环
                if achievement.isUnlocked {
                    Circle()
                        .fill(
                            RadialGradient(
                                gradient: Gradient(colors: [
                                    Color.hexColor(achievement.iconColors.first ?? "#FFD700").opacity(0.3),
                                    Color.hexColor(achievement.iconColors.last ?? "#FFA500").opacity(0.1),
                                    Color.clear
                                ]),
                                center: .center,
                                startRadius: 0,
                                endRadius: 80
                            )
                        )
                        .frame(width: 160, height: 160)
                        .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: achievement.isUnlocked)
                }
                
                // 主徽章背景
                Circle()
                    .fill(
                        achievement.isUnlocked ?
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.hexColor(achievement.iconColors.first ?? "#FFD700").opacity(0.8),
                                Color.hexColor(achievement.iconColors.last ?? "#FFA500").opacity(0.6)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ) :
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.white.opacity(0.1),
                                Color.white.opacity(0.05)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 120, height: 120)
                    .shadow(
                        color: achievement.isUnlocked ? 
                            Color.hexColor(achievement.iconColors.first ?? "#FFD700").opacity(0.5) : 
                            Color.clear,
                        radius: achievement.isUnlocked ? 15 : 0,
                        x: 0,
                        y: 0
                    )
                
                // 徽章图标
                Text(achievement.icon)
                    .font(.system(size: 48))
                    .opacity(achievement.isUnlocked ? 1.0 : 0.4)
                
                // 锁定图标（未解锁时显示）
                if !achievement.isUnlocked {
                    ZStack {
                        Circle()
                            .fill(Color.black.opacity(0.6))
                            .frame(width: 120, height: 120)
                        
                        Image(systemName: "lock.fill")
                            .font(.system(size: 32, weight: .medium))
                            .foregroundColor(Color.white.opacity(0.8))
                    }
                }
            }
            
            // 状态指示
            statusBadge
        }
    }
    
    // MARK: - Achievement Info Section
    private var achievementInfoSection: some View {
        VStack(spacing: 16) {
            // 成就名称
            Text(achievement.name)
                .font(.system(size: 28, weight: .bold))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
            
            // 成就描述
            Text(achievement.description)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(Color.white.opacity(0.8))
                .multilineTextAlignment(.center)
                .lineSpacing(4)
        }
        .padding(.horizontal, 20)
    }
    
    // MARK: - Progress Section
    private var progressSection: some View {
        VStack(spacing: 16) {
            // 标题
            HStack {
                Text("进度")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            // 进度卡片
            VStack(spacing: 16) {
                // 进度条
                VStack(spacing: 8) {
                    HStack {
                        Text("当前进度")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(Color.white.opacity(0.8))
                        
                        Spacer()
                        
                        Text("\(achievement.progress)/\(achievement.maxProgress)")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(Color.hexColor(achievement.iconColors.first ?? "#FFD700"))
                    }
                    
                    GeometryReader { geometry in
                        ZStack(alignment: .leading) {
                            // 背景
                            RoundedRectangle(cornerRadius: 4)
                                .fill(Color.white.opacity(0.2))
                                .frame(height: 8)
                            
                            // 进度
                            RoundedRectangle(cornerRadius: 4)
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color.hexColor(achievement.iconColors.first ?? "#FFD700"),
                                            Color.hexColor(achievement.iconColors.last ?? "#FFA500")
                                        ]),
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                                .frame(width: geometry.size.width * achievement.progressPercentage, height: 8)
                                .animation(.easeInOut(duration: 1.0), value: achievement.progressPercentage)
                        }
                    }
                    .frame(height: 8)
                }
                
                // 进度百分比
                Text(String(format: "%.0f%% 完成", achievement.progressPercentage * 100))
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(Color.hexColor(achievement.iconColors.first ?? "#FFD700"))
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.hexColor(achievement.iconColors.first ?? "#FFD700").opacity(0.3), lineWidth: 1)
                    )
            )
        }
    }
    
    // MARK: - Story Section
    private var storySection: some View {
        VStack(spacing: 16) {
            // 标题
            HStack {
                Text(achievement.isUnlocked ? "成就故事" : "解锁条件")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            // 故事内容
            Text(achievement.story)
                .font(.system(size: 15, weight: .regular))
                .foregroundColor(Color.white.opacity(0.8))
                .lineSpacing(6)
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(20)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.white.opacity(0.05))
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color.white.opacity(0.1), lineWidth: 1)
                        )
                )
        }
    }
    
    // MARK: - Unlocked Date Section
    private func unlockedDateSection(date: Date) -> some View {
        VStack(spacing: 16) {
            // 标题
            HStack {
                Text("获得时间")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            // 时间信息
            HStack(spacing: 16) {
                Image(systemName: "calendar")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.hexColor("40E0D0"))
                
                Text(formatDate(date))
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                
                Spacer()
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.hexColor("40E0D0").opacity(0.3), lineWidth: 1)
                    )
            )
        }
    }
    
    // MARK: - Share Section
    private var shareSection: some View {
        VStack(spacing: 16) {
            // 分享按钮
            Button(action: {
                activeSheet = .share
            }) {
                HStack(spacing: 12) {
                    Image(systemName: "square.and.arrow.up")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                    
                    Text("分享成就")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.hexColor("40E0D0"),
                                    Color.hexColor("00C8C8")
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .shadow(
                            color: Color.hexColor("40E0D0").opacity(0.4),
                            radius: 8,
                            x: 0,
                            y: 4
                        )
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    // MARK: - Status Badge
    private var statusBadge: some View {
        Group {
            if achievement.isUnlocked {
                if achievement.isCompleted {
                    // 已完成
                    HStack(spacing: 8) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(Color.hexColor("32CD32"))
                        
                        Text("已获得")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(Color.hexColor("32CD32"))
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(Color.hexColor("32CD32").opacity(0.2))
                            .overlay(
                                RoundedRectangle(cornerRadius: 20)
                                    .stroke(Color.hexColor("32CD32").opacity(0.5), lineWidth: 1)
                            )
                    )
                } else {
                    // 进行中
                    HStack(spacing: 8) {
                        Image(systemName: "clock.fill")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(Color.hexColor("FFD700"))
                        
                        Text("进行中")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(Color.hexColor("FFD700"))
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(Color.hexColor("FFD700").opacity(0.2))
                            .overlay(
                                RoundedRectangle(cornerRadius: 20)
                                    .stroke(Color.hexColor("FFD700").opacity(0.5), lineWidth: 1)
                            )
                    )
                }
            } else {
                // 未解锁
                HStack(spacing: 8) {
                    Image(systemName: "lock.fill")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(Color.white.opacity(0.5))
                    
                    Text("未解锁")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(Color.white.opacity(0.5))
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color.white.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                        )
                )
            }
        }
    }
    
    // MARK: - Helper Methods
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .long
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
    
    private func generateShareText() -> String {
        if achievement.isUnlocked {
            return "🎉 我在Evolve AI中获得了「\(achievement.name)」成就！\n\n\(achievement.description)\n\n\(achievement.story)\n\n#EvolveAI #习惯养成 #自我提升"
        } else {
            return "💪 我正在Evolve AI中挑战「\(achievement.name)」成就！\n\n\(achievement.description)\n\n当前进度：\(achievement.progress)/\(achievement.maxProgress)\n\n#EvolveAI #习惯养成 #自我提升"
        }
    }
}

// MARK: - 分享功能
private struct EAShareSheet: UIViewControllerRepresentable {
    let items: [Any]
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(activityItems: items, applicationActivities: nil)
        return controller
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

// MARK: - Preview
#Preview("成就详情 - 已获得") {
    let achievement = EAAchievement(
        id: "first_light",
        name: "初光者",
        description: "完成第一个习惯",
        icon: "✨",
        iconColors: ["#FFD700", "#FFA500"],
        isUnlocked: true,
        progress: 1,
        maxProgress: 1,
        story: "每一段伟大的旅程都始于第一步。这枚徽章见证了你踏出习惯养成的第一步，愿这微光指引你前行的道路。在这个充满挑战的世界里，你选择了改变，选择了成长。这不仅仅是一个简单的开始，更是你对更好生活的承诺。",
        unlockedDate: Date()
    )
    
    return NavigationView {
        EAAchievementDetailView(achievement: achievement)
    }
    .preferredColorScheme(.dark)
}

#Preview("成就详情 - 进行中") {
    let achievement = EAAchievement(
        id: "sprout_power",
        name: "萌芽之力",
        description: "连续完成任意习惯7天",
        icon: "🌿",
        iconColors: ["#32CD32", "#008B8B"],
        isUnlocked: true,
        progress: 3,
        maxProgress: 7,
        story: "每一株参天大树，都始于一颗微小的种子破土而出。这枚徽章象征着你坚持不懈的努力，成功培育了习惯的嫩芽。继续灌溉，它将茁壮成长！坚持是一种品质，也是一种力量。在这个快节奏的时代，能够持续专注于一件事情是多么珍贵。",
        unlockedDate: nil
    )
    
    return NavigationView {
        EAAchievementDetailView(achievement: achievement)
    }
    .preferredColorScheme(.dark)
}

#Preview("成就详情 - 未解锁") {
    let achievement = EAAchievement(
        id: "mystery_badge",
        name: "神秘徽章",
        description: "???",
        icon: "❓",
        iconColors: ["#808080", "#696969"],
        isUnlocked: false,
        progress: 0,
        maxProgress: 1,
        story: "这是一个神秘的徽章，它的获得条件还未被发现。也许需要特殊的行为组合，也许需要达到某个隐藏的里程碑。保持好奇心，继续探索，答案就在前方等待着你。",
        unlockedDate: nil
    )
    
    return NavigationView {
        EAAchievementDetailView(achievement: achievement)
    }
    .preferredColorScheme(.dark)
} 