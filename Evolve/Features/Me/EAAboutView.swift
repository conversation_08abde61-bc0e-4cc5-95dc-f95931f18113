import SwiftUI

/// 关于页面
/// 展示应用介绍、开发团队信息、版本信息等
struct EAAboutView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ZStack {
                // 背景
                EABackgroundView()
                    .ignoresSafeArea(.all)
                
                // 主要内容
                ScrollView {
                    VStack(spacing: 32) {
                        // 顶部间距
                        Spacer(minLength: 16)
                        
                        // 应用图标和名称
                        appHeaderSection
                        
                        // 应用介绍
                        appDescriptionSection
                        
                        // 核心功能
                        coreFeaturesSection
                        
                        // 开发团队
                        teamSection
                        
                        // 版权信息
                        copyrightSection
                        
                        // 底部间距
                        Spacer(minLength: 32)
                    }
                    .padding(.horizontal, 16)
                }
            }
            .navigationTitle("关于应用")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        dismiss()
                    }) {
                        HStack(spacing: 4) {
                            Image(systemName: "chevron.left")
                                .font(.system(size: 16, weight: .semibold))
                            Text("返回")
                                .font(.system(size: 17, weight: .regular))
                        }
                        .foregroundColor(Color.accentColor)
                    }
                }
            }
        }
    }
    
    // MARK: - App Header Section
    private var appHeaderSection: some View {
        VStack(spacing: 16) {
            // 应用图标
            ZStack {
                Circle()
                    .fill(appIconGradient)
                    .frame(width: 100, height: 100)
                    .shadow(
                        color: Color.accentColor.opacity(0.5),
                        radius: 20,
                        x: 0,
                        y: 0
                    )
                
                Image(systemName: "sparkles")
                    .font(.system(size: 40, weight: .medium))
                    .foregroundColor(.white)
            }
            
            VStack(spacing: 8) {
                Text("Evolve AI")
                    .font(.system(size: 28, weight: .bold))
                    .foregroundColor(.white)
                
                Text("极光助手")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.white.opacity(0.8))
                
                Text("版本 1.0.0")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color.white.opacity(0.6))
            }
        }
    }
    
    private var appIconGradient: RadialGradient {
        RadialGradient(
            gradient: Gradient(colors: [
                Color.accentColor.opacity(0.8),
                Color.accentColor.opacity(0.7),
                Color.accentColor.opacity(0.5)
            ]),
            center: .center,
            startRadius: 0,
            endRadius: 50
        )
    }
    
    // MARK: - App Description Section
    private var appDescriptionSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("关于Evolve")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)
            
                            Text("Evolve AI（极光助手）是一款基于AI驱动的个人计划养成应用。我们相信，每个人都有无限的潜能，只需要正确的引导和坚持不懈的努力。\n\n通过AI的智慧陪伴和科学的计划培养方法，Evolve帮助您建立积极的生活计划，实现持续的自我提升和成长。")
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(Color.white.opacity(0.8))
                .lineSpacing(4)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(20)
        .background(cardBackground)
    }
    
    // MARK: - Core Features Section
    private var coreFeaturesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("核心功能")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)
            
            VStack(spacing: 12) {
                featureItem(
                    icon: "brain.head.profile",
                    title: "AI智能陪伴",
                    description: "个性化的AI助手Aura，提供专业的计划养成指导"
                )
                
                featureItem(
                    icon: "chart.line.uptrend.xyaxis",
                    title: "数据分析洞察",
                    description: "深度分析您的计划数据，提供科学的改进建议"
                )
                
                featureItem(
                    icon: "trophy",
                    title: "成就激励系统",
                    description: "丰富的徽章和成就，让计划养成更有趣味性"
                )
                
                featureItem(
                    icon: "bell",
                    title: "智能提醒",
                    description: "基于您的作息计划，提供最佳的提醒时机"
                )
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(20)
        .background(cardBackground)
    }
    
    private func featureItem(icon: String, title: String, description: String) -> some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(Color.accentColor)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(Color.white.opacity(0.7))
            }
            
            Spacer()
        }
    }
    
    // MARK: - Team Section
    private var teamSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("开发团队")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)
            
            Text("由一群热爱生活、追求进步的开发者精心打造。我们致力于通过技术的力量，帮助更多人实现自我提升的梦想。")
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(Color.white.opacity(0.8))
                .lineSpacing(4)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(20)
        .background(cardBackground)
    }
    
    // MARK: - Copyright Section
    private var copyrightSection: some View {
        VStack(spacing: 8) {
            Text("© 2025 Evolve AI. All rights reserved.")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(Color.white.opacity(0.5))
            
            Text("感谢您选择Evolve，让我们一起成长！")
                .font(.system(size: 11, weight: .regular))
                .foregroundColor(Color.white.opacity(0.4))
        }
        .padding(.top, 16)
    }
    
    // MARK: - Helper Views
    private var cardBackground: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(Color.white.opacity(0.05))
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.white.opacity(0.1), lineWidth: 1)
            )
    }
}

// MARK: - Preview
#Preview("关于页面") {
    EAAboutView()
        .preferredColorScheme(.dark)
} 