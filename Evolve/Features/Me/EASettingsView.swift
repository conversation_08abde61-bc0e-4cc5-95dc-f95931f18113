import SwiftUI
import SwiftData

/// Sheet类型枚举 - 统一管理所有Sheet状态
enum EASettingsSheetType: Identifiable {
    case about
    case dataExportAlert
    case clearDataAlert
    
    var id: String {
        switch self {
        case .about: return "about"
        case .dataExportAlert: return "dataExportAlert"
        case .clearDataAlert: return "clearDataAlert"
        }
    }
}

/// 设置页面
/// 包含通知设置、隐私设置、数据管理、关于应用等功能
/// ✅ 修复：实现统一Sheet状态管理，符合.cursorrules规范
struct EASettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.repositoryContainer) private var repositoryContainer
    @EnvironmentObject var sessionManager: EASessionManager
    
    // 使用设置服务进行数据持久化
    @State private var settingsService: EASettingsService?
    
    // ✅ 修复：统一Sheet状态管理
    @State private var activeSheet: EASettingsSheetType?
    
    var body: some View {
        ZStack {
            // 背景
            EABackgroundView()
                .ignoresSafeArea(.all)
            
            // 主要内容
            ScrollView {
                VStack(spacing: 24) {
                    // 顶部间距
                    Spacer(minLength: 16)
                    
                    // 通知设置
                    notificationSection
                    
                    // 隐私设置
                    privacySection
                    
                    // 数据管理
                    dataManagementSection
                    
                    // 关于应用
                    aboutSection
                    
                    // 底部间距
                    Spacer(minLength: 32)
                }
                .padding(.horizontal, 16)
            }
        }
        .navigationTitle("设置")
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: {
                    dismiss()
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .medium))
                        Text("返回")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(Color.hexColor("40E0D0"))
                }
            }
        }
        .sheet(item: $activeSheet) { sheet in
            switch sheet {
            case .about:
                EAAboutView()
            case .dataExportAlert:
                // 实现数据导出功能
                Text("数据导出功能")
            case .clearDataAlert:
                // 实现清除数据功能
                Text("清除数据功能")
            }
        }
        .onAppear {
            // ✅ 修复：初始化设置服务
            if settingsService == nil, let container = repositoryContainer {
                settingsService = EASettingsService(sessionManager: sessionManager, repositoryContainer: container)
            }
            // 加载设置
            settingsService?.loadSettings()
        }
    }
    
    // MARK: - Notification Section
    private var notificationSection: some View {
        VStack(spacing: 16) {
            // 标题
            sectionHeader(title: "通知设置", icon: "bell")
            
            // 设置项
            VStack(spacing: 12) {
                // 启用通知
                settingToggleItem(
                    title: "启用通知",
                    subtitle: "接收计划提醒和鼓励消息",
                    isOn: Binding(
                        get: { settingsService?.notificationsEnabled ?? false },
                        set: { newValue in
                            settingsService?.updateNotificationsEnabled(newValue)
                            Task {
                                await settingsService?.saveSettings()
                            }
                        }
                    )
                )
                
                // 智能提醒时间
                settingToggleItem(
                    title: "智能提醒时间",
                    subtitle: "基于您的使用计划优化提醒时间",
                    isOn: Binding(
                        get: { settingsService?.smartTimingEnabled ?? false },
                        set: { newValue in
                            settingsService?.updateSmartTimingEnabled(newValue)
                            Task {
                                await settingsService?.saveSettings()
                            }
                        }
                    )
                )
                .disabled(!(settingsService?.notificationsEnabled ?? false))
                .opacity((settingsService?.notificationsEnabled ?? false) ? 1.0 : 0.6)
                
                // 提醒风格
                reminderStylePickerItem(
                    title: "提醒风格",
                    subtitle: settingsService?.reminderStyle ?? "gentle",
                    selection: Binding(
                        get: { 
                            EAReminderStyle(rawValue: settingsService?.reminderStyle ?? "gentle") ?? .gentle
                        },
                        set: { newValue in
                            settingsService?.updateReminderStyle(newValue.rawValue)
                            Task {
                                await settingsService?.saveSettings()
                            }
                        }
                    )
                )
                .disabled(!(settingsService?.notificationsEnabled ?? false))
                .opacity((settingsService?.notificationsEnabled ?? false) ? 1.0 : 0.6)
            }
        }
    }
    
    // MARK: - Privacy Section
    private var privacySection: some View {
        VStack(spacing: 16) {
            // 标题
            sectionHeader(title: "隐私设置", icon: "lock.shield")
            
            // 设置项
            VStack(spacing: 12) {
                // 数据分析
                settingToggleItem(
                    title: "数据分析",
                    subtitle: "帮助改进应用体验（匿名数据）",
                    isOn: Binding(
                        get: { settingsService?.dataAnalyticsEnabled ?? false },
                        set: { newValue in
                            Task {
                                await settingsService?.updateDataAnalytics(enabled: newValue)
                            }
                        }
                    )
                )
                
                // 崩溃报告
                settingToggleItem(
                    title: "崩溃报告",
                    subtitle: "自动发送崩溃报告以改进稳定性",
                    isOn: Binding(
                        get: { settingsService?.crashReportingEnabled ?? false },
                        set: { newValue in
                            Task {
                                await settingsService?.updateCrashReporting(enabled: newValue)
                            }
                        }
                    )
                )
                
                // 隐私政策
                settingActionItem(
                    title: "隐私政策",
                    subtitle: "查看我们如何保护您的隐私",
                    action: {
                        // 打开隐私政策页面
                    }
                )
                
                // 用户协议
                settingActionItem(
                    title: "用户协议",
                    subtitle: "查看服务条款和使用协议",
                    action: {
                        // 打开用户协议页面
                    }
                )
            }
        }
    }
    
    // MARK: - Data Management Section
    private var dataManagementSection: some View {
        VStack(spacing: 16) {
            // 标题
            sectionHeader(title: "数据管理", icon: "externaldrive")
            
            // 设置项
            VStack(spacing: 12) {
                // 导出数据
                settingActionItem(
                    title: "导出数据",
                    subtitle: "导出所有计划数据和统计信息",
                    action: {
                        activeSheet = .dataExportAlert
                    }
                )
                
                // 清除本地数据
                settingActionItem(
                    title: "清除本地数据",
                    subtitle: "删除所有本地存储的数据",
                    isDestructive: true,
                    action: {
                        activeSheet = .clearDataAlert
                    }
                )
            }
        }
    }
    
    // MARK: - About Section
    private var aboutSection: some View {
        VStack(spacing: 16) {
            // 标题
            sectionHeader(title: "关于应用", icon: "info.circle")
            
            // 设置项
            VStack(spacing: 12) {
                // 应用版本
                settingInfoItem(
                    title: "版本",
                    value: "1.0.0"
                )
                
                // 构建版本
                settingInfoItem(
                    title: "构建版本",
                    value: "2025.01.01"
                )
                
                // 关于Evolve
                settingActionItem(
                    title: "关于Evolve",
                    subtitle: "了解更多关于极光助手",
                    action: {
                        activeSheet = .about
                    }
                )
                
                // 反馈建议
                settingActionItem(
                    title: "反馈建议",
                    subtitle: "告诉我们您的想法和建议",
                    action: {
                        // 打开反馈页面
                    }
                )
                
                // 评价应用
                settingActionItem(
                    title: "评价应用",
                    subtitle: "在App Store中为我们评分",
                    action: {
                        // 打开App Store评价页面
                    }
                )
            }
        }
    }
    
    // MARK: - Helper Views
    private func sectionHeader(title: String, icon: String) -> some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(Color.accentColor)
            
            Text(title)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)
            
            Spacer()
        }
    }
    
    private func settingToggleItem(title: String, subtitle: String, isOn: Binding<Bool>) -> some View {
        HStack(spacing: 16) {
            // 文字信息
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                
                Text(subtitle)
                    .font(.system(size: 13, weight: .regular))
                    .foregroundColor(Color.white.opacity(0.7))
            }
            
            Spacer()
            
            // 开关
            Toggle("", isOn: isOn)
                .toggleStyle(SwitchToggleStyle(tint: Color.accentColor))
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
    
    private func settingPickerItem<T: CaseIterable & Hashable & RawRepresentable>(
        title: String,
        subtitle: String,
        selection: Binding<T>
    ) -> some View where T.RawValue == String {
        Menu {
            ForEach(Array(T.allCases), id: \.self) { option in
                Button(action: {
                    selection.wrappedValue = option
                }) {
                    HStack {
                        Text(option.rawValue)
                        if selection.wrappedValue == option {
                            Image(systemName: "checkmark")
                        }
                    }
                }
            }
        } label: {
            HStack(spacing: 16) {
                // 文字信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                    
                    Text(subtitle)
                        .font(.system(size: 13, weight: .regular))
                        .foregroundColor(Color.white.opacity(0.7))
                }
                
                Spacer()
                
                // 箭头
                Image(systemName: "chevron.down")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(Color.white.opacity(0.5))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.white.opacity(0.1), lineWidth: 1)
                    )
            )
        }
    }
    
    private func reminderStylePickerItem(
        title: String,
        subtitle: String,
        selection: Binding<EAReminderStyle>
    ) -> some View {
        Menu {
            ForEach(EAReminderStyle.allCases, id: \.self) { style in
                Button(action: {
                    selection.wrappedValue = style
                }) {
                    HStack {
                        Text(style.description)
                        if selection.wrappedValue == style {
                            Image(systemName: "checkmark")
                        }
                    }
                }
            }
        } label: {
            HStack(spacing: 16) {
                // 文字信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                    
                    Text(subtitle)
                        .font(.system(size: 13, weight: .regular))
                        .foregroundColor(Color.white.opacity(0.7))
                }
                
                Spacer()
                
                // 箭头
                Image(systemName: "chevron.down")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(Color.white.opacity(0.5))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.white.opacity(0.1), lineWidth: 1)
                    )
            )
        }
    }
    
    private func settingActionItem(
        title: String,
        subtitle: String,
        isDestructive: Bool = false,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // 文字信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(isDestructive ? Color.red : .white)
                    
                    Text(subtitle)
                        .font(.system(size: 13, weight: .regular))
                        .foregroundColor(Color.white.opacity(0.7))
                }
                
                Spacer()
                
                // 箭头
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(Color.white.opacity(0.5))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(
                                isDestructive ? Color.red.opacity(0.3) : Color.white.opacity(0.1),
                                lineWidth: 1
                            )
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func settingInfoItem(title: String, value: String) -> some View {
        HStack(spacing: 16) {
            // 标题
            Text(title)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
            
            Spacer()
            
            // 值
            Text(value)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(Color.white.opacity(0.7))
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
}

// MARK: - Supporting Types
// ReminderStyle枚举已移至EASettingsService.swift，避免重复定义

// MARK: - About View已移至独立文件EAAboutView.swift

// MARK: - Preview
#Preview("设置页面") {
    @MainActor
    func createPreview() -> some View {
        // ✅ 修复：使用本地实例替代单例
        let sessionManager = EASessionManager()
        return NavigationView {
            EASettingsView()
        }
        .environmentObject(sessionManager)
        .preferredColorScheme(.dark)
    }
    
    return createPreview()
}

#Preview("关于页面") {
    EAAboutView()
        .preferredColorScheme(.dark)
} 