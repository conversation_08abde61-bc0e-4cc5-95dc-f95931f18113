import SwiftUI
import SwiftData

/// "我的"页面ViewModel
/// 管理用户统计数据、成就系统、Pro状态和各种导航状态
@MainActor
final class EAMeViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var userStats: EAUserStats = EAUserStats()
    @Published var achievements: [EAAchievement] = []
    @Published var currentUser: EAUser? = nil

    // 🔑 优化：合并加载状态，减少@Published属性数量
    @Published var loadingState: LoadingState = .idle
    @Published var errorMessage: String? = nil

    // 导航状态
    @Published var showAchievementHall = false
    @Published var showProCenter = false
    @Published var showSettings = false
    @Published var showDataExport = false

    // 🔑 新增：加载状态枚举
    enum LoadingState {
        case idle
        case loadingUser
        case loadingStats
        case loadingAchievements
        case loadingAll
    }
    @Published var showAbout = false
    @Published var selectedAchievement: EAAchievement?
    
    // MARK: - Dependencies
    private var repositoryContainer: EARepositoryContainer?
    
    /// 获取Repository容器的访问器（供外部组件使用）
    var repositoryContainerReference: EARepositoryContainer? {
        return repositoryContainer
    }
    private let sessionManager: EASessionManager
    
    // MARK: - Initialization
    init(sessionManager: EASessionManager) {
        // 使用传入的sessionManager，避免使用禁止的单例模式
        self.sessionManager = sessionManager
        self.userStats = EAUserStats()
        self.achievements = []
    }
    
    // MARK: - 便利初始化方法（用于需要同时传入repositoryContainer的情况）
    convenience init(sessionManager: EASessionManager? = nil, repositoryContainer: EARepositoryContainer) {
        // 如果提供了sessionManager，使用它；否则创建新的
        let finalSessionManager = sessionManager ?? EASessionManager(repositoryContainer: repositoryContainer)
        self.init(sessionManager: finalSessionManager)
        
        // 设置Repository容器
        self.repositoryContainer = repositoryContainer
    }
    
    // MARK: - 🔑 标准化异步用户加载方法

    /// 异步加载当前用户信息
    @MainActor
    private func loadCurrentUser() async {
        loadingState = .loadingUser
        errorMessage = nil
        defer { loadingState = .idle }

        guard let user = await sessionManager.safeCurrentUser else {
            errorMessage = "无法获取用户信息"
            return
        }

        // 更新依赖用户信息的属性
        updateUserDependentProperties(user)
    }

    /// 更新依赖用户信息的属性
    @MainActor
    private func updateUserDependentProperties(_ user: EAUser) {
        self.currentUser = user

        // 清除错误状态
        self.errorMessage = nil

        // 触发相关数据加载
        Task {
            await loadUserStats()
            await loadAchievements()
        }
    }

    // MARK: - Public Methods
    /// 设置Repository容器
    func setRepositoryContainer(_ container: EARepositoryContainer) {
        self.repositoryContainer = container

        // 设置Repository后，异步加载用户信息
        Task { @MainActor in
            await loadCurrentUser()
        }
    }
    
    /// 加载用户统计数据
    func loadUserStats() async {
        guard let container = repositoryContainer else { return }

        // 🔑 重构：使用异步获取的用户信息
        let currentUser: EAUser
        if let user = self.currentUser {
            currentUser = user
        } else if let user = await sessionManager.safeCurrentUser {
            currentUser = user
        } else {
            return
        }

        loadingState = .loadingStats
        defer { loadingState = .idle }
        
        do {
            // 通过Repository获取用户习惯
            let userHabits = try await container.habitRepository.fetchActiveHabits(for: currentUser.id)
            
            // 通过Repository获取用户完成记录
            let userCompletions = try await container.completionRepository.fetchTodayCompletions(for: currentUser.id)
            
            // 计算统计数据
            let totalHabits = userHabits.count
            let totalCompletions = userCompletions.count
            let currentStreak = calculateCurrentStreak(completions: userCompletions)
            let completionRate = calculateCompletionRate(habits: userHabits, completions: userCompletions)
            
            userStats = EAUserStats(
                totalHabits: totalHabits,
                totalCompletions: totalCompletions,
                currentStreak: currentStreak,
                completionRate: completionRate,
                completedHabits: totalCompletions, // 使用totalCompletions作为completedHabits的值
                longestStreak: currentStreak // 暂时使用currentStreak作为longestStreak的值
            )
        } catch {
            // 加载用户统计数据失败，静默处理
        }
    }
    
    /// 加载成就数据
    func loadAchievements() async {
        loadingState = .loadingAchievements
        defer { loadingState = .idle }
        
        // 生成成就数据（基于HTML原型）
        achievements = generateAchievements()
    }
    
    /// 选择成就（用于显示详情）
    func selectAchievement(_ achievement: EAAchievement) {
        selectedAchievement = achievement
    }
    
    /// 🔑 关键修复：更新用户头像数据
    func updateUserAvatar(_ avatarData: EAAvatarData?) async throws {
        guard let container = repositoryContainer else {
            throw DataModelError.userNotFound
        }

        // 🔑 重构：使用异步获取的用户信息
        let currentUser: EAUser
        if let user = self.currentUser {
            currentUser = user
        } else if let user = await sessionManager.safeCurrentUser {
            currentUser = user
        } else {
            throw DataModelError.userNotFound
        }
        
        do {
            // 1. 更新用户模型中的头像数据
            currentUser.avatarData = avatarData
            
            // 2. 通过Repository保存用户数据到SwiftData
            try await container.userRepository.saveUser(currentUser)
            
            // 3. 通过SessionManager更新当前用户状态
            try await sessionManager.updateUser(currentUser)
            
        } catch {
            throw error
        }
    }
    
    // MARK: - Private Methods
    /// 计算当前连续天数
    private func calculateCurrentStreak(completions: [EACompletion]) -> Int {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        
        // 按日期分组完成记录
        let completionsByDate = Dictionary(grouping: completions) { completion in
            calendar.startOfDay(for: completion.date)
        }
        
        var streak = 0
        var currentDate = today
        
        // 从今天开始往前计算连续天数
        while completionsByDate[currentDate] != nil {
            streak += 1
            currentDate = calendar.date(byAdding: .day, value: -1, to: currentDate) ?? currentDate
        }
        
        return streak
    }
    
    /// 计算完成率
    private func calculateCompletionRate(habits: [EAHabit], completions: [EACompletion]) -> Double {
        guard !habits.isEmpty else { return 0.0 }
        
        let calendar = Calendar.current
        let today = Date()
        let thirtyDaysAgo = calendar.date(byAdding: .day, value: -30, to: today) ?? today
        
        // 计算过去30天的预期完成次数
        let recentCompletions = completions.filter { completion in
            completion.date >= thirtyDaysAgo && completion.date <= today
        }
        
        // 简化计算：假设每个习惯每天应该完成一次
        let expectedCompletions = habits.count * 30
        let actualCompletions = recentCompletions.count
        
        return expectedCompletions > 0 ? Double(actualCompletions) / Double(expectedCompletions) : 0.0
    }
    
    /// 生成成就数据
    private func generateAchievements() -> [EAAchievement] {
        return [
            EAAchievement(
                id: "first_light",
                name: "初光者",
                description: "完成第一个习惯",
                icon: "✨",
                iconColors: ["#FFD700", "#FFA500"],
                isUnlocked: userStats.totalCompletions > 0,
                progress: min(userStats.totalCompletions, 1),
                maxProgress: 1,
                story: "每一段伟大的旅程都始于第一步。这枚徽章见证了你踏出习惯养成的第一步，愿这微光指引你前行的道路。",
                unlockedDate: userStats.totalCompletions > 0 ? Date() : nil
            ),
            EAAchievement(
                id: "sprout_power",
                name: "萌芽之力",
                description: "连续完成任意习惯7天",
                icon: "🌿",
                iconColors: ["#32CD32", "#008B8B"],
                isUnlocked: userStats.currentStreak >= 7,
                progress: min(userStats.currentStreak, 7),
                maxProgress: 7,
                story: "每一株参天大树，都始于一颗微小的种子破土而出。这枚徽章象征着你坚持不懈的努力，成功培育了习惯的嫩芽。继续灌溉，它将茁壮成长！",
                unlockedDate: userStats.currentStreak >= 7 ? Date() : nil
            ),
            EAAchievement(
                id: "persistent_drop",
                name: "恒心水滴",
                description: "累计完成习惯50次",
                icon: "💧",
                iconColors: ["#87CEEB", "#4169E1"],
                isUnlocked: userStats.totalCompletions >= 50,
                progress: min(userStats.totalCompletions, 50),
                maxProgress: 50,
                story: "水滴石穿，非一日之功。每一次的坚持都是对目标的承诺，每一滴汗水都在雕琢更好的自己。",
                unlockedDate: userStats.totalCompletions >= 50 ? Date() : nil
            ),
            EAAchievement(
                id: "wisdom_crystal",
                name: "智慧水晶",
                description: "连续完成任意习惯50天",
                icon: "🔮",
                iconColors: ["#9370DB", "#4B0082"],
                isUnlocked: userStats.currentStreak >= 50,
                progress: min(userStats.currentStreak, 50),
                maxProgress: 50,
                story: "智慧如水晶般珍贵，需要时间的沉淀和坚持的打磨。你的毅力已经凝结成这颗闪耀的智慧水晶。",
                unlockedDate: userStats.currentStreak >= 50 ? Date() : nil
            ),
            EAAchievement(
                id: "eternal_flame",
                name: "不息之火",
                description: "累计完成习惯100次",
                icon: "🔥",
                iconColors: ["#FF69B4", "#DC143C"],
                isUnlocked: userStats.totalCompletions >= 100,
                progress: min(userStats.totalCompletions, 100),
                maxProgress: 100,
                story: "内心的火焰永不熄灭，它照亮前行的路，温暖疲惫的心。你已经点燃了这团不息之火。",
                unlockedDate: userStats.totalCompletions >= 100 ? Date() : nil
            ),
            EAAchievement(
                id: "mystery_badge",
                name: "神秘徽章",
                description: "???",
                icon: "❓",
                iconColors: ["#808080", "#696969"],
                isUnlocked: false,
                progress: 0,
                maxProgress: 1,
                story: "这是一个神秘的徽章，它的获得条件还未被发现...",
                unlockedDate: nil
            )
        ]
    }
}

// MARK: - Supporting Data Models
/// 用户统计数据
struct EAUserStats {
    let totalHabits: Int
    let totalCompletions: Int
    let currentStreak: Int
    let completionRate: Double
    let completedHabits: Int // 添加completedHabits属性
    let longestStreak: Int // 添加longestStreak属性
    
    init(totalHabits: Int = 0, totalCompletions: Int = 0, currentStreak: Int = 0, completionRate: Double = 0.0, completedHabits: Int = 0, longestStreak: Int = 0) {
        self.totalHabits = totalHabits
        self.totalCompletions = totalCompletions
        self.currentStreak = currentStreak
        self.completionRate = completionRate
        self.completedHabits = completedHabits
        self.longestStreak = longestStreak
    }
}

/// 成就数据模型
struct EAAchievement: Identifiable, Hashable {
    let id: String
    let name: String
    let description: String
    let icon: String
    let iconColors: [String]
    let isUnlocked: Bool
    let progress: Int
    let maxProgress: Int
    let story: String
    let unlockedDate: Date?
    
    // 计算进度百分比
    var progressPercentage: Double {
        return maxProgress > 0 ? Double(progress) / Double(maxProgress) : 0.0
    }
    
    // 是否完成
    var isCompleted: Bool {
        return progress >= maxProgress
    }
} 