import Foundation
import SwiftData
import SwiftUI
import UserNotifications

// MARK: - Today页面ViewModel
/// Today页面的数据管理和业务逻辑处理
/// 
/// 🔑 核心功能：
/// - 管理今日习惯完成状态
/// - 处理AI消息提示
/// - 生成每日洞察内容
/// - 响应数据变化通知
@MainActor
class EATodayViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    @Published var currentDate: Date = Date()
    @Published var dailyInsight: String = ""
    @Published var completedHabitIds: Set<UUID> = []
    @Published var todayProgress: Double = 0.0
    
    // ✅ 新增：今日习惯数据
    @Published var todayHabits: [EAHabit] = []
    
    // AI消息相关属性
    @Published var hasAIMessages: Bool = false
    @Published var aiMessageCount: Int = 0
    
    // MARK: - Private Properties
    private var repositoryContainer: EARepositoryContainer?
    private var notificationObservers: [NSObjectProtocol] = []
    private let sessionManager: EASessionManager
    
    // ✅ 智能显示逻辑缓存
    /// 每周完成次数缓存，避免重复查询
    private var weeklyCompletionCache: [UUID: Int] = [:]
    /// 每月完成次数缓存，避免重复查询
    private var monthlyCompletionCache: [UUID: Int] = [:]
    
    // MARK: - Computed Properties
    
    /// 当前登录用户（异步获取）
    func getCurrentUser() async -> EAUser? {
        return await sessionManager.safeCurrentUser
    }
    
    /// 今日日期字符串（中文格式）
    var todayDateString: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "M月d日"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: currentDate)
    }
    
    /// 星期字符串（中文格式）
    var weekdayString: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEEE"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: currentDate)
    }
    
    // MARK: - Initialization
    
    /// 初始化Today页面ViewModel
    /// - Parameter sessionManager: 会话管理器，管理用户登录状态
    /// ✅ 修复：强制Repository模式，移除ModelContext依赖
    init(sessionManager: EASessionManager) {
        self.sessionManager = sessionManager
        self.currentDate = Date()
        self.dailyInsight = generateDailyInsight()
        
        // 设置通知监听
        setupNotificationObservers()
    }
    
    deinit {
        // 清理通知观察者
        notificationObservers.forEach { NotificationCenter.default.removeObserver($0) }
    }
    
    // MARK: - Public Methods
    
    /// 设置Repository容器
    /// - Parameter container: Repository容器
    func setRepositoryContainer(_ container: EARepositoryContainer) {
        self.repositoryContainer = container
        Task {
            await refreshCompletionStatus()
        }
    }
    

    
    /// ✅ 修复：过滤用户习惯（包含今日过滤逻辑）
    /// - Parameter allHabits: 所有习惯数据
    /// - Returns: 当前用户今日需要打卡的习惯
    func filterUserHabits(from allHabits: [EAHabit]) async -> [EAHabit] {
        guard let currentUser = await sessionManager.safeCurrentUser else {
            return []
        }
        
        // 🔑 关键修复：在ViewModel中处理关系链查询，避免SwiftData @Query的TERNARY错误
        let userHabits = allHabits.filter { habit in
            guard let habitUserId = habit.user?.id else { return false }
            return habitUserId == currentUser.id
        }
        
        // 🚀 关键修复：筛选当天需要打卡的习惯
        let todayRequiredHabits = filterTodayRequiredHabits(from: userHabits)
        
        return todayRequiredHabits
    }
    
    /// ✅ 重构：智能过滤今日需要执行的习惯
    /// 
    /// 根据不同频率类型和完成情况，智能判断今日是否需要显示该习惯：
    /// - 每日习惯：每天显示
    /// - 每周习惯：在选定星期几显示，或每周X次模式未达目标时显示
    /// - 每月习惯（target模式）：本月未达到目标次数时显示
    /// - 每月习惯（dates模式）：只在指定日期显示
    /// - 一次性习惯：只在计划的具体日期显示
    /// 
    /// - Parameter habits: 用户的所有活跃习惯
    /// - Returns: 今日需要显示的习惯列表
    private func filterTodayRequiredHabits(from habits: [EAHabit]) -> [EAHabit] {
        let calendar = Calendar.current
        let today = Date()
        let todayWeekday = calendar.component(.weekday, from: today)
        let dayOfMonth = calendar.component(.day, from: today)
        
        // 🔑 关键修复：获取今天的日期字符串（yyyy-MM-dd格式）
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let todayDateString = dateFormatter.string(from: today)
        
        return habits.filter { habit in
            guard habit.isActive else { return false }
            
            var shouldShow = false
            
            switch habit.frequencyType {
            case "daily":
                // 每日习惯：每天都显示
                shouldShow = true
                
            case "weekly":
                // ✅ 每周习惯逻辑
                let selectedWeekdays = habit.selectedWeekdays
                if selectedWeekdays.isEmpty {
                    // 每周X次模式：检查本周是否还需要完成
                    shouldShow = checkWeeklyTargetNotMet(habit: habit)
                } else {
                    // 指定星期几模式：只在选定星期几显示
                    shouldShow = selectedWeekdays.contains(todayWeekday)
                }
                
            case "monthly":
                let monthlyMode = habit.monthlyMode
                
                if monthlyMode == "target" {
                    // 每月X次模式：检查本月是否还需要完成
                    shouldShow = checkMonthlyTargetNotMet(habit: habit)
                } else if monthlyMode == "dates" {
                    // 按指定日期模式：只在选定日期显示
                    let selectedDates = habit.selectedMonthlyDates
                    shouldShow = selectedDates.contains(dayOfMonth)
                } else {
                    // 未知模式，默认显示（保守处理）
                    shouldShow = true
                }
                
            case "oneTime":
                // 🔑 关键修复：一次性习惯只在计划的具体日期显示
                let selectedDates = habit.selectedOneTimeDates
                shouldShow = selectedDates.contains(todayDateString)
                
            default:
                // 未知频率类型：默认显示
                shouldShow = true
            }
            
            return shouldShow
        }
    }
    
    /// ✅ 检查每周X次类型习惯是否未达到目标
    /// 
    /// 逻辑说明：
    /// - 每周X次类型的习惯没有指定具体星期几（selectedWeekdays为空）
    /// - 用户可以在一周内的任意天完成，只要达到targetFrequency次数即可
    /// - 如果本周已完成targetFrequency次，则不需要再显示
    /// - 如果本周未达到目标，则每天都应该显示，提醒用户完成
    private func checkWeeklyTargetNotMet(habit: EAHabit) -> Bool {
        guard let repositoryContainer = repositoryContainer else { return true }
        
        // 使用缓存避免重复查询
        if let cachedCount = weeklyCompletionCache[habit.id] {
            return cachedCount < habit.targetFrequency
        }
        
        // 异步查询本周完成次数
        Task {
            do {
                let weeklyCount = try await repositoryContainer.completionRepository.fetchWeeklyCompletions(for: habit.id)
                await MainActor.run {
                    self.weeklyCompletionCache[habit.id] = weeklyCount
                }
            } catch {
                // 查询失败时默认显示，避免遗漏
                await MainActor.run {
                    self.weeklyCompletionCache[habit.id] = 0
                }
            }
        }
        
        // 首次加载时默认显示，避免闪烁
        return true
    }
    
    /// ✅ 检查每月X次类型习惯是否未达到目标
    /// 
    /// 逻辑说明：
    /// - 每月X次类型的习惯（monthlyMode为"target"）没有指定具体日期
    /// - 用户可以在一个月内的任意天完成，只要达到monthlyTarget次数即可
    /// - 如果本月已完成monthlyTarget次，则不需要再显示
    /// - 如果本月未达到目标，则每天都应该显示，提醒用户完成
    private func checkMonthlyTargetNotMet(habit: EAHabit) -> Bool {
        guard let repositoryContainer = repositoryContainer else { return true }
        
        // 使用缓存避免重复查询
        if let cachedCount = monthlyCompletionCache[habit.id] {
            return cachedCount < habit.monthlyTarget
        }
        
        // 异步查询本月完成次数
        Task {
            do {
                let monthlyCount = try await repositoryContainer.completionRepository.fetchMonthlyCompletions(for: habit.id)
                await MainActor.run {
                    self.monthlyCompletionCache[habit.id] = monthlyCount
                }
            } catch {
                // 查询失败时默认显示，避免遗漏
                await MainActor.run {
                    self.monthlyCompletionCache[habit.id] = 0
                }
            }
        }
        
        // 首次加载时默认显示，避免闪烁
        return true
    }
    

    
    /// ✅ 新增：过滤今日完成记录
    /// - Parameters:
    ///   - allCompletions: 所有完成记录
    ///   - date: 目标日期
    /// - Returns: 指定日期的完成记录
    func filterTodayCompletions(from allCompletions: [EACompletion], for date: Date) -> [EACompletion] {
        let calendar = Calendar.current
        return allCompletions.filter { completion in
            calendar.isDate(completion.date, inSameDayAs: date)
        }
    }
    
    /// ✅ 新增：计算已完成习惯数量
    /// - Parameters:
    ///   - habits: 习惯列表
    ///   - completions: 完成记录列表
    /// - Returns: 已完成的习惯数量
    func calculateCompletedCount(habits: [EAHabit], completions: [EACompletion]) -> Int {
        return habits.filter { habit in
            completions.contains(where: { $0.habit?.id == habit.id })
        }.count
    }
    
    /// ✅ 新增：加载今日数据（包括今日习惯）
    func loadTodayData() {
        currentDate = Date()
        dailyInsight = generateDailyInsight()
        Task {
            await loadTodayHabits()
            await refreshCompletionStatus()
        }
    }
    
    /// ✅ 新增：加载今日习惯数据
    private func loadTodayHabits() async {
        guard let repositoryContainer = repositoryContainer,
              let currentUser = await sessionManager.safeCurrentUser else {
            await MainActor.run {
                self.todayHabits = []
            }
            return
        }
        
        // ✅ 清理缓存，确保获取最新数据
        await MainActor.run {
            self.weeklyCompletionCache.removeAll()
            self.monthlyCompletionCache.removeAll()
        }
        
        do {
            // 1. 获取用户的所有活跃习惯
            let allUserHabits = try await repositoryContainer.habitRepository.fetchActiveHabits(for: currentUser.id)
            
            // 2. 过滤出今日需要打卡的习惯
            let filteredHabits = filterTodayRequiredHabits(from: allUserHabits)
            
            await MainActor.run {
                self.todayHabits = filteredHabits
            }
        } catch {
            await MainActor.run {
                self.todayHabits = []
                self.errorMessage = "加载今日习惯失败: \(error.localizedDescription)"
            }
        }
    }
    
    /// 刷新完成状态 - 通过Repository
    /// 
    /// 检查今日习惯完成情况并更新UI状态
    func refreshCompletionStatus() async {
        guard repositoryContainer != nil else {
            await MainActor.run {
                self.completedHabitIds = []
                self.isLoading = false
            }
            return
        }
        
        isLoading = true
        errorMessage = nil
            
        await calculateTodayProgress()
        await checkAIMessageStatus()
        
        isLoading = false
    }
    
    /// ✅ 修复：切换习惯完成状态 - 集成星际能量奖励系统
    /// - Parameter habit: 要切换完成状态的习惯
    /// 
    /// 🔑 功能说明：
    /// - 检查今日是否已完成该习惯
    /// - 若已完成则移除记录并回退能量，若未完成则添加记录并奖励能量
    /// - 通过Repository确保数据一致性
    /// - 集成星际能量奖励系统
    func toggleHabitCompletion(_ habit: EAHabit) {
        guard let repositoryContainer = repositoryContainer else { return }

        Task {
            guard let currentUser = await sessionManager.safeCurrentUser else { return }
            do {
                let isCompleted = completedHabitIds.contains(habit.id)
                
                if isCompleted {
                    // ✅ 取消打卡：删除完成记录并回退能量
                    let todayCompletions = try await repositoryContainer.completionRepository.fetchTodayCompletions(for: currentUser.id)
                    if let completion = todayCompletions.first(where: { $0.habit?.id == habit.id }) {
                        // 检查是否为连续完成
                        let wasConsecutive = await checkIsConsecutiveCompletion(habitId: habit.id, userId: currentUser.id)
                        
                        // 删除完成记录
                        try await repositoryContainer.completionRepository.deleteCompletion(completion)
                        
                        // ✅ 修复：使用EAStellarEnergyService减少能量，确保与增加逻辑一致
                        let energyDeducted = await deductStellarEnergyUsingService(
                            habitId: habit.id,
                            userId: currentUser.id,
                            wasConsecutive: wasConsecutive
                        )
                        
                        await MainActor.run {
                            _ = self.completedHabitIds.remove(habit.id)
                        }
                        
                        // 发送能量减少通知
                        await showEnergyDeductionNotification(energyDeducted: energyDeducted, habitName: habit.name)
                    }
                } else {
                    // ✅ 完成打卡：创建完成记录并奖励能量
                    _ = try await repositoryContainer.completionRepository.createCompletion(for: habit.id, note: nil, energyLevel: 5)
                    
                    // 计算连续完成状态
                    let isConsecutive = await checkIsConsecutiveCompletion(habitId: habit.id, userId: currentUser.id)
                    
                    // 奖励星际能量 - 使用新方法
                    let energyGained = await awardStellarEnergy(
                        habit: habit,
                        user: currentUser,
                        isConsecutive: isConsecutive
                    )
                    
                    await MainActor.run {
                        _ = self.completedHabitIds.insert(habit.id)
                    }
                    
                    // 发送能量奖励通知
                    await showEnergyRewardNotification(energyGained: energyGained, habitName: habit.name)
                }
                
                sendHabitDataChangedNotification(habitId: habit.id)
                
                // ✅ 新增：刷新今日习惯列表，因为完成状态变化可能影响显示逻辑
                // 例如：每周5次的习惯完成5次后应该隐藏
                await loadTodayHabits()
                
            } catch {
                await MainActor.run {
                    self.errorMessage = "操作失败，请重试"
                }
            }
        }
    }
    
    /// 刷新数据（公共方法）
    /// 
    /// 重新加载当前日期和每日洞察，并刷新完成状态
    func refreshData() async {
        currentDate = Date()
        dailyInsight = generateDailyInsight()
        
        // ✅ 清理智能显示逻辑缓存，确保重新计算
        weeklyCompletionCache.removeAll()
        monthlyCompletionCache.removeAll()
        
        await loadTodayHabits()
        await refreshCompletionStatus()
    }
    
    /// 获取习惯连续天数
    /// - Parameter habit: 目标习惯
    /// - Returns: 连续完成天数
    func getHabitStreak(_ habit: EAHabit) -> Int {
        return habit.currentStreak
    }
    
    /// 检查习惯是否已完成（使用缓存状态）
    /// - Parameter habit: 要检查的习惯
    /// - Returns: 是否已完成
    func isHabitCompleted(_ habit: EAHabit) -> Bool {
        return completedHabitIds.contains(habit.id)
    }
    
    /// 导航到Aura灵境空间
    /// 
    /// 发送导航通知，由主应用处理页面切换
    func navigateToAuraSpace() {
        NotificationCenter.default.post(
            name: NSNotification.Name(EAAppConstants.Today.Notifications.navigateToAuraSpace),
            object: nil
        )
    }
    
    /// 清除错误信息
    func clearError() {
        errorMessage = nil
    }
    
    /// 检查AI消息状态
    /// 
    /// 根据系统推送权限状态决定是否显示AI消息提示
    func checkAIMessageStatus() async {
        let notificationCenter = UNUserNotificationCenter.current()
        let settings = await notificationCenter.notificationSettings()
        
        if settings.authorizationStatus != .authorized {
            updateAIMessageStatus()
        } else {
            hasAIMessages = false
            aiMessageCount = 0
        }
    }
    
    // MARK: - Private Methods - 习惯完成状态管理
    
    /// 发送习惯数据变化通知
    /// - Parameter habitId: 发生变化的习惯ID
    private func sendHabitDataChangedNotification(habitId: UUID) {
        NotificationCenter.default.post(
            name: NSNotification.Name(EAAppConstants.Today.Notifications.habitDataChanged),
            object: habitId
        )
    }
    
    /// 获取今日日期范围
    /// - Returns: (今日开始时间, 明日开始时间)
    private func getTodayDateRange() -> (Date, Date) {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today) ?? today
        return (today, tomorrow)
    }
    
    // MARK: - Private Methods - 数据管理
    
    /// 设置通知观察者
    private func setupNotificationObservers() {
        // 监听习惯数据变更通知
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name(EAAppConstants.Today.Notifications.habitDataChanged),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task {
                await self?.loadTodayHabits()
                await self?.refreshCompletionStatus()
            }
        }
        
        // ✅ 关键新增：监听习惯创建通知
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("EAHabitCreated"),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task {
                // 习惯创建后立即重新加载今日习惯列表
                await self?.loadTodayHabits()
                await self?.refreshCompletionStatus()
            }
        }
        
        // 监听会话变更通知
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("EASessionUserChanged"),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task {
                await self?.loadTodayHabits()
                await self?.refreshCompletionStatus()
            }
        }
    }
    
    /// 处理习惯删除
    /// - Parameter habitId: 被删除的习惯ID
    private func handleHabitDeleted(habitId: UUID) {
        completedHabitIds.remove(habitId)
    }
    
    /// 计算今日进度 - 通过Repository
    private func calculateTodayProgress() async {
        guard let repositoryContainer = repositoryContainer,
              let currentUser = await sessionManager.safeCurrentUser else {
            await MainActor.run {
            self.completedHabitIds = []
                self.todayProgress = 0.0
            }
            return 
        }
        
        do {
            // 通过Repository获取今日完成记录
            let todayCompletions = try await repositoryContainer.completionRepository.fetchTodayCompletions(for: currentUser.id)
            
            let completedHabitIds = Set(todayCompletions.compactMap { completion in
                completion.habit?.id
            })
            
            // 获取用户活跃习惯总数来计算进度
            let activeHabits = try await repositoryContainer.habitRepository.fetchActiveHabits(for: currentUser.id)
            
            await MainActor.run {
            self.completedHabitIds = completedHabitIds
                self.todayProgress = activeHabits.isEmpty ? 0.0 : Double(completedHabitIds.count) / Double(activeHabits.count)
            }
            
        } catch {
            await MainActor.run {
                self.completedHabitIds = []
                self.todayProgress = 0.0
            }
        }
    }
    
    /// 更新AI消息状态
    private func updateAIMessageStatus() {
        let shouldShowAIMessage = completedHabitIds.count < 3
        hasAIMessages = shouldShowAIMessage
        aiMessageCount = shouldShowAIMessage ? Int.random(in: 1...3) : 0
    }
    
    /// 生成每日洞察
    /// - Returns: 随机选择的每日洞察文案
    private func generateDailyInsight() -> String {
        let insights = EAAppConstants.Today.Text.dailyInsights
        return insights.randomElement() ?? insights[0]
    }
    
    /// 加载用户历史数据（暂时禁用）
    private func loadCompletionHistory() {
        // 功能暂时禁用，等待Repository实现
    }
    
    // MARK: - ✅ 新增：星际能量奖励系统集成
    
    /// ✅ 新方法：奖励星际能量（使用完整对象）
    /// - Parameters:
    ///   - habit: 习惯对象
    ///   - user: 用户对象
    ///   - isConsecutive: 是否连续完成
    /// - Returns: 获得的能量值
    private func awardStellarEnergy(habit: EAHabit, user: EAUser, isConsecutive: Bool) async -> Int {
        // 创建星际能量服务实例
        guard let repositoryContainer = repositoryContainer else { return 0 }
        
        let cacheManager = EAAICacheManager()
        let energyService = EAStellarEnergyService(
            repositoryContainer: repositoryContainer,
            cacheManager: cacheManager
        )
        
        // 计算并奖励星际能量
        let energyGained = await energyService.calculateHabitCompletionEnergy(
            for: habit,
            user: user,
            isConsecutive: isConsecutive
        )
        
        return energyGained
    }
    
    /// @deprecated 使用接收完整对象的方法替代
    /// 奖励星际能量
    /// - Parameters:
    ///   - habitId: 习惯ID
    ///   - userId: 用户ID
    ///   - isConsecutive: 是否连续完成
    /// - Returns: 获得的能量值
    @available(*, deprecated, message: "Use awardStellarEnergy(habit:user:isConsecutive:) instead")
    private func awardStellarEnergy(habitId: UUID, userId: UUID, isConsecutive: Bool) async -> Int {
        // 创建星际能量服务实例
        guard let repositoryContainer = repositoryContainer else { return 0 }
        
        let cacheManager = EAAICacheManager()
        let energyService = EAStellarEnergyService(
            repositoryContainer: repositoryContainer,
            cacheManager: cacheManager
        )
        
        // 计算并奖励星际能量
        let energyGained = await energyService.calculateHabitCompletionEnergy(
            habitId: habitId,
            userId: userId,
            isConsecutive: isConsecutive
        )
        
        return energyGained
    }
    
    /// ✅ 新增：使用EAStellarEnergyService减少能量（确保与增加逻辑一致）
    /// - Parameters:
    ///   - habitId: 习惯ID
    ///   - userId: 用户ID
    ///   - wasConsecutive: 之前是否为连续完成
    /// - Returns: 减少的能量值
    private func deductStellarEnergyUsingService(habitId: UUID, userId: UUID, wasConsecutive: Bool) async -> Int {
        // 创建星际能量服务实例
        guard let repositoryContainer = repositoryContainer else { return 0 }
        
        let energyService = EAStellarEnergyService(repositoryContainer: repositoryContainer)
        
        // 使用服务减少星际能量，确保与增加逻辑完全一致
        let energyDeducted = await energyService.deductHabitCompletionEnergy(
            habitId: habitId,
            userId: userId,
            wasConsecutive: wasConsecutive
        )
        
        return energyDeducted
    }
    
    /// ✅ 新增：检查是否为连续完成
    /// - Parameters:
    ///   - habitId: 习惯ID
    ///   - userId: 用户ID
    /// - Returns: 是否为连续完成
    private func checkIsConsecutiveCompletion(habitId: UUID, userId: UUID) async -> Bool {
        guard let repositoryContainer = repositoryContainer else { return false }
        
        // 获取最近的完成记录
        let recentCompletions = (try? await repositoryContainer.completionRepository.fetchRecentCompletions(
            userId: userId,
            days: 2
        )) ?? []
        
        // 过滤出指定习惯的完成记录
        let habitCompletions = recentCompletions.filter { $0.habit?.id == habitId }
        
        // 如果昨天也有完成记录，则认为是连续完成
        let yesterday = Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date()
        let yesterdayCompletions = habitCompletions.filter { completion in
            Calendar.current.isDate(completion.date, inSameDayAs: yesterday)
        }
        
        return !yesterdayCompletions.isEmpty
    }
    

    
    /// 计算当前连击天数
    /// - Parameter habitId: 习惯ID
    /// - Returns: 连击天数
    private func calculateCurrentStreak(habitId: UUID) async -> Int {
        guard let repositoryContainer = repositoryContainer,
              let currentUser = await sessionManager.safeCurrentUser else { return 0 }
        
        do {
            let recentCompletions = try await repositoryContainer.completionRepository.fetchRecentCompletions(userId: currentUser.id, days: 365)
            let habitCompletions = recentCompletions.filter { $0.habit?.id == habitId }
            
            let calendar = Calendar.current
            let today = Date()
            var streak = 0
            
            // 从今天开始往前计算连击
            for day in 0..<365 {
                let checkDate = calendar.date(byAdding: .day, value: -day, to: today)!
                let dayStart = calendar.startOfDay(for: checkDate)
                let dayEnd = calendar.date(byAdding: .day, value: 1, to: dayStart)!
                
                let hasCompletion = habitCompletions.contains { completion in
                    completion.date >= dayStart && completion.date < dayEnd
                }
                
                if hasCompletion {
                    streak += 1
                } else {
                    break
                }
            }
            
            return streak
            
        } catch {
            return 0
        }
    }
    
    /// 计算连击奖励
    /// - Parameter streak: 连击天数
    /// - Returns: 奖励能量
    private func calculateStreakBonus(streak: Int) -> Int {
        switch streak {
        case 3...6:
            return 5
        case 7...13:
            return 10
        case 14...29:
            return 20
        case 30...:
            return 50
        default:
            return 0
        }
    }
    
    /// 显示能量奖励通知
    /// - Parameters:
    ///   - energyGained: 获得的能量
    ///   - habitName: 习惯名称
    private func showEnergyRewardNotification(energyGained: Int, habitName: String) async {
        await MainActor.run {
            // 这里可以显示一个临时的能量奖励提示
            // 可以通过NotificationCenter发送通知给UI层显示
            NotificationCenter.default.post(
                name: NSNotification.Name("EAStellarEnergyGained"),
                object: nil,
                userInfo: [
                    "energyGained": energyGained,
                    "habitName": habitName,
                    "message": "完成「\(habitName)」获得 \(energyGained) 星际能量！"
                ]
            )
        }
    }
    
    /// ✅ 新增：显示能量减少通知
    /// - Parameters:
    ///   - energyDeducted: 减少的能量
    ///   - habitName: 习惯名称
    private func showEnergyDeductionNotification(energyDeducted: Int, habitName: String) async {
        await MainActor.run {
            NotificationCenter.default.post(
                name: NSNotification.Name("EAStellarEnergyDeducted"),
                object: nil,
                userInfo: [
                    "energyDeducted": energyDeducted,
                    "habitName": habitName,
                    "message": "取消「\(habitName)」减少 \(energyDeducted) 星际能量"
                ]
            )
        }
    }
}

// MARK: - Preview Support
extension EATodayViewModel {
    /// 预览用的ViewModel实例
    static var preview: EATodayViewModel {
        // ✅ 修复：使用临时SessionManager创建预览实例
        let viewModel = EATodayViewModel(sessionManager: EASessionManager())
        
        // 模拟数据
        viewModel.completedHabitIds = [UUID()]
        viewModel.dailyInsight = "每一个微小的进步，都是生态系统的能量流转"
        
        return viewModel
    }
} 