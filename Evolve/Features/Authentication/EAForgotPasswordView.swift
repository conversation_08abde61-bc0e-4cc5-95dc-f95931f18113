import SwiftUI

// MARK: - 忘记密码页面

struct EAForgotPasswordView: View {
    @EnvironmentObject var authViewModel: EAAuthViewModel
    
    // 流程状态枚举
    enum ResetStep {
        case enterPhone      // 输入手机号
        case enterCode       // 输入验证码
        case setNewPassword  // 设置新密码
    }
    
    // 当前步骤
    @State private var currentStep: ResetStep = .enterPhone
    
    // 表单数据
    @State private var phoneNumber = ""
    @State private var verificationCode = ""
    @State private var newPassword = ""
    @State private var confirmPassword = ""
    
    // 状态管理
    @State private var isLoading = false
    @State private var showError = false
    @State private var errorMessage = ""
    
    // 验证码倒计时
    @State private var countdown = 0
    @State private var timer: Timer?
    
    // 验证状态
    @State private var phoneError: String? = nil
    @State private var codeError: String? = nil
    @State private var passwordError: String? = nil
    @State private var confirmPasswordError: String? = nil
    
    // 导航控制
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationStack {
            ZStack {
                // 背景
                EABackgroundView(style: .authentication, showParticles: true)
                
                // 内容
                GeometryReader { geometry in
                    ScrollView {
                        VStack(spacing: 0) {
                            // 确保内容可以垂直居中
                            Spacer()
                                .frame(minHeight: 80)
                            
                            // 忘记密码表单容器
                            forgotPasswordFormContainer
                                .frame(minHeight: geometry.size.height - 160)
                            
                            Spacer()
                                .frame(minHeight: 80)
                        }
                    }
                    .scrollIndicators(.hidden)
                }
                
                // 返回按钮
                VStack {
                    HStack {
                        Button {
                            if currentStep == .enterPhone {
                                dismiss()
                            } else {
                                // 返回上一步
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    switch currentStep {
                                    case .enterCode:
                                        currentStep = .enterPhone
                                        stopCountdown()
                                    case .setNewPassword:
                                        currentStep = .enterCode
                                    case .enterPhone:
                                        break
                                    }
                                }
                            }
                        } label: {
                            HStack(spacing: 4) {
                            Image(systemName: "chevron.left")
                                    .font(.system(size: 16, weight: .medium))
                                Text("返回")
                                    .font(.system(size: 16, weight: .medium))
                            }
                                .foregroundColor(Color.hexColor("40E0D0"))
                            .frame(height: 44)
                        }
                        .padding(.leading, 20)
                        .padding(.top, 20)
                        
                        Spacer()
                    }
                    
                    Spacer()
                }
            }
            .preferredColorScheme(.dark)
            .navigationBarHidden(true)
        }
        .onDisappear {
            stopCountdown()
        }
    }
    
    // MARK: - 忘记密码表单容器
    
    @ViewBuilder
    private var forgotPasswordFormContainer: some View {
        VStack(spacing: 32) {
            // 进度指示器
            progressIndicator
            
            // 标题区域
            titleSection
            
            // 主要内容区域
            switch currentStep {
            case .enterPhone:
                phoneInputSection
            case .enterCode:
                codeInputSection
            case .setNewPassword:
                passwordInputSection
            }
        }
        .padding(.horizontal, 28)
    }
    
    // MARK: - 进度指示器
    
    @ViewBuilder
    private var progressIndicator: some View {
        HStack(spacing: 8) {
            ForEach(0..<3) { index in
                Circle()
                    .fill(index <= currentStep.rawValue ? Color.hexColor("40E0D0") : Color.white.opacity(0.3))
                    .frame(width: 8, height: 8)
                    .animation(.easeInOut(duration: 0.3), value: currentStep)
            }
        }
        .padding(.bottom, 16)
    }
    
    // MARK: - 标题区域
    
    @ViewBuilder
    private var titleSection: some View {
        VStack(spacing: 16) {
            // 图标
            Image(systemName: stepIcon)
                .font(.system(size: 64, weight: .light))
                .foregroundColor(Color.hexColor("40E0D0"))
                .padding(.bottom, 8)
            
            Text(stepTitle)
                .font(.system(size: 32, weight: .bold, design: .rounded))
                .foregroundColor(Color.hexColor("e0f2fe"))
                .multilineTextAlignment(.center)
            
            Text(stepSubtitle)
                .font(.body)
                .foregroundColor(Color.hexColor("bae6fd").opacity(0.9))
                .multilineTextAlignment(.center)
                .padding(.horizontal, 20)
        }
    }
    
    // MARK: - 手机号输入区域
    
    @ViewBuilder
    private var phoneInputSection: some View {
        VStack(spacing: 24) {
            // 手机号码输入框
            EATextField(
                text: $phoneNumber,
                placeholder: "手机号码",
                type: .phone,
                errorMessage: phoneError,
                leftIcon: "phone",
                validator: EATextField.phoneValidator
            )
            
            // 发送验证码按钮
            EAButton(
                title: "发送验证码",
                style: .primary,
                size: .large,
                isEnabled: !phoneNumber.isEmpty && EATextField.phoneValidator(phoneNumber),
                isLoading: isLoading
            ) {
                handleSendVerificationCode()
            }
            
            // 返回登录链接
            Button {
                dismiss()
            } label: {
                Text("返回登录")
                    .font(.callout)
                    .foregroundColor(Color.hexColor("67e8f9"))
            }
            .padding(.top, 16)
        }
    }
    
    // MARK: - 验证码输入区域
    
    @ViewBuilder
    private var codeInputSection: some View {
        VStack(spacing: 24) {
            // 显示发送到的手机号
            Text("验证码已发送至 \(formatPhoneNumber(phoneNumber))")
                .font(.callout)
                .foregroundColor(Color.primary.opacity(0.8))
                .padding(.bottom, 8)
            
            // 验证码输入框
            EATextField(
                text: $verificationCode,
                placeholder: "6位验证码",
                type: .number,
                errorMessage: codeError,
                leftIcon: "number",
                validator: { code in code.count == 6 && code.allSatisfy { $0.isNumber } }
            )
            
            // 重新发送验证码
            HStack {
                Spacer()
                Button {
                    if countdown == 0 {
                        handleSendVerificationCode()
                    }
                } label: {
                    Text(countdown > 0 ? "重新发送(\(countdown)s)" : "重新发送验证码")
                        .font(.callout)
                        .foregroundColor(countdown > 0 ? Color.primary.opacity(0.5) : Color.hexColor("67e8f9"))
                }
                .disabled(countdown > 0)
            }
            
            // 下一步按钮
            EAButton(
                title: "下一步",
                style: .primary,
                size: .large,
                isEnabled: verificationCode.count == 6,
                isLoading: isLoading
            ) {
                handleVerifyCode()
            }
        }
    }
    
    // MARK: - 新密码设置区域
    
    @ViewBuilder
    private var passwordInputSection: some View {
        VStack(spacing: 24) {
            // 新密码输入框
            EATextField(
                text: $newPassword,
                placeholder: "设置新密码",
                type: .password,
                errorMessage: passwordError,
                helperText: "至少6位，包含字母和数字",
                leftIcon: "lock",
                validator: EATextField.passwordValidator
            )
            
            // 确认密码输入框
            EATextField(
                text: $confirmPassword,
                placeholder: "确认新密码",
                type: .password,
                errorMessage: confirmPasswordError,
                leftIcon: "lock",
                validator: { _ in newPassword == confirmPassword && !newPassword.isEmpty }
            )
            
            // 完成重置按钮
            EAButton(
                title: "完成重置",
                style: .primary,
                size: .large,
                isEnabled: !newPassword.isEmpty && newPassword == confirmPassword && EATextField.passwordValidator(newPassword),
                isLoading: isLoading
            ) {
                handleResetPassword()
            }
        }
    }
    
    // MARK: - 计算属性
    
    private var stepIcon: String {
        switch currentStep {
        case .enterPhone: return "phone.circle"
        case .enterCode: return "envelope.circle"
        case .setNewPassword: return "lock.circle"
        }
    }
    
    private var stepTitle: String {
        switch currentStep {
        case .enterPhone: return "重置密码"
        case .enterCode: return "输入验证码"
        case .setNewPassword: return "设置新密码"
        }
    }
    
    private var stepSubtitle: String {
        switch currentStep {
        case .enterPhone: return "输入您的手机号码，我们将发送验证码"
        case .enterCode: return "请输入收到的6位验证码"
        case .setNewPassword: return "请设置您的新密码"
        }
    }
    
    // MARK: - 业务逻辑方法
    
    private func handleSendVerificationCode() {
        // 验证手机号
        guard EATextField.phoneValidator(phoneNumber) else {
            phoneError = "请输入有效的手机号"
            return
        }
        
        clearErrors()
        isLoading = true
        
        // 模拟发送验证码
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            isLoading = false
            
            // 模拟发送成功
            withAnimation(.easeInOut(duration: 0.3)) {
                currentStep = .enterCode
            }
            startCountdown()
        }
    }
    
    private func handleVerifyCode() {
        // 验证验证码
        guard verificationCode.count == 6 else {
            codeError = "请输入6位验证码"
            return
        }
        
        clearErrors()
        isLoading = true
        
        // 模拟验证码验证
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            isLoading = false
            
            // 模拟验证成功（验证码为"123456"时成功）
            if verificationCode == "123456" {
                withAnimation(.easeInOut(duration: 0.3)) {
                    currentStep = .setNewPassword
                }
                stopCountdown()
            } else {
                codeError = "验证码错误，请重新输入"
            }
        }
    }
    
    private func handleResetPassword() {
        // 验证密码
        guard EATextField.passwordValidator(newPassword) else {
            passwordError = "密码至少6位，需包含字母和数字"
            return
        }
        
        guard newPassword == confirmPassword else {
            confirmPasswordError = "两次输入的密码不一致"
            return
        }
        
        clearErrors()
        isLoading = true
        
        // 模拟密码重置
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            isLoading = false
            
            // 显示成功提示并返回登录页
            showSuccessAndDismiss()
        }
    }
    
    private func showSuccessAndDismiss() {
        // 这里可以显示一个成功提示
        // 然后返回登录页面
        dismiss()
    }
    
    private func startCountdown() {
        countdown = 60
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            if countdown > 0 {
                countdown -= 1
            } else {
                stopCountdown()
            }
        }
    }
    
    private func stopCountdown() {
        timer?.invalidate()
        timer = nil
        countdown = 0
    }
    
    private func clearErrors() {
        phoneError = nil
        codeError = nil
        passwordError = nil
        confirmPasswordError = nil
    }
    
    private func formatPhoneNumber(_ phone: String) -> String {
        guard phone.count >= 7 else { return phone }
        let start = phone.prefix(3)
        let end = phone.suffix(4)
        return "\(start)****\(end)"
    }
}

// MARK: - 扩展：ResetStep的rawValue

extension EAForgotPasswordView.ResetStep: CaseIterable {
    var rawValue: Int {
        switch self {
        case .enterPhone: return 0
        case .enterCode: return 1
        case .setNewPassword: return 2
        }
    }
}

// MARK: - 预览

#Preview("Forgot Password - Enter Phone") {
    let authViewModel = EAAuthViewModel()
    EAForgotPasswordView()
        .environmentObject(authViewModel)
}

#Preview("Forgot Password - Enter Code") {
    let authViewModel = EAAuthViewModel()
    EAForgotPasswordView()
        .environmentObject(authViewModel)
        .onAppear {
            // 预览验证码输入状态
        }
} 