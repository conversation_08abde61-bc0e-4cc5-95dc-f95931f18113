import SwiftUI

// MARK: - 注册页面

struct EARegistrationView: View {
    // 使用ViewModel替代本地状态
    @EnvironmentObject var authViewModel: EAAuthViewModel

    // 导航状态
    @State private var showLogin = false

    // 🔑 修复：统一焦点管理
    @FocusState private var focusedField: RegistrationField?

    // 焦点字段枚举
    private enum RegistrationField: CaseIterable {
        case phone, password, confirmPassword
    }
    
    var body: some View {
        NavigationStack {
            ZStack {
                // 背景
                EABackgroundView(style: .authentication, showParticles: true)
                
                // 内容
                GeometryReader { geometry in
                    ScrollView {
                        VStack(spacing: 0) {
                            // 确保内容可以垂直居中，即使在小屏幕上也能滚动
                            Spacer()
                                .frame(minHeight: 40) // 较小的顶部间距
                            
                            // 注册表单容器
                            registrationFormContainer
                                .frame(minHeight: geometry.size.height - 80) // 减去最小间距
                            
                            Spacer()
                                .frame(minHeight: 40) // 较小的底部间距
                        }
                    }
                    .scrollIndicators(.hidden)
                }
            }
            .preferredColorScheme(.dark)
            .navigationBarHidden(true)
            // 导航目标
            .navigationDestination(isPresented: $showLogin) {
                EALoginView()
            }
        }
        // 错误和成功消息提示
        .alert("提示", isPresented: .constant(authViewModel.errorMessage != nil || authViewModel.successMessage != nil)) {
            Button("确定") {
                authViewModel.clearErrors()
            }
        } message: {
            Text(authViewModel.errorMessage ?? authViewModel.successMessage ?? "")
        }
    }
    
    // MARK: - 注册表单容器
    
    @ViewBuilder
    private var registrationFormContainer: some View {
        VStack(spacing: 24) {
            // 标题区域
            titleSection
            
            // 表单区域
            formSection
            
            // 分割线和社交注册
            socialRegistrationSection
            
            // 登录链接
            signInSection
        }
        .padding(.horizontal, 24)
    }
    
    // MARK: - 标题区域
    
    @ViewBuilder
    private var titleSection: some View {
        VStack(spacing: 16) {
            Text("加入 Evolve")
                .font(.system(size: 36, weight: .bold, design: .rounded))
                .foregroundColor(Color.hexColor("e0f2fe")) // 浅天蓝
                .multilineTextAlignment(.center)
            
            Text("开启您的 AI 心境成长之旅")
                .font(.body)
                .foregroundColor(Color.hexColor("bae6fd").opacity(0.9)) // 天蓝 300
                .multilineTextAlignment(.center)
        }
    }
    
    // MARK: - 表单区域
    
    @ViewBuilder
    private var formSection: some View {
        VStack(spacing: 16) {
            // 手机号输入框
            EATextField(
                text: $authViewModel.registrationPhoneNumber,
                placeholder: "手机号",
                type: .phone,
                errorMessage: authViewModel.registrationPhoneError,
                leftIcon: "phone",
                validator: EATextField.phoneValidator
            )
            .id("ea_reg_phone_v1") // 🔑 修复：版本化视图标识
            .focused($focusedField, equals: .phone) // 🔑 修复：统一焦点管理
            .onSubmit {
                focusedField = .password // 🔑 修复：Return键焦点流转
            }
            .onChange(of: authViewModel.registrationPhoneNumber) { _, _ in
                authViewModel.registrationPhoneError = nil // 清除错误状态
            }
            
            // 密码输入框
            EATextField(
                text: $authViewModel.registrationPassword,
                placeholder: "设置密码",
                type: .password,
                errorMessage: authViewModel.registrationPasswordError,
                helperText: "至少6位，包含字母和数字",
                leftIcon: "lock",
                validator: EATextField.passwordValidator
            )
            .id("ea_reg_password_v1") // 🔑 修复：版本化视图标识
            .focused($focusedField, equals: .password) // 🔑 修复：统一焦点管理
            .onSubmit {
                focusedField = .confirmPassword // 🔑 修复：Return键焦点流转
            }
            
            // 确认密码输入框
            EATextField(
                text: $authViewModel.registrationConfirmPassword,
                placeholder: "确认密码",
                type: .confirmPassword,  // 🔑 修复：使用专门的确认密码类型
                errorMessage: authViewModel.registrationConfirmPasswordError,
                leftIcon: "lock",
                validator: { _ in authViewModel.registrationPassword == authViewModel.registrationConfirmPassword && !authViewModel.registrationPassword.isEmpty }
            )
            .id("ea_reg_confirm_pwd_v1") // 🔑 修复：版本化视图标识
            .focused($focusedField, equals: .confirmPassword) // 🔑 修复：统一焦点管理
            .onSubmit {
                focusedField = nil // 🔑 修复：最后一个字段完成后隐藏键盘
                if authViewModel.registrationAgreedToTerms {
                    Task {
                        await authViewModel.register() // 🔑 修复：Return键直接注册
                    }
                }
            }
            
            // 用户协议勾选
            termsAgreementSection
                .padding(.top, 4)
            
            // 注册按钮
            EAButton(
                title: "立即注册",
                style: .primary,
                size: .large,
                isEnabled: authViewModel.registrationAgreedToTerms,
                isLoading: authViewModel.isLoading
            ) {
                Task {
                    await authViewModel.register()
                }
            }
            .padding(.top, 4)
        }
    }
    
    // MARK: - 用户协议区域
    
    @ViewBuilder
    private var termsAgreementSection: some View {
        VStack(alignment: .leading, spacing: 6) {
            HStack(alignment: .top, spacing: 10) {
                Button {
                    authViewModel.registrationAgreedToTerms.toggle()
                } label: {
                    Image(systemName: authViewModel.registrationAgreedToTerms ? "checkmark.square.fill" : "square")
                        .font(.system(size: 16))
                        .foregroundColor(authViewModel.registrationAgreedToTerms ? Color.hexColor("40E0D0") : Color.primary.opacity(0.6))
                }
                
                // 使用自动换行的HStack，优先尝试一行显示
                VStack(alignment: .leading, spacing: 2) {
                    // 使用HStack + Spacer让内容自然换行
                    HStack(spacing: 2) {
                        Text("我已阅读并同意")
                            .font(.caption)
                            .foregroundColor(Color.primary.opacity(0.8))
                        
                        Button {
                            // 显示用户协议
                            // 显示用户协议
                        } label: {
                            Text("《用户协议》")
                                .font(.caption)
                                .foregroundColor(Color.hexColor("67e8f9"))
                                .underline()
                        }
                        
                        Text("和")
                            .font(.caption)
                            .foregroundColor(Color.primary.opacity(0.8))
                        
                        Button {
                            // 显示隐私政策
                            // 显示隐私政策
                        } label: {
                            Text("《隐私政策》")
                                .font(.caption)
                                .foregroundColor(Color.hexColor("67e8f9"))
                                .underline()
                        }
                        
                        Spacer() // 允许内容自然换行
                    }
                    .lineLimit(nil) // 允许多行显示
                    .fixedSize(horizontal: false, vertical: true) // 允许垂直扩展
                }
                
                Spacer()
            }
            
            // 协议错误提示
            if let termsError = authViewModel.registrationTermsError {
                Text(termsError)
                    .font(.caption2)
                    .foregroundColor(Color.red)
                    .padding(.leading, 26)
            }
        }
    }
    
    // MARK: - 社交注册区域
    
    @ViewBuilder
    private var socialRegistrationSection: some View {
        VStack(spacing: 20) {
            // 分割线
            HStack {
                Rectangle()
                    .frame(height: 1)
                    .foregroundColor(Color.white.opacity(0.15))
                
                Text("或通过以下方式注册")
                    .font(.caption)
                    .foregroundColor(Color.primary.opacity(0.6))
                    .padding(.horizontal, 12)
                
                Rectangle()
                    .frame(height: 1)
                    .foregroundColor(Color.white.opacity(0.15))
            }
            
            // 社交注册按钮
            HStack(spacing: 16) {
                EASocialButton(provider: .apple) {
                    Task {
                        await authViewModel.socialLogin(provider: .apple)
                    }
                }
                
                EASocialButton(provider: .wechat) {
                    Task {
                        await authViewModel.socialLogin(provider: .wechat)
                    }
                }
                
                EASocialButton(provider: .phone) {
                    Task {
                        await authViewModel.socialLogin(provider: .phone)
                    }
                }
            }
        }
    }
    
    // MARK: - 登录链接区域
    
    @ViewBuilder
    private var signInSection: some View {
        HStack(spacing: 4) {
            Text("已有账户？")
                .font(.callout)
                .foregroundColor(Color.primary.opacity(0.6))
            
            Button {
                showLogin = true
            } label: {
                Text("立即登录")
                    .font(.callout)
                    .fontWeight(.semibold)
                    .foregroundColor(Color.hexColor("67e8f9")) // 天蓝
            }
        }
    }
}

// MARK: - 预览

struct EARegistrationView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // 交互式预览 - 专门用于测试键盘输入
            EARegistrationInteractivePreview()
                .previewDisplayName("Registration - Interactive")
            
            // 标准预览
            EARegistrationView()
                .previewDisplayName("Registration View")
                .previewDevice(PreviewDevice(rawValue: "iPhone 15 Pro"))
            
            // iPhone SE 预览
            EARegistrationView()
                .previewDisplayName("Registration View - iPhone SE")
                .previewDevice(PreviewDevice(rawValue: "iPhone SE (3rd generation)"))
        }
        .environmentObject(EAAuthViewModel())
    }
}

// 专门用于测试键盘输入的交互式预览
private struct EARegistrationInteractivePreview: View {
    @StateObject private var viewModel = EAAuthViewModel()
    
    var body: some View {
        NavigationView {
            EARegistrationView()
                .environmentObject(viewModel)
        }
        .previewDisplayName("Registration - Keyboard Test")
    }
} 