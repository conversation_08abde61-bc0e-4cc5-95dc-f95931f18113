import SwiftUI

/// 导航类型枚举 - 统一管理所有导航状态
enum EALoginNavigationType: Identifiable {
    case forgotPassword
    case registration
    
    var id: String {
        switch self {
        case .forgotPassword: return "forgotPassword"
        case .registration: return "registration"
        }
    }
}

// MARK: - 登录页面
/// ✅ 修复：实现统一导航状态管理，符合.cursorrules规范
struct EALoginView: View {
    // ✅ 修复：通过Environment获取authViewModel，而非直接创建
    @EnvironmentObject private var authViewModel: EAAuthViewModel
    
    // ✅ 修复：统一导航状态管理
    @State private var activeNavigation: EALoginNavigationType?
    
    var body: some View {
        NavigationStack {
            ZStack {
                // 背景
                EABackgroundView(style: .authentication, showParticles: true)
                
                // 内容
                GeometryReader { geometry in
                    ScrollView {
                        VStack(spacing: 0) {
                            // 确保内容可以垂直居中，即使在小屏幕上也能滚动
                            Spacer()
                                .frame(minHeight: 60) // 最小顶部间距
                            
                            // 登录表单容器
                            loginFormContainer
                                .frame(minHeight: geometry.size.height - 120) // 减去最小间距
                            
                            Spacer()
                                .frame(minHeight: 60) // 最小底部间距
                        }
                    }
                    .scrollIndicators(.hidden)
                }
            }
            .preferredColorScheme(.dark)
            .navigationBarHidden(true)
            // 导航目标
            .navigationDestination(item: $activeNavigation) { navigationType in
                switch navigationType {
                case .forgotPassword:
                    EAForgotPasswordView()
                        .environmentObject(authViewModel)
                case .registration:
                    EARegistrationView()
                        .environmentObject(authViewModel)
                }
            }
        }
        // 错误消息提示（只在有错误时弹窗）
        .alert("提示", isPresented: .constant(authViewModel.errorMessage != nil)) {
            Button("确定") {
                authViewModel.clearErrors()
            }
        } message: {
            Text(authViewModel.errorMessage ?? "")
        }
    }
    
    // MARK: - 登录表单容器
    
    @ViewBuilder
    private var loginFormContainer: some View {
        VStack(spacing: 32) {
            // 标题区域
            titleSection
            
            // 表单区域
            formSection
            
            // 分割线和社交登录
            socialLoginSection
            
            // 注册链接
            signUpSection
        }
        .padding(.horizontal, 28)
    }
    
    // MARK: - 标题区域
    
    @ViewBuilder
    private var titleSection: some View {
        VStack(spacing: 16) {
            Text("欢迎回来")
                .font(.system(size: 36, weight: .bold, design: .rounded))
                .foregroundColor(Color.hexColor("e0f2fe")) // 浅天蓝
                .multilineTextAlignment(.center)
            
            Text("登录您的 Evolve AI 心境账户")
                .font(.body)
                .foregroundColor(Color.hexColor("bae6fd").opacity(0.9)) // 天蓝 300
                .multilineTextAlignment(.center)
        }
    }
    
    // MARK: - 表单区域
    
    @ViewBuilder
    private var formSection: some View {
        VStack(spacing: 20) {
            // 手机号输入框
            EATextField(
                text: $authViewModel.loginPhoneNumber,
                placeholder: "手机号",
                type: .phone,
                isRequired: true,
                errorMessage: authViewModel.loginPhoneError,
                leftIcon: "phone",
                validator: EATextField.phoneValidator
            )
            
            // 密码输入框
            EATextField(
                text: $authViewModel.loginPassword,
                placeholder: "密码",
                type: .password,
                isRequired: true,
                errorMessage: authViewModel.loginPasswordError,
                leftIcon: "lock"
            )
            
            // 忘记密码链接
            HStack {
                Spacer()
                Button {
                    activeNavigation = .forgotPassword
                } label: {
                    Text("忘记密码？")
                        .font(.callout)
                        .foregroundColor(Color.hexColor("67e8f9")) // 天蓝
                }
            }
            .padding(.top, 4)
            
            // 登录按钮
            EAButton(
                title: "登录",
                style: .primary,
                size: .large,
                isLoading: authViewModel.isLoading
            ) {
                Task {
                    await authViewModel.login()
                }
            }
            .padding(.top, 8)
        }
    }
    
    // MARK: - 社交登录区域
    
    @ViewBuilder
    private var socialLoginSection: some View {
        VStack(spacing: 24) {
            // 分割线
            HStack {
                Rectangle()
                    .frame(height: 1)
                    .foregroundColor(Color.white.opacity(0.15))
                
                Text("或通过以下方式登录")
                    .font(.caption)
                    .foregroundColor(Color.primary.opacity(0.6))
                    .padding(.horizontal, 16)
                
                Rectangle()
                    .frame(height: 1)
                    .foregroundColor(Color.white.opacity(0.15))
            }
            
            // 社交登录按钮
            HStack(spacing: 20) {
                EASocialButton(provider: .apple) {
                    Task {
                        await authViewModel.socialLogin(provider: .apple)
                    }
                }
                
                EASocialButton(provider: .wechat) {
                    Task {
                        await authViewModel.socialLogin(provider: .wechat)
                    }
                }
                
                EASocialButton(provider: .phone) {
                    Task {
                        await authViewModel.socialLogin(provider: .phone)
                    }
                }
            }
        }
    }
    
    // MARK: - 注册链接区域
    
    @ViewBuilder
    private var signUpSection: some View {
        HStack(spacing: 4) {
            Text("还没有账户？")
                .font(.callout)
                .foregroundColor(Color.primary.opacity(0.6))
            
            Button {
                activeNavigation = .registration
            } label: {
                Text("立即注册")
                    .font(.callout)
                    .fontWeight(.semibold)
                    .foregroundColor(Color.hexColor("67e8f9")) // 天蓝
            }
        }
    }
}

// MARK: - 预览

struct EALoginView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // 交互式预览 - 专门用于测试键盘输入
            EALoginInteractivePreview()
                .previewDisplayName("Login - Interactive")
            
            // 标准预览
            EALoginView()
                .previewDisplayName("Login View")
                .previewDevice(PreviewDevice(rawValue: "iPhone 15 Pro"))
            
            // iPhone SE 预览
            EALoginView()
                .previewDisplayName("Login View - iPhone SE")
                .previewDevice(PreviewDevice(rawValue: "iPhone SE (3rd generation)"))
        }
        .environmentObject(EAAuthViewModel())
    }
}

// 专门用于测试键盘输入的交互式预览
private struct EALoginInteractivePreview: View {
    @StateObject private var viewModel = EAAuthViewModel()
    
    var body: some View {
        NavigationView {
            EALoginView()
                .environmentObject(viewModel)
        }
        .previewDisplayName("Login - Keyboard Test")
    }
} 