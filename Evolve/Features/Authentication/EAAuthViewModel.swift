import SwiftUI
import Foundation
import SwiftData
import Combine

// MARK: - 认证错误枚举

enum EAAuthError: LocalizedError {
    case invalidCredentials
    case userNotFound
    case phoneAlreadyExists
    case emailAlreadyExists
    case weakPassword
    case networkError
    case serverError
    case phoneNotVerified
    case emailNotVerified
    case invalidPhoneNumber
    case passwordMismatch
    case termsNotAgreed
    case unknown
    case dataContextUnavailable
    
    var errorDescription: String? {
        switch self {
        case .invalidCredentials:
            return "手机号或密码错误"
        case .userNotFound:
            return "用户不存在"
        case .phoneAlreadyExists:
            return "该手机号已被注册"
        case .emailAlreadyExists:
            return "该邮箱已被注册"
        case .weakPassword:
            return "密码强度不够，请设置更复杂的密码"
        case .networkError:
            return "网络连接失败，请检查网络设置"
        case .serverError:
            return "服务器繁忙，请稍后重试"
        case .phoneNotVerified:
            return "手机号尚未验证，请查看短信"
        case .emailNotVerified:
            return "邮箱尚未验证，请查看邮件"
        case .invalidPhoneNumber:
            return "请输入有效的手机号"
        case .passwordMismatch:
            return "两次输入的密码不一致"
        case .termsNotAgreed:
            return "请阅读并同意用户协议和隐私政策"
        case .unknown:
            return "发生未知错误，请重试"
        case .dataContextUnavailable:
            return "数据上下文不可用"
        }
    }
}

// MARK: - 认证视图模型

@MainActor
class EAAuthViewModel: ObservableObject {
    // MARK: - Published Properties
    
    // 用户状态
    @Published var isAuthenticated = false
    @Published var currentUser: EAUser?

    // 🔑 新增：用户加载状态管理
    @Published var isLoadingUser = false
    @Published var userLoadError: String? = nil

    // UI 状态
    @Published var isLoading = false
    @Published var errorMessage: String? = nil
    @Published var successMessage: String? = nil
    
    // 登录表单
    @Published var loginPhoneNumber: String = "" {
        didSet {
            if loginPhoneError != nil {
                loginPhoneError = nil
            }
        }
    }
    @Published var loginPassword: String = "" {
        didSet {
            if loginPasswordError != nil {
                loginPasswordError = nil
            }
        }
    }
    @Published var loginPhoneError: String? = nil
    @Published var loginPasswordError: String? = nil
    
    // 注册表单
    @Published var registrationPhoneNumber: String = "" {
        didSet {
            if registrationPhoneError != nil {
                registrationPhoneError = nil
            }
        }
    }
    @Published var registrationPassword: String = "" {
        didSet {
            if registrationPasswordError != nil {
                registrationPasswordError = nil
            }
        }
    }
    @Published var registrationConfirmPassword: String = "" {
        didSet {
            if registrationConfirmPasswordError != nil {
                registrationConfirmPasswordError = nil
            }
        }
    }
    @Published var registrationAgreedToTerms: Bool = false {
        didSet {
            if registrationTermsError != nil {
                registrationTermsError = nil
            }
        }
    }
    @Published var registrationPhoneError: String? = nil
    @Published var registrationPasswordError: String? = nil
    @Published var registrationConfirmPasswordError: String? = nil
    @Published var registrationTermsError: String? = nil
    
    // 忘记密码表单
    @Published var resetPhoneNumber: String = "" {
        didSet {
            if resetPhoneError != nil {
                resetPhoneError = nil
            }
        }
    }
    @Published var resetPhoneError: String? = nil
    @Published var isResetEmailSent = false
    
    // MARK: - Private Properties
    
    private let authService: EAAuthServiceProtocol
    private var sessionManager: EASessionManager?
    
    // ✅ 修复：强制Repository模式，移除ModelContext依赖
    private var repositoryContainer: EARepositoryContainer?

    // MARK: - Combine
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    /// 无参数初始化方法（用于@StateObject）
    init() {
        // 创建临时的SessionManager和AuthService，稍后通过setSessionManager更新
        let tempSessionManager = EASessionManager()
        self.authService = EAAuthService(sessionManager: tempSessionManager)
        self.sessionManager = nil
    }
    
    init(
        authService: EAAuthServiceProtocol? = nil,
        sessionManager: EASessionManager
    ) {
        // ✅ 修复：在MainActor上下文中创建EAAuthService
        if let authService = authService {
        self.authService = authService
        } else {
            self.authService = EAAuthService(sessionManager: sessionManager)
        }
        
        self.sessionManager = sessionManager

        self.isAuthenticated = sessionManager.isLoggedIn
        // 🔑 重构：使用标准化异步加载方法
        Task { @MainActor in
            await self.loadCurrentUser()
        }

        setupSessionObserver()
    }
    
    // MARK: - 依赖注入方法
    
    /// 设置SessionManager（用于延迟依赖注入）
    func setSessionManager(_ manager: EASessionManager) {
        self.sessionManager = manager
        self.isAuthenticated = manager.isLoggedIn
        // 🔑 重构：使用标准化异步加载方法
        Task { @MainActor in
            await self.loadCurrentUser()
        }
        setupSessionObserver()
    }
    
    // MARK: - 会话观察
    
    private func setupSessionObserver() {
        guard let sessionManager = sessionManager else { return }

        sessionManager.$isLoggedIn
            .receive(on: DispatchQueue.main)
            .assign(to: &$isAuthenticated)

        // 🔑 重构：观察currentUserID变化，使用标准化异步加载方法
        sessionManager.$currentUserID
            .receive(on: DispatchQueue.main)
            .sink { [weak self] userID in
                guard let self = self else { return }
                if userID != nil {
                    // 使用标准化异步加载方法
                    Task { @MainActor in
                        await self.loadCurrentUser()
                    }
                } else {
                    self.currentUser = nil
                    self.userLoadError = nil
                }
            }
            .store(in: &cancellables)
    }

    // MARK: - 🔑 标准化异步用户加载方法

    /// 异步加载当前用户信息
    @MainActor
    private func loadCurrentUser() async {
        isLoadingUser = true
        userLoadError = nil
        defer { isLoadingUser = false }

        guard let sessionManager = sessionManager else {
            userLoadError = "会话管理器未初始化"
            return
        }

        guard let user = await sessionManager.safeCurrentUser else {
            userLoadError = "无法获取用户信息"
            return
        }

        // 更新依赖用户信息的属性
        updateUserDependentProperties(user)
    }

    /// 更新依赖用户信息的属性
    @MainActor
    private func updateUserDependentProperties(_ user: EAUser) {
        self.currentUser = user
        self.isAuthenticated = true

        // 清除错误状态
        self.userLoadError = nil
        self.errorMessage = nil
    }

    // MARK: - Public Methods
    
    /// 设置Repository容器
    func setRepositoryContainer(_ container: EARepositoryContainer) {
        self.repositoryContainer = container
        
        // 🔑 关键修复：同时设置AuthService的Repository容器，避免依赖注入链断裂
        Task {
            await authService.setRepositoryContainer(container)
        }
    }
    
    // MARK: - 🔑 核心修复：强化用户注册流程，确保新用户有完整身份档案
    
    /// 注册新用户（增强版，确保完整档案创建）
    func registerEnhanced() async {
        clearErrors()
        
        guard validateRegistrationInputs() else { return }
        
        isLoading = true
        
        do {
            // 🔑 关键修复：先验证Repository容器可用性
            guard let repositoryContainer = repositoryContainer else {
                errorMessage = "系统初始化未完成，请稍后重试"
                isLoading = false
                return
            }
            
            // 步骤1：使用Repository直接创建完整用户（跳过Auth服务，避免时序问题）
            let newUser = try await createCompleteUserProfileDirect(
                phoneNumber: registrationPhoneNumber,
                password: registrationPassword,
                repositoryContainer: repositoryContainer
            )
            
            // 步骤2：验证用户身份完整性
            let isValid = await validateUserCompleteness(user: newUser)
            guard isValid else {
                errorMessage = "用户档案创建不完整，请重试"
                isLoading = false
                return
            }
            
            // 步骤3：使用SessionManager建立会话（简化版）
            if let sessionManager = sessionManager {
                #if DEBUG
                print("🔧 [AuthVM] Setting current user with ID: \(newUser.id.uuidString)")
                #endif
                
                try await sessionManager.setCurrentUser(newUser)
            }
            
            // 步骤4：使用身份守护器进行最终验证
            if let integrityGuard = await getIntegrityGuard() {
                let _ = await integrityGuard.ensureUserIntegrity(for: newUser)
            }
            
            // 步骤5：更新UI状态
            await MainActor.run {
                self.currentUser = newUser
                self.isAuthenticated = true
                self.successMessage = "注册成功，欢迎加入数字宇宙！"
                self.clearRegistrationForm()
            }
            
        } catch {
            await MainActor.run {
                self.handleError(error)
            }
        }
        
        isLoading = false
    }

    /// 🔑 核心修复：直接创建完整用户档案（同步方法，确保数据完整性）
    private func createCompleteUserProfileDirect(
        phoneNumber: String, 
        password: String,
        repositoryContainer: EARepositoryContainer
    ) async throws -> EAUser {
        
        let userRepo = repositoryContainer.userRepository
        
        // 🔑 关键：使用Repository的createUserWithAuth方法，这是已验证的完整用户创建方法
        let newUser = try await userRepo.createUserWithAuth(
            username: phoneNumber,
            email: nil,
            phoneNumber: phoneNumber,
            password: password
        )
        
        // 🔑 关键验证：确保所有必要的档案都已创建
        // ✅ 修复：使用_忽略不需要的返回值，只检查存在性
        guard let socialProfile = newUser.socialProfile,
              let _ = newUser.dataProfile,
              let _ = newUser.moderationProfile else {
            throw EAAuthError.dataContextUnavailable
        }
        
        // 🔑 关键验证：确保数字宇宙数据已正确初始化
        guard socialProfile.stellarLevel != nil,
              socialProfile.totalStellarEnergy != nil,
              socialProfile.explorerTitle != nil,
              socialProfile.universeRegion != nil else {
            throw EAAuthError.dataContextUnavailable
        }
        
        return newUser
    }
    
    /// 🔑 新增：验证用户完整性
    private func validateUserCompleteness(user: EAUser) async -> Bool {
        // 基础验证
        guard let socialProfile = user.socialProfile,
              let dataProfile = user.dataProfile,
              let moderationProfile = user.moderationProfile else {
            return false
        }
        
        // 数字宇宙数据验证
        guard socialProfile.stellarLevel != nil,
              socialProfile.totalStellarEnergy != nil,
              socialProfile.explorerTitle != nil,
              socialProfile.universeRegion != nil else {
            return false
        }
        
        // 关系链验证
        guard socialProfile.user?.id == user.id,
              dataProfile.user?.id == user.id,
              moderationProfile.user?.id == user.id else {
            return false
        }
        
        return true
    }

    /// 创建完整的用户档案（保持向后兼容）
    private func createCompleteUserProfile(phoneNumber: String) async {
        guard let repositoryContainer = repositoryContainer else {
            #if DEBUG
            // 调试环境下记录Repository容器不可用
            #endif
            return
        }
        
        do {
            // 🔑 关键修复：使用Repository模式创建完整的用户档案
            let userRepo = repositoryContainer.userRepository
            
            // 🔑 关键修复：使用createUserWithAuth方法，这是已有的完整用户创建方法
            let _ = try await userRepo.createUserWithAuth(
                username: phoneNumber, 
                email: nil, 
                phoneNumber: phoneNumber,
                password: registrationPassword
            )
            
            // 🔑 验证用户创建成功并设置为当前用户
            if let newUser = try await userRepo.fetchUserByPhone(phoneNumber: phoneNumber) {
                // 验证完整档案已创建
                guard newUser.socialProfile != nil,
                      newUser.dataProfile != nil,
                      newUser.moderationProfile != nil else {
                    #if DEBUG
                    // 调试环境下记录档案创建不完整
                    #endif
                    return
                }
                
                // 🔑 关键：设置为当前用户
                await MainActor.run {
                    self.currentUser = newUser
                }
            }
            
        } catch {
            #if DEBUG
            // 调试环境下记录档案创建失败，但不影响注册流程
            #endif
        }
    }

    /// 获取用户身份完整性守护器
    private func getIntegrityGuard() async -> EAUserIntegrityGuard? {
        guard let repositoryContainer = repositoryContainer else { return nil }
        
        return EAUserIntegrityGuard(repositoryContainer: repositoryContainer)
    }
    
    /// 登录
    func login() async {
        clearErrors()
        guard validateLoginInputs() else {
            return
        }
        
        isLoading = true
        
        do {
            let response = try await authService.login(
                phoneNumber: loginPhoneNumber,
                password: loginPassword
            )
            
            if response.success, let _ = response.token {
                isAuthenticated = true
                successMessage = "登录成功，欢迎回来！"
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    self.successMessage = nil
                }
                if let sessionManager = sessionManager, !sessionManager.isLoggedIn {
                    try await Task.sleep(nanoseconds: 50_000_000) // 0.05秒
                }
            } else {
                errorMessage = response.message
            }
        } catch {
            handleError(error)
        }
        
        isLoading = false
    }
    
    /// 注册（调用增强版方法）
    func register() async {
        // 🔑 关键修复：调用增强版注册方法，确保新用户有完整档案
        await registerEnhanced()
    }
    
    /// 社交登录
    func socialLogin(provider: EASocialButton.SocialProvider) async {
        clearErrors()
        isLoading = true
        
        do {
            let token = "mock_\(provider.rawValue.lowercased())_token"
            
            let response = try await authService.socialLogin(provider: provider.rawValue, token: token)
            
            if response.success, let _ = response.token {
                // 社交登录成功，创建用户数据
                let userData: [String: Any] = [
                    "id": UUID().uuidString,
                    "username": "\(provider.rawValue)用户",
                    "phoneNumber": "",
                    "isPro": false,
                    "preferredCoachStyle": "温柔鼓励型",
                    "creationDate": ISO8601DateFormatter().string(from: Date())
                ]
                
                await createUserProfile(from: userData)
                
                // 使用SessionManager的login方法
                if let sessionManager = sessionManager {
                    try await sessionManager.login(username: "\(provider.rawValue)用户")
                }
                
                isAuthenticated = true
                successMessage = response.message
                // 第三方登录成功
            } else {
                errorMessage = response.message
                // 第三方登录失败，错误信息已设置
            }
            
        } catch {
            handleError(error)
        }
        
        isLoading = false
    }
    
    /// 忘记密码
    func sendPasswordReset() async -> Bool {
        clearErrors()
        
        if resetPhoneNumber.isEmpty {
            resetPhoneError = "请输入手机号"
            return false
        } else if !EATextField.phoneValidator(resetPhoneNumber) {
            resetPhoneError = "请输入有效的手机号"
            return false
        }
        
        isLoading = true
        
        do {
            let response = try await authService.sendPasswordResetCode(phoneNumber: resetPhoneNumber)
            
            if response.success && response.verificationCodeSent {
                successMessage = response.message
                isResetEmailSent = true
                isLoading = false
                return true
            } else {
                resetPhoneError = response.message
                isLoading = false
                return false
            }
            
        } catch {
            handleError(error)
            isLoading = false
            return false
        }
    }
    
    /// 登出
    func logout() async {
        isLoading = true
        
        do {
            let success = try await authService.logout()
            
            if success {
                if let sessionManager = sessionManager {
                    sessionManager.clearSession()
                }
                
                Task {
                    let networkService = EANetworkService()
                    networkService.clearAuthToken()
                }
                
                clearAllForms()
                
                successMessage = "已安全退出"
            } else {
                errorMessage = "退出失败，请重试"
            }
            
        } catch {
            if let sessionManager = sessionManager {
                sessionManager.clearSession()
            }
            Task {
                let networkService = EANetworkService()
                networkService.clearAuthToken()
            }
            clearAllForms()
            
            // 登出网络请求失败，但已清除本地会话
        }
        
        isLoading = false
    }
    
    // MARK: - 用户档案管理（已简化为新架构）
    
    // 旧的方法已移除，使用新的createUserProfile(from: [String: Any])方法
    
    // MARK: - 表单验证
    
    private func validateLoginInputs() -> Bool {
        var isValid = true
        
        if loginPhoneNumber.isEmpty {
            loginPhoneError = "请输入手机号"
            isValid = false
        } else if !EATextField.phoneValidator(loginPhoneNumber) {
            loginPhoneError = "请输入有效的手机号"
            isValid = false
        }
        
        if loginPassword.isEmpty {
            loginPasswordError = "请输入密码"
            isValid = false
        }
        
        return isValid
    }
    
    private func validateRegistrationInputs() -> Bool {
        var isValid = true
        
        if registrationPhoneNumber.isEmpty {
            registrationPhoneError = "请输入手机号"
            isValid = false
        } else if !EATextField.phoneValidator(registrationPhoneNumber) {
            registrationPhoneError = "请输入有效的手机号"
            isValid = false
        }
        
        if registrationPassword.isEmpty {
            registrationPasswordError = "请设置密码"
            isValid = false
        } else if !EATextField.passwordValidator(registrationPassword) {
            registrationPasswordError = "密码至少6位，需包含字母和数字"
            isValid = false
        }
        
        if registrationConfirmPassword.isEmpty {
            registrationConfirmPasswordError = "请确认密码"
            isValid = false
        } else if registrationPassword != registrationConfirmPassword {
            registrationConfirmPasswordError = "两次输入的密码不一致"
            isValid = false
        }
        
        if !registrationAgreedToTerms {
            registrationTermsError = "请阅读并同意用户协议和隐私政策"
            isValid = false
        }
        
        return isValid
    }
    
    // MARK: - 错误处理
    
    private func handleError(_ error: Error) {
        if let authError = error as? EAAuthError {
            errorMessage = authError.localizedDescription
        } else if let networkError = error as? EANetworkError {
            errorMessage = networkError.localizedDescription
        } else {
            errorMessage = "发生未知错误：\(error.localizedDescription)"
        }
        
        // 记录认证错误
    }
    
    func clearErrors() {
        errorMessage = nil
        successMessage = nil
        loginPhoneError = nil
        loginPasswordError = nil
        registrationPhoneError = nil
        registrationPasswordError = nil
        registrationConfirmPasswordError = nil
        registrationTermsError = nil
        resetPhoneError = nil
    }
    
    // MARK: - 表单清理
    
    private func clearLoginForm() {
        loginPhoneNumber = ""
        loginPassword = ""
    }
    
    private func clearRegistrationForm() {
        registrationPhoneNumber = ""
        registrationPassword = ""
        registrationConfirmPassword = ""
        registrationAgreedToTerms = false
    }
    
    private func clearAllForms() {
        clearLoginForm()
        clearRegistrationForm()
        resetPhoneNumber = ""
        isResetEmailSent = false
    }
    
    // MARK: - Repository容器支持（新架构迁移准备）
    
    /// 检查是否支持新架构
    private var supportsNewArchitecture: Bool {
        return repositoryContainer != nil
    }
    
    // MARK: - 用户档案创建（新架构适配）
    
    /// 创建用户档案（适配新架构）
    private func createUserProfile(from userData: [String: Any]) async {
        // 新架构中，用户创建逻辑已经移到SessionManager.login方法中
        // 这里不需要直接操作ModelContext，而是通过Repository层处理
        // 用户档案创建逻辑已集成
    }
}

// MARK: - 扩展：社交登录提供商

extension EASocialButton.SocialProvider {
    var rawValue: String {
        switch self {
        case .apple: return "Apple"
        case .wechat: return "WeChat"
        case .phone: return "Phone"
        }
    }
} 