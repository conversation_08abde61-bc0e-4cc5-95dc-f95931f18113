import SwiftUI
import Foundation

// MARK: - Onboarding ViewModel

/// 引导页面的ViewModel，负责管理引导流程的状态和业务逻辑
/// 遵循MVVM架构模式，将业务逻辑从View中分离
/// ✅ 修复：添加@MainActor确保UI更新在主线程
@MainActor
final class EAOnboardingViewModel: ObservableObject {
    
    // MARK: - Published Properties
    
    /// 当前显示的引导页面索引 (0, 1, 2)
    @Published var currentPage: Int = 0
    
    /// 用户是否已完成引导流程
    @AppStorage("hasCompletedOnboarding") var hasCompletedOnboarding: Bool = false
    
    // MARK: - Constants
    
    /// 引导页面总数
    let totalPages: Int = 3
    
    /// 引导页面数据源
    let onboardingPages: [OnboardingPageUIDataV2] = [
        OnboardingPageUIDataV2(pageIndex: 0),
        OnboardingPageUIDataV2(pageIndex: 1),
        OnboardingPageUIDataV2(pageIndex: 2)
    ]
    
    // MARK: - Computed Properties
    
    /// 是否为最后一页
    var isLastPage: Bool {
        currentPage == totalPages - 1
    }
    
    /// 是否为第一页
    var isFirstPage: Bool {
        currentPage == 0
    }
    
    /// 是否显示跳过按钮
    var shouldShowSkipButton: Bool {
        !isLastPage
    }
    
    /// 是否显示下一步按钮
    var shouldShowNextButton: Bool {
        !isLastPage
    }
    
    /// 是否显示完成按钮
    var shouldShowCompleteButton: Bool {
        isLastPage
    }
    
    // MARK: - Initialization
    
    init() {
        // ViewModel初始化时的额外配置可以放在这里
    }
    
    // MARK: - Public Methods
    
    /// 前往下一页
    func nextPage() {
        guard !isLastPage else { return }
        
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            currentPage += 1
        }
    }
    
    /// 前往上一页
    func previousPage() {
        guard !isFirstPage else { return }
        
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            currentPage -= 1
        }
    }
    
    /// 跳转到指定页面
    /// - Parameter page: 目标页面索引 (0-2)
    func goToPage(_ page: Int) {
        guard page >= 0 && page < totalPages else { return }
        
        withAnimation(.easeInOut(duration: 0.8)) {
            currentPage = page
        }
    }
    
    /// 跳过引导流程
    func skipOnboarding() {
        completeOnboarding()
    }
    
    /// 完成引导流程
    func completeOnboarding() {
        hasCompletedOnboarding = true
    }
    
    /// 重置引导流程（用于测试或重新展示引导）
    func resetOnboarding() {
        hasCompletedOnboarding = false
        currentPage = 0
    }
    
    // MARK: - Helper Methods
    
    /// 获取当前页面的数据
    var currentPageData: OnboardingPageUIDataV2 {
        return onboardingPages[currentPage]
    }
    
    /// 检查是否可以前往下一页
    func canGoToNextPage() -> Bool {
        return !isLastPage
    }
    
    /// 检查是否可以返回上一页
    func canGoToPreviousPage() -> Bool {
        return !isFirstPage
    }
}

// MARK: - Preview Helper

#if DEBUG
extension EAOnboardingViewModel {
    /// 用于SwiftUI预览的静态实例
    static let preview: EAOnboardingViewModel = {
        let viewModel = EAOnboardingViewModel()
        viewModel.currentPage = 1 // 设置为中间页面便于预览
        return viewModel
    }()
}
#endif 