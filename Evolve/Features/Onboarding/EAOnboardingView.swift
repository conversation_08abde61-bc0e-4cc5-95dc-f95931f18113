import SwiftUI
import Foundation

// MARK: - Data Models

struct OnboardingPageUIDataV2: Identifiable {
    let id = UUID()
    let pageIndex: Int // 0, 1, 2

    // 文字颜色
    static let primaryTextColor = Color.hexColor("FFFFFF") // 高亮白
    static let secondaryTextColor = Color.hexColor("FFFFF0").opacity(0.9) // 浅象牙白

    // 能量色（设计规范中的精确值）
    static let energyColorCyan = Color.hexColor("40E0D0") // 荧光青/薄荷绿
    static let energyColorCoral = Color.hexColor("FF7F50") // 珊瑚粉/日落橙

    // 页面特定内容
    var title: String {
        switch pageIndex {
        case 0: return "你好，未来的探索者"
        case 1: return "AI智慧灌溉，成长不再孤单"
        case 2: return "内在生态绽放，遇见更好的自己"
        default: return ""
        }
    }

    var subtitle: String {
        switch pageIndex {
        case 0: return "Evolve AI，你的私人AI微计划教练，与你一同播种希望，见证每一个微小改变汇聚成巨大能量。"
        case 1: return "Evolve AI 深入理解你的需求与挑战，提供个性化的微计划建议与实时的积极反馈，全程陪伴你的每一步。"
        case 2: return "让积极计划成为你生活的一部分。Evolve AI 助你构建平衡、健康的内在生态，持续赋能，成就更好的你。"
        default: return ""
        }
    }
    
    var visualConceptIdentifier: String {
        switch pageIndex {
        case 0: return "seedOfHope"
        case 1: return "wisdomIrrigation"
        case 2: return "ecosystemBloom"
        default: return ""
        }
    }
    
    // 页面特定背景渐变（基于最佳实践增强层次）
    @ViewBuilder
    var backgroundGradient: some View {
        switch pageIndex {
        case 0: // 页面1：初识微光
            ZStack {
                // 主背景渐变
                LinearGradient(
                    gradient: Gradient(stops: [
                        .init(color: Color.hexColor("0A2F51"), location: 0.0),  // 深邃蓝
                        .init(color: Color.hexColor("005A4B"), location: 0.35), // 中蓝绿
                        .init(color: Color.hexColor("008080"), location: 0.65), // 浅蓝绿
                        .init(color: Color.hexColor("002b20"), location: 1.0)   // 深绿
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
                
                // 多层径向光晕 - 薄荷绿主要光源
                RadialGradient(
                    gradient: Gradient(colors: [
                        OnboardingPageUIDataV2.energyColorCyan.opacity(0.4),
                        OnboardingPageUIDataV2.energyColorCyan.opacity(0.2),
                        Color.clear
                    ]),
                    center: UnitPoint(x: 0.15, y: 0.2),
                    startRadius: 0,
                    endRadius: 250
                )
                
                // 次要光源 - 青绿
                RadialGradient(
                    gradient: Gradient(colors: [
                        Color.hexColor("008080").opacity(0.3),
                        Color.hexColor("008080").opacity(0.1),
                        Color.clear
                    ]),
                    center: UnitPoint(x: 0.8, y: 0.8),
                    startRadius: 0,
                    endRadius: 180
                )
                
                // 微弱的珊瑚色点缀
                RadialGradient(
                    gradient: Gradient(colors: [
                        OnboardingPageUIDataV2.energyColorCoral.opacity(0.1),
                        Color.clear
                    ]),
                    center: UnitPoint(x: 0.5, y: 0.7),
                    startRadius: 0,
                    endRadius: 120
                )
            }
        case 1: // 页面2：AI赋能
            ZStack {
                // 主背景渐变
                LinearGradient(
                    gradient: Gradient(stops: [
                        .init(color: Color.hexColor("003E54"), location: 0.0),  // 深海蓝
                        .init(color: Color.hexColor("006064"), location: 0.3),  // 蓝绿
                        .init(color: Color.hexColor("00838F"), location: 0.7),  // 青蓝
                        .init(color: Color.hexColor("004D40"), location: 1.0)   // 深蓝绿
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
                
                // 智慧核心主光源
                RadialGradient(
                    gradient: Gradient(colors: [
                        Color.hexColor("00C8C8").opacity(0.6), // 青色核心
                        Color.hexColor("40E0D0").opacity(0.3), // 薄荷绿扩散
                        Color.clear
                    ]),
                    center: UnitPoint(x: 0.5, y: 0.3),
                    startRadius: 0,
                    endRadius: 200
                )
                
                // 左下角温暖光源
                RadialGradient(
                    gradient: Gradient(colors: [
                        OnboardingPageUIDataV2.energyColorCoral.opacity(0.2),
                        OnboardingPageUIDataV2.energyColorCoral.opacity(0.1),
                        Color.clear
                    ]),
                    center: UnitPoint(x: 0.2, y: 0.8),
                    startRadius: 0,
                    endRadius: 150
                )
                
                // 右上角青色光点
                RadialGradient(
                    gradient: Gradient(colors: [
                        Color.hexColor("87CEEB").opacity(0.15),
                        Color.clear
                    ]),
                    center: UnitPoint(x: 0.8, y: 0.15),
                    startRadius: 0,
                    endRadius: 100
                )
            }
        case 2: // 页面3：生态绽放
            ZStack {
                // 主背景渐变 - 更丰富的色彩层次
                LinearGradient(
                    gradient: Gradient(stops: [
                        .init(color: Color.hexColor("0D47A1"), location: 0.0),  // 深蓝
                        .init(color: Color.hexColor("00695C"), location: 0.25), // 蓝绿
                        .init(color: Color.hexColor("FF8F00"), location: 0.75), // 日落橙
                        .init(color: Color.hexColor("F57C00"), location: 1.0)   // 橙色
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
                
                // 底部日落主光源
                RadialGradient(
                    gradient: Gradient(colors: [
                        Color.hexColor("FFA500").opacity(0.5), // 橙色核心
                        Color.hexColor("FF7F50").opacity(0.3), // 珊瑚扩散
                        Color.hexColor("FF8F00").opacity(0.1), // 边缘晕染
                        Color.clear
                    ]),
                    center: UnitPoint(x: 0.5, y: 0.9),
                    startRadius: 0,
                    endRadius: 250
                )
                
                // 左上角青色光点
                RadialGradient(
                    gradient: Gradient(colors: [
                        Color.hexColor("00C8C8").opacity(0.3),
                        Color.hexColor("40E0D0").opacity(0.15),
                        Color.clear
                    ]),
                    center: UnitPoint(x: 0.2, y: 0.15),
                    startRadius: 0,
                    endRadius: 120
                )
                
                // 右侧中部薄荷绿光晕
                RadialGradient(
                    gradient: Gradient(colors: [
                        OnboardingPageUIDataV2.energyColorCyan.opacity(0.2),
                        Color.clear
                    ]),
                    center: UnitPoint(x: 0.85, y: 0.5),
                    startRadius: 0,
                    endRadius: 100
                )
            }
        default:
            ZStack {
                Color.black
            }
        }
    }
}

// MARK: - 优化的Canvas粒子系统（兼容性优先）

struct UltraFluidParticleSystem: View {
    let particleCount: Int
    let pageIndex: Int
    
    @State private var particles: [FluidParticle] = []
    @State private var startTime = Date()
    
    init(particleCount: Int = 30, pageIndex: Int) {
        self.particleCount = particleCount
        self.pageIndex = pageIndex
    }
    
    var body: some View {
        GeometryReader { geometry in
            TimelineView(.animation) { timeline in
                Canvas { context, size in
                    // 使用更温和的滤镜设置，确保兼容性
                    context.addFilter(.blur(radius: 8)) // 降低模糊半径
                    
                    let timeElapsed = timeline.date.timeIntervalSince(startTime)
                    
                    // 简化的单层渲染，减少嵌套
                    for (index, particle) in particles.enumerated() {
                        drawOptimizedParticle(
                            context: context,
                            particle: particle,
                            time: timeElapsed,
                            index: index,
                            size: size
                        )
                    }
                }
                .blendMode(.plusLighter)
            }
        }
        .onAppear {
            initializeFluidParticles()
        }
    }
    
    private func initializeFluidParticles() {
        particles = (0..<particleCount).map { i in
            FluidParticle(
                id: i,
                initialPosition: CGPoint(
                    x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                    y: CGFloat.random(in: -100...UIScreen.main.bounds.height)
                ),
                color: getFluidPageColors()[i % getFluidPageColors().count],
                size: CGFloat.random(in: 25...60), // 增大粒子尺寸
                speed: CGFloat.random(in: 15...45),
                phase: Double.random(in: 0...(2 * .pi)),
                amplitude: CGFloat.random(in: 40...100),
                organicFactor: Double.random(in: 0.3...0.8)
            )
        }
    }
    
    private func getFluidPageColors() -> [Color] {
        switch pageIndex {
        case 0:
            return [
                OnboardingPageUIDataV2.energyColorCyan.opacity(0.8), // 提高透明度
                Color.hexColor("87CEEB").opacity(0.7),
                Color.hexColor("98FB98").opacity(0.6),
                Color.hexColor("AFEEEE").opacity(0.8)
            ]
        case 1:
            return [
                OnboardingPageUIDataV2.energyColorCyan.opacity(0.9),
                Color.hexColor("00C8C8").opacity(0.8),
                OnboardingPageUIDataV2.energyColorCoral.opacity(0.6),
                Color.hexColor("87CEEB").opacity(0.7)
            ]
        case 2:
            return [
                OnboardingPageUIDataV2.energyColorCoral.opacity(0.8),
                Color.hexColor("FFD700").opacity(0.7),
                Color.hexColor("DDA0DD").opacity(0.6),
                OnboardingPageUIDataV2.energyColorCyan.opacity(0.7),
                Color.hexColor("98FB98").opacity(0.6)
            ]
        default:
            return [OnboardingPageUIDataV2.energyColorCyan.opacity(0.8)]
        }
    }
    
    private func drawOptimizedParticle(
        context: GraphicsContext,
        particle: FluidParticle,
        time: Double,
        index: Int,
        size: CGSize
    ) {
        // 简化的有机运动算法
        let cycleDuration: Double = 20.0 + Double(index % 5) * 2.0
        let progress = (time * particle.organicFactor + particle.phase).truncatingRemainder(dividingBy: cycleDuration) / cycleDuration
        
        // 简化的运动计算
        let waveX = sin(progress * 2 * .pi + particle.phase) * particle.amplitude
        let waveY = cos(progress * 1.5 * .pi + particle.phase) * particle.amplitude * 0.5
        
        let currentX = particle.initialPosition.x + waveX
        let currentY = particle.initialPosition.y + CGFloat(progress) * (size.height + 200) - 100 + waveY
        
        // 简化的透明度计算，确保可见性
        let fadeIn = min(progress * 3, 1.0)
        let fadeOut = max(1.0 - max(progress - 0.7, 0) * 2.0, 0.0)
        let breathe = (sin(time * 1.2 + Double(index) * 0.2) + 1) / 2
        let alpha = fadeIn * fadeOut * (0.6 + breathe * 0.4) // 提高基础透明度
        
        // 确保alpha不会太小
        let finalAlpha = max(alpha, 0.2)
        
        // 动态尺寸
        let sizeVariation = 1.0 + sin(time * 1.5 + Double(index) * 0.4) * 0.2
        let currentSize = particle.size * sizeVariation
        
        // 多层粒子效果，但简化层数
        for i in 0..<2 {
            let layerScale = 1.0 - CGFloat(i) * 0.3
            let layerSize = currentSize * layerScale
            let layerAlpha = finalAlpha * (1.0 - CGFloat(i) * 0.3)
            
            let rect = CGRect(
                x: currentX - layerSize/2,
                y: currentY - layerSize/2,
                width: layerSize,
                height: layerSize
            )
            
            let path = Path(ellipseIn: rect)
            context.fill(path, with: .color(particle.color.opacity(layerAlpha)))
        }
    }
}

struct FluidParticle {
    let id: Int
    let initialPosition: CGPoint
    let color: Color
    let size: CGFloat
    let speed: CGFloat
    let phase: Double
    let amplitude: CGFloat
    let organicFactor: Double
}

// MARK: - 核心视觉组件系统（优化版 - 真正的呼吸发光圆体）

struct FluidSeedOfHope: View {
    let geometry: GeometryProxy
    @State private var breathe = false
    @State private var rotation = 0.0
    @State private var innerPulse = false
    
    var body: some View {
        ZStack {
            // 外层发光圆体 - 主呼吸效果
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            OnboardingPageUIDataV2.energyColorCyan.opacity(0.8),
                            OnboardingPageUIDataV2.energyColorCyan.opacity(0.4),
                            OnboardingPageUIDataV2.energyColorCyan.opacity(0.2),
                            Color.clear
                        ]),
                        center: .center,
                        startRadius: 10,
                        endRadius: 100
                    )
                )
                .frame(width: 160, height: 160)
                .scaleEffect(breathe ? 1.3 : 1.0)
                .opacity(breathe ? 0.9 : 0.6)
                .blur(radius: 3)
                .shadow(color: OnboardingPageUIDataV2.energyColorCyan.opacity(0.6), radius: breathe ? 40 : 25)
                .animation(.easeInOut(duration: 3.0).repeatForever(autoreverses: true), value: breathe)
            
            // 中层呼吸核心
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            OnboardingPageUIDataV2.energyColorCyan.opacity(0.9),
                            OnboardingPageUIDataV2.energyColorCyan.opacity(0.6),
                            Color.clear
                        ]),
                        center: .center,
                        startRadius: 0,
                        endRadius: 50
                    )
                )
                .frame(width: 100, height: 100)
                .scaleEffect(innerPulse ? 1.2 : 0.8)
                .opacity(innerPulse ? 1.0 : 0.7)
                .blur(radius: 2)
                .animation(.easeInOut(duration: 2.5).repeatForever(autoreverses: true), value: innerPulse)
            
            // 内核白光
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.9),
                            Color.white.opacity(0.5),
                            Color.clear
                        ]),
                        center: .center,
                        startRadius: 0,
                        endRadius: 20
                    )
                )
                .frame(width: 30, height: 30)
                .scaleEffect(breathe ? 1.1 : 0.9)
                .blur(radius: 1)
                .animation(.easeInOut(duration: 3.0).repeatForever(autoreverses: true), value: breathe)
            
            // 旋转光环
            createRotatingRing()
        }
        .onAppear {
            breathe = true
            innerPulse = true
            rotation = 360
        }
    }
    
    @ViewBuilder
    private func createRotatingRing() -> some View {
        Circle()
            .stroke(
                AngularGradient(
                    gradient: Gradient(colors: [
                        Color.clear,
                        OnboardingPageUIDataV2.energyColorCyan.opacity(0.8),
                        Color.clear,
                        OnboardingPageUIDataV2.energyColorCyan.opacity(0.6),
                        Color.clear
                    ]),
                    center: .center
                ),
                lineWidth: 2
            )
            .frame(width: 180, height: 180)
            .rotationEffect(.degrees(rotation))
            .blur(radius: 1)
            .animation(.linear(duration: 8.0).repeatForever(autoreverses: false), value: rotation)
    }
}

struct FluidWisdomCore: View {
    let geometry: GeometryProxy
    @State private var breathe = false
    @State private var ripplePhase = 0.0
    @State private var coreGlow = false
    
    var body: some View {
        ZStack {
            // 智慧核心主体
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color.hexColor("38EF7D").opacity(0.9), // 智慧核心颜色
                            Color.hexColor("00C8C8").opacity(0.7),
                            Color.hexColor("008080").opacity(0.5),
                            Color.clear
                        ]),
                        center: .center,
                        startRadius: 15,
                        endRadius: 90
                    )
                )
                .frame(width: 140, height: 140)
                .scaleEffect(breathe ? 1.4 : 1.0)
                .opacity(breathe ? 1.0 : 0.8)
                .blur(radius: 3)
                .shadow(color: Color.hexColor("38EF7D").opacity(0.7), radius: breathe ? 50 : 30)
                .animation(.easeInOut(duration: 4.0).repeatForever(autoreverses: true), value: breathe)
            
            // 智慧内核
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.9),
                            Color.hexColor("38EF7D").opacity(0.6),
                            Color.clear
                        ]),
                        center: .center,
                        startRadius: 0,
                        endRadius: 25
                    )
                )
                .frame(width: 40, height: 40)
                .scaleEffect(coreGlow ? 1.2 : 0.8)
                .opacity(coreGlow ? 1.0 : 0.7)
                .animation(.easeInOut(duration: 3.0).repeatForever(autoreverses: true), value: coreGlow)
            
            // 涟漪效果
            createRippleEffect()
            
            // 能量线条
            createEnergyLines()
        }
        .onAppear {
            breathe = true
            coreGlow = true
            ripplePhase = 4 * .pi
        }
    }
    
    @ViewBuilder
    private func createRippleEffect() -> some View {
        ForEach(0..<3, id: \.self) { index in
            Circle()
                .stroke(
                    OnboardingPageUIDataV2.energyColorCyan.opacity(0.6 - Double(index) * 0.15),
                    lineWidth: 2
                )
                .frame(width: 120 + CGFloat(index) * 40, height: 120 + CGFloat(index) * 40)
                .scaleEffect(1.0 + sin(ripplePhase + Double(index) * .pi / 2) * 0.3)
                .opacity(0.8 - sin(ripplePhase + Double(index) * .pi / 2) * 0.3)
                .blur(radius: 1)
                .animation(.linear(duration: 3.0).repeatForever(autoreverses: false), value: ripplePhase)
        }
    }
    
    @ViewBuilder
    private func createEnergyLines() -> some View {
        ForEach(0..<6, id: \.self) { index in
            RoundedRectangle(cornerRadius: 2)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            OnboardingPageUIDataV2.energyColorCyan.opacity(0.8),
                            OnboardingPageUIDataV2.energyColorCoral.opacity(0.6),
                            Color.clear
                        ]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .frame(width: 60, height: 3)
                .offset(x: 30)
                .rotationEffect(.degrees(Double(index) * 60))
                .scaleEffect(breathe ? 1.2 : 0.8)
                .opacity(breathe ? 0.9 : 0.6)
                .blur(radius: 1)
                .animation(.easeInOut(duration: 4.0).repeatForever(autoreverses: true), value: breathe)
        }
    }
}

struct FluidEcosystemBloom: View {
    let geometry: GeometryProxy
    @State private var twinkle = false
    @State private var centralGlow = false
    @State private var lightBeamPhase = 0.0
    
    var body: some View {
        ZStack {
            // 中央光束
            createCentralLightBeam()
            
            // 围绕星辰
            createSurroundingStars()
            
            // 漂浮微光粒子
            createFloatingParticles()
        }
        .onAppear {
            twinkle = true
            centralGlow = true
            lightBeamPhase = 4 * .pi
        }
    }
    
    @ViewBuilder
    private func createCentralLightBeam() -> some View {
        VStack(spacing: 0) {
            // 上升光束
            RoundedRectangle(cornerRadius: 8)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            OnboardingPageUIDataV2.energyColorCoral.opacity(0.9),
                            Color.hexColor("FFD700").opacity(0.8),
                            OnboardingPageUIDataV2.energyColorCyan.opacity(0.7),
                            Color.clear
                        ]),
                        startPoint: .bottom,
                        endPoint: .top
                    )
                )
                .frame(width: 12, height: 120)
                .blur(radius: 3)
                .scaleEffect(x: centralGlow ? 1.5 : 1.0, y: centralGlow ? 1.2 : 0.8)
                .opacity(centralGlow ? 1.0 : 0.7)
                .shadow(color: OnboardingPageUIDataV2.energyColorCoral.opacity(0.5), radius: centralGlow ? 20 : 10)
                .animation(.easeInOut(duration: 3.5).repeatForever(autoreverses: true), value: centralGlow)
            
            // 底部发光源
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color.hexColor("FFD700").opacity(0.9),
                            OnboardingPageUIDataV2.energyColorCoral.opacity(0.8),
                            Color.clear
                        ]),
                        center: .center,
                        startRadius: 0,
                        endRadius: 30
                    )
                )
                .frame(width: 40, height: 40)
                .scaleEffect(centralGlow ? 1.3 : 1.0)
                .blur(radius: 2)
                .shadow(color: Color.hexColor("FFD700").opacity(0.6), radius: centralGlow ? 25 : 15)
                .animation(.easeInOut(duration: 3.5).repeatForever(autoreverses: true), value: centralGlow)
        }
    }
    
    @ViewBuilder
    private func createSurroundingStars() -> some View {
        ForEach(0..<8, id: \.self) { index in
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            getStarColor(index: index).opacity(0.9),
                            getStarColor(index: index).opacity(0.6),
                            Color.clear
                        ]),
                        center: .center,
                        startRadius: 2,
                        endRadius: 15
                    )
                )
                .frame(width: getStarSize(index: index), height: getStarSize(index: index))
                .offset(
                    x: cos(Double(index) * .pi / 4 + lightBeamPhase) * getOrbitRadius(index: index),
                    y: sin(Double(index) * .pi / 4 + lightBeamPhase) * getOrbitRadius(index: index)
                )
                .scaleEffect(twinkle ? (1.0 + sin(Double(index) * 0.5) * 0.3) : 0.8)
                .opacity(twinkle ? (0.9 + sin(Double(index) * 0.7) * 0.1) : 0.6)
                .blur(radius: 1)
                .shadow(color: getStarColor(index: index).opacity(0.5), radius: twinkle ? 15 : 8)
                .animation(
                    .easeInOut(duration: 2.5 + Double(index) * 0.3)
                    .repeatForever(autoreverses: true),
                    value: twinkle
                )
        }
    }
    
    @ViewBuilder
    private func createFloatingParticles() -> some View {
        ForEach(0..<12, id: \.self) { index in
            Circle()
                .fill(getStarColor(index: index).opacity(0.6))
                .frame(width: 6, height: 6)
                .offset(
                    x: cos(Double(index) * .pi / 6 + lightBeamPhase * 0.5) * 110,
                    y: sin(Double(index) * .pi / 6 + lightBeamPhase * 0.5) * 110 + sin(lightBeamPhase + Double(index)) * 20
                )
                .opacity(twinkle ? 0.8 : 0.5)
                .blur(radius: 1)
                .animation(.linear(duration: 12.0).repeatForever(autoreverses: false), value: lightBeamPhase)
        }
    }
    
    private func getStarColor(index: Int) -> Color {
        let colors: [Color] = [
            OnboardingPageUIDataV2.energyColorCyan,
            OnboardingPageUIDataV2.energyColorCoral,
            Color.hexColor("FFD700"),
            Color.hexColor("98FB98"),
            Color.hexColor("DDA0DD")
        ]
        return colors[index % colors.count]
    }
    
    private func getStarSize(index: Int) -> CGFloat {
        return CGFloat(15 + (index % 3) * 8)
    }
    
    private func getOrbitRadius(index: Int) -> CGFloat {
        return CGFloat(60 + (index % 4) * 15)
    }
}

// MARK: - Main Onboarding View

struct EAOnboardingView: View {
    @StateObject private var viewModel = EAOnboardingViewModel()

    // 响应式字体
    private func titleFont(for geometry: GeometryProxy) -> Font {
        let baseSize: CGFloat = UIDevice.current.userInterfaceIdiom == .pad ? 42 : 28
        let calculatedSize = min(geometry.size.width * 0.08, baseSize)
        return .system(size: calculatedSize, weight: .bold, design: .default)
    }
    
    private func subtitleFont(for geometry: GeometryProxy) -> Font {
        let baseSize: CGFloat = UIDevice.current.userInterfaceIdiom == .pad ? 20 : 15
        let calculatedSize = min(geometry.size.width * 0.04, baseSize)
        return .system(size: calculatedSize, weight: .regular, design: .default)
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 终极背景特效系统
                ZStack {
                    // 动态背景渐变
                    viewModel.onboardingPages[viewModel.currentPage].backgroundGradient
                        .animation(.easeInOut(duration: 1.2), value: viewModel.currentPage)
                    
                    // 终极Canvas粒子系统
                    UltraFluidParticleSystem(particleCount: 30, pageIndex: viewModel.currentPage)
                        .opacity(0.85)
                        .animation(.easeInOut(duration: 1.2), value: viewModel.currentPage)
                }
                .edgesIgnoringSafeArea(.all)
                
                // 主要内容
                VStack(spacing: 0) {
                    TabView(selection: $viewModel.currentPage) {
                        ForEach(viewModel.onboardingPages) { pageData in
                            onboardingPageContent(
                                data: pageData,
                                geometry: geometry
                            )
                            .tag(pageData.pageIndex)
                        }
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                    .animation(.easeInOut(duration: 0.8), value: viewModel.currentPage)

                    // 底部导航区域
                    bottomNavigationView(geometry: geometry)
                }
            }
            .preferredColorScheme(.dark)
        }
    }
    
    @ViewBuilder
    private func bottomNavigationView(geometry: GeometryProxy) -> some View {
        ZStack {
            // 分页指示点 - 永远居中显示
            EAPaginationDotsView(numberOfPages: viewModel.totalPages, currentPage: $viewModel.currentPage)
            
            // 导航按钮层
            HStack {
                if viewModel.shouldShowSkipButton {
                    Button("跳过") {
                        viewModel.skipOnboarding()
                    }
                    .font(subtitleFont(for: geometry).weight(.medium))
                    .foregroundColor(OnboardingPageUIDataV2.secondaryTextColor.opacity(0.6))
                    .padding()

                    Spacer()
                    
                    Button {
                        viewModel.nextPage()
                    } label: {
                        Image(systemName: "arrow.right.circle.fill")
                            .font(titleFont(for: geometry).weight(.light))
                            .foregroundColor(OnboardingPageUIDataV2.energyColorCyan)
                            .shadow(color: OnboardingPageUIDataV2.energyColorCyan.opacity(0.6), radius: 10, x: 0, y: 0)
                            .shadow(color: OnboardingPageUIDataV2.energyColorCyan.opacity(0.3), radius: 20, x: 0, y: 0)
                    }
                    .padding()
                } else {
                    Spacer()
                }
            }
        }
        .frame(height: 60)
        .padding(.horizontal)

        if viewModel.shouldShowCompleteButton {
            EAOnboardingActionButton(title: "开启Evolve之旅") {
                viewModel.completeOnboarding()
            }
            .padding(.horizontal, 40)
            .padding(.bottom, geometry.safeAreaInsets.bottom > 0 ? 10 : 20)
        } else {
            Rectangle().fill(Color.clear)
                .frame(height: (50 + (geometry.safeAreaInsets.bottom > 0 ? 10 : 20)))
                .padding(.horizontal, 40)
        }
    }

    @ViewBuilder
    private func onboardingPageContent(data: OnboardingPageUIDataV2, geometry: GeometryProxy) -> some View {
        VStack(spacing: 0) {
            Spacer().frame(height: geometry.safeAreaInsets.top + geometry.size.height * 0.06)

            // 核心视觉区域
            ZStack {
                coreVisual(for: data.visualConceptIdentifier, geometry: geometry)
            }
            .frame(height: geometry.size.height * 0.45)

            Spacer().frame(height: geometry.size.height * 0.04)

            Text(data.title)
                .font(titleFont(for: geometry))
                .foregroundColor(OnboardingPageUIDataV2.primaryTextColor)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 25)
                .shadow(color: .black.opacity(0.4), radius: 3, x: 0, y: 2)

            Spacer().frame(height: geometry.size.height * 0.025)

            Text(data.subtitle)
                .font(subtitleFont(for: geometry))
                .foregroundColor(OnboardingPageUIDataV2.secondaryTextColor)
                .multilineTextAlignment(.center)
                .lineSpacing(6)
                .padding(.horizontal, 35)
                .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 1)
            
            Spacer()
        }
        .frame(width: geometry.size.width, height: geometry.size.height)
    }

    @ViewBuilder
    private func coreVisual(for concept: String, geometry: GeometryProxy) -> some View {
        Group {
            switch concept {
            case "seedOfHope":
                FluidSeedOfHope(geometry: geometry)
            case "wisdomIrrigation":
                FluidWisdomCore(geometry: geometry)
            case "ecosystemBloom":
                FluidEcosystemBloom(geometry: geometry)
            default:
                EmptyView()
            }
        }
    }
}

// MARK: - Previews

#Preview("Ultra Fluid Onboarding") {
    EAOnboardingView()
}

#Preview("Onboarding - Page 2") {
    EAOnboardingView()
} 