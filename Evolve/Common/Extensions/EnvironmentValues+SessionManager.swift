//
//  EnvironmentValues+SessionManager.swift
//  Evolve
//
//  Created by AI Assistant on 2025-06-05.
//

import SwiftUI
import SwiftData
import Foundation
import Combine

// MARK: - 环境值扩展：会话管理器

/// 环境值键：会话管理器
@preconcurrency 
private struct SessionManagerKey: EnvironmentKey {
    // ✅ 修复：使用nonisolated静态属性，通过闭包延迟初始化
    nonisolated static let defaultValue: EASessionManager = {
        // 在主线程上创建SessionManager
        return MainActor.assumeIsolated {
            EASessionManager()
        }
    }()
}



extension EnvironmentValues {
    
    /// 会话管理器环境值
    /// ✅ 修复：使用具体的EASessionManager类型，支持ObservableObject
    var sessionManager: EASessionManager {
        get { self[SessionManagerKey.self] }
        set { self[SessionManagerKey.self] = newValue }
    }
}



// MARK: - 视图扩展：便捷方法

extension View {
    
    /// 注入会话管理器到环境
    /// - Parameter sessionManager: 会话管理器实例
    /// - Returns: 配置了会话管理器的视图
    func sessionManager(_ sessionManager: EASessionManager) -> some View {
        self.environment(\.sessionManager, sessionManager)
    }
    
    /// 注入生产环境会话管理器到环境
    /// - Parameter repositoryContainer: Repository容器实例
    /// - Returns: 配置了生产环境会话管理器的视图
    @MainActor
    func productionSessionManager(repositoryContainer: EARepositoryContainer) -> some View {
        let sessionManager = EASessionManager(repositoryContainer: repositoryContainer)
        return self.environment(\.sessionManager, sessionManager)
    }
    
    #if DEBUG
    /// 注入Mock会话管理器到环境（仅用于测试和预览）
    /// - Returns: 配置了Mock会话管理器的视图
    func mockSessionManager() -> some View {
        // ✅ 修复：使用简单的EASessionManager实例，避免MainActor问题
        let mockSessionManager = EASessionManager()
        return self.environment(\.sessionManager, mockSessionManager)
    }
    #endif
} 