import SwiftUI

// MARK: - Color Hex 扩展

extension Color {
    
    // 🔑 系统性性能优化：静态颜色缓存，避免重复计算
    private static let colorCache: [String: Color] = [
        "2D3748": Color(red: 45.0/255.0, green: 55.0/255.0, blue: 72.0/255.0),
        "1A365D": Color(red: 26.0/255.0, green: 54.0/255.0, blue: 93.0/255.0),
        "2C3E50": Color(red: 44.0/255.0, green: 62.0/255.0, blue: 80.0/255.0),
        "40E0D0": Color(red: 64.0/255.0, green: 224.0/255.0, blue: 208.0/255.0)
    ]
    
    /// 从十六进制字符串创建Color，使用静态缓存优化性能
    /// - Parameter hex: 十六进制颜色字符串，支持"#FFFFFF"或"FFFFFF"格式
    /// - Returns: Color实例
    static func hexColor(_ hex: String) -> Color {
        let cleanHex = hex.trimmingCharacters(in: .whitespacesAndNewlines)
            .replacingOccurrences(of: "#", with: "")
        
        // 🔑 性能优化：优先使用静态缓存
        if let cachedColor = colorCache[cleanHex] {
            return cachedColor
        }
        
        // 🔑 回退到动态计算（但只在必要时）
        var rgbValue: UInt64 = 0
        Scanner(string: cleanHex).scanHexInt64(&rgbValue)
        
        let red = Double((rgbValue & 0xFF0000) >> 16) / 255.0
        let green = Double((rgbValue & 0x00FF00) >> 8) / 255.0
        let blue = Double(rgbValue & 0x0000FF) / 255.0
        
        return Color(red: red, green: green, blue: blue)
    }
    
    /// 可选构造：从十六进制字符串创建Color，失败返回nil
    init?(hex: String) {
        var cleanHex = hex.trimmingCharacters(in: .whitespacesAndNewlines)
        cleanHex = cleanHex.replacingOccurrences(of: "#", with: "")
        guard cleanHex.count == 6, let rgbValue = UInt64(cleanHex, radix: 16) else { return nil }
        let red = Double((rgbValue & 0xFF0000) >> 16) / 255.0
        let green = Double((rgbValue & 0x00FF00) >> 8) / 255.0
        let blue = Double(rgbValue & 0x0000FF) / 255.0
        self.init(red: red, green: green, blue: blue)
    }
} 