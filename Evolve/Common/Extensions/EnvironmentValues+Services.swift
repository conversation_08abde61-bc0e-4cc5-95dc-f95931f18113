//
//  EnvironmentValues+Services.swift
//  Evolve
//
//  Created by AI Assistant on 2025-01-27.
//

import SwiftUI
import SwiftData
import Foundation
import Combine

// MARK: - AI提醒管理器环境值

@preconcurrency
private struct AIInAppReminderManagerKey: EnvironmentKey {
    nonisolated static let defaultValue: EAAIInAppReminderManager = {
        return MainActor.assumeIsolated {
            EAAIInAppReminderManager()
        }
    }()
}

// MARK: - 通知代理环境值

@preconcurrency
private struct NotificationDelegateKey: EnvironmentKey {
    nonisolated static let defaultValue: NotificationDelegate = {
        return MainActor.assumeIsolated {
            NotificationDelegate()
        }
    }()
}

// MARK: - 性能监控器环境值

@preconcurrency
private struct RepositoryPerformanceMonitorKey: EnvironmentKey {
    nonisolated static let defaultValue: EARepositoryPerformanceMonitor = {
        return MainActor.assumeIsolated {
            EARepositoryPerformanceMonitor()
        }
    }()
}

// MARK: - 用户身份系统服务环境值（新增）

@preconcurrency
private struct UserIntegrityGuardKey: EnvironmentKey {
    static let defaultValue: EAUserIntegrityGuard? = nil
}

@preconcurrency
private struct AsyncProfileInitializerKey: EnvironmentKey {
    static let defaultValue: EAAsyncProfileInitializer? = nil
}

@preconcurrency
private struct UserIdentityMonitorKey: EnvironmentKey {
    static let defaultValue: EAUserIdentityMonitor? = nil
}

// MARK: - 错误处理系统服务环境值（阶段2.4新增）

@preconcurrency
private struct UserFriendlyErrorServiceKey: EnvironmentKey {
    static let defaultValue: EAUserFriendlyErrorService? = nil
}

@preconcurrency
private struct GracefulDegradationManagerKey: EnvironmentKey {
    static let defaultValue: EAGracefulDegradationManager? = nil
}

// MARK: - 性能阈值管理器环境值（阶段3新增）

@preconcurrency
private struct PerformanceThresholdManagerKey: EnvironmentKey {
    static let defaultValue: EAPerformanceThresholdManager? = nil
}

// MARK: - Environment扩展

extension EnvironmentValues {
    
    /// AI应用内提醒管理器
    var aiInAppReminderManager: EAAIInAppReminderManager {
        get { self[AIInAppReminderManagerKey.self] }
        set { self[AIInAppReminderManagerKey.self] = newValue }
    }
    
    /// 通知代理
    var notificationDelegate: NotificationDelegate {
        get { self[NotificationDelegateKey.self] }
        set { self[NotificationDelegateKey.self] = newValue }
    }
    
    /// Repository性能监控器
    var repositoryPerformanceMonitor: EARepositoryPerformanceMonitor {
        get { self[RepositoryPerformanceMonitorKey.self] }
        set { self[RepositoryPerformanceMonitorKey.self] = newValue }
    }
    
    // MARK: - 用户身份系统服务（新增）
    
    /// 用户完整性守护服务
    var userIntegrityGuard: EAUserIntegrityGuard? {
        get { self[UserIntegrityGuardKey.self] }
        set { self[UserIntegrityGuardKey.self] = newValue }
    }
    
    /// 异步档案初始化器
    var asyncProfileInitializer: EAAsyncProfileInitializer? {
        get { self[AsyncProfileInitializerKey.self] }
        set { self[AsyncProfileInitializerKey.self] = newValue }
    }
    
    /// 用户身份监控服务
    var userIdentityMonitor: EAUserIdentityMonitor? {
        get { self[UserIdentityMonitorKey.self] }
        set { self[UserIdentityMonitorKey.self] = newValue }
    }
    
    // MARK: - 错误处理系统服务（阶段2.4新增）
    
    /// 用户友好错误处理服务
    var userFriendlyErrorService: EAUserFriendlyErrorService? {
        get { self[UserFriendlyErrorServiceKey.self] }
        set { self[UserFriendlyErrorServiceKey.self] = newValue }
    }
    
    /// 降级策略管理器
    var gracefulDegradationManager: EAGracefulDegradationManager? {
        get { self[GracefulDegradationManagerKey.self] }
        set { self[GracefulDegradationManagerKey.self] = newValue }
    }
    
    // MARK: - 性能阈值管理器（阶段3新增）
    
    /// 性能阈值管理器
    var performanceThresholdManager: EAPerformanceThresholdManager? {
        get { self[PerformanceThresholdManagerKey.self] }
        set { self[PerformanceThresholdManagerKey.self] = newValue }
    }
}

// MARK: - 视图扩展：便捷方法

extension View {
    
    /// 注入AI提醒管理器到环境
    func aiInAppReminderManager(_ manager: EAAIInAppReminderManager) -> some View {
        self.environment(\.aiInAppReminderManager, manager)
    }
    
    /// 注入通知代理到环境
    func notificationDelegate(_ delegate: NotificationDelegate) -> some View {
        self.environment(\.notificationDelegate, delegate)
    }
    
    /// 注入性能监控器到环境
    func repositoryPerformanceMonitor(_ monitor: EARepositoryPerformanceMonitor) -> some View {
        self.environment(\.repositoryPerformanceMonitor, monitor)
    }
    
    // MARK: - 用户身份系统服务注入（新增）
    
    /// 注入用户完整性守护服务到环境
    func userIntegrityGuard(_ guardService: EAUserIntegrityGuard) -> some View {
        self.environment(\.userIntegrityGuard, guardService)
    }
    
    /// 注入异步档案初始化器到环境
    func asyncProfileInitializer(_ initializer: EAAsyncProfileInitializer) -> some View {
        self.environment(\.asyncProfileInitializer, initializer)
    }
    
    /// 注入用户身份监控服务到环境
    func userIdentityMonitor(_ monitor: EAUserIdentityMonitor) -> some View {
        self.environment(\.userIdentityMonitor, monitor)
    }
    
    /// 注入完整的用户身份系统服务到环境
    func userIdentityServices(
        guard guardService: EAUserIntegrityGuard,
        initializer: EAAsyncProfileInitializer,
        monitor: EAUserIdentityMonitor
    ) -> some View {
        self
            .environment(\.userIntegrityGuard, guardService)
            .environment(\.asyncProfileInitializer, initializer)
            .environment(\.userIdentityMonitor, monitor)
    }
    
    // MARK: - 错误处理系统服务注入（阶段2.4新增）
    
    /// 注入用户友好错误处理服务到环境
    func userFriendlyErrorService(_ service: EAUserFriendlyErrorService) -> some View {
        self.environment(\.userFriendlyErrorService, service)
    }
    
    /// 注入降级策略管理器到环境
    func gracefulDegradationManager(_ manager: EAGracefulDegradationManager) -> some View {
        self.environment(\.gracefulDegradationManager, manager)
    }
    
    /// 注入完整的错误处理系统服务到环境
    func errorHandlingServices(
        userFriendlyService: EAUserFriendlyErrorService,
        degradationManager: EAGracefulDegradationManager
    ) -> some View {
        self
            .environment(\.userFriendlyErrorService, userFriendlyService)
            .environment(\.gracefulDegradationManager, degradationManager)
    }
    
    // MARK: - 性能阈值管理器注入（阶段3新增）
    
    /// 注入性能阈值管理器到环境
    func performanceThresholdManager(_ manager: EAPerformanceThresholdManager) -> some View {
        self.environment(\.performanceThresholdManager, manager)
    }
    
    #if DEBUG
    /// 注入Mock服务到环境（仅用于测试和预览）
    func mockServices() -> some View {
        let mockAIManager = EAAIInAppReminderManager()
        let mockDelegate = NotificationDelegate()
        let mockMonitor = EARepositoryPerformanceMonitor()
        
        return self
            .environment(\.aiInAppReminderManager, mockAIManager)
            .environment(\.notificationDelegate, mockDelegate)
            .environment(\.repositoryPerformanceMonitor, mockMonitor)
    }
    #endif
} 