import UIKit

extension UIImage {
    
    /// 🔑 调整图片尺寸到指定大小
    func resized(to targetSize: CGSize) -> UIImage? {
        let size = self.size
        
        let widthRatio  = targetSize.width  / size.width
        let heightRatio = targetSize.height / size.height
        
        // 计算新的尺寸，保持宽高比
        var newSize: CGSize
        if widthRatio > heightRatio {
            newSize = CGSize(width: size.width * heightRatio, height: size.height * heightRatio)
        } else {
            newSize = CGSize(width: size.width * widthRatio, height: size.height * widthRatio)
        }
        
        // 生成缩放后的图片
        let rect = CGRect(x: 0, y: 0, width: newSize.width, height: newSize.height)
        
        UIGraphicsBeginImageContextWithOptions(newSize, false, 1.0)
        defer { UIGraphicsEndImageContext() }
        
        self.draw(in: rect)
        
        return UIGraphicsGetImageFromCurrentImageContext()
    }
    
    /// 🔑 生成指定最大尺寸的缩略图
    func thumbnail(maxSize: CGFloat = 200) -> UIImage? {
        let size = self.size
        let maxDimension = max(size.width, size.height)
        
        if maxDimension <= maxSize {
            return self
        }
        
        let scale = maxSize / maxDimension
        let newSize = CGSize(width: size.width * scale, height: size.height * scale)
        
        return resized(to: newSize)
    }
} 