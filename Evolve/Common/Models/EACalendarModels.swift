import SwiftUI
import Foundation

// MARK: - 日历数据模型

/// 日历日期数据结构（用于创建页面的性能优化）
/// ✅ iOS 18.5真机性能优化：将所有计算逻辑从视图层分离到数据层
struct CalendarDayCreation: Identifiable, Hashable {
    // 🔥 决定性修复：使用稳定的ID，基于日期和月份确保唯一性
    var id: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: date)
    }

    let date: Date
    let dayNumber: String        // "1", "2", "31"
    let isSelectable: Bool       // 是否可选择
    let isSelected: Bool         // 是否已被选中
    let isToday: Bool           // 是否是今天
    let isInCurrentMonth: Bool   // 是否属于当前月份
    let isWeekend: Bool         // 是否是周末
    
    /// 创建空白日期（用于月初填充）
    static func empty() -> CalendarDayCreation {
        return CalendarDayCreation(
            date: Date(),
            dayNumber: "",
            isSelectable: false,
            isSelected: false,
            isToday: false,
            isInCurrentMonth: false,
            isWeekend: false
        )
    }
}

/// 日历日期数据结构（用于详情页面的状态显示）
/// 保持原有的CalendarDay结构，用于详情页面
struct CalendarDayDetail {
    let date: Date
    let dayNumber: Int
    let isToday: Bool
    let isCompleted: Bool
    let isMissed: Bool
}
