# Xcode 预览问题排查指南

## TextField 在预览中无法输入文字的问题

### 问题描述
在 Xcode 预览中，TextField 无法响应键盘输入，但在 iOS 模拟器中工作正常。这是一个已知的 Xcode 预览系统 bug。

### 解决方案

#### 1. 使用 PreviewProvider 替代 #Preview 宏
```swift
// ❌ 避免使用 #Preview 宏
#Preview {
    MyView()
}

// ✅ 使用 PreviewProvider
struct MyView_Previews: PreviewProvider {
    static var previews: some View {
        MyView()
    }
}
```

#### 2. 使用预览辅助工具
```swift
// 使用项目中的 PreviewHelper
PreviewHelper.textFieldPreviewWrapper {
    MyTextFieldView()
}
```

#### 3. 添加 NavigationView 包装
```swift
NavigationView {
    MyTextFieldView()
}
.navigationViewStyle(StackNavigationViewStyle())
```

#### 4. 创建专门的交互式预览
```swift
private struct InteractivePreview: View {
    @StateObject private var viewModel = MyViewModel()
    
    var body: some View {
        NavigationView {
            MyView()
                .environmentObject(viewModel)
        }
    }
}
```

### 系统级解决方案

#### 1. 重置预览缓存
```bash
# 在终端中执行
xcrun simctl --set previews shutdown all
xcrun simctl --set previews delete all
```

#### 2. 清理 Xcode 缓存
- 关闭 Xcode
- 删除 `~/Library/Developer/Xcode/DerivedData` 中的项目文件夹
- 重新打开 Xcode

#### 3. 重启预览服务
- 在 Xcode 中按 `Cmd + Option + P` 刷新预览
- 或者点击预览面板中的刷新按钮

### 测试方法

#### 使用专门的测试组件
项目中提供了以下测试组件：

1. `EATextFieldPreviewTest` - 综合 TextField 测试
2. `TextFieldPreviewTester` - 简单 TextField 测试
3. `PreviewHelper` - 预览辅助工具

#### 测试步骤
1. 打开 `EATextFieldPreviewTest.swift` 文件
2. 在预览面板中选择 "TextField 键盘测试"
3. 尝试点击输入框并输入文字
4. 观察是否能正常输入和显示

### 已知限制

1. **预览环境限制**：Xcode 预览环境不是完整的 iOS 环境，某些功能可能受限
2. **键盘支持**：预览中的虚拟键盘支持有限
3. **焦点管理**：@FocusState 在预览中可能不完全工作

### 最佳实践

1. **开发阶段**：使用 iOS 模拟器进行 TextField 功能测试
2. **预览用途**：主要用于 UI 布局和样式验证
3. **交互测试**：复杂交互功能在真机或模拟器中测试
4. **预览优化**：为预览创建简化的测试版本

### 项目中的实现

项目已经实现了以下优化：

- ✅ 所有 TextField 相关视图使用 PreviewProvider
- ✅ 提供专门的交互式预览组件
- ✅ 创建预览辅助工具 `PreviewHelper`
- ✅ 添加预览修复扩展方法
- ✅ 提供多种测试预览配置

### 如果问题仍然存在

1. 确认 Xcode 版本（建议使用最新版本）
2. 检查 macOS 版本兼容性
3. 尝试在不同设备预览中测试
4. 使用 iOS 模拟器作为主要测试环境
5. 考虑使用 Xcode Instruments 进行深度调试

### 相关文件

- `Evolve/UIComponents/EATextFieldPreviewTest.swift` - TextField 测试组件
- `Evolve/Common/Tools/PreviewHelper.swift` - 预览辅助工具
- 各个 Feature 模块中的 PreviewProvider 实现 