import SwiftUI

/// 预览辅助工具
/// 提供解决Xcode预览中常见问题的方法和配置
struct PreviewHelper {
    
    /// 解决TextField在预览中无法输入的问题
    /// 使用这个包装器来包装包含TextField的视图
    static func textFieldPreviewWrapper<Content: View>(
        @ViewBuilder content: @escaping () -> Content
    ) -> some View {
        // 使用NavigationView包装，有时能解决预览问题
        NavigationView {
            content()
        }
        .navigationViewStyle(StackNavigationViewStyle()) // 强制使用Stack样式
    }
    
    /// 创建一个支持键盘输入的预览环境
    static func keyboardSupportedPreview<Content: View>(
        title: String = "预览测试",
        @ViewBuilder content: @escaping () -> Content
    ) -> some View {
        ZStack {
            // 背景
            Color.black.ignoresSafeArea()
            
            VStack {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding()
                
                content()
                
                Spacer()
            }
        }
    }
    
    /// 预览设备配置
    static let previewDevices = [
        "iPhone 15 Pro",
        "iPhone 15",
        "iPhone SE (3rd generation)",
        "iPad Pro (11-inch) (4th generation)"
    ]
    
    /// 创建多设备预览
    static func multiDevicePreview<Content: View>(
        @ViewBuilder content: @escaping () -> Content
    ) -> some View {
        ForEach(previewDevices, id: \.self) { device in
            content()
                .previewDevice(PreviewDevice(rawValue: device))
                .previewDisplayName(device)
        }
    }
}

/// TextField预览测试专用组件
/// 用于验证TextField在预览中的输入功能
struct TextFieldPreviewTester: View {
    @State private var testText = ""
    let placeholder: String
    let title: String
    
    init(title: String = "TextField测试", placeholder: String = "请输入文字") {
        self.title = title
        self.placeholder = placeholder
    }
    
    var body: some View {
        VStack(spacing: 20) {
            Text(title)
                .font(.title2)
                .fontWeight(.bold)
            
            // 使用原生TextField测试
            TextField(placeholder, text: $testText)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .padding()
            
            // 使用自定义EATextField测试
            EATextField(
                text: $testText,
                placeholder: placeholder,
                type: .text,
                leftIcon: "textformat"
            )
            .padding()
            
            // 显示当前值
            Text("当前输入: \(testText.isEmpty ? "无" : testText)")
                .font(.caption)
                .foregroundColor(.secondary)
            
            Spacer()
        }
        .padding()
    }
}

// MARK: - 预览修复扩展

extension View {
    /// 为包含TextField的视图添加预览修复
    func textFieldPreviewFix() -> some View {
        self
            .onAppear {
                // 强制刷新预览环境
                DispatchQueue.main.async {
                    // 这个空操作有时能触发预览刷新
                }
            }
    }
    
    /// 添加预览键盘支持
    func previewKeyboardSupport() -> some View {
        NavigationView {
            self
        }
        .navigationViewStyle(StackNavigationViewStyle())
    }
}

// MARK: - 预览

struct PreviewHelper_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // 基础TextField测试
            PreviewHelper.keyboardSupportedPreview(title: "基础测试") {
                TextFieldPreviewTester()
            }
            .previewDisplayName("基础TextField测试")
            
            // 包装器测试
            PreviewHelper.textFieldPreviewWrapper {
                TextFieldPreviewTester(
                    title: "包装器测试",
                    placeholder: "测试包装器效果"
                )
            }
            .previewDisplayName("包装器TextField测试")
            
            // 原生TextField对比测试
            VStack {
                Text("原生TextField对比")
                    .font(.headline)
                    .padding()
                
                TextField("原生TextField", text: .constant(""))
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .padding()
                
                EATextField(
                    text: .constant(""),
                    placeholder: "自定义EATextField",
                    type: .text,
                    leftIcon: "textformat"
                )
                .padding()
                
                Spacer()
            }
            .previewDisplayName("原生vs自定义对比")
        }
    }
} 